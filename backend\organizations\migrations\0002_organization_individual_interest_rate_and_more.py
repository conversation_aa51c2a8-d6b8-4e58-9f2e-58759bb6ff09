# Generated by Django 5.2.3 on 2025-07-16 03:38

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("organizations", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="organization",
            name="individual_interest_rate",
            field=models.DecimalField(
                decimal_places=2,
                default=2.0,
                help_text="Monthly interest rate percentage for individual taxpayers",
                max_digits=5,
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="individual_penalty_rate",
            field=models.DecimalField(
                decimal_places=2,
                default=5.0,
                help_text="Penalty rate percentage for individual taxpayers",
                max_digits=5,
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="organization_interest_rate",
            field=models.DecimalField(
                decimal_places=2,
                default=3.0,
                help_text="Monthly interest rate percentage for organization taxpayers",
                max_digits=5,
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="organization_penalty_rate",
            field=models.DecimalField(
                decimal_places=2,
                default=10.0,
                help_text="Penalty rate percentage for organization taxpayers",
                max_digits=5,
            ),
        ),
    ]
