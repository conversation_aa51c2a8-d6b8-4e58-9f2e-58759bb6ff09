[{"model": "taxpayers.taxpayerlevel", "pk": "550e8400-e29b-41d4-a716-446655440001", "fields": {"name": "Category A - Large Taxpayers", "code": "A", "description": "Large taxpayers with annual turnover above 10 million ETB. Subject to comprehensive tax obligations and regular audits.", "minimum_annual_turnover": "10000000.00", "maximum_annual_turnover": null, "is_active": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}}, {"model": "taxpayers.taxpayerlevel", "pk": "550e8400-e29b-41d4-a716-446655440002", "fields": {"name": "Category B - Medium Taxpayers", "code": "B", "description": "Medium taxpayers with annual turnover between 1 million and 10 million ETB. Subject to standard tax obligations.", "minimum_annual_turnover": "1000000.00", "maximum_annual_turnover": "10000000.00", "is_active": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}}, {"model": "taxpayers.taxpayerlevel", "pk": "550e8400-e29b-41d4-a716-446655440003", "fields": {"name": "Category C - Small Taxpayers", "code": "C", "description": "Small taxpayers with annual turnover between 100,000 and 1 million ETB. Subject to simplified tax obligations.", "minimum_annual_turnover": "100000.00", "maximum_annual_turnover": "1000000.00", "is_active": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}}, {"model": "taxpayers.taxpayerlevel", "pk": "550e8400-e29b-41d4-a716-446655440004", "fields": {"name": "Category D - Micro Taxpayers", "code": "D", "description": "Micro taxpayers with annual turnover below 100,000 ETB. Subject to minimal tax obligations.", "minimum_annual_turnover": null, "maximum_annual_turnover": "100000.00", "is_active": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}}, {"model": "taxpayers.organizationbusinesstype", "pk": "550e8400-e29b-41d4-a716-446655440011", "fields": {"code": "PLC", "name": "Private Limited Company", "description": "Private limited company with limited liability for shareholders.", "requires_vat_registration": true, "is_active": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}}, {"model": "taxpayers.organizationbusinesstype", "pk": "550e8400-e29b-41d4-a716-446655440012", "fields": {"code": "SC", "name": "Share Company", "description": "Share company with publicly traded shares.", "requires_vat_registration": true, "is_active": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}}, {"model": "taxpayers.organizationbusinesstype", "pk": "550e8400-e29b-41d4-a716-446655440013", "fields": {"code": "COOP", "name": "Cooperative", "description": "Cooperative organization owned and operated by members.", "requires_vat_registration": false, "is_active": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}}, {"model": "taxpayers.organizationbusinesstype", "pk": "550e8400-e29b-41d4-a716-446655440014", "fields": {"code": "NGO", "name": "Non-Governmental Organization", "description": "Non-profit organization operating independently of government.", "requires_vat_registration": false, "is_active": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}}, {"model": "taxpayers.organizationbusinesstype", "pk": "550e8400-e29b-41d4-a716-446655440015", "fields": {"code": "SOLE", "name": "Sole Proprietorship", "description": "Business owned and operated by a single individual.", "requires_vat_registration": false, "is_active": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}}, {"model": "taxpayers.businesssector", "pk": "550e8400-e29b-41d4-a716-************", "fields": {"code": "AGR", "name": "Agriculture", "description": "Agricultural activities including crop production, livestock, forestry, and fishing.", "is_active": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z", "created_by": null}}, {"model": "taxpayers.businesssector", "pk": "550e8400-e29b-41d4-a716-446655440022", "fields": {"code": "MAN", "name": "Manufacturing", "description": "Manufacturing and industrial production activities.", "is_active": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z", "created_by": null}}, {"model": "taxpayers.businesssector", "pk": "550e8400-e29b-41d4-a716-************", "fields": {"code": "SER", "name": "Services", "description": "Service-based businesses including consulting, healthcare, education, and professional services.", "is_active": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z", "created_by": null}}, {"model": "taxpayers.businesssector", "pk": "550e8400-e29b-41d4-a716-************", "fields": {"code": "TRA", "name": "Trade", "description": "Trading activities including wholesale and retail trade.", "is_active": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z", "created_by": null}}, {"model": "taxpayers.businesssector", "pk": "550e8400-e29b-41d4-a716-446655440025", "fields": {"code": "CON", "name": "Construction", "description": "Construction and real estate development activities.", "is_active": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z", "created_by": null}}, {"model": "taxpayers.businesssubsector", "pk": "550e8400-e29b-41d4-a716-446655440031", "fields": {"business_sector": "550e8400-e29b-41d4-a716-************", "code": "001", "name": "Crop Production", "description": "Growing of crops including cereals, vegetables, fruits, and cash crops.", "is_active": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z", "created_by": null}}, {"model": "taxpayers.businesssubsector", "pk": "550e8400-e29b-41d4-a716-446655440032", "fields": {"business_sector": "550e8400-e29b-41d4-a716-************", "code": "002", "name": "Livestock", "description": "Raising of livestock including cattle, sheep, goats, and poultry.", "is_active": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z", "created_by": null}}, {"model": "taxpayers.businesssubsector", "pk": "550e8400-e29b-41d4-a716-************", "fields": {"business_sector": "550e8400-e29b-41d4-a716-************", "code": "001", "name": "Professional Services", "description": "Professional services including legal, accounting, consulting, and engineering services.", "is_active": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z", "created_by": null}}, {"model": "taxpayers.businesssubsector", "pk": "550e8400-e29b-41d4-a716-************", "fields": {"business_sector": "550e8400-e29b-41d4-a716-************", "code": "001", "name": "Retail Trade", "description": "Retail sale of goods to consumers through stores, markets, and online platforms.", "is_active": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z", "created_by": null}}]