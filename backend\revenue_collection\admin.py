"""
Django Admin Configuration for Revenue Collection Models

This module provides comprehensive admin interfaces for all revenue collection
models with proper filtering, search capabilities, and administrative actions.
"""

from django.contrib import admin
from django.db.models import Sum
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import (
    RegionalCategory, CityServiceCategory,
    RegionalRevenueSource, CityServiceRevenueSource,
    RevenuePeriod,
    RegionalRevenueCollection, CityServiceRevenueCollection,
    RevenueSummary, TaxpayerPaymentSummary
)


@admin.register(RegionalCategory)
class RegionalCategoryAdmin(admin.ModelAdmin):
    """Admin interface for Regional Categories"""
    list_display = ('code', 'name', 'is_active', 'revenue_sources_count', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'code', 'description')
    readonly_fields = ('id', 'created_at', 'updated_at')
    ordering = ('code', 'name')

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'code', 'description', 'is_active')
        }),
        ('Metadata', {
            'fields': ('id', 'created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def revenue_sources_count(self, obj):
        """Display count of revenue sources in this category"""
        count = obj.revenue_sources.count()
        if count > 0:
            url = reverse('admin:revenue_collection_regionalrevenuesource_changelist')
            return format_html('<a href="{}?category__id__exact={}">{} sources</a>', url, obj.id, count)
        return '0 sources'
    revenue_sources_count.short_description = 'Revenue Sources'

    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(CityServiceCategory)
class CityServiceCategoryAdmin(admin.ModelAdmin):
    """Admin interface for City Service Categories"""
    list_display = ('code', 'name', 'is_active', 'revenue_sources_count', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'code', 'description')
    readonly_fields = ('id', 'created_at', 'updated_at')
    ordering = ('code', 'name')

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'code', 'description', 'is_active')
        }),
        ('Metadata', {
            'fields': ('id', 'created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def revenue_sources_count(self, obj):
        """Display count of revenue sources in this category"""
        count = obj.revenue_sources.count()
        if count > 0:
            url = reverse('admin:revenue_collection_cityservicerevenuesource_changelist')
            return format_html('<a href="{}?category__id__exact={}">{} sources</a>', url, obj.id, count)
        return '0 sources'
    revenue_sources_count.short_description = 'Revenue Sources'

    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(RegionalRevenueSource)
class RegionalRevenueSourceAdmin(admin.ModelAdmin):
    """Admin interface for Regional Revenue Sources"""
    list_display = ('code', 'name', 'category', 'is_active', 'collections_count', 'created_at')
    list_filter = ('category', 'is_active', 'created_at')
    search_fields = ('name', 'code', 'description', 'category__name')
    readonly_fields = ('id', 'created_at', 'updated_at')
    ordering = ('category__name', 'code', 'name')

    fieldsets = (
        ('Basic Information', {
            'fields': ('category', 'name', 'code', 'description', 'is_active')
        }),
        ('Metadata', {
            'fields': ('id', 'created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def collections_count(self, obj):
        """Display count of collections for this source"""
        count = obj.regionalrevenuecollection_set.count()
        if count > 0:
            url = reverse('admin:revenue_collection_regionalrevenuecollection_changelist')
            return format_html('<a href="{}?revenue_source__id__exact={}">{} collections</a>', url, obj.id, count)
        return '0 collections'
    collections_count.short_description = 'Collections'

    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(CityServiceRevenueSource)
class CityServiceRevenueSourceAdmin(admin.ModelAdmin):
    """Admin interface for City Service Revenue Sources"""
    list_display = ('code', 'name', 'category', 'is_active', 'collections_count', 'created_at')
    list_filter = ('category', 'is_active', 'created_at')
    search_fields = ('name', 'code', 'description', 'category__name')
    readonly_fields = ('id', 'created_at', 'updated_at')
    ordering = ('category__name', 'code', 'name')

    fieldsets = (
        ('Basic Information', {
            'fields': ('category', 'name', 'code', 'description', 'is_active')
        }),
        ('Metadata', {
            'fields': ('id', 'created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def collections_count(self, obj):
        """Display count of collections for this source"""
        count = obj.cityservicerevenuecollection_set.count()
        if count > 0:
            url = reverse('admin:revenue_collection_cityservicerevenuecollection_changelist')
            return format_html('<a href="{}?revenue_source__id__exact={}">{} collections</a>', url, obj.id, count)
        return '0 collections'
    collections_count.short_description = 'Collections'

    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(RevenuePeriod)
class RevenuePeriodAdmin(admin.ModelAdmin):
    """Admin interface for Revenue Periods"""
    list_display = ('code', 'name', 'start_date', 'end_date', 'is_closed', 'is_current_period', 'collections_count')
    list_filter = ('is_closed', 'start_date', 'end_date')
    search_fields = ('name', 'code')
    readonly_fields = ('id', 'created_at', 'updated_at', 'is_current_period')
    ordering = ('-start_date',)
    date_hierarchy = 'start_date'

    fieldsets = (
        ('Period Information', {
            'fields': ('name', 'code', 'start_date', 'end_date', 'is_closed')
        }),
        ('Status', {
            'fields': ('is_current_period',),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('id', 'created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def is_current_period(self, obj):
        """Display if this is the current period"""
        if obj.is_current:
            return format_html('<span style="color: green;">✓ Current</span>')
        return format_html('<span style="color: gray;">Not Current</span>')
    is_current_period.short_description = 'Current Period'

    def collections_count(self, obj):
        """Display total collections count for this period"""
        regional_count = obj.regionalrevenuecollection_set.count()
        city_count = obj.cityservicerevenuecollection_set.count()
        total_count = regional_count + city_count

        if total_count > 0:
            return format_html(
                'Regional: {} | City: {} | <strong>Total: {}</strong>',
                regional_count, city_count, total_count
            )
        return '0 collections'
    collections_count.short_description = 'Collections'

    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


class BaseRevenueCollectionAdmin(admin.ModelAdmin):
    """Base admin class for revenue collections"""
    list_per_page = 50
    date_hierarchy = 'collection_date'
    readonly_fields = ('id', 'taxpayer_display', 'recorded_date', 'last_modified')

    fieldsets = (
        ('Collection Information', {
            'fields': ('revenue_source', 'period', 'amount', 'collection_date', 'receipt_number')
        }),
        ('Payment Information', {
            'fields': ('due_date', 'payment_date', 'paid_amount', 'payment_status')
        }),
        ('Penalty and Interest', {
            'fields': (
                ('penalty_amount', 'penalty_rate', 'penalty_calculated_date'),
                ('interest_amount', 'interest_rate', 'interest_calculated_date')
            ),
            'classes': ('collapse',)
        }),
        ('Taxpayer Information', {
            'fields': ('individual_taxpayer', 'organization_taxpayer', 'taxpayer_display')
        }),
        ('Additional Information', {
            'fields': ('notes',)
        }),
        ('Metadata', {
            'fields': ('id', 'recorded_by', 'recorded_date', 'last_modified_by', 'last_modified'),
            'classes': ('collapse',)
        }),
    )

    def taxpayer_display(self, obj):
        """Display taxpayer information with link"""
        taxpayer = obj.taxpayer
        if not taxpayer:
            return "No taxpayer"

        if obj.individual_taxpayer:
            url = reverse('admin:taxpayers_individualtaxpayer_change', args=[taxpayer.id])
            return format_html('<a href="{}">{} (TIN: {})</a>', url, taxpayer.get_full_name(), taxpayer.tin)
        elif obj.organization_taxpayer:
            url = reverse('admin:taxpayers_organizationtaxpayer_change', args=[taxpayer.id])
            return format_html('<a href="{}">{} (TIN: {})</a>', url, taxpayer.business_name, taxpayer.tin)

        return f"{obj.taxpayer_name} (TIN: {obj.taxpayer_tin})"
    taxpayer_display.short_description = 'Taxpayer'

    def payment_status_display(self, obj):
        """Display payment status with color coding"""
        status_colors = {
            'PENDING': '#ffc107',  # Yellow
            'PARTIAL': '#17a2b8',  # Blue
            'OVERDUE': '#dc3545',  # Red
            'PAID': '#28a745',     # Green
        }

        color = status_colors.get(obj.payment_status, '#6c757d')
        return format_html(
            '<span style="background: {}; color: white; padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">{}</span>',
            color, obj.payment_status
        )
    payment_status_display.short_description = 'Payment Status'

    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.recorded_by = request.user
        else:  # Updating existing object
            obj.last_modified_by = request.user

        # Update payment status when saving
        obj.update_payment_status()
        super().save_model(request, obj, form, change)


@admin.register(RegionalRevenueCollection)
class RegionalRevenueCollectionAdmin(BaseRevenueCollectionAdmin):
    """Admin interface for Regional Revenue Collections"""
    list_display = ('taxpayer_name_display', 'revenue_source', 'period', 'amount', 'payment_status_display', 'collection_date', 'recorded_by')
    list_filter = (
        'revenue_source__category',
        'revenue_source',
        'period',
        'payment_status',
        'collection_date',
        'recorded_by'
    )
    search_fields = (
        'individual_taxpayer__first_name',
        'individual_taxpayer__last_name',
        'individual_taxpayer__tin',
        'organization_taxpayer__business_name',
        'organization_taxpayer__tin',
        'revenue_source__name',
        'receipt_number'
    )

    def taxpayer_name_display(self, obj):
        """Display taxpayer name with type indicator"""
        if obj.individual_taxpayer:
            return format_html('<span title="Individual">👤 {}</span>', obj.taxpayer_name)
        elif obj.organization_taxpayer:
            return format_html('<span title="Organization">🏢 {}</span>', obj.taxpayer_name)
        return obj.taxpayer_name
    taxpayer_name_display.short_description = 'Taxpayer'


@admin.register(CityServiceRevenueCollection)
class CityServiceRevenueCollectionAdmin(BaseRevenueCollectionAdmin):
    """Admin interface for City Service Revenue Collections"""
    list_display = ('taxpayer_name_display', 'revenue_source', 'period', 'amount', 'payment_status_display', 'collection_date', 'recorded_by')
    list_filter = (
        'revenue_source__category',
        'revenue_source',
        'payment_status',
        'period',
        'collection_date',
        'recorded_by'
    )
    search_fields = (
        'individual_taxpayer__first_name',
        'individual_taxpayer__last_name',
        'individual_taxpayer__tin',
        'organization_taxpayer__business_name',
        'organization_taxpayer__tin',
        'revenue_source__name',
        'receipt_number'
    )

    def taxpayer_name_display(self, obj):
        """Display taxpayer name with type indicator"""
        if obj.individual_taxpayer:
            return format_html('<span title="Individual">👤 {}</span>', obj.taxpayer_name)
        elif obj.organization_taxpayer:
            return format_html('<span title="Organization">🏢 {}</span>', obj.taxpayer_name)
        return obj.taxpayer_name
    taxpayer_name_display.short_description = 'Taxpayer'


@admin.register(RevenueSummary)
class RevenueSummaryAdmin(admin.ModelAdmin):
    """Admin interface for Revenue Summaries"""
    list_display = (
        'period', 'location_display', 'regional_total', 'city_service_total',
        'grand_total', 'last_updated'
    )
    list_filter = ('period', 'region', 'city', 'subcity', 'last_updated')
    search_fields = (
        'period__name',
        'region__name',
        'city__name',
        'subcity__name',
        'kebele__name'
    )
    readonly_fields = (
        'id', 'summary_date', 'last_updated', 'location_hierarchy_display',
        'regional_total', 'city_service_total', 'grand_total'
    )
    ordering = ('-period__start_date', '-last_updated')
    actions = ['update_summary_totals']

    fieldsets = (
        ('Period Information', {
            'fields': ('period',)
        }),
        ('Location Hierarchy', {
            'fields': ('country', 'region', 'zone', 'city', 'subcity', 'kebele', 'location_hierarchy_display')
        }),
        ('Revenue Totals', {
            'fields': ('regional_total', 'city_service_total', 'grand_total'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('id', 'created_by', 'summary_date', 'last_updated'),
            'classes': ('collapse',)
        }),
    )

    def location_display(self, obj):
        """Display location hierarchy in a compact format"""
        parts = []
        if obj.kebele:
            parts.append(f"Kebele {obj.kebele.name}")
        if obj.subcity:
            parts.append(f"SubCity {obj.subcity.name}")
        if obj.city:
            parts.append(f"City {obj.city.name}")
        if obj.region:
            parts.append(f"Region {obj.region.name}")

        return " → ".join(reversed(parts)) if parts else "All Locations"
    location_display.short_description = 'Location'

    def location_hierarchy_display(self, obj):
        """Display full location hierarchy"""
        hierarchy = []
        if obj.country:
            hierarchy.append(f"Country: {obj.country.name}")
        if obj.region:
            hierarchy.append(f"Region: {obj.region.name}")
        if obj.zone:
            hierarchy.append(f"Zone: {obj.zone.name}")
        if obj.city:
            hierarchy.append(f"City: {obj.city.name}")
        if obj.subcity:
            hierarchy.append(f"SubCity: {obj.subcity.name}")
        if obj.kebele:
            hierarchy.append(f"Kebele: {obj.kebele.name}")

        return mark_safe("<br>".join(hierarchy)) if hierarchy else "All Locations"
    location_hierarchy_display.short_description = 'Location Hierarchy'

    def update_summary_totals(self, request, queryset):
        """Admin action to update summary totals"""
        updated_count = 0
        for summary in queryset:
            summary.update_totals()
            updated_count += 1

        self.message_user(
            request,
            f"Successfully updated totals for {updated_count} revenue summaries."
        )
    update_summary_totals.short_description = "Update selected summary totals"

    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)
        # Auto-update totals when saving
        obj.update_totals()


@admin.register(TaxpayerPaymentSummary)
class TaxpayerPaymentSummaryAdmin(admin.ModelAdmin):
    """Admin interface for Taxpayer Payment Summaries"""
    list_display = (
        'taxpayer_name', 'taxpayer_tin', 'period', 'total_assessed',
        'total_paid', 'total_outstanding', 'total_overdue',
        'payment_completion_rate_display', 'last_payment_date'
    )
    list_filter = (
        'period', 'created_at', 'last_payment_date',
    )
    search_fields = (
        'individual_taxpayer__full_name',
        'individual_taxpayer__tin',
        'organization_taxpayer__business_name',
        'organization_taxpayer__tin',
    )
    readonly_fields = (
        'taxpayer_name', 'taxpayer_tin', 'payment_completion_rate',
        'created_at', 'updated_at'
    )
    fieldsets = (
        ('Taxpayer Information', {
            'fields': ('individual_taxpayer', 'organization_taxpayer', 'period')
        }),
        ('Payment Summary', {
            'fields': (
                'total_assessed', 'total_paid', 'total_outstanding', 'total_overdue',
                'payment_completion_rate'
            )
        }),
        ('Collection Counts', {
            'fields': ('total_collections', 'paid_collections', 'overdue_collections')
        }),
        ('Important Dates', {
            'fields': ('last_payment_date', 'next_due_date')
        }),
        ('Audit Information', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    actions = ['update_summaries']

    def taxpayer_name(self, obj):
        return obj.taxpayer_name
    taxpayer_name.short_description = "Taxpayer Name"

    def taxpayer_tin(self, obj):
        return obj.taxpayer_tin
    taxpayer_tin.short_description = "TIN"

    def payment_completion_rate_display(self, obj):
        rate = obj.payment_completion_rate
        color = 'green' if rate >= 80 else 'orange' if rate >= 50 else 'red'
        return format_html(
            '<span style="color: {};">{:.1f}%</span>',
            color, rate
        )
    payment_completion_rate_display.short_description = "Completion Rate"

    def update_summaries(self, request, queryset):
        """Update selected payment summaries"""
        updated = 0
        for summary in queryset:
            summary.update_summary()
            summary.save()
            updated += 1

        self.message_user(
            request,
            f"Successfully updated {updated} payment summaries."
        )
    update_summaries.short_description = "Update selected payment summaries"

    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)
        # Auto-update summary when saving
        obj.update_summary()
