import axios from 'axios';
import type { AxiosInstance } from 'axios';

// Base API configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api';

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem('refresh_token');
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/accounts/auth/token/refresh/`, {
            refresh: refreshToken,
          });

          const { access } = response.data;
          localStorage.setItem('access_token', access);

          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${access}`;
          return apiClient(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// API response types
export interface LoginResponse {
  access: string;
  refresh: string;
  user: any;
}

export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  full_name: string;
  role: string;
  employee_id?: string;
  phone_number?: string;
  department?: string;
  organization: number;
  organization_name: string;
  can_approve_requests: boolean;
  can_manage_documents: boolean;
  is_read_only: boolean;
  date_joined: string;
  last_login?: string;
}

export interface Organization {
  id: number;
  name: string;
  short_name: string;
  logo?: string;
  email?: string;
  phone?: string;
  address_line1?: string;
  address_line2?: string;
  city?: string;
  state_province?: string;
  postal_code?: string;
  country: string;
  full_address: string;
  office_hours_start: string;
  office_hours_end: string;
  office_hours_display: string;
  is_active: boolean;
  document_retention_days: number;
  max_file_size_mb: number;
  user_count: number;
  department_count: number;
  created_at: string;
  updated_at: string;
}

export interface Document {
  id: string;
  title: string;
  description?: string;
  document_type: number;
  document_type_name: string;
  organization_name: string;
  mode: 'physical' | 'digital' | 'hybrid';
  status: 'active' | 'checked_out' | 'archived' | 'destroyed' | 'lost';
  tags: string[];
  reference_number?: string;
  document_date?: string;
  expiry_date?: string;
  document_file?: string;
  file_name?: string;
  file_number?: string;
  kent?: number;
  kent_location?: string;
  kent_code?: string;
  file?: string;
  file_size?: number;
  number_of_pages?: number;
  barcode_image?: string;
  qr_code_image?: string;
  location_display: string;
  is_expired: boolean;
  is_physical: boolean;
  is_digital: boolean;
  can_be_requested: boolean;
  retention_date: string;
  is_active: boolean;
  created_by: number;
  created_by_name: string;
  created_at: string;
  updated_at: string;
}

export interface DocumentRequest {
  id: string;
  request_number: string;
  requested_by: number;
  requested_by_name: string;
  documents: number[];
  documents_detail: any[];
  purpose: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  requested_date: string;
  required_date: string;
  due_date: string;
  status: 'pending' | 'approved' | 'rejected' | 'checked_out' | 'returned' | 'overdue' | 'cancelled';
  approved_by?: number;
  approved_by_name?: string;
  approved_date?: string;
  rejection_reason?: string;
  checked_out_date?: string;
  checked_out_by?: number;
  checked_out_by_name?: string;
  returned_date?: string;
  returned_to?: number;
  returned_to_name?: string;
  notes?: string;
  is_overdue: boolean;
  days_until_due?: number;
  can_be_approved: boolean;
  can_be_checked_out: boolean;
  can_be_returned: boolean;
  created_at: string;
  updated_at: string;
}

// Authentication API
export const authAPI = {
  login: (username: string, password: string): Promise<LoginResponse> =>
    apiClient.post('/accounts/auth/login/', { username, password }).then(res => res.data),

  logout: (): Promise<void> =>
    apiClient.post('/accounts/auth/logout/').then(res => res.data),

  getProfile: (): Promise<User> =>
    apiClient.get('/accounts/profile/').then(res => res.data),

  updateProfile: (data: Partial<User>): Promise<User> =>
    apiClient.patch('/accounts/profile/update/', data).then(res => res.data),

  changePassword: (oldPassword: string, newPassword: string, newPasswordConfirm: string): Promise<void> =>
    apiClient.post('/accounts/profile/change-password/', {
      old_password: oldPassword,
      new_password: newPassword,
      new_password_confirm: newPasswordConfirm,
    }).then(res => res.data),
};

// Users API
export const usersAPI = {
  getUsers: (): Promise<User[]> =>
    apiClient.get('/accounts/users/').then(res => res.data.results || res.data),

  getUser: (id: number): Promise<User> =>
    apiClient.get(`/accounts/users/${id}/`).then(res => res.data),

  createUser: (data: any): Promise<User> =>
    apiClient.post('/accounts/users/', data).then(res => res.data),

  updateUser: (id: number, data: any): Promise<User> =>
    apiClient.patch(`/accounts/users/${id}/`, data).then(res => res.data),

  deleteUser: (id: number): Promise<void> =>
    apiClient.delete(`/accounts/users/${id}/`).then(res => res.data),
};

// Organizations API
export const organizationsAPI = {
  getOrganizations: (): Promise<Organization[]> =>
    apiClient.get('/organizations/').then(res => res.data.results || res.data),

  getOrganization: (id: number): Promise<Organization> =>
    apiClient.get(`/organizations/${id}/`).then(res => res.data),

  createOrganization: (data: any): Promise<Organization> =>
    apiClient.post('/organizations/', data).then(res => res.data),

  updateOrganization: (id: number, data: any): Promise<Organization> =>
    apiClient.patch(`/organizations/${id}/`, data).then(res => res.data),

  deleteOrganization: (id: number): Promise<void> =>
    apiClient.delete(`/organizations/${id}/`).then(res => res.data),
};

// Documents API
export const documentsAPI = {
  getDocuments: (params?: any): Promise<{ results: Document[]; count: number }> =>
    apiClient.get('/documents/', { params }).then(res => res.data),

  getDocument: (id: string): Promise<Document> =>
    apiClient.get(`/documents/${id}/`).then(res => res.data),

  createDocument: (data: FormData): Promise<Document> =>
    apiClient.post('/documents/', data, {
      headers: { 'Content-Type': 'multipart/form-data' }
    }).then(res => res.data),

  updateDocument: (id: string, data: any): Promise<Document> =>
    apiClient.patch(`/documents/${id}/`, data).then(res => res.data),

  deleteDocument: (id: string): Promise<void> =>
    apiClient.delete(`/documents/${id}/`).then(res => res.data),

  downloadDocument: (id: string): Promise<Blob> =>
    apiClient.get(`/documents/${id}/download/`, { responseType: 'blob' }).then(res => res.data),

  searchDocuments: (params: any): Promise<{ results: Document[]; count: number }> =>
    apiClient.get('/documents/search/', { params }).then(res => res.data),

  getDocumentStats: (): Promise<any> =>
    apiClient.get('/documents/stats/').then(res => res.data),
};

// Requests API
export const requestsAPI = {
  getRequests: (params?: any): Promise<{ results: DocumentRequest[]; count: number }> =>
    apiClient.get('/requests/', { params }).then(res => res.data),

  getRequest: (id: string): Promise<DocumentRequest> =>
    apiClient.get(`/requests/${id}/`).then(res => res.data),

  createRequest: (data: any): Promise<DocumentRequest> =>
    apiClient.post('/requests/', data).then(res => res.data),

  updateRequest: (id: string, data: any): Promise<DocumentRequest> =>
    apiClient.patch(`/requests/${id}/`, data).then(res => res.data),

  approveRequest: (id: string, comments?: string): Promise<void> =>
    apiClient.post(`/requests/${id}/approve/`, { comments }).then(res => res.data),

  rejectRequest: (id: string, comments: string): Promise<void> =>
    apiClient.post(`/requests/${id}/reject/`, { comments }).then(res => res.data),

  checkoutRequest: (id: string, comments?: string): Promise<void> =>
    apiClient.post(`/requests/${id}/checkout/`, { comments }).then(res => res.data),

  returnRequest: (id: string, conditionNotes?: string): Promise<void> =>
    apiClient.post(`/requests/${id}/return/`, { condition_notes: conditionNotes }).then(res => res.data),

  getRequestStats: (): Promise<any> =>
    apiClient.get('/requests/stats/').then(res => res.data),

  getDashboardStats: (): Promise<any> =>
    apiClient.get('/requests/dashboard/').then(res => res.data),
};

export default apiClient;
