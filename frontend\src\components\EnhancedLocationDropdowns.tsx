import React, { useState, useEffect } from 'react';
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Typography,
  Alert,
  Chip,
  Box,
  FormHelperText,
  CircularProgress,
} from '@mui/material';
import { LocationOn, Error as ErrorIcon } from '@mui/icons-material';
import geographicalLocationService from '../services/geographicalLocationService';

// Debug: Check if service is imported correctly
console.log('🔍 GeographicalLocationService imported:', geographicalLocationService);
import type { LocationSelectOption } from '../services/geographicalLocationService';

// Local interface definitions
interface Country {
  id: number;
  name: string;
  code: string;
}

interface Region {
  id: number;
  name: string;
  code: string;
}



interface City {
  id: number;
  name: string;
  code: string;
}

interface SubCity {
  id: number;
  name: string;
  code: string;
  type: 'subcity' | 'woreda' | 'district';
}

interface Kebele {
  id: number;
  name: string;
  code: string;
  number: number;
}

interface LocationDropdownsProps {
  // Current values (IDs)
  countryId?: number | null;
  regionId?: number | null;
  cityId?: number | null;
  subCityId?: number | null;
  kebeleId?: number | null;

  // Change handlers (return both ID and object)
  onCountryChange?: (countryId: number | null, country: Country | null) => void;
  onRegionChange?: (regionId: number | null, region: Region | null) => void;
  onCityChange?: (cityId: number | null, city: City | null) => void;
  onSubCityChange?: (subCityId: number | null, subCity: SubCity | null) => void;
  onKebeleChange?: (kebeleId: number | null, kebele: Kebele | null) => void;
  
  // Validation and display
  errors?: {
    country?: string;
    region?: string;
    city?: string;
    subCity?: string;
    kebele?: string;
  };
  size?: 'small' | 'medium';
  disabled?: boolean;
  required?: boolean;
  showBreadcrumbs?: boolean;
  showValidation?: boolean;
}

const EnhancedLocationDropdowns: React.FC<LocationDropdownsProps> = ({
  countryId,
  regionId,
  cityId,
  subCityId,
  kebeleId,
  onCountryChange,
  onRegionChange,
  onCityChange,
  onSubCityChange,
  onKebeleChange,
  errors = {},
  size = 'medium',
  disabled = false,
  required = false,
  showBreadcrumbs = true,
  showValidation = true,
}) => {
  console.log('🚀 EnhancedLocationDropdowns rendered with props:', {
    countryId, regionId, cityId, subCityId, kebeleId
  });
  // State for dropdown options
  const [countries, setCountries] = useState<Country[]>([]);
  const [regions, setRegions] = useState<Region[]>([]);
  const [cities, setCities] = useState<City[]>([]);
  const [subCities, setSubCities] = useState<SubCity[]>([]);
  const [kebeles, setKebeles] = useState<Kebele[]>([]);

  // Loading states
  const [loading, setLoading] = useState({
    countries: false,
    regions: false,
    cities: false,
    subCities: false,
    kebeles: false,
  });

  // Selected objects for breadcrumbs
  const [selectedCountry, setSelectedCountry] = useState<Country | null>(null);
  const [selectedRegion, setSelectedRegion] = useState<Region | null>(null);
  const [selectedCity, setSelectedCity] = useState<City | null>(null);
  const [selectedSubCity, setSelectedSubCity] = useState<SubCity | null>(null);
  const [selectedKebele, setSelectedKebele] = useState<Kebele | null>(null);

  // Load countries on mount
  useEffect(() => {
    console.log('🚀 EnhancedLocationDropdowns mounted, loading countries...');
    loadCountries();
    // Also load all regions and cities initially for better UX
    loadAllRegions();
    loadAllCities();
  }, []);

  // Load all regions initially (not filtered by country)
  const loadAllRegions = async () => {
    console.log('🔄 Loading all regions...');
    setLoading(prev => ({ ...prev, regions: true }));
    try {
      const data = await geographicalLocationService.getRegions(); // No country filter
      console.log('✅ All regions loaded:', data);
      setRegions(data);
    } catch (error) {
      console.error('❌ Error loading all regions:', error);
    } finally {
      setLoading(prev => ({ ...prev, regions: false }));
    }
  };

  // Load all cities initially (not filtered by region)
  const loadAllCities = async () => {
    console.log('🔄 Loading all cities...');
    setLoading(prev => ({ ...prev, cities: true }));
    try {
      const data = await geographicalLocationService.getCities(); // No region filter
      console.log('✅ All cities loaded:', data);
      setCities(data);
    } catch (error) {
      console.error('❌ Error loading all cities:', error);
    } finally {
      setLoading(prev => ({ ...prev, cities: false }));
    }
  };

  // Load dependent dropdowns when parent changes
  useEffect(() => {
    if (countryId) {
      loadRegions(countryId);
    } else {
      setRegions([]);
      setCities([]);
      setSubCities([]);
      setKebeles([]);
    }
  }, [countryId]);

  useEffect(() => {
    if (regionId) {
      loadCities(regionId);
    } else {
      setCities([]);
      setSubCities([]);
      setKebeles([]);
    }
  }, [regionId]);

  useEffect(() => {
    if (cityId) {
      loadSubCities(cityId);
    } else {
      setSubCities([]);
      setKebeles([]);
    }
  }, [cityId]);

  useEffect(() => {
    if (subCityId) {
      loadKebeles(subCityId);
    } else {
      setKebeles([]);
    }
  }, [subCityId]);

  // Load functions
  const loadCountries = async () => {
    console.log('🔄 Loading countries...');
    setLoading(prev => ({ ...prev, countries: true }));
    try {
      const data = await geographicalLocationService.getCountries();
      console.log('✅ Countries loaded:', data);
      setCountries(data);
    } catch (error) {
      console.error('❌ Error loading countries:', error);
    } finally {
      setLoading(prev => ({ ...prev, countries: false }));
    }
  };

  const loadRegions = async (countryId: number) => {
    console.log('🔄 Loading regions for country:', countryId);
    setLoading(prev => ({ ...prev, regions: true }));
    try {
      const data = await geographicalLocationService.getRegions(countryId);
      console.log('✅ Regions loaded:', data);
      setRegions(data);
    } catch (error) {
      console.error('❌ Error loading regions:', error);
    } finally {
      setLoading(prev => ({ ...prev, regions: false }));
    }
  };



  const loadCities = async (regionId: number) => {
    console.log('🔄 Loading cities for region:', regionId);
    setLoading(prev => ({ ...prev, cities: true }));
    try {
      const data = await geographicalLocationService.getCities(regionId);
      console.log('✅ Cities loaded:', data);
      setCities(data);
    } catch (error) {
      console.error('❌ Error loading cities:', error);
    } finally {
      setLoading(prev => ({ ...prev, cities: false }));
    }
  };

  const loadSubCities = async (cityId: number) => {
    setLoading(prev => ({ ...prev, subCities: true }));
    try {
      const data = await geographicalLocationService.getSubCities(cityId);
      setSubCities(data);
    } catch (error) {
      console.error('Error loading subcities:', error);
    } finally {
      setLoading(prev => ({ ...prev, subCities: false }));
    }
  };

  const loadKebeles = async (subCityId: number) => {
    setLoading(prev => ({ ...prev, kebeles: true }));
    try {
      const data = await geographicalLocationService.getKebeles(subCityId);
      setKebeles(data);
    } catch (error) {
      console.error('Error loading kebeles:', error);
    } finally {
      setLoading(prev => ({ ...prev, kebeles: false }));
    }
  };

  // Handle change functions
  const handleCountryChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    const value = event.target.value as number;
    const country = countries.find(c => c.id === value) || null;
    setSelectedCountry(country);
    onCountryChange?.(value || null, country);
    
    // Reset dependent selections
    setSelectedRegion(null);
    setSelectedCity(null);
    setSelectedSubCity(null);
    setSelectedKebele(null);
    onRegionChange?.(null, null);
    onCityChange?.(null, null);
    onSubCityChange?.(null, null);
    onKebeleChange?.(null, null);
  };

  const handleRegionChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    const value = event.target.value as number;
    const region = regions.find(r => r.id === value) || null;
    setSelectedRegion(region);
    onRegionChange?.(value || null, region);

    // Reset dependent selections
    setSelectedCity(null);
    setSelectedSubCity(null);
    setSelectedKebele(null);
    onCityChange?.(null, null);
    onSubCityChange?.(null, null);
    onKebeleChange?.(null, null);
  };



  const handleCityChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    const value = event.target.value as number;
    const city = cities.find(c => c.id === value) || null;
    setSelectedCity(city);
    onCityChange?.(value || null, city);

    // Reset dependent selections
    setSelectedSubCity(null);
    setSelectedKebele(null);
    onSubCityChange?.(null, null);
    onKebeleChange?.(null, null);
  };

  const handleSubCityChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    const value = event.target.value as number;
    const subCity = subCities.find(sc => sc.id === value) || null;
    setSelectedSubCity(subCity);
    onSubCityChange?.(value || null, subCity);

    // Reset dependent selections
    setSelectedKebele(null);
    onKebeleChange?.(null, null);
  };

  const handleKebeleChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    const value = event.target.value as number;
    const kebele = kebeles.find(k => k.id === value) || null;
    setSelectedKebele(kebele);
    onKebeleChange?.(value || null, kebele);
  };

  // Breadcrumbs component
  const LocationBreadcrumbs = () => {
    if (!showBreadcrumbs) return null;

    const breadcrumbItems = [
      selectedCountry?.name,
      selectedRegion?.name,
      selectedCity?.name,
      selectedSubCity?.name,
      selectedKebele?.name,
    ].filter(Boolean);

    if (breadcrumbItems.length === 0) return null;

    return (
      <Box sx={{ mb: 2 }}>
        <Typography variant="caption" color="text.secondary" sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <LocationOn sx={{ fontSize: 16, mr: 0.5 }} />
          Location Path:
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
          {breadcrumbItems.map((item, index) => (
            <Chip
              key={index}
              label={item}
              size="small"
              variant="outlined"
              color="primary"
            />
          ))}
        </Box>
      </Box>
    );
  };

  // Debug current state
  console.log('🔍 Current state:', {
    countries: countries.length,
    regions: regions.length,
    cities: cities.length,
    loading,
    countryId,
    regionId
  });

  return (
    <Box>
      <LocationBreadcrumbs />
      
      <Grid container spacing={2}>
        {/* Country Dropdown */}
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth size={size} error={!!errors.country} disabled={disabled}>
            <InputLabel required={required}>Country</InputLabel>
            <Select
              value={countryId || ''}
              onChange={handleCountryChange}
              label="Country"
              endAdornment={loading.countries && <CircularProgress size={20} />}
            >
              <MenuItem value="">
                <em>Select Country</em>
              </MenuItem>
              {countries.map((country) => {
                console.log('🌍 Rendering country:', country);
                return (
                  <MenuItem key={country.id} value={country.id}>
                    {country.name}
                  </MenuItem>
                );
              })}
            </Select>
            {errors.country && <FormHelperText>{errors.country}</FormHelperText>}
          </FormControl>
        </Grid>

        {/* Region Dropdown */}
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth size={size} error={!!errors.region} disabled={disabled || !countryId}>
            <InputLabel>Region/State</InputLabel>
            <Select
              value={regionId || ''}
              onChange={handleRegionChange}
              label="Region/State"
              endAdornment={loading.regions && <CircularProgress size={20} />}
            >
              <MenuItem value="">
                <em>Select Region</em>
              </MenuItem>
              {regions.map((region) => (
                <MenuItem key={region.id} value={region.id}>
                  {region.name}
                </MenuItem>
              ))}
            </Select>
            {errors.region && <FormHelperText>{errors.region}</FormHelperText>}
          </FormControl>
        </Grid>



        {/* City Dropdown */}
        <Grid size={{ xs: 12, sm: 6 }}>
          <FormControl fullWidth size={size} error={!!errors.city} disabled={disabled || !regionId}>
            <InputLabel>City</InputLabel>
            <Select
              value={cityId || ''}
              onChange={handleCityChange}
              label="City"
              endAdornment={loading.cities && <CircularProgress size={20} />}
            >
              <MenuItem value="">
                <em>Select City</em>
              </MenuItem>
              {cities.map((city) => (
                <MenuItem key={city.id} value={city.id}>
                  {city.name}
                </MenuItem>
              ))}
            </Select>
            {errors.city && <FormHelperText>{errors.city}</FormHelperText>}
          </FormControl>
        </Grid>

        {/* SubCity Dropdown */}
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth size={size} error={!!errors.subCity} disabled={disabled || !cityId}>
            <InputLabel>SubCity/Woreda</InputLabel>
            <Select
              value={subCityId || ''}
              onChange={handleSubCityChange}
              label="SubCity/Woreda"
              endAdornment={loading.subCities && <CircularProgress size={20} />}
            >
              <MenuItem value="">
                <em>Select SubCity/Woreda</em>
              </MenuItem>
              {subCities.map((subCity) => (
                <MenuItem key={subCity.id} value={subCity.id}>
                  {subCity.name} ({subCity.type})
                </MenuItem>
              ))}
            </Select>
            {errors.subCity && <FormHelperText>{errors.subCity}</FormHelperText>}
          </FormControl>
        </Grid>

        {/* Kebele Dropdown */}
        <Grid size={{ xs: 12, sm: 6 }}>
          <FormControl fullWidth size={size} error={!!errors.kebele} disabled={disabled || !subCityId}>
            <InputLabel>Kebele</InputLabel>
            <Select
              value={kebeleId || ''}
              onChange={handleKebeleChange}
              label="Kebele"
              endAdornment={loading.kebeles && <CircularProgress size={20} />}
            >
              <MenuItem value="">
                <em>Select Kebele</em>
              </MenuItem>
              {kebeles.map((kebele) => (
                <MenuItem key={kebele.id} value={kebele.id}>
                  {kebele.name} (#{kebele.number})
                </MenuItem>
              ))}
            </Select>
            {errors.kebele && <FormHelperText>{errors.kebele}</FormHelperText>}
          </FormControl>
        </Grid>
      </Grid>

      {showValidation && Object.keys(errors).length > 0 && (
        <Alert severity="error" sx={{ mt: 2 }} icon={<ErrorIcon />}>
          Please correct the location selection errors above.
        </Alert>
      )}
    </Box>
  );
};

export default EnhancedLocationDropdowns;
