import apiClient from './api';

export interface Document {
  id: string;
  title: string;
  description?: string;
  document_type: number;
  document_type_name: string;
  organization: number;
  organization_name: string;
  mode: 'physical' | 'digital' | 'hybrid';
  status: 'active' | 'checked_out' | 'archived' | 'destroyed' | 'lost';
  tags: string[];
  reference_number?: string;
  document_date?: string;
  expiry_date?: string;
  document_file?: string;
  file_name?: string;
  file_number?: string;
  kent?: number;
  kent_location?: string;
  kent_code?: string;
  file?: string;
  file_size?: number;
  number_of_pages?: number;
  file_name?: string;
  file_type?: string;
  barcode_image?: string;
  qr_code_image?: string;
  location_display: string;
  is_expired: boolean;
  is_physical: boolean;
  is_digital: boolean;
  can_be_requested: boolean;
  retention_date: string;
  is_active: boolean;
  created_by: number;
  created_by_name: string;
  created_at: string;
  updated_at: string;
}

export interface DocumentCreate {
  title: string;
  description?: string;
  document_type: number;
  mode: 'physical' | 'digital' | 'hybrid';
  tags?: string[];
  number_of_pages?: number;
  reference_number?: string;
  document_date?: string;
  expiry_date?: string;
  document_file?: string;  // File ID instead of Kent ID
  kent?: number;  // Keep for backward compatibility
  file?: File;
}

export interface DocumentUpdate {
  title?: string;
  description?: string;
  document_type?: number;
  mode?: 'physical' | 'digital' | 'hybrid';
  tags?: string[];
  number_of_pages?: number;
  reference_number?: string;
  document_date?: string;
  expiry_date?: string;
  document_file?: string;  // File ID instead of Kent ID
  kent?: number;  // Keep for backward compatibility
  status?: 'active' | 'checked_out' | 'archived' | 'destroyed' | 'lost';
}

export interface DocumentListResponse {
  count: number;
  next?: string;
  previous?: string;
  results: Document[];
}

export interface DocumentStats {
  total_documents: number;
  active_documents: number;
  checked_out_documents: number;
  archived_documents: number;
  digital_documents: number;
  physical_documents: number;
  hybrid_documents: number;
  documents_by_type: { [key: string]: number };
  documents_by_status: { [key: string]: number };
  recent_uploads: number;
  expiring_soon: number;
}

class DocumentService {
  private baseUrl = '/documents';

  async getDocuments(params?: {
    page?: number;
    page_size?: number;
    search?: string;
    document_type?: number;
    mode?: string;
    status?: string;
    organization?: number;
    kent?: number;
    tags?: string;
    created_by?: number;
    is_expired?: boolean;
    ordering?: string;
  }): Promise<DocumentListResponse> {
    const response = await apiClient.get(`${this.baseUrl}/`, { params });
    return response.data;
  }

  async getDocument(id: string): Promise<Document> {
    const response = await apiClient.get(`${this.baseUrl}/${id}/`);
    return response.data;
  }

  async getDocumentsByFile(fileId: string, params?: {
    page?: number;
    page_size?: number;
    search?: string;
    document_type?: number;
    status?: string;
    ordering?: string;
  }): Promise<DocumentListResponse> {
    // Use the dedicated file documents endpoint
    const response = await apiClient.get(`/locations/files/${fileId}/documents/`);

    // The endpoint returns a file with documents, so we need to extract the documents
    const fileWithDocuments = response.data;

    return {
      count: fileWithDocuments.documents?.length || 0,
      results: fileWithDocuments.documents || []
    };
  }

  async createDocument(data: DocumentCreate): Promise<Document> {
    const formData = new FormData();
    
    // Add text fields
    formData.append('title', data.title);
    if (data.description) formData.append('description', data.description);
    formData.append('document_type', data.document_type.toString());
    formData.append('mode', data.mode);
    if (data.reference_number) formData.append('reference_number', data.reference_number);
    if (data.document_date) formData.append('document_date', data.document_date);
    if (data.expiry_date) formData.append('expiry_date', data.expiry_date);
    if (data.document_file) formData.append('document_file', data.document_file);
    if (data.kent) formData.append('kent', data.kent.toString());
    if (data.number_of_pages) formData.append('number_of_pages', data.number_of_pages.toString());
    
    // Add tags as JSON
    if (data.tags && data.tags.length > 0) {
      formData.append('tags', JSON.stringify(data.tags));
    }
    
    // Add file if present
    if (data.file) {
      formData.append('file', data.file);
    }

    const response = await apiClient.post(`${this.baseUrl}/`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  async updateDocument(id: string, data: DocumentUpdate): Promise<Document> {
    const response = await apiClient.patch(`${this.baseUrl}/${id}/`, data);
    return response.data;
  }

  async uploadFile(id: string, file: File): Promise<Document> {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await apiClient.patch(`${this.baseUrl}/${id}/upload-file/`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  async deleteDocument(id: string): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/${id}/`);
  }

  async downloadDocument(id: string): Promise<Blob> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/${id}/download/`, {
        responseType: 'blob',
      });

      return response.data;
    } catch (error) {
      console.error('Download failed:', error);
      throw error;
    }
  }

  async viewDocument(id: string): Promise<string> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/${id}/view/`, {
        responseType: 'blob',
      });

      // Create blob URL for viewing with proper MIME type
      const blob = new Blob([response.data], {
        type: response.headers['content-type'] || 'application/octet-stream'
      });
      const url = window.URL.createObjectURL(blob);
      return url;
    } catch (error) {
      console.error('View failed:', error);
      throw error;
    }
  }

  async getDocumentPreviewUrl(id: string): Promise<{ url: string; type: string; canPreview: boolean }> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/${id}/view/`, {
        responseType: 'blob',
      });

      const contentType = response.headers['content-type'] || 'application/octet-stream';
      const blob = new Blob([response.data], { type: contentType });
      const url = window.URL.createObjectURL(blob);

      // Determine if the file can be previewed inline
      const previewableTypes = [
        'application/pdf',
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
        'image/webp',
        'text/plain',
        'text/html',
        // Microsoft Office documents
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation'
      ];

      const canPreview = previewableTypes.includes(contentType.toLowerCase());

      return {
        url,
        type: contentType,
        canPreview
      };
    } catch (error) {
      console.error('Preview failed:', error);
      throw error;
    }
  }



  async printDocument(id: string): Promise<void> {
    try {
      const url = await this.viewDocument(id);

      // Open in new window for printing
      const printWindow = window.open(url, '_blank');
      if (printWindow) {
        printWindow.onload = () => {
          printWindow.print();
          // Clean up the URL after printing
          setTimeout(() => {
            window.URL.revokeObjectURL(url);
            printWindow.close();
          }, 1000);
        };
      } else {
        // Fallback: clean up URL if window couldn't be opened
        window.URL.revokeObjectURL(url);
        throw new Error('Could not open print window. Please check your popup blocker settings.');
      }
    } catch (error) {
      console.error('Print failed:', error);
      throw error;
    }
  }

  getDocumentPreviewUrl(id: string): string {
    return `${apiClient.defaults.baseURL}${this.baseUrl}/${id}/view/`;
  }

  async searchDocuments(query: string, filters?: {
    document_type?: number;
    mode?: string;
    status?: string;
    tags?: string[];
  }): Promise<DocumentListResponse> {
    const params = {
      search: query,
      ...filters,
      tags: filters?.tags?.join(','),
    };
    
    const response = await apiClient.get(`${this.baseUrl}/search/`, { params });
    return response.data;
  }

  async getDocumentStats(organizationId?: number): Promise<DocumentStats> {
    const params = organizationId ? { organization: organizationId } : {};
    const response = await apiClient.get(`${this.baseUrl}/stats/`, { params });
    return response.data;
  }

  async generateBarcode(id: string): Promise<Document> {
    const response = await apiClient.post(`${this.baseUrl}/${id}/generate-barcode/`);
    return response.data;
  }

  async generateQRCode(id: string): Promise<Document> {
    const response = await apiClient.post(`${this.baseUrl}/${id}/generate-qr/`);
    return response.data;
  }

  // DocumentType CRUD operations
  async getDocumentTypes(params?: {
    page?: number;
    page_size?: number;
    search?: string;
    organization?: number;
    is_active?: boolean;
  }): Promise<{ results: any[]; count: number }> {
    const response = await apiClient.get(`${this.baseUrl}/types/`, { params });
    return response.data;
  }

  async getDocumentType(id: number): Promise<any> {
    const response = await apiClient.get(`${this.baseUrl}/types/${id}/`);
    return response.data;
  }

  async createDocumentType(data: any): Promise<any> {
    const response = await apiClient.post(`${this.baseUrl}/types/`, data);
    return response.data;
  }

  async updateDocumentType(id: number, data: any): Promise<any> {
    const response = await apiClient.patch(`${this.baseUrl}/types/${id}/`, data);
    return response.data;
  }

  async deleteDocumentType(id: number): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/types/${id}/`);
  }

  // Utility methods
  getStatusColor(status: string): 'success' | 'warning' | 'error' | 'info' | 'default' {
    switch (status) {
      case 'active': return 'success';
      case 'checked_out': return 'warning';
      case 'archived': return 'info';
      case 'destroyed': return 'error';
      case 'lost': return 'error';
      default: return 'default';
    }
  }

  getModeColor(mode: string): 'primary' | 'secondary' | 'success' | 'default' {
    switch (mode) {
      case 'digital': return 'primary';
      case 'physical': return 'secondary';
      case 'hybrid': return 'success';
      default: return 'default';
    }
  }

  formatFileSize(sizeInBytes?: number): string {
    if (!sizeInBytes) return 'Unknown size';
    
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = sizeInBytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  getFileIcon(fileName?: string): string {
    if (!fileName) return 'description';
    
    const extension = fileName.split('.').pop()?.toLowerCase();
    
    switch (extension) {
      case 'pdf': return 'picture_as_pdf';
      case 'doc':
      case 'docx': return 'description';
      case 'xls':
      case 'xlsx': return 'table_chart';
      case 'ppt':
      case 'pptx': return 'slideshow';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp': return 'image';
      case 'zip':
      case 'rar': return 'archive';
      default: return 'insert_drive_file';
    }
  }

  validateFile(file: File, allowedExtensions: string[], maxSizeMB: number): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // Check file size
    const fileSizeMB = file.size / (1024 * 1024);
    if (fileSizeMB > maxSizeMB) {
      errors.push(`File size (${this.formatFileSize(file.size)}) exceeds maximum allowed size (${maxSizeMB} MB)`);
    }
    
    // Check file extension
    const extension = file.name.split('.').pop()?.toLowerCase();
    if (!extension || !allowedExtensions.includes(extension)) {
      errors.push(`File type .${extension} is not allowed. Allowed types: ${allowedExtensions.join(', ')}`);
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  formatDateTime(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  isExpiringSoon(expiryDate?: string, daysThreshold: number = 30): boolean {
    if (!expiryDate) return false;
    
    const expiry = new Date(expiryDate);
    const now = new Date();
    const diffTime = expiry.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays <= daysThreshold && diffDays > 0;
  }

  getDaysUntilExpiry(expiryDate?: string): number | null {
    if (!expiryDate) return null;

    const expiry = new Date(expiryDate);
    const now = new Date();
    const diffTime = expiry.getTime() - now.getTime();

    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  formatFileSize(bytes?: number): string {
    if (!bytes) return 'Unknown size';

    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }
}

export default new DocumentService();
