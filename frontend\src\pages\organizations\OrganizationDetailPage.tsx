import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Avatar,
  Chip,
  Grid,
  Card,
  CardContent,
  Divider,
  IconButton,
  Alert,
  CircularProgress,
  Breadcrumbs,
  Link,
} from '@mui/material';
import {
  ArrowBack,
  Edit,
  Delete,
  Star,
  Business,
  Email,
  Phone,
  Language,
  LocationOn,
  Schedule,
  CalendarToday,
  Badge,
  Receipt,
  Person,
  Home,
  Fax,
  Facebook,
  Twitter,
  LinkedIn,
  YouTube,
} from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import organizationService from '../../services/organizationService';
import type { Organization } from '../../services/organizationService';
import ConfirmationDialog from '../../components/common/ConfirmationDialog';

const OrganizationDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showNotification } = useNotification();
  
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [loading, setLoading] = useState(true);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    if (id) {
      loadOrganization();
    }
  }, [id]);

  const loadOrganization = async () => {
    try {
      setLoading(true);
      const data = await organizationService.getOrganization(Number(id));
      setOrganization(data);
    } catch (error) {
      console.error('Error loading organization:', error);
      showNotification('Failed to load organization', 'error');
      navigate('/organizations');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    navigate(`/organizations/${id}/edit`);
  };

  const handleDelete = () => {
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!organization) return;
    
    try {
      setDeleting(true);
      await organizationService.deleteOrganization(organization.id);
      showNotification('Organization deleted successfully', 'success');
      navigate('/organizations');
    } catch (error) {
      console.error('Error deleting organization:', error);
      showNotification('Failed to delete organization', 'error');
    } finally {
      setDeleting(false);
      setDeleteDialogOpen(false);
    }
  };

  const handleSetAsDefault = async () => {
    if (!organization) return;
    
    try {
      await organizationService.setAsDefault(organization.id);
      showNotification('Organization set as default successfully', 'success');
      loadOrganization();
    } catch (error) {
      console.error('Error setting as default:', error);
      showNotification('Failed to set as default', 'error');
    }
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (!organization) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error">Organization not found</Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 3 }}>
        <Link
          component="button"
          variant="body2"
          onClick={() => navigate('/dashboard')}
          sx={{ display: 'flex', alignItems: 'center', gap: 0.5, textDecoration: 'none' }}
        >
          <Home fontSize="small" />
          Dashboard
        </Link>
        <Link
          component="button"
          variant="body2"
          onClick={() => navigate('/organizations')}
          sx={{ display: 'flex', alignItems: 'center', gap: 0.5, textDecoration: 'none' }}
        >
          <Business fontSize="small" />
          Organizations
        </Link>
        <Typography variant="body2" color="text.primary">
          {organization.name}
        </Typography>
      </Breadcrumbs>

      {/* Header */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<ArrowBack />}
              onClick={() => navigate('/organizations')}
            >
              Back to Organizations
            </Button>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            {!organization.is_default && (
              <Button
                variant="outlined"
                startIcon={<Star />}
                onClick={handleSetAsDefault}
                color="warning"
              >
                Set as Default
              </Button>
            )}
            <Button
              variant="outlined"
              startIcon={<Edit />}
              onClick={handleEdit}
              color="primary"
            >
              Edit
            </Button>
            <Button
              variant="outlined"
              startIcon={<Delete />}
              onClick={handleDelete}
              color="error"
              disabled={organization.is_default}
            >
              Delete
            </Button>
          </Box>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
          {organization.logo ? (
            <Avatar
              src={organization.logo}
              sx={{ width: 80, height: 80 }}
              alt={organization.name}
            />
          ) : (
            <Avatar sx={{ bgcolor: 'primary.main', width: 80, height: 80 }}>
              <Business sx={{ fontSize: 40 }} />
            </Avatar>
          )}
          <Box sx={{ flex: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
              <Typography variant="h4" component="h1">
                {organization.name}
              </Typography>
              {organization.is_default && (
                <Chip
                  label="Default"
                  color="primary"
                  icon={<Star />}
                  size="small"
                />
              )}
            </Box>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              {organization.short_name}
            </Typography>
            {organization.tagline && (
              <Typography variant="body1" color="text.secondary" sx={{ mb: 1 }}>
                {organization.tagline}
              </Typography>
            )}
            {organization.motto && (
              <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                "{organization.motto}"
              </Typography>
            )}
          </Box>
        </Box>
      </Paper>

      {/* Organization Details */}
      <Grid container spacing={3}>
        {/* Basic Information */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                color: 'primary.main',
                fontWeight: 600,
                borderBottom: '2px solid',
                borderColor: 'primary.main',
                pb: 1,
                mb: 3
              }}>
                <Business />
                Basic Information
              </Typography>

              {organization.description && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" color="text.secondary" sx={{ fontWeight: 600, mb: 1 }}>
                    Description
                  </Typography>
                  <Typography variant="body2" sx={{
                    p: 2,
                    bgcolor: 'grey.50',
                    borderRadius: 1,
                    border: '1px solid',
                    borderColor: 'grey.200'
                  }}>
                    {organization.description}
                  </Typography>
                </Box>
              )}

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {organization.established_date && (
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    p: 2,
                    bgcolor: 'success.50',
                    borderRadius: 1,
                    border: '1px solid',
                    borderColor: 'success.200'
                  }}>
                    <CalendarToday sx={{ color: 'success.main', mr: 2 }} />
                    <Box>
                      <Typography variant="subtitle2" color="success.main" sx={{ fontWeight: 600 }}>
                        Established
                      </Typography>
                      <Typography variant="body2" color="success.dark">
                        {new Date(organization.established_date).toLocaleDateString()}
                      </Typography>
                    </Box>
                  </Box>
                )}

                {organization.registration_number && (
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    p: 2,
                    bgcolor: 'info.50',
                    borderRadius: 1,
                    border: '1px solid',
                    borderColor: 'info.200'
                  }}>
                    <Badge sx={{ color: 'info.main', mr: 2 }} />
                    <Box>
                      <Typography variant="subtitle2" color="info.main" sx={{ fontWeight: 600 }}>
                        Registration Number
                      </Typography>
                      <Typography variant="body2" color="info.dark">
                        {organization.registration_number}
                      </Typography>
                    </Box>
                  </Box>
                )}

                {organization.tax_id && (
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    p: 2,
                    bgcolor: 'warning.50',
                    borderRadius: 1,
                    border: '1px solid',
                    borderColor: 'warning.200'
                  }}>
                    <Receipt sx={{ color: 'warning.main', mr: 2 }} />
                    <Box>
                      <Typography variant="subtitle2" color="warning.main" sx={{ fontWeight: 600 }}>
                        Tax ID
                      </Typography>
                      <Typography variant="body2" color="warning.dark">
                        {organization.tax_id}
                      </Typography>
                    </Box>
                  </Box>
                )}

                {organization.license_number && (
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    p: 2,
                    bgcolor: 'secondary.50',
                    borderRadius: 1,
                    border: '1px solid',
                    borderColor: 'secondary.200'
                  }}>
                    <Badge sx={{ color: 'secondary.main', mr: 2 }} />
                    <Box>
                      <Typography variant="subtitle2" color="secondary.main" sx={{ fontWeight: 600 }}>
                        License Number
                      </Typography>
                      <Typography variant="body2" color="secondary.dark">
                        {organization.license_number}
                      </Typography>
                    </Box>
                  </Box>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Contact Information */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                color: 'primary.main',
                fontWeight: 600,
                borderBottom: '2px solid',
                borderColor: 'primary.main',
                pb: 1,
                mb: 3
              }}>
                <Email />
                Contact Information
              </Typography>

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {organization.email && (
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    p: 2,
                    bgcolor: 'primary.50',
                    borderRadius: 1,
                    border: '1px solid',
                    borderColor: 'primary.200'
                  }}>
                    <Email sx={{ color: 'primary.main', mr: 2 }} />
                    <Box>
                      <Typography variant="subtitle2" color="primary.main" sx={{ fontWeight: 600 }}>
                        Email
                      </Typography>
                      <Link
                        href={`mailto:${organization.email}`}
                        sx={{
                          color: 'primary.dark',
                          textDecoration: 'none',
                          '&:hover': { textDecoration: 'underline' }
                        }}
                      >
                        {organization.email}
                      </Link>
                    </Box>
                  </Box>
                )}

                {organization.phone && (
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    p: 2,
                    bgcolor: 'success.50',
                    borderRadius: 1,
                    border: '1px solid',
                    borderColor: 'success.200'
                  }}>
                    <Phone sx={{ color: 'success.main', mr: 2 }} />
                    <Box>
                      <Typography variant="subtitle2" color="success.main" sx={{ fontWeight: 600 }}>
                        Phone
                      </Typography>
                      <Link
                        href={`tel:${organization.phone}`}
                        sx={{
                          color: 'success.dark',
                          textDecoration: 'none',
                          '&:hover': { textDecoration: 'underline' }
                        }}
                      >
                        {organization.phone}
                      </Link>
                    </Box>
                  </Box>
                )}

                {organization.fax && (
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    p: 2,
                    bgcolor: 'info.50',
                    borderRadius: 1,
                    border: '1px solid',
                    borderColor: 'info.200'
                  }}>
                    <Fax sx={{ color: 'info.main', mr: 2 }} />
                    <Box>
                      <Typography variant="subtitle2" color="info.main" sx={{ fontWeight: 600 }}>
                        Fax
                      </Typography>
                      <Typography variant="body2" color="info.dark">
                        {organization.fax}
                      </Typography>
                    </Box>
                  </Box>
                )}

                {organization.website && (
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    p: 2,
                    bgcolor: 'warning.50',
                    borderRadius: 1,
                    border: '1px solid',
                    borderColor: 'warning.200'
                  }}>
                    <Language sx={{ color: 'warning.main', mr: 2 }} />
                    <Box>
                      <Typography variant="subtitle2" color="warning.main" sx={{ fontWeight: 600 }}>
                        Website
                      </Typography>
                      <Link
                        href={organization.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        sx={{
                          color: 'warning.dark',
                          textDecoration: 'none',
                          '&:hover': { textDecoration: 'underline' }
                        }}
                      >
                        {organization.website}
                      </Link>
                    </Box>
                  </Box>
                )}

                {organization.full_address && (
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'flex-start',
                    p: 2,
                    bgcolor: 'secondary.50',
                    borderRadius: 1,
                    border: '1px solid',
                    borderColor: 'secondary.200'
                  }}>
                    <LocationOn sx={{ color: 'secondary.main', mr: 2, mt: 0.5 }} />
                    <Box>
                      <Typography variant="subtitle2" color="secondary.main" sx={{ fontWeight: 600 }}>
                        Address
                      </Typography>
                      <Typography variant="body2" color="secondary.dark">
                        {organization.full_address}
                      </Typography>
                    </Box>
                  </Box>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* System Settings */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                color: 'primary.main',
                fontWeight: 600,
                borderBottom: '2px solid',
                borderColor: 'primary.main',
                pb: 1,
                mb: 3
              }}>
                <Schedule />
                System Settings
              </Typography>

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  p: 2,
                  bgcolor: 'info.50',
                  borderRadius: 1,
                  border: '1px solid',
                  borderColor: 'info.200'
                }}>
                  <Schedule sx={{ color: 'info.main', mr: 2 }} />
                  <Box>
                    <Typography variant="subtitle2" color="info.main" sx={{ fontWeight: 600 }}>
                      Document Retention
                    </Typography>
                    <Typography variant="body2" color="info.dark">
                      {organization.document_retention_days} days ({Math.round(organization.document_retention_days / 365)} years)
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  p: 2,
                  bgcolor: 'warning.50',
                  borderRadius: 1,
                  border: '1px solid',
                  borderColor: 'warning.200'
                }}>
                  <Receipt sx={{ color: 'warning.main', mr: 2 }} />
                  <Box>
                    <Typography variant="subtitle2" color="warning.main" sx={{ fontWeight: 600 }}>
                      Max File Size
                    </Typography>
                    <Typography variant="body2" color="warning.dark">
                      {organization.max_file_size_mb} MB
                    </Typography>
                  </Box>
                </Box>

                {organization.office_hours_start && organization.office_hours_end && (
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    p: 2,
                    bgcolor: 'success.50',
                    borderRadius: 1,
                    border: '1px solid',
                    borderColor: 'success.200'
                  }}>
                    <Schedule sx={{ color: 'success.main', mr: 2 }} />
                    <Box>
                      <Typography variant="subtitle2" color="success.main" sx={{ fontWeight: 600 }}>
                        Office Hours
                      </Typography>
                      <Typography variant="body2" color="success.dark">
                        {organization.office_hours_start} - {organization.office_hours_end}
                      </Typography>
                    </Box>
                  </Box>
                )}

                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  p: 2,
                  bgcolor: 'secondary.50',
                  borderRadius: 1,
                  border: '1px solid',
                  borderColor: 'secondary.200'
                }}>
                  <Business sx={{ color: 'secondary.main', mr: 2 }} />
                  <Box>
                    <Typography variant="subtitle2" color="secondary.main" sx={{ fontWeight: 600 }}>
                      Users & Departments
                    </Typography>
                    <Typography variant="body2" color="secondary.dark">
                      {organization.user_count || 0} users, {organization.department_count || 0} departments
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Social Media & Branding */}
        {(organization.social_media || organization.primary_color) && (
          <Grid size={{ xs: 12, md: 6 }}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  color: 'primary.main',
                  fontWeight: 600,
                  borderBottom: '2px solid',
                  borderColor: 'primary.main',
                  pb: 1,
                  mb: 3
                }}>
                  <Language />
                  Social Media & Branding
                </Typography>

                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  {organization.social_media?.facebook && (
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      p: 2,
                      bgcolor: '#1877f2',
                      color: 'white',
                      borderRadius: 1
                    }}>
                      <Facebook sx={{ mr: 2 }} />
                      <Box>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                          Facebook
                        </Typography>
                        <Link
                          href={organization.social_media.facebook}
                          target="_blank"
                          rel="noopener noreferrer"
                          sx={{ color: 'white', textDecoration: 'none', '&:hover': { textDecoration: 'underline' } }}
                        >
                          {organization.social_media.facebook}
                        </Link>
                      </Box>
                    </Box>
                  )}

                  {organization.social_media?.twitter && (
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      p: 2,
                      bgcolor: '#1da1f2',
                      color: 'white',
                      borderRadius: 1
                    }}>
                      <Twitter sx={{ mr: 2 }} />
                      <Box>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                          Twitter
                        </Typography>
                        <Link
                          href={organization.social_media.twitter}
                          target="_blank"
                          rel="noopener noreferrer"
                          sx={{ color: 'white', textDecoration: 'none', '&:hover': { textDecoration: 'underline' } }}
                        >
                          {organization.social_media.twitter}
                        </Link>
                      </Box>
                    </Box>
                  )}

                  {organization.social_media?.linkedin && (
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      p: 2,
                      bgcolor: '#0077b5',
                      color: 'white',
                      borderRadius: 1
                    }}>
                      <LinkedIn sx={{ mr: 2 }} />
                      <Box>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                          LinkedIn
                        </Typography>
                        <Link
                          href={organization.social_media.linkedin}
                          target="_blank"
                          rel="noopener noreferrer"
                          sx={{ color: 'white', textDecoration: 'none', '&:hover': { textDecoration: 'underline' } }}
                        >
                          {organization.social_media.linkedin}
                        </Link>
                      </Box>
                    </Box>
                  )}

                  {organization.social_media?.youtube && (
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      p: 2,
                      bgcolor: '#ff0000',
                      color: 'white',
                      borderRadius: 1
                    }}>
                      <YouTube sx={{ mr: 2 }} />
                      <Box>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                          YouTube
                        </Typography>
                        <Link
                          href={organization.social_media.youtube}
                          target="_blank"
                          rel="noopener noreferrer"
                          sx={{ color: 'white', textDecoration: 'none', '&:hover': { textDecoration: 'underline' } }}
                        >
                          {organization.social_media.youtube}
                        </Link>
                      </Box>
                    </Box>
                  )}

                  {(organization.primary_color || organization.secondary_color || organization.accent_color) && (
                    <Box sx={{
                      p: 2,
                      bgcolor: 'grey.50',
                      borderRadius: 1,
                      border: '1px solid',
                      borderColor: 'grey.200'
                    }}>
                      <Typography variant="subtitle2" color="text.secondary" sx={{ fontWeight: 600, mb: 2 }}>
                        Brand Colors
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                        {organization.primary_color && (
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Box sx={{
                              width: 24,
                              height: 24,
                              bgcolor: organization.primary_color,
                              borderRadius: '50%',
                              border: '2px solid white',
                              boxShadow: 1
                            }} />
                            <Typography variant="caption">Primary</Typography>
                          </Box>
                        )}
                        {organization.secondary_color && (
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Box sx={{
                              width: 24,
                              height: 24,
                              bgcolor: organization.secondary_color,
                              borderRadius: '50%',
                              border: '2px solid white',
                              boxShadow: 1
                            }} />
                            <Typography variant="caption">Secondary</Typography>
                          </Box>
                        )}
                        {organization.accent_color && (
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Box sx={{
                              width: 24,
                              height: 24,
                              bgcolor: organization.accent_color,
                              borderRadius: '50%',
                              border: '2px solid white',
                              boxShadow: 1
                            }} />
                            <Typography variant="caption">Accent</Typography>
                          </Box>
                        )}
                      </Box>
                    </Box>
                  )}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        )}

        {/* Tax Collection Settings - Read Only Display */}
        <Grid size={{ xs: 12 }}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                color: 'primary.main'
              }}>
                <Receipt />
                Tax Collection Settings
              </Typography>

              <Alert severity="info" sx={{ mb: 2 }}>
                Tax collection rates are used for calculating penalties and interest on overdue payments.
                To modify these settings, use the Edit Organization page.
              </Alert>

              <Grid container spacing={3}>
                {/* Individual Taxpayer Rates */}
                <Grid size={{ xs: 12, md: 6 }}>
                  <Paper sx={{ p: 2, bgcolor: 'primary.50', border: '1px solid', borderColor: 'primary.200' }}>
                    <Box display="flex" alignItems="center" gap={1} mb={2}>
                      <Person color="primary" />
                      <Typography variant="h6" color="primary.main">
                        Individual Taxpayers
                      </Typography>
                    </Box>

                    <Grid container spacing={2}>
                      <Grid size={{ xs: 6 }}>
                        <Typography variant="body2" color="text.secondary">
                          Penalty Rate
                        </Typography>
                        <Typography variant="h6" color="primary.main">
                          {organization.individual_penalty_rate || 5.0}%
                        </Typography>
                      </Grid>
                      <Grid size={{ xs: 6 }}>
                        <Typography variant="body2" color="text.secondary">
                          Interest Rate (Monthly)
                        </Typography>
                        <Typography variant="h6" color="primary.main">
                          {organization.individual_interest_rate || 2.0}% / month
                        </Typography>
                      </Grid>
                    </Grid>
                  </Paper>
                </Grid>

                {/* Organization Taxpayer Rates */}
                <Grid size={{ xs: 12, md: 6 }}>
                  <Paper sx={{ p: 2, bgcolor: 'secondary.50', border: '1px solid', borderColor: 'secondary.200' }}>
                    <Box display="flex" alignItems="center" gap={1} mb={2}>
                      <Business color="secondary" />
                      <Typography variant="h6" color="secondary.main">
                        Organization Taxpayers
                      </Typography>
                    </Box>

                    <Grid container spacing={2}>
                      <Grid size={{ xs: 6 }}>
                        <Typography variant="body2" color="text.secondary">
                          Penalty Rate
                        </Typography>
                        <Typography variant="h6" color="secondary.main">
                          {organization.organization_penalty_rate || 10.0}%
                        </Typography>
                      </Grid>
                      <Grid size={{ xs: 6 }}>
                        <Typography variant="body2" color="text.secondary">
                          Interest Rate (Monthly)
                        </Typography>
                        <Typography variant="h6" color="secondary.main">
                          {organization.organization_interest_rate || 3.0}% / month
                        </Typography>
                      </Grid>
                    </Grid>
                  </Paper>
                </Grid>
              </Grid>

              {/* Edit Button */}
              <Box mt={2} display="flex" justifyContent="flex-end">
                <Button
                  variant="outlined"
                  startIcon={<Edit />}
                  onClick={handleEdit}
                >
                  Edit Tax Settings
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleConfirmDelete}
        title="Delete Organization"
        itemName={organization.name}
        itemType="Organization"
        message={`Are you sure you want to delete "${organization.name}"? This action cannot be undone.`}
        confirmText="Delete Organization"
        severity="error"
        loading={deleting}
      />
    </Container>
  );
};

export default OrganizationDetailPage;
