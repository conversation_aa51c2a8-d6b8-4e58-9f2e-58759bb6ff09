# Generated by Django 5.2.3 on 2025-07-14 20:09

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
        ("documents", "0002_initial"),
        ("locations", "0002_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="DocumentRequest",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "request_number",
                    models.CharField(
                        help_text="Auto-generated request number",
                        max_length=20,
                        unique=True,
                    ),
                ),
                (
                    "purpose",
                    models.TextField(
                        help_text="Purpose or reason for requesting the documents"
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("normal", "Normal"),
                            ("high", "High"),
                            ("urgent", "Urgent"),
                        ],
                        default="normal",
                        max_length=10,
                    ),
                ),
                ("requested_date", models.DateTimeField(auto_now_add=True)),
                (
                    "required_date",
                    models.DateTimeField(
                        help_text="Date and time when documents are needed"
                    ),
                ),
                (
                    "due_date",
                    models.DateTimeField(
                        help_text="Date and time when documents should be returned"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending Approval"),
                            ("approved", "Approved"),
                            ("rejected", "Rejected"),
                            ("checked_out", "Checked Out"),
                            ("returned", "Returned"),
                            ("overdue", "Overdue"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("approved_date", models.DateTimeField(blank=True, null=True)),
                (
                    "rejection_reason",
                    models.TextField(
                        blank=True,
                        help_text="Reason for rejection (if applicable)",
                        null=True,
                    ),
                ),
                ("checked_out_date", models.DateTimeField(blank=True, null=True)),
                ("returned_date", models.DateTimeField(blank=True, null=True)),
                (
                    "notes",
                    models.TextField(
                        blank=True, help_text="Additional notes or comments", null=True
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who approved the request",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="approved_requests",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "checked_out_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="Staff member who processed the checkout",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="checked_out_requests",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "documents",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Individual documents being requested (legacy)",
                        related_name="requests",
                        to="documents.document",
                    ),
                ),
                (
                    "files",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Files being requested (e.g., 'Abebe's Hotel File')",
                        related_name="requests",
                        to="locations.file",
                    ),
                ),
                (
                    "requested_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="document_requests",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "returned_to",
                    models.ForeignKey(
                        blank=True,
                        help_text="Staff member who processed the return",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="received_returns",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Document Request",
                "verbose_name_plural": "Document Requests",
                "db_table": "requests_document_request",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Notification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=255)),
                ("message", models.TextField()),
                (
                    "notification_type",
                    models.CharField(
                        choices=[
                            ("info", "Information"),
                            ("warning", "Warning"),
                            ("error", "Error"),
                            ("success", "Success"),
                        ],
                        default="info",
                        max_length=10,
                    ),
                ),
                ("object_id", models.CharField(blank=True, max_length=255, null=True)),
                ("is_read", models.BooleanField(default=False)),
                ("read_at", models.DateTimeField(blank=True, null=True)),
                ("email_sent", models.BooleanField(default=False)),
                ("email_sent_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "content_type",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.contenttype",
                    ),
                ),
                (
                    "recipient",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notifications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Notification",
                "verbose_name_plural": "Notifications",
                "db_table": "requests_notification",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="RequestApproval",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "action",
                    models.CharField(
                        choices=[
                            ("approved", "Approved"),
                            ("rejected", "Rejected"),
                            ("returned_for_revision", "Returned for Revision"),
                        ],
                        max_length=25,
                    ),
                ),
                ("comments", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "approver",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="given_approvals",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "request",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="approvals",
                        to="requests.documentrequest",
                    ),
                ),
            ],
            options={
                "verbose_name": "Request Approval",
                "verbose_name_plural": "Request Approvals",
                "db_table": "requests_request_approval",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="AuditLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "action",
                    models.CharField(
                        choices=[
                            ("create", "Create"),
                            ("update", "Update"),
                            ("delete", "Delete"),
                            ("view", "View"),
                            ("download", "Download"),
                            ("checkout", "Checkout"),
                            ("return", "Return"),
                            ("approve", "Approve"),
                            ("reject", "Reject"),
                            ("login", "Login"),
                            ("logout", "Logout"),
                        ],
                        max_length=20,
                    ),
                ),
                ("object_id", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "object_repr",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Detailed description of the action",
                        null=True,
                    ),
                ),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True, null=True)),
                (
                    "changes",
                    models.JSONField(
                        blank=True,
                        help_text="JSON representation of changes made",
                        null=True,
                    ),
                ),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                (
                    "content_type",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.contenttype",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="audit_logs",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Audit Log",
                "verbose_name_plural": "Audit Logs",
                "db_table": "requests_audit_log",
                "ordering": ["-timestamp"],
                "indexes": [
                    models.Index(
                        fields=["user", "timestamp"],
                        name="requests_au_user_id_f5215e_idx",
                    ),
                    models.Index(
                        fields=["action", "timestamp"],
                        name="requests_au_action_0d3d4f_idx",
                    ),
                    models.Index(
                        fields=["content_type", "object_id"],
                        name="requests_au_content_db3395_idx",
                    ),
                ],
            },
        ),
        migrations.AddIndex(
            model_name="documentrequest",
            index=models.Index(
                fields=["status", "requested_date"],
                name="requests_do_status_c34d27_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="documentrequest",
            index=models.Index(
                fields=["requested_by"], name="requests_do_request_5419b5_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="documentrequest",
            index=models.Index(
                fields=["due_date"], name="requests_do_due_dat_db29d9_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="notification",
            index=models.Index(
                fields=["recipient", "is_read"], name="requests_no_recipie_240acb_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="notification",
            index=models.Index(
                fields=["created_at"], name="requests_no_created_6d9f2a_idx"
            ),
        ),
    ]
