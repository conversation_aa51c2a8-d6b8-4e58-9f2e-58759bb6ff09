/**
 * API Health Check Utility
 * Helps diagnose API connectivity issues
 */

export interface HealthCheckResult {
  endpoint: string;
  status: 'success' | 'error' | 'network_error' | 'server_error';
  statusCode?: number;
  message: string;
  responseTime: number;
}

export class ApiHealthChecker {
  private baseUrl: string;

  constructor(baseUrl: string = '/api') {
    this.baseUrl = baseUrl;
  }

  async checkEndpoint(endpoint: string): Promise<HealthCheckResult> {
    const startTime = Date.now();
    const fullUrl = `${this.baseUrl}${endpoint}`;

    try {
      const response = await fetch(fullUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      });

      const responseTime = Date.now() - startTime;

      if (response.ok) {
        return {
          endpoint: fullUrl,
          status: 'success',
          statusCode: response.status,
          message: `API endpoint is working (${response.status})`,
          responseTime,
        };
      } else {
        // Try to get error details
        let errorMessage = `HTTP ${response.status}`;
        try {
          const text = await response.text();
          if (text.includes('<!doctype') || text.includes('<html>')) {
            errorMessage = 'Server returned HTML instead of JSON (likely Django error page)';
          } else {
            const errorData = JSON.parse(text);
            errorMessage = errorData.detail || errorData.message || errorMessage;
          }
        } catch {
          // Ignore parsing errors
        }

        return {
          endpoint: fullUrl,
          status: 'server_error',
          statusCode: response.status,
          message: errorMessage,
          responseTime,
        };
      }
    } catch (error: any) {
      const responseTime = Date.now() - startTime;

      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        return {
          endpoint: fullUrl,
          status: 'network_error',
          message: 'Network error: Cannot connect to server',
          responseTime,
        };
      }

      return {
        endpoint: fullUrl,
        status: 'error',
        message: error.message || 'Unknown error',
        responseTime,
      };
    }
  }

  async checkMultipleEndpoints(endpoints: string[]): Promise<HealthCheckResult[]> {
    const results = await Promise.allSettled(
      endpoints.map(endpoint => this.checkEndpoint(endpoint))
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          endpoint: `${this.baseUrl}${endpoints[index]}`,
          status: 'error' as const,
          message: result.reason?.message || 'Promise rejected',
          responseTime: 0,
        };
      }
    });
  }

  async checkLocationHierarchyEndpoints(): Promise<HealthCheckResult[]> {
    const endpoints = [
      '/locations/countries/',
      '/locations/regions/',
      '/locations/zones/',
      '/locations/cities/',
      '/locations/subcities/',
      '/locations/kebeles/',
      '/locations/special-locations/',
      '/locations/kebeles/select/',
      '/locations/special-locations/select/',
    ];

    return this.checkMultipleEndpoints(endpoints);
  }

  formatResults(results: HealthCheckResult[]): string {
    let report = 'API Health Check Report\n';
    report += '========================\n\n';

    const successful = results.filter(r => r.status === 'success');
    const failed = results.filter(r => r.status !== 'success');

    report += `✅ Successful: ${successful.length}\n`;
    report += `❌ Failed: ${failed.length}\n\n`;

    if (failed.length > 0) {
      report += 'Failed Endpoints:\n';
      report += '-----------------\n';
      failed.forEach(result => {
        report += `❌ ${result.endpoint}\n`;
        report += `   Status: ${result.status}\n`;
        report += `   Message: ${result.message}\n`;
        if (result.statusCode) {
          report += `   HTTP Status: ${result.statusCode}\n`;
        }
        report += `   Response Time: ${result.responseTime}ms\n\n`;
      });
    }

    if (successful.length > 0) {
      report += 'Successful Endpoints:\n';
      report += '--------------------\n';
      successful.forEach(result => {
        report += `✅ ${result.endpoint} (${result.responseTime}ms)\n`;
      });
    }

    return report;
  }
}

// Export a default instance
export const apiHealthChecker = new ApiHealthChecker();

// Helper function for quick checks
export const checkApiHealth = async (): Promise<void> => {
  console.log('🔍 Checking API health...');
  
  const results = await apiHealthChecker.checkLocationHierarchyEndpoints();
  const report = apiHealthChecker.formatResults(results);
  
  console.log(report);
  
  const failedCount = results.filter(r => r.status !== 'success').length;
  if (failedCount > 0) {
    console.error(`❌ ${failedCount} API endpoints are not working properly`);
  } else {
    console.log('✅ All API endpoints are working correctly');
  }
};
