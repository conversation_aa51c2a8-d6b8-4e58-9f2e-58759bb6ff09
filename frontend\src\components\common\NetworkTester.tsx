import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  Alert,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import { ExpandMore, NetworkCheck } from '@mui/icons-material';
import axios from 'axios';

const NetworkTester: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const testEndpoints = [
    {
      name: 'Backend Health Check',
      url: 'http://localhost:8000/api/',
      method: 'GET',
    },
    {
      name: 'Login Endpoint',
      url: 'http://localhost:8000/api/accounts/auth/login/',
      method: 'POST',
      data: { username: 'admin', password: 'admin123' },
    },
    {
      name: 'CORS Preflight',
      url: 'http://localhost:8000/api/accounts/auth/login/',
      method: 'OPTIONS',
    },
  ];

  const testEndpoint = async (endpoint: typeof testEndpoints[0]) => {
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log(`🧪 Testing ${endpoint.name}...`);
      
      const config: any = {
        method: endpoint.method,
        url: endpoint.url,
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
        },
      };

      if (endpoint.data) {
        config.data = endpoint.data;
      }

      const response = await axios(config);
      
      const resultData = {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
        data: response.data,
        config: {
          url: response.config.url,
          method: response.config.method,
          headers: response.config.headers,
        },
      };

      setResult(resultData);
      console.log(`✅ ${endpoint.name} successful:`, resultData);
      
    } catch (err: any) {
      const errorData = {
        message: err.message,
        code: err.code,
        status: err.response?.status,
        statusText: err.response?.statusText,
        data: err.response?.data,
        headers: err.response?.headers,
      };
      
      setError(JSON.stringify(errorData, null, 2));
      console.error(`❌ ${endpoint.name} failed:`, errorData);
    } finally {
      setIsLoading(false);
    }
  };

  const testAllEndpoints = async () => {
    for (const endpoint of testEndpoints) {
      await testEndpoint(endpoint);
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second between tests
    }
  };

  return (
    <Box sx={{ mb: 3 }}>
      <Accordion>
        <AccordionSummary expandIcon={<ExpandMore />}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <NetworkCheck />
            <Typography variant="h6">Network Connectivity Tester</Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
            {testEndpoints.map((endpoint, index) => (
              <Button
                key={index}
                variant="outlined"
                size="small"
                onClick={() => testEndpoint(endpoint)}
                disabled={isLoading}
              >
                Test {endpoint.name}
              </Button>
            ))}
            <Button
              variant="contained"
              size="small"
              onClick={testAllEndpoints}
              disabled={isLoading}
            >
              Test All
            </Button>
          </Box>

          {isLoading && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
              <CircularProgress size={20} />
              <Typography variant="body2">Testing...</Typography>
            </Box>
          )}

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              <Typography variant="subtitle2">Request Failed:</Typography>
              <pre style={{ fontSize: '0.75rem', margin: 0, whiteSpace: 'pre-wrap' }}>
                {error}
              </pre>
            </Alert>
          )}

          {result && (
            <Alert severity="success" sx={{ mb: 2 }}>
              <Typography variant="subtitle2">Request Successful:</Typography>
              <pre style={{ fontSize: '0.75rem', margin: 0, whiteSpace: 'pre-wrap' }}>
                {JSON.stringify(result, null, 2)}
              </pre>
            </Alert>
          )}

          <Typography variant="body2" color="text.secondary">
            This tool helps debug network connectivity issues between the frontend and backend.
            Check the Debug Console below for detailed logs.
          </Typography>
        </AccordionDetails>
      </Accordion>
    </Box>
  );
};

export default NetworkTester;
