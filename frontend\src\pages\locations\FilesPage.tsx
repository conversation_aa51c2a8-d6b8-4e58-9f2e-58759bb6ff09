import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  TextField,
  InputAdornment,
  Avatar,
  Divider,
  Stack,
  Fade,
  Alert,
  CircularProgress,
  IconButton,
  Breadcrumbs,
  Link,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TablePagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Folder,
  ArrowBack,
  Home,
  Add,
  Edit,
  Delete,
  Inventory,
  Business,
  Description,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useNotification } from '../../contexts/NotificationContext';
import locationService from '../../services/locationService';
import fileService from '../../services/fileService';
import ConfirmationDialog from '../../components/common/ConfirmationDialog';
import type { File, FileCreate } from '../../services/fileService';
import type { Kent, FileType } from '../../services/types';

const FilesPage: React.FC = () => {
  const navigate = useNavigate();
  const { showNotification } = useNotification();

  const [files, setFiles] = useState<File[]>([]);
  const [kents, setKents] = useState<Kent[]>([]);
  const [fileTypes, setFileTypes] = useState<FileType[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingFile, setEditingFile] = useState<File | null>(null);
  const [formData, setFormData] = useState<FileCreate>({
    kent: 0,
    name: '',
    file_number: '',
    file_type: 0,
    description: '',
  });
  const [formErrors, setFormErrors] = useState<any>({});
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [fileToDelete, setFileToDelete] = useState<File | null>(null);
  const [deleting, setDeleting] = useState(false);

  // Remove the hardcoded options - we'll load from backend

  useEffect(() => {
    loadFiles();
    loadKents();
    loadFileTypes();
  }, [page, rowsPerPage]);

  const loadFiles = async () => {
    try {
      setLoading(true);
      const response = await fileService.getFiles({
        page: page + 1,
        page_size: rowsPerPage,
      });
      setFiles(response.results);
      setTotalCount(response.count);
    } catch (error) {
      console.error('Error loading files:', error);
      showNotification('Failed to load files', 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadKents = async () => {
    try {
      const response = await locationService.getKents({ page_size: 100 });
      setKents(response.results);
    } catch (error) {
      console.error('Error loading kents:', error);
    }
  };

  const loadFileTypes = async () => {
    try {
      const response = await locationService.getFileTypes({ page_size: 100 });
      setFileTypes(response.results);
    } catch (error) {
      console.error('Error loading file types:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setSubmitting(true);
      setFormErrors({});

      if (editingFile) {
        await fileService.updateFile(editingFile.id, formData);
        showNotification('File updated successfully', 'success');
      } else {
        await fileService.createFile(formData);
        showNotification('File created successfully', 'success');
      }

      resetForm();
      loadFiles();
    } catch (error: any) {
      console.error('Error submitting form:', error);
      if (error.response?.data) {
        setFormErrors(error.response.data);
      }
      showNotification('Failed to save file', 'error');
    } finally {
      setSubmitting(false);
    }
  };

  const handleEdit = (file: File) => {
    setEditingFile(file);
    setFormData({
      kent: file.kent,
      name: file.name,
      file_number: file.file_number,
      file_type: file.file_type || 0,
      description: file.description || '',
    });
    setShowForm(true);
  };

  const handleDelete = (file: File) => {
    setFileToDelete(file);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!fileToDelete) return;
    
    try {
      setDeleting(true);
      await fileService.deleteFile(fileToDelete.id);
      showNotification('File deleted successfully', 'success');
      loadFiles();
      handleCloseDeleteDialog();
    } catch (error) {
      console.error('Error deleting file:', error);
      showNotification('Failed to delete file', 'error');
    } finally {
      setDeleting(false);
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setFileToDelete(null);
    setDeleting(false);
  };

  const resetForm = () => {
    setFormData({
      kent: 0,
      name: '',
      file_number: '',
      file_type: 0,
      description: '',
    });
    setFormErrors({});
    setEditingFile(null);
    setShowForm(false);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Fade in timeout={800}>
        <Box sx={{ mb: 4 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs sx={{ mb: 2 }}>
            <Link
              component="button"
              variant="body1"
              onClick={() => navigate('/locations')}
              sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
            >
              <Home fontSize="small" />
              Physical Location Center
            </Link>
            <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <Folder fontSize="small" />
              Files
            </Typography>
          </Breadcrumbs>

          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'between', mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <IconButton onClick={() => navigate('/locations')} sx={{ mr: 1 }}>
                <ArrowBack />
              </IconButton>
              <Avatar sx={{ bgcolor: 'info.main' }}>
                <Folder />
              </Avatar>
              <Box>
                <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
                  Files Management
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Organize business files within kents
                </Typography>
              </Box>
            </Box>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setShowForm(true)}
              size="large"
              sx={{ ml: 'auto' }}
            >
              Add File
            </Button>
          </Box>
        </Box>
      </Fade>

      {/* Form Section */}
      {showForm && (
        <Fade in timeout={600}>
          <Card sx={{ mb: 4 }}>
            <CardContent sx={{ p: 4 }}>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                {editingFile ? 'Edit File' : 'Add New File'}
              </Typography>
              <Divider sx={{ mb: 3 }} />
              
              <form onSubmit={handleSubmit}>
                <Stack spacing={3}>
                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                    <TextField
                      label="File Name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      error={!!formErrors.name}
                      helperText={formErrors.name || 'Enter the file name or identifier'}
                      required
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Folder color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />

                    <TextField
                      label="File Number"
                      value={formData.file_number}
                      onChange={(e) => setFormData({ ...formData, file_number: e.target.value })}
                      error={!!formErrors.file_number}
                      helperText={formErrors.file_number || 'Unique file number'}
                      required
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Description color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>
                  
                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                    <FormControl fullWidth required error={!!formErrors.kent}>
                      <InputLabel>Kent</InputLabel>
                      <Select
                        value={formData.kent}
                        onChange={(e) => setFormData({ ...formData, kent: Number(e.target.value) })}
                        label="Kent"
                        startAdornment={
                          <InputAdornment position="start">
                            <Inventory color="action" />
                          </InputAdornment>
                        }
                      >
                        {kents.map((kent) => (
                          <MenuItem key={kent.id} value={kent.id}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Box
                                sx={{
                                  width: 16,
                                  height: 16,
                                  borderRadius: '50%',
                                  bgcolor: kent.color || '#2196f3',
                                  border: '1px solid #ccc',
                                }}
                              />
                              {kent.name}
                              {kent.location_path && (
                                <Typography variant="caption" color="text.secondary">
                                  ({kent.location_path})
                                </Typography>
                              )}
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                      {formErrors.kent && (
                        <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 2 }}>
                          {formErrors.kent}
                        </Typography>
                      )}
                    </FormControl>
                    
                    <FormControl fullWidth required error={!!formErrors.file_type}>
                      <InputLabel>File Type</InputLabel>
                      <Select
                        value={formData.file_type}
                        onChange={(e) => setFormData({ ...formData, file_type: Number(e.target.value) })}
                        label="File Type"
                        startAdornment={
                          <InputAdornment position="start">
                            <Business color="action" />
                          </InputAdornment>
                        }
                      >
                        {fileTypes.map((fileType) => (
                          <MenuItem key={fileType.id} value={fileType.id}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Box
                                sx={{
                                  width: 16,
                                  height: 16,
                                  borderRadius: '50%',
                                  bgcolor: fileType.color || '#2196f3',
                                  border: '1px solid #ccc',
                                }}
                              />
                              <Chip label={fileType.code} size="small" />
                              {fileType.name}
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                      {formErrors.file_type && (
                        <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 2 }}>
                          {formErrors.file_type}
                        </Typography>
                      )}
                    </FormControl>
                  </Box>

                  <TextField
                    label="Description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    error={!!formErrors.description}
                    helperText={formErrors.description || 'Optional description for this file'}
                    multiline
                    rows={3}
                    fullWidth
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Description color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />

                  <Box sx={{ display: 'flex', gap: 2, pt: 2 }}>
                    <Button
                      type="submit"
                      variant="contained"
                      disabled={submitting}
                      startIcon={submitting ? <CircularProgress size={20} /> : <Add />}
                      size="large"
                    >
                      {submitting ? 'Saving...' : editingFile ? 'Update File' : 'Create File'}
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={resetForm}
                      size="large"
                    >
                      Cancel
                    </Button>
                  </Box>
                </Stack>
              </form>
            </CardContent>
          </Card>
        </Fade>
      )}

      {/* Files Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
            Files List
          </Typography>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : files.length === 0 ? (
            <Alert severity="info">
              No files found. Click "Add File" to create your first file.
            </Alert>
          ) : (
            <>
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>File</TableCell>
                      <TableCell>Kent</TableCell>
                      <TableCell>Type</TableCell>
                      <TableCell>Documents</TableCell>
                      <TableCell>Created</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {files.map((file) => (
                      <TableRow key={file.id} hover>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <Avatar sx={{ bgcolor: 'info.main', width: 32, height: 32 }}>
                              <Folder fontSize="small" />
                            </Avatar>
                            <Box>
                              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                {file.name}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {file.description || 'No description'}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Box
                              sx={{
                                width: 12,
                                height: 12,
                                borderRadius: '50%',
                                bgcolor: file.kent_color || '#2196f3',
                                border: '1px solid #ccc',
                              }}
                            />
                            {file.kent_name || `Kent ${file.kent}`}
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Box
                              sx={{
                                width: 12,
                                height: 12,
                                borderRadius: '50%',
                                bgcolor: file.file_type_color || '#2196f3',
                                border: '1px solid #ccc',
                              }}
                            />
                            <Chip
                              label={file.file_type_name || 'Unknown'}
                              size="small"
                              color="primary"
                              variant="outlined"
                            />
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {file.document_count || 0} documents
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {new Date(file.created_at).toLocaleDateString()}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                            <IconButton
                              size="small"
                              onClick={() => handleEdit(file)}
                              color="primary"
                            >
                              <Edit fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleDelete(file)}
                              color="error"
                            >
                              <Delete fontSize="small" />
                            </IconButton>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              <TablePagination
                component="div"
                count={totalCount}
                page={page}
                onPageChange={handleChangePage}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                rowsPerPageOptions={[5, 10, 25, 50]}
              />
            </>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        title="Delete File"
        itemName={fileToDelete?.name}
        itemType="File"
        message={`Are you sure you want to delete "${fileToDelete?.name}"? This will also delete all documents within this file. This action cannot be undone.`}
        confirmText="Delete File"
        severity="error"
        loading={deleting}
      />
    </Container>
  );
};

export default FilesPage;
