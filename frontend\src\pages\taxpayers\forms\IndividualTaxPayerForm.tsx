import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Typography,
  Avatar,
  IconButton,
  Alert,
  CircularProgress,
  Divider,
} from '@mui/material';
import {
  PhotoCamera,
  Person,
  Save,
  Cancel,
  Add,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { useNotification } from '../../../contexts/NotificationContext';
// Import the service
import taxpayerService from '../../../services/taxpayerService';

// Define interfaces locally to avoid import issues
interface IndividualTaxPayer {
  id: string;
  tin: string;
  first_name: string;
  middle_name: string;
  last_name: string;
  full_name: string;
  display_name: string;
  nationality: string;
  gender: string;
  date_of_birth: string;
  tax_payer_level: string;
  tax_payer_level_name: string;
  business_sector: string;
  business_sector_name: string;
  business_sub_sector: string;
  business_sub_sector_name: string;
  business_registration_date: string;
  business_name: string;
  business_license_number: string;
  phone: string;
  phone_secondary: string;
  email: string;
  country: string;
  country_name: string;
  region: string;
  region_name: string;
  zone: string;
  zone_name: string;
  city: string;
  city_name: string;
  subcity: string;
  subcity_name: string;
  kebele: string;
  kebele_name: string;
  house_number: string;
  street_address: string;
  postal_code: string;
  location_display: string;
  profile_picture?: string;

  tax_file_name?: string;
  is_active: boolean;
  registration_date: string;
  last_updated: string;
}

interface IndividualTaxPayerCreate {
  tin: string;
  first_name: string;
  middle_name?: string;
  last_name: string;
  nationality: string;
  gender: string;
  date_of_birth: string;
  tax_payer_level: string;
  business_sector: string;
  business_sub_sector: string;
  business_registration_date: string;
  business_name?: string;
  business_license_number?: string;
  phone: string;
  phone_secondary?: string;
  email?: string;
  country?: string;
  region?: string;
  zone?: string;
  city?: string;
  subcity?: string;
  kebele?: string;
  house_number?: string;
  street_address?: string;
  postal_code?: string;
  profile_picture?: File;

}

interface BusinessSector {
  id: string;
  code: string;
  name: string;
  description: string;
  is_active: boolean;
  sub_sectors_count: number;
  created_at: string;
  updated_at: string;
}

interface BusinessSubSector {
  id: string;
  business_sector: string;
  business_sector_name: string;
  business_sector_code: string;
  code: string;
  name: string;
  description: string;
  full_code: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface TaxPayerLevel {
  id: string;
  name: string;
  code: string;
  description: string;
  minimum_annual_turnover?: number;
  maximum_annual_turnover?: number;
  turnover_range: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}
import locationService from '../../../services/locationService';
import locationHierarchyService from '../../../services/locationHierarchyService';

// Define location hierarchy interfaces locally
interface SubCity {
  id: number;
  city: number;
  city_name: string;
  zone_name: string;
  region_name: string;
  country_name: string;
  name: string;
  code: string;
  type: 'subcity' | 'woreda' | 'district';
  type_display: string;
  population?: number;
  area_km2?: number;
  is_active: boolean;
  full_name: string;
  kebele_count: number;
  created_at: string;
  updated_at: string;
}

interface Kebele {
  id: number;
  subcity: number;
  subcity_name: string;
  city_name: string;
  zone_name: string;
  region_name: string;
  country_name: string;
  name: string;
  code: string;
  number: number;
  population?: number;
  area_km2?: number;
  is_active: boolean;
  full_name: string;
  display_name: string;
  created_at: string;
  updated_at: string;
}



import CompactLocationSelector from '../../../components/locations/CompactLocationSelector';
import BusinessSubSectorCreator from '../components/BusinessSubSectorCreator';

interface IndividualTaxPayerFormProps {
  taxpayer?: IndividualTaxPayer | null;
  onSubmit: () => void;
  onCancel: () => void;
}

const IndividualTaxPayerForm: React.FC<IndividualTaxPayerFormProps> = ({
  taxpayer,
  onSubmit,
  onCancel,
}) => {
  const { showNotification } = useNotification();
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Form data
  const [formData, setFormData] = useState<IndividualTaxPayerCreate>({
    tin: '',
    first_name: '',
    middle_name: '',
    last_name: '',
    nationality: 'ET',
    gender: '',
    date_of_birth: '',
    tax_payer_level: '',
    business_sector: '',
    business_sub_sector: '',
    business_registration_date: '',
    business_name: '',
    business_license_number: '',
    phone: '',
    phone_secondary: '',
    email: '',
    country: '',
    region: '',
    zone: '',
    city: '',
    subcity: '',
    kebele: '',
    house_number: '',
    street_address: '',
    postal_code: '',
  });

  // Profile picture
  const [profilePicture, setProfilePicture] = useState<File | null>(null);
  const [profilePicturePreview, setProfilePicturePreview] = useState<string>('');

  // Options
  const [levels, setLevels] = useState<TaxPayerLevel[]>([]);
  const [sectors, setSectors] = useState<BusinessSector[]>([]);
  const [subSectors, setSubSectors] = useState<BusinessSubSector[]>([]);


  // Sub-sector creation
  const [subSectorCreatorOpen, setSubSectorCreatorOpen] = useState(false);

  useEffect(() => {
    loadOptions();
    if (taxpayer) {
      populateForm(taxpayer);
    }
  }, [taxpayer]);

  useEffect(() => {
    if (formData.business_sector) {
      loadSubSectors(formData.business_sector);
    } else {
      setSubSectors([]);
      setFormData(prev => ({ ...prev, business_sub_sector: '' }));
    }
  }, [formData.business_sector]);

  const loadOptions = async () => {
    try {
      const [levelsData, sectorsData] = await Promise.all([
        taxpayerService.getTaxPayerLevelsSimple(),
        taxpayerService.getBusinessSectorsSimple(),
      ]);

      setLevels(levelsData);
      setSectors(sectorsData);
    } catch (error) {
      console.error('Failed to load options:', error);
      showNotification('Failed to load form options', 'error');
    }
  };

  const loadSubSectors = async (sectorId: string) => {
    try {
      const subSectorsData = await taxpayerService.getBusinessSubSectorsSimple(sectorId);
      setSubSectors(subSectorsData);
    } catch (error) {
      console.error('Failed to load sub-sectors:', error);
    }
  };

  const populateForm = (taxpayer: IndividualTaxPayer) => {
    setFormData({
      tin: taxpayer.tin,
      first_name: taxpayer.first_name,
      middle_name: taxpayer.middle_name || '',
      last_name: taxpayer.last_name,
      nationality: taxpayer.nationality,
      gender: taxpayer.gender,
      date_of_birth: taxpayer.date_of_birth,
      tax_payer_level: taxpayer.tax_payer_level,
      business_sector: taxpayer.business_sector,
      business_sub_sector: taxpayer.business_sub_sector,
      business_registration_date: taxpayer.business_registration_date,
      business_name: taxpayer.business_name || '',
      business_license_number: taxpayer.business_license_number || '',
      phone: taxpayer.phone,
      phone_secondary: taxpayer.phone_secondary || '',
      email: taxpayer.email || '',
      country: taxpayer.country || '',
      region: taxpayer.region || '',
      zone: taxpayer.zone || '',
      city: taxpayer.city || '',
      subcity: taxpayer.subcity || '',
      kebele: taxpayer.kebele || '',
      house_number: taxpayer.house_number || '',
      street_address: taxpayer.street_address || '',
      postal_code: taxpayer.postal_code || '',

    });

    if (taxpayer.profile_picture) {
      setProfilePicturePreview(taxpayer.profile_picture);
    }
  };

  const handleInputChange = (field: keyof IndividualTaxPayerCreate, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleLocationChange = (locationData: any) => {
    setFormData(prev => ({
      ...prev,
      country: locationData.country || '',
      region: locationData.region || '',
      zone: locationData.zone || '',
      city: locationData.city || '',
      subcity: locationData.subcity || '',
      kebele: locationData.kebele || '',
    }));
  };

  const handleProfilePictureChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setProfilePicture(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setProfilePicturePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubSectorCreated = (newSubSector: BusinessSubSector) => {
    setSubSectors(prev => [...prev, newSubSector]);
    setFormData(prev => ({ ...prev, business_sub_sector: newSubSector.id }));
    showNotification('Sub-sector created and selected', 'success');
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.tin) newErrors.tin = 'TIN is required';
    else if (!/^\d{10}$/.test(formData.tin)) newErrors.tin = 'TIN must be exactly 10 digits';

    if (!formData.first_name) newErrors.first_name = 'First name is required';
    if (!formData.last_name) newErrors.last_name = 'Last name is required';
    if (!formData.gender) newErrors.gender = 'Gender is required';
    if (!formData.date_of_birth) newErrors.date_of_birth = 'Date of birth is required';
    if (!formData.tax_payer_level) newErrors.tax_payer_level = 'Tax payer level is required';
    if (!formData.business_sector) newErrors.business_sector = 'Business sector is required';
    if (!formData.business_sub_sector) newErrors.business_sub_sector = 'Business sub-sector is required';
    if (!formData.business_registration_date) newErrors.business_registration_date = 'Business registration date is required';
    if (!formData.phone) newErrors.phone = 'Phone number is required';

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Invalid email format';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!validateForm()) {
      showNotification('Please fix the form errors', 'error');
      return;
    }

    try {
      setLoading(true);
      
      const submitData = { ...formData };
      if (profilePicture) {
        submitData.profile_picture = profilePicture;
      }

      if (taxpayer) {
        await taxpayerService.updateIndividualTaxPayer(taxpayer.id, submitData);
        showNotification('Individual taxpayer updated successfully', 'success');
      } else {
        await taxpayerService.createIndividualTaxPayer(submitData);
        showNotification('Individual taxpayer created successfully', 'success');
      }
      
      onSubmit();
    } catch (error: any) {
      console.error('Failed to save taxpayer:', error);
      
      if (error.response?.data) {
        setErrors(error.response.data);
        showNotification('Please fix the form errors', 'error');
      } else {
        showNotification('Failed to save taxpayer', 'error');
      }
    } finally {
      setLoading(false);
    }
  };

  const nationalityOptions = [
    { value: 'ET', label: 'Ethiopian' },
    { value: 'US', label: 'American' },
    { value: 'UK', label: 'British' },
    { value: 'CA', label: 'Canadian' },
    { value: 'DE', label: 'German' },
    { value: 'FR', label: 'French' },
    { value: 'OTHER', label: 'Other' },
  ];

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
        {/* Profile Picture */}
        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
          <Box sx={{ position: 'relative' }}>
            <Avatar
              src={profilePicturePreview}
              sx={{ width: 100, height: 100, bgcolor: 'primary.main' }}
            >
              <Person sx={{ fontSize: 50 }} />
            </Avatar>
            <IconButton
              component="label"
              sx={{
                position: 'absolute',
                bottom: -8,
                right: -8,
                bgcolor: 'primary.main',
                color: 'white',
                '&:hover': { bgcolor: 'primary.dark' }
              }}
            >
              <PhotoCamera />
              <input
                type="file"
                hidden
                accept="image/*"
                onChange={handleProfilePictureChange}
              />
            </IconButton>
          </Box>
        </Box>

        <Grid container spacing={3}>
          {/* Personal Information */}
          <Grid size={{ xs: 12 }}>
            <Typography variant="h6" gutterBottom>
              Personal Information
            </Typography>
            <Divider sx={{ mb: 2 }} />
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <TextField
              fullWidth
              label="TIN *"
              value={formData.tin}
              onChange={(e) => handleInputChange('tin', e.target.value)}
              error={!!errors.tin}
              helperText={errors.tin}
              inputProps={{ maxLength: 10 }}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <FormControl fullWidth error={!!errors.nationality}>
              <InputLabel>Nationality *</InputLabel>
              <Select
                value={formData.nationality}
                onChange={(e) => handleInputChange('nationality', e.target.value)}
                label="Nationality *"
              >
                {nationalityOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid size={{ xs: 12, md: 4 }}>
            <TextField
              fullWidth
              label="First Name *"
              value={formData.first_name}
              onChange={(e) => handleInputChange('first_name', e.target.value)}
              error={!!errors.first_name}
              helperText={errors.first_name}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 4 }}>
            <TextField
              fullWidth
              label="Middle Name"
              value={formData.middle_name}
              onChange={(e) => handleInputChange('middle_name', e.target.value)}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 4 }}>
            <TextField
              fullWidth
              label="Last Name *"
              value={formData.last_name}
              onChange={(e) => handleInputChange('last_name', e.target.value)}
              error={!!errors.last_name}
              helperText={errors.last_name}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <FormControl fullWidth error={!!errors.gender}>
              <InputLabel>Gender *</InputLabel>
              <Select
                value={formData.gender}
                onChange={(e) => handleInputChange('gender', e.target.value)}
                label="Gender *"
              >
                <MenuItem value="M">Male</MenuItem>
                <MenuItem value="F">Female</MenuItem>
                <MenuItem value="O">Other</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <DatePicker
              label="Date of Birth *"
              value={formData.date_of_birth ? new Date(formData.date_of_birth) : null}
              onChange={(date) => {
                if (date && !isNaN(date.getTime())) {
                  handleInputChange('date_of_birth', date.toISOString().split('T')[0]);
                } else {
                  handleInputChange('date_of_birth', '');
                }
              }}
              slotProps={{
                textField: {
                  fullWidth: true,
                  error: !!errors.date_of_birth,
                  helperText: errors.date_of_birth,
                }
              }}
            />
          </Grid>

          {/* Business Information */}
          <Grid size={{ xs: 12 }}>
            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
              Business Information
            </Typography>
            <Divider sx={{ mb: 2 }} />
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <FormControl fullWidth error={!!errors.tax_payer_level}>
              <InputLabel>Tax Payer Level *</InputLabel>
              <Select
                value={formData.tax_payer_level}
                onChange={(e) => handleInputChange('tax_payer_level', e.target.value)}
                label="Tax Payer Level *"
              >
                {levels.map((level) => (
                  <MenuItem key={level.id} value={level.id}>
                    {level.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <DatePicker
              label="Business Registration Date *"
              value={formData.business_registration_date ? new Date(formData.business_registration_date) : null}
              onChange={(date) => {
                if (date && !isNaN(date.getTime())) {
                  handleInputChange('business_registration_date', date.toISOString().split('T')[0]);
                } else {
                  handleInputChange('business_registration_date', '');
                }
              }}
              slotProps={{
                textField: {
                  fullWidth: true,
                  error: !!errors.business_registration_date,
                  helperText: errors.business_registration_date,
                }
              }}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <FormControl fullWidth error={!!errors.business_sector}>
              <InputLabel>Business Sector *</InputLabel>
              <Select
                value={formData.business_sector}
                onChange={(e) => handleInputChange('business_sector', e.target.value)}
                label="Business Sector *"
              >
                {sectors.map((sector) => (
                  <MenuItem key={sector.id} value={sector.id}>
                    {sector.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-start' }}>
              <FormControl fullWidth error={!!errors.business_sub_sector}>
                <InputLabel>Business Sub-Sector *</InputLabel>
                <Select
                  value={formData.business_sub_sector}
                  onChange={(e) => handleInputChange('business_sub_sector', e.target.value)}
                  label="Business Sub-Sector *"
                  disabled={!formData.business_sector}
                >
                  {subSectors.map((subSector) => (
                    <MenuItem key={subSector.id} value={subSector.id}>
                      {subSector.name}
                    </MenuItem>
                  ))}
                </Select>
                {errors.business_sub_sector && (
                  <Typography variant="caption" color="error" sx={{ mt: 0.5 }}>
                    {errors.business_sub_sector}
                  </Typography>
                )}
              </FormControl>
              <Button
                variant="outlined"
                onClick={() => setSubSectorCreatorOpen(true)}
                disabled={!formData.business_sector}
                sx={{ minWidth: 56, height: 56 }}
                title="Create new sub-sector"
              >
                <Add />
              </Button>
            </Box>
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <TextField
              fullWidth
              label="Business Name"
              value={formData.business_name}
              onChange={(e) => handleInputChange('business_name', e.target.value)}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <TextField
              fullWidth
              label="Business License Number"
              value={formData.business_license_number}
              onChange={(e) => handleInputChange('business_license_number', e.target.value)}
            />
          </Grid>

          {/* Contact Information */}
          <Grid size={{ xs: 12 }}>
            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
              Contact Information
            </Typography>
            <Divider sx={{ mb: 2 }} />
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <TextField
              fullWidth
              label="Phone Number *"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              error={!!errors.phone}
              helperText={errors.phone}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <TextField
              fullWidth
              label="Secondary Phone"
              value={formData.phone_secondary}
              onChange={(e) => handleInputChange('phone_secondary', e.target.value)}
            />
          </Grid>

          <Grid size={{ xs: 12 }}>
            <TextField
              fullWidth
              label="Email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              error={!!errors.email}
              helperText={errors.email}
            />
          </Grid>

          {/* Location */}
          <Grid size={{ xs: 12 }}>
            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
              Address Information
            </Typography>
            <Divider sx={{ mb: 2 }} />
          </Grid>

          <Grid size={{ xs: 12 }}>
            <CompactLocationSelector
              value={{
                country: formData.country ? parseInt(formData.country) : null,
                region: formData.region ? parseInt(formData.region) : null,
                zone: formData.zone ? parseInt(formData.zone) : null,
                city: formData.city ? parseInt(formData.city) : null,
                subcity: formData.subcity ? parseInt(formData.subcity) : null,
                kebele: formData.kebele ? parseInt(formData.kebele) : null,
              }}
              onChange={handleLocationChange}
              errors={{}}
              required={true}
              title="Address Information"
            />
          </Grid>

          <Grid size={{ xs: 12, md: 4 }}>
            <TextField
              fullWidth
              label="House Number"
              value={formData.house_number}
              onChange={(e) => handleInputChange('house_number', e.target.value)}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 4 }}>
            <TextField
              fullWidth
              label="Street Address"
              value={formData.street_address}
              onChange={(e) => handleInputChange('street_address', e.target.value)}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 4 }}>
            <TextField
              fullWidth
              label="Postal Code"
              value={formData.postal_code}
              onChange={(e) => handleInputChange('postal_code', e.target.value)}
            />
          </Grid>

          {/* File Management */}
          <Grid size={{ xs: 12 }}>
            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
              File Management
            </Typography>
            <Divider sx={{ mb: 2 }} />
          </Grid>


        </Grid>

        {/* Form Actions */}
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 4 }}>
          <Button
            variant="outlined"
            onClick={onCancel}
            disabled={loading}
            startIcon={<Cancel />}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : <Save />}
          >
            {loading ? 'Saving...' : taxpayer ? 'Update' : 'Create'}
          </Button>
        </Box>

        {Object.keys(errors).length > 0 && (
          <Alert severity="error" sx={{ mt: 2 }}>
            Please fix the errors above and try again.
          </Alert>
        )}

        {/* Business Sub-Sector Creator */}
        <BusinessSubSectorCreator
          open={subSectorCreatorOpen}
          onClose={() => setSubSectorCreatorOpen(false)}
          onCreated={handleSubSectorCreated}
          preSelectedSector={formData.business_sector}
        />
      </Box>
    </LocalizationProvider>
  );
};

export default IndividualTaxPayerForm;
