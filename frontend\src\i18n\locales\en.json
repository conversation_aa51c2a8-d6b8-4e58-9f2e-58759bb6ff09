{"common": {"save": "Save", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "create": "Create", "back": "Back", "next": "Next", "previous": "Previous", "loading": "Loading...", "search": "Search", "filter": "Filter", "actions": "Actions", "yes": "Yes", "no": "No", "confirm": "Confirm", "close": "Close", "submit": "Submit", "reset": "Reset", "clear": "Clear", "select": "Select", "required": "Required", "optional": "Optional", "name": "Name", "description": "Description", "status": "Status", "active": "Active", "inactive": "Inactive", "date": "Date", "time": "Time", "email": "Email", "phone": "Phone", "address": "Address", "city": "City", "country": "Country", "website": "Website"}, "navigation": {"dashboard": "Dashboard", "organizations": "Organizations", "users": "Users", "locations": "Locations", "documents": "Documents", "settings": "Settings", "profile": "Profile", "logout": "Logout", "login": "<PERSON><PERSON>"}, "organizations": {"title": "Organizations", "subtitle": "Manage your organizations", "create": "Create Organization", "edit": "Edit Organization", "delete": "Delete Organization", "view": "View Organization", "list": "Organization List", "details": "Organization Details", "noOrganizations": "No organizations found", "searchPlaceholder": "Search organizations...", "form": {"basicInformation": "Basic Information", "contactDetails": "Contact Details", "brandingSettings": "Branding & Settings", "review": "Review", "organizationName": "Organization Name", "shortName": "Short Name / Acronym", "motto": "<PERSON><PERSON>", "tagline": "Tagline", "description": "Description", "website": "Website", "email": "Email Address", "phone": "Phone Number", "fax": "Fax Number", "addressLine1": "Address Line 1", "addressLine2": "Address Line 2", "city": "City", "stateProvince": "State/Province", "postalCode": "Postal Code", "country": "Country", "officeHoursStart": "Office Hours Start", "officeHoursEnd": "Office Hours End", "establishedDate": "Established Date", "registrationNumber": "Registration Number", "taxId": "Tax ID", "licenseNumber": "License Number", "primaryColor": "Primary Color", "secondaryColor": "Secondary Color", "accentColor": "Accent Color", "documentRetentionDays": "Document Retention (Days)", "maxFileSizeMb": "<PERSON> (MB)", "logo": "Logo", "uploadLogo": "Upload Logo", "changeLogo": "Change Logo", "removeLogo": "Remove Logo", "socialMedia": "Social Media", "facebook": "Facebook", "twitter": "Twitter", "linkedin": "LinkedIn", "instagram": "Instagram", "youtube": "YouTube"}, "validation": {"nameRequired": "Organization name is required", "shortNameRequired": "Short name is required", "emailRequired": "Email is required", "phoneRequired": "Phone number is required", "addressRequired": "Address is required", "cityRequired": "City is required", "emailInvalid": "Please enter a valid email address", "logoTooLarge": "Logo file size must be less than 5MB"}, "messages": {"createSuccess": "Organization created successfully!", "updateSuccess": "Organization updated successfully!", "deleteSuccess": "Organization deleted successfully!", "createError": "Failed to create organization", "updateError": "Failed to update organization", "deleteError": "Failed to delete organization", "loadError": "Failed to load organization data"}, "default": "<PERSON><PERSON><PERSON>", "setAsDefault": "Set as <PERSON><PERSON><PERSON>", "departments": "Departments"}, "locations": {"title": "Locations", "subtitle": "Manage geographical locations", "hierarchy": "Location Hierarchy", "countries": "Countries", "regions": "Regions", "zones": "Zones", "cities": "Cities", "subcities": "SubCities/Woredas", "kebeles": "<PERSON><PERSON><PERSON>"}, "users": {"title": "Users", "subtitle": "Manage system users", "create": "Create User", "edit": "Edit User", "profile": "User Profile"}, "documents": {"title": "Documents", "subtitle": "Manage documents and files", "upload": "Upload Document", "download": "Download", "view": "View Document"}, "settings": {"title": "Settings", "subtitle": "System configuration", "language": "Language", "theme": "Theme", "notifications": "Notifications"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "username": "Username", "password": "Password", "rememberMe": "Remember Me", "forgotPassword": "Forgot Password?", "loginError": "Invalid username or password", "loginSuccess": "Login successful", "logoutSuccess": "Logout successful"}, "errors": {"generic": "An error occurred", "networkError": "Network error occurred", "unauthorized": "Unauthorized access", "forbidden": "Access forbidden", "notFound": "Resource not found", "serverError": "Server error occurred"}}