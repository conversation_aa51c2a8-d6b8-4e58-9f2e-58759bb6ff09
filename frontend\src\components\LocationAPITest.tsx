import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>, CardContent, Typography, Box } from '@mui/material';
import geographicalLocationService from '../services/geographicalLocationService';

const LocationAPITest: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testAPI = async () => {
    setLoading(true);
    setTestResults([]);

    try {
      // Test API Base URL
      addResult(`🔗 API Base URL: ${import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api'}`);

      // Test Countries
      addResult('Testing countries API...');
      const countries = await geographicalLocationService.getCountries();
      addResult(`✅ Countries: ${countries.length} found`);
      addResult(`   Raw response: ${JSON.stringify(countries)}`);
      if (countries.length > 0) {
        addResult(`   Sample: ${countries[0].name} (ID: ${countries[0].id})`);
        
        // Test Regions
        addResult('Testing regions API...');
        const regions = await geographicalLocationService.getRegions(countries[0].id);
        addResult(`✅ Regions: ${regions.length} found`);
        if (regions.length > 0) {
          addResult(`   Sample: ${regions[0].name} (ID: ${regions[0].id})`);
          
          // Test Cities
          addResult('Testing cities API...');
          const cities = await geographicalLocationService.getCities(regions[0].id);
          addResult(`✅ Cities: ${cities.length} found`);
          if (cities.length > 0) {
            addResult(`   Sample: ${cities[0].name} (ID: ${cities[0].id})`);
            
            // Test SubCities
            addResult('Testing subcities API...');
            const subcities = await geographicalLocationService.getSubCities(cities[0].id);
            addResult(`✅ SubCities: ${subcities.length} found`);
            if (subcities.length > 0) {
              addResult(`   Sample: ${subcities[0].name} (ID: ${subcities[0].id})`);
              
              // Test Kebeles
              addResult('Testing kebeles API...');
              const kebeles = await geographicalLocationService.getKebeles(subcities[0].id);
              addResult(`✅ Kebeles: ${kebeles.length} found`);
              if (kebeles.length > 0) {
                addResult(`   Sample: ${kebeles[0].name} (ID: ${kebeles[0].id})`);
              }
            }
          }
        }
      }
      
      addResult('🎉 All API tests completed successfully!');
    } catch (error: any) {
      addResult(`❌ Error: ${error.message}`);
      console.error('API Test Error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card sx={{ m: 2, p: 2 }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Location API Test
        </Typography>
        
        <Button 
          variant="contained" 
          onClick={testAPI} 
          disabled={loading}
          sx={{ mb: 2 }}
        >
          {loading ? 'Testing...' : 'Test Location APIs'}
        </Button>
        
        <Box sx={{ maxHeight: 400, overflow: 'auto', bgcolor: '#f5f5f5', p: 2, borderRadius: 1 }}>
          {testResults.map((result, index) => (
            <Typography 
              key={index} 
              variant="body2" 
              sx={{ 
                fontFamily: 'monospace', 
                mb: 0.5,
                color: result.includes('❌') ? 'error.main' : 
                       result.includes('✅') ? 'success.main' : 
                       result.includes('🎉') ? 'primary.main' : 'text.primary'
              }}
            >
              {result}
            </Typography>
          ))}
        </Box>
      </CardContent>
    </Card>
  );
};

export default LocationAPITest;
