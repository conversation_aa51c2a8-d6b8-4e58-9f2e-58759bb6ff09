import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Grid,
  <PERSON>,
  But<PERSON>,
  Alert,
  CircularProgress,
  <PERSON>ge,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  TrendingUp,
  Notifications,
  Assessment,
  Schedule,
  CheckCircle,
  Cancel,
  Refresh,
} from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import taxpayerService from '../../services/taxpayerService';

interface DashboardStats {
  pending_upgrades: number;
  unread_notifications: number;
  recent_analyses: number;
  level_distribution: Array<{
    recommended_level__code: string;
    recommended_level__name: string;
    count: number;
  }>;
}

interface LevelUpgradeDashboardProps {
  onViewPendingUpgrades: () => void;
  onViewNotifications: () => void;
  onCreateAnalysis: () => void;
}

const LevelUpgradeDashboard: React.FC<LevelUpgradeDashboardProps> = ({
  onViewPendingUpgrades,
  onViewNotifications,
  onCreateAnalysis,
}) => {
  const { showNotification } = useNotification();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const loadStats = async (showRefreshIndicator = false) => {
    try {
      if (showRefreshIndicator) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      
      const data = await taxpayerService.getLevelUpgradeStats();
      setStats(data);
    } catch (error: any) {
      console.error('Failed to load dashboard stats:', error);
      showNotification('Failed to load dashboard statistics', 'error');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadStats();
  }, []);

  const handleRefresh = () => {
    loadStats(true);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
        <CircularProgress />
      </Box>
    );
  }

  if (!stats) {
    return (
      <Alert severity="error">
        Failed to load dashboard statistics. Please try refreshing the page.
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5" component="h2" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Assessment color="primary" />
          Taxpayer Level Assessment Dashboard
        </Typography>
        <Tooltip title="Refresh Statistics">
          <IconButton onClick={handleRefresh} disabled={refreshing}>
            <Refresh sx={{ animation: refreshing ? 'spin 1s linear infinite' : 'none' }} />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={3} mb={4}>
        {/* Pending Upgrades */}
        <Grid item xs={12} sm={6} md={3}>
          <Card 
            sx={{ 
              cursor: 'pointer',
              transition: 'all 0.2s',
              '&:hover': { 
                transform: 'translateY(-2px)',
                boxShadow: 4 
              }
            }}
            onClick={onViewPendingUpgrades}
          >
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Pending Upgrades
                  </Typography>
                  <Typography variant="h4" component="div" color="warning.main">
                    {stats.pending_upgrades}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Awaiting Review
                  </Typography>
                </Box>
                <TrendingUp sx={{ fontSize: 40, color: 'warning.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Unread Notifications */}
        <Grid item xs={12} sm={6} md={3}>
          <Card 
            sx={{ 
              cursor: 'pointer',
              transition: 'all 0.2s',
              '&:hover': { 
                transform: 'translateY(-2px)',
                boxShadow: 4 
              }
            }}
            onClick={onViewNotifications}
          >
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Unread Notifications
                  </Typography>
                  <Typography variant="h4" component="div" color="error.main">
                    {stats.unread_notifications}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Need Attention
                  </Typography>
                </Box>
                <Badge badgeContent={stats.unread_notifications} color="error">
                  <Notifications sx={{ fontSize: 40, color: 'error.main' }} />
                </Badge>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Analyses */}
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Recent Analyses
                  </Typography>
                  <Typography variant="h4" component="div" color="info.main">
                    {stats.recent_analyses}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Last 30 Days
                  </Typography>
                </Box>
                <Schedule sx={{ fontSize: 40, color: 'info.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom variant="body2">
                Quick Actions
              </Typography>
              <Box display="flex" flexDirection="column" gap={1} mt={1}>
                <Button
                  variant="contained"
                  size="small"
                  startIcon={<Assessment />}
                  onClick={onCreateAnalysis}
                  fullWidth
                >
                  New Analysis
                </Button>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<CheckCircle />}
                  onClick={onViewPendingUpgrades}
                  fullWidth
                >
                  Review Upgrades
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Level Distribution */}
      {stats.level_distribution.length > 0 && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <TrendingUp color="primary" />
              Recommended Level Upgrades Distribution
            </Typography>
            <Box display="flex" flexWrap="wrap" gap={2} mt={2}>
              {stats.level_distribution.map((level, index) => (
                <Chip
                  key={index}
                  label={`Level ${level.recommended_level__code}: ${level.count} businesses`}
                  color="primary"
                  variant="outlined"
                  size="medium"
                />
              ))}
            </Box>
            {stats.pending_upgrades > 0 && (
              <Alert severity="info" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  <strong>{stats.pending_upgrades} businesses</strong> are eligible for level upgrades 
                  based on their annual income analysis. Click "Review Upgrades" to approve or reject these recommendations.
                </Typography>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      {/* No Pending Upgrades Message */}
      {stats.pending_upgrades === 0 && (
        <Alert severity="success" sx={{ mt: 2 }}>
          <Typography variant="body2">
            <strong>All caught up!</strong> There are no pending level upgrades at this time. 
            You can create new income analyses to assess taxpayer levels for the current year.
          </Typography>
        </Alert>
      )}
    </Box>
  );
};

export default LevelUpgradeDashboard;
