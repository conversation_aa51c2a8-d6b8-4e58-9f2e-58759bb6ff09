from django.urls import path
from . import views
from .analytics_views import TaxPayerAnalyticsView

app_name = 'taxpayers'

urlpatterns = [
    # Business Sector endpoints
    path('business-sectors/', views.BusinessSectorListCreateView.as_view(), name='business_sector_list_create'),
    path('business-sectors/<uuid:pk>/', views.BusinessSectorDetailView.as_view(), name='business_sector_detail'),
    path('business-sectors/simple/', views.BusinessSectorSimpleListView.as_view(), name='business_sector_simple'),
    
    # Business Sub-Sector endpoints
    path('business-sub-sectors/', views.BusinessSubSectorListCreateView.as_view(), name='business_sub_sector_list_create'),
    path('business-sub-sectors/<uuid:pk>/', views.BusinessSubSectorDetailView.as_view(), name='business_sub_sector_detail'),
    path('business-sub-sectors/simple/', views.BusinessSubSectorSimpleListView.as_view(), name='business_sub_sector_simple'),
    
    # Tax Payer Level endpoints
    path('tax-payer-levels/', views.TaxPayerLevelListCreateView.as_view(), name='tax_payer_level_list_create'),
    path('tax-payer-levels/<uuid:pk>/', views.TaxPayerLevelDetailView.as_view(), name='tax_payer_level_detail'),
    path('tax-payer-levels/simple/', views.TaxPayerLevelSimpleListView.as_view(), name='tax_payer_level_simple'),
    
    # Organization Business Type endpoints
    path('organization-business-types/', views.OrganizationBusinessTypeListCreateView.as_view(), name='organization_business_type_list_create'),
    path('organization-business-types/<uuid:pk>/', views.OrganizationBusinessTypeDetailView.as_view(), name='organization_business_type_detail'),
    path('organization-business-types/simple/', views.OrganizationBusinessTypeSimpleListView.as_view(), name='organization_business_type_simple'),
    
    # Individual Tax Payer endpoints
    path('individuals/', views.IndividualTaxPayerListCreateView.as_view(), name='individual_taxpayer_list_create'),
    path('individuals/<uuid:pk>/', views.IndividualTaxPayerDetailView.as_view(), name='individual_taxpayer_detail'),
    path('individuals/<uuid:pk>/close-business/', views.IndividualTaxPayerCloseBusinessView.as_view(), name='individual_taxpayer_close_business'),
    path('individuals/<uuid:pk>/reopen-business/', views.IndividualTaxPayerReopenBusinessView.as_view(), name='individual_taxpayer_reopen_business'),

    # Organization Tax Payer endpoints
    path('organizations/', views.OrganizationTaxPayerListCreateView.as_view(), name='organization_taxpayer_list_create'),
    path('organizations/<uuid:pk>/', views.OrganizationTaxPayerDetailView.as_view(), name='organization_taxpayer_detail'),
    path('organizations/<uuid:pk>/close-business/', views.OrganizationTaxPayerCloseBusinessView.as_view(), name='organization_taxpayer_close_business'),
    path('organizations/<uuid:pk>/reopen-business/', views.OrganizationTaxPayerReopenBusinessView.as_view(), name='organization_taxpayer_reopen_business'),
    
    # Statistics and search endpoints
    path('statistics/', views.taxpayer_statistics, name='taxpayer_statistics'),
    path('search/', views.search_taxpayers, name='search_taxpayers'),

    # Analytics endpoint
    path('analytics/', TaxPayerAnalyticsView.as_view(), name='taxpayer_analytics'),

    # Daily Income Analysis endpoints
    path('income-analyses/', views.DailyIncomeAnalysisListCreateView.as_view(), name='income_analysis_list_create'),
    path('income-analyses/<uuid:pk>/', views.DailyIncomeAnalysisDetailView.as_view(), name='income_analysis_detail'),
    path('income-analyses/<uuid:pk>/approve/', views.ApproveLevelUpgradeView.as_view(), name='approve_level_upgrade'),
    path('income-analyses/<uuid:pk>/reject/', views.RejectLevelUpgradeView.as_view(), name='reject_level_upgrade'),

    # Level Upgrade Notification endpoints
    path('upgrade-notifications/', views.LevelUpgradeNotificationListView.as_view(), name='upgrade_notification_list'),
    path('upgrade-notifications/<uuid:pk>/', views.LevelUpgradeNotificationDetailView.as_view(), name='upgrade_notification_detail'),
    path('upgrade-notifications/<uuid:pk>/read/', views.MarkNotificationReadView.as_view(), name='mark_notification_read'),
    path('upgrade-notifications/<uuid:pk>/dismiss/', views.DismissNotificationView.as_view(), name='dismiss_notification'),

    # Dashboard statistics
    path('level-upgrade-stats/', views.level_upgrade_dashboard_stats, name='level_upgrade_stats'),
]
