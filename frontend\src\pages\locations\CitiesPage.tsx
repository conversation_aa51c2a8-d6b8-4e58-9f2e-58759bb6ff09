import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  <PERSON><PERSON><PERSON>,
  Card,
  CardContent,
  Button,
  TextField,
  FormControlLabel,
  Switch,
  InputAdornment,
  Avatar,
  Divider,
  Stack,
  Fade,
  Alert,
  CircularProgress,
  IconButton,
  Breadcrumbs,
  Link,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TablePagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  LocationCity,
  ArrowBack,
  Home,
  Add,
  Edit,
  Delete,
  CheckCircle,
  Cancel,
  LocationOn,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useNotification } from '../../contexts/NotificationContext';
import locationHierarchyService from '../../services/locationHierarchyService';
import ConfirmationDialog from '../../components/common/ConfirmationDialog';

interface City {
  id: number;
  name: string;
  code: string;
  zone: number;
  zone_name?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface Zone {
  id: number;
  name: string;
  code: string;
  region_name?: string;
}

const CitiesPage: React.FC = () => {
  const navigate = useNavigate();
  const { showNotification } = useNotification();

  const [cities, setCities] = useState<City[]>([]);
  const [zones, setZones] = useState<Zone[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingCity, setEditingCity] = useState<City | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    zone: '',
    is_active: true,
  });
  const [formErrors, setFormErrors] = useState<any>({});
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [cityToDelete, setCityToDelete] = useState<City | null>(null);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    loadCities();
    loadZones();
  }, [page, rowsPerPage]);

  const loadCities = async () => {
    try {
      setLoading(true);
      const response = await locationHierarchyService.getCities({
        page: page + 1,
        page_size: rowsPerPage,
      });
      setCities(response.results);
      setTotalCount(response.count);
    } catch (error) {
      console.error('Error loading cities:', error);
      showNotification('Failed to load cities', 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadZones = async () => {
    try {
      const response = await locationHierarchyService.getZones({ page_size: 100 });
      setZones(response.results);
    } catch (error) {
      console.error('Error loading zones:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setSubmitting(true);
      setFormErrors({});

      if (editingCity) {
        await locationHierarchyService.updateCity(editingCity.id, formData);
        showNotification('City updated successfully', 'success');
      } else {
        await locationHierarchyService.createCity(formData);
        showNotification('City created successfully', 'success');
      }

      resetForm();
      loadCities();
    } catch (error: any) {
      console.error('Error submitting form:', error);
      if (error.response?.data) {
        setFormErrors(error.response.data);
      }
      showNotification('Failed to save city', 'error');
    } finally {
      setSubmitting(false);
    }
  };

  const handleEdit = (city: City) => {
    setEditingCity(city);
    setFormData({
      name: city.name,
      code: city.code,
      zone: city.zone.toString(),
      is_active: city.is_active,
    });
    setShowForm(true);
  };

  const handleDelete = (city: City) => {
    setCityToDelete(city);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!cityToDelete) return;

    try {
      setDeleting(true);
      await locationHierarchyService.deleteCity(cityToDelete.id);
      showNotification('City deleted successfully', 'success');
      loadCities();
      handleCloseDeleteDialog();
    } catch (error) {
      console.error('Error deleting city:', error);
      showNotification('Failed to delete city', 'error');
    } finally {
      setDeleting(false);
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setCityToDelete(null);
    setDeleting(false);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      code: '',
      zone: '',
      is_active: true,
    });
    setFormErrors({});
    setEditingCity(null);
    setShowForm(false);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Fade in timeout={800}>
        <Box sx={{ mb: 4 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs sx={{ mb: 2 }}>
            <Link
              component="button"
              variant="body1"
              onClick={() => navigate('/locations/hierarchy')}
              sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
            >
              <Home fontSize="small" />
              Location Hierarchy
            </Link>
            <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <LocationCity fontSize="small" />
              Cities
            </Typography>
          </Breadcrumbs>

          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'between', mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <IconButton onClick={() => navigate('/locations/hierarchy')} sx={{ mr: 1 }}>
                <ArrowBack />
              </IconButton>
              <Avatar sx={{ bgcolor: 'warning.main' }}>
                <LocationCity />
              </Avatar>
              <Box>
                <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
                  Cities Management
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Manage cities within zones
                </Typography>
              </Box>
            </Box>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setShowForm(true)}
              size="large"
              sx={{ ml: 'auto' }}
            >
              Add City
            </Button>
          </Box>
        </Box>
      </Fade>

      {/* Form Section */}
      {showForm && (
        <Fade in timeout={600}>
          <Card sx={{ mb: 4 }}>
            <CardContent sx={{ p: 4 }}>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                {editingCity ? 'Edit City' : 'Add New City'}
              </Typography>
              <Divider sx={{ mb: 3 }} />

              <form onSubmit={handleSubmit}>
                <Stack spacing={3}>
                  <TextField
                    label="City Name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    error={!!formErrors.name}
                    helperText={formErrors.name || 'Enter the full city name'}
                    fullWidth
                    required
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <LocationCity color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />

                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                    <TextField
                      label="City Code"
                      value={formData.code}
                      onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
                      error={!!formErrors.code}
                      helperText={formErrors.code || 'e.g., AA, DD, BH'}
                      required
                      inputProps={{ maxLength: 10 }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <LocationCity color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />

                    <FormControl fullWidth required error={!!formErrors.zone}>
                      <InputLabel>Zone</InputLabel>
                      <Select
                        value={formData.zone}
                        onChange={(e) => setFormData({ ...formData, zone: e.target.value })}
                        label="Zone"
                        startAdornment={
                          <InputAdornment position="start">
                            <LocationOn color="action" />
                          </InputAdornment>
                        }
                      >
                        {zones.map((zone) => (
                          <MenuItem key={zone.id} value={zone.id.toString()}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Chip label={zone.code} size="small" />
                              {zone.name}
                              {zone.region_name && (
                                <Typography variant="caption" color="text.secondary">
                                  ({zone.region_name})
                                </Typography>
                              )}
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                      {formErrors.zone && (
                        <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 2 }}>
                          {formErrors.zone}
                        </Typography>
                      )}
                    </FormControl>
                  </Box>

                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.is_active}
                        onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                        color="primary"
                      />
                    }
                    label="Active Status"
                  />

                  <Box sx={{ display: 'flex', gap: 2, pt: 2 }}>
                    <Button
                      type="submit"
                      variant="contained"
                      disabled={submitting}
                      startIcon={submitting ? <CircularProgress size={20} /> : <Add />}
                      size="large"
                    >
                      {submitting ? 'Saving...' : editingCity ? 'Update City' : 'Create City'}
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={resetForm}
                      size="large"
                    >
                      Cancel
                    </Button>
                  </Box>
                </Stack>
              </form>
            </CardContent>
          </Card>
        </Fade>
      )}

      {/* Cities Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
            Cities List
          </Typography>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : cities.length === 0 ? (
            <Alert severity="info">
              No cities found. Click "Add City" to create your first city.
            </Alert>
          ) : (
            <>
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>City</TableCell>
                      <TableCell>Code</TableCell>
                      <TableCell>Zone</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Created</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {cities.map((city) => (
                      <TableRow key={city.id} hover>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <Avatar sx={{ bgcolor: 'warning.main', width: 32, height: 32 }}>
                              <LocationCity fontSize="small" />
                            </Avatar>
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {city.name}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip label={city.code} size="small" color="warning" />
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <LocationOn fontSize="small" color="action" />
                            {city.zone_name || `Zone ${city.zone}`}
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={city.is_active ? 'Active' : 'Inactive'}
                            color={city.is_active ? 'success' : 'error'}
                            size="small"
                            icon={city.is_active ? <CheckCircle /> : <Cancel />}
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {new Date(city.created_at).toLocaleDateString()}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                            <IconButton
                              size="small"
                              onClick={() => handleEdit(city)}
                              color="primary"
                            >
                              <Edit fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleDelete(city)}
                              color="error"
                            >
                              <Delete fontSize="small" />
                            </IconButton>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              <TablePagination
                component="div"
                count={totalCount}
                page={page}
                onPageChange={handleChangePage}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                rowsPerPageOptions={[5, 10, 25, 50]}
              />
            </>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        title="Delete City"
        itemName={cityToDelete?.name}
        itemType="City"
        message={`Are you sure you want to delete "${cityToDelete?.name}"? This will also delete all subcities and kebeles within this city. This action cannot be undone.`}
        confirmText="Delete City"
        severity="error"
        loading={deleting}
      />
    </Container>
  );
};

export default CitiesPage;
