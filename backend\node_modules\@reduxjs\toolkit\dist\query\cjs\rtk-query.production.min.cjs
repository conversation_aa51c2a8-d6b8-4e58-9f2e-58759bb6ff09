"use strict";var Ce=Object.defineProperty;var Bt=Object.getOwnPropertyDescriptor;var Mt=Object.getOwnPropertyNames;var wt=Object.prototype.hasOwnProperty;var Ct=(e,t)=>{for(var p in t)Ce(e,p,{get:t[p],enumerable:!0})},Ft=(e,t,p,m)=>{if(t&&typeof t=="object"||typeof t=="function")for(let y of Mt(t))!wt.call(e,y)&&y!==p&&Ce(e,y,{get:()=>t[y],enumerable:!(m=Bt(t,y))||m.enumerable});return e};var vt=e=>Ft(Ce({},"__esModule",{value:!0}),e);var Wt={};Ct(Wt,{NamedSchemaError:()=>oe,QueryStatus:()=>De,_NEVER:()=>ft,buildCreateApi:()=>Me,copyWithStructuralSharing:()=>me,coreModule:()=>we,coreModuleName:()=>Se,createApi:()=>It,defaultSerializeQueryArgs:()=>Re,fakeBaseQuery:()=>mt,fetchBaseQuery:()=>Je,retry:()=>Ye,setupListeners:()=>Xe,skipToken:()=>Ae});module.exports=vt(Wt);var De=(y=>(y.uninitialized="uninitialized",y.pending="pending",y.fulfilled="fulfilled",y.rejected="rejected",y))(De||{});function Fe(e){return{status:e,isUninitialized:e==="uninitialized",isLoading:e==="pending",isSuccess:e==="fulfilled",isError:e==="rejected"}}var i=require("@reduxjs/toolkit");var Le=i.isPlainObject;function me(e,t){if(e===t||!(Le(e)&&Le(t)||Array.isArray(e)&&Array.isArray(t)))return t;let p=Object.keys(t),m=Object.keys(e),y=p.length===m.length,D=Array.isArray(t)?[]:{};for(let h of p)D[h]=me(e[h],t[h]),y&&(y=e[h]===D[h]);return y?e:D}function J(e){let t=0;for(let p in e)t++;return t}var ve=e=>[].concat(...e);function je(e){return new RegExp("(^|:)//").test(e)}function He(){return typeof document>"u"?!0:document.visibilityState!=="hidden"}function re(e){return e!=null}function _e(){return typeof navigator>"u"||navigator.onLine===void 0?!0:navigator.onLine}var Ot=e=>e.replace(/\/$/,""),Nt=e=>e.replace(/^\//,"");function Ve(e,t){if(!e)return t;if(!t)return e;if(je(t))return t;let p=e.endsWith("/")||!t.startsWith("?")?"/":"";return e=Ot(e),t=Nt(t),`${e}${p}${t}`}function ze(e,t,p){return e.has(t)?e.get(t):e.set(t,p).get(t)}var We=(...e)=>fetch(...e),qt=e=>e.status>=200&&e.status<=299,Kt=e=>/ion\/(vnd\.api\+)?json/.test(e.get("content-type")||"");function $e(e){if(!(0,i.isPlainObject)(e))return e;let t={...e};for(let[p,m]of Object.entries(t))m===void 0&&delete t[p];return t}function Je({baseUrl:e,prepareHeaders:t=A=>A,fetchFn:p=We,paramsSerializer:m,isJsonContentType:y=Kt,jsonContentType:D="application/json",jsonReplacer:h,timeout:B,responseHandler:C,validateStatus:R,...I}={}){return typeof fetch>"u"&&p===We&&console.warn("Warning: `fetch` is not available. Please supply a custom `fetchFn` property to use `fetchBaseQuery` on SSR environments."),async(S,o,g)=>{let{getState:M,extra:c,endpoint:s,forced:x,type:l}=o,d,{url:Q,headers:b=new Headers(I.headers),params:E=void 0,responseHandler:k=C??"json",validateStatus:T=R??qt,timeout:u=B,...n}=typeof S=="string"?{url:S}:S,r,a=o.signal;u&&(r=new AbortController,o.signal.addEventListener("abort",r.abort),a=r.signal);let f={...I,signal:a,...n};b=new Headers($e(b)),f.headers=await t(b,{getState:M,arg:S,extra:c,endpoint:s,forced:x,type:l,extraOptions:g})||b;let P=q=>typeof q=="object"&&((0,i.isPlainObject)(q)||Array.isArray(q)||typeof q.toJSON=="function");if(!f.headers.has("content-type")&&P(f.body)&&f.headers.set("content-type",D),P(f.body)&&y(f.headers)&&(f.body=JSON.stringify(f.body,h)),E){let q=~Q.indexOf("?")?"&":"?",v=m?m(E):new URLSearchParams($e(E));Q+=q+v}Q=Ve(e,Q);let O=new Request(Q,f);d={request:new Request(Q,f)};let F,w=!1,U=r&&setTimeout(()=>{w=!0,r.abort()},u);try{F=await p(O)}catch(q){return{error:{status:w?"TIMEOUT_ERROR":"FETCH_ERROR",error:String(q)},meta:d}}finally{U&&clearTimeout(U),r?.signal.removeEventListener("abort",r.abort)}let H=F.clone();d.response=H;let W,j="";try{let q;if(await Promise.all([A(F,k).then(v=>W=v,v=>q=v),H.text().then(v=>j=v,()=>{})]),q)throw q}catch(q){return{error:{status:"PARSING_ERROR",originalStatus:F.status,data:j,error:String(q)},meta:d}}return T(F,W)?{data:W,meta:d}:{error:{status:F.status,data:W},meta:d}};async function A(S,o){if(typeof o=="function")return o(S);if(o==="content-type"&&(o=y(S.headers)?"json":"text"),o==="json"){let g=await S.text();return g.length?JSON.parse(g):null}return S.text()}}var G=class{constructor(t,p=void 0){this.value=t;this.meta=p}};async function Ut(e=0,t=5){let p=Math.min(e,t),m=~~((Math.random()+.4)*(300<<p));await new Promise(y=>setTimeout(D=>y(D),m))}function Lt(e,t){throw Object.assign(new G({error:e,meta:t}),{throwImmediately:!0})}var Ge={},jt=(e,t)=>async(p,m,y)=>{let D=[5,(t||Ge).maxRetries,(y||Ge).maxRetries].filter(I=>I!==void 0),[h]=D.slice(-1),C={maxRetries:h,backoff:Ut,retryCondition:(I,A,{attempt:S})=>S<=h,...t,...y},R=0;for(;;)try{let I=await e(p,m,y);if(I.error)throw new G(I);return I}catch(I){if(R++,I.throwImmediately){if(I instanceof G)return I.value;throw I}if(I instanceof G&&!C.retryCondition(I.value.error,p,{attempt:R,baseQueryApi:m,extraOptions:y}))return I.value;await C.backoff(R,C.maxRetries)}},Ye=Object.assign(jt,{fail:Lt});var ee=(0,i.createAction)("__rtkq/focused"),pe=(0,i.createAction)("__rtkq/unfocused"),te=(0,i.createAction)("__rtkq/online"),ce=(0,i.createAction)("__rtkq/offline"),Oe=!1;function Xe(e,t){function p(){let m=()=>e(ee()),y=()=>e(pe()),D=()=>e(te()),h=()=>e(ce()),B=()=>{window.document.visibilityState==="visible"?m():y()};return Oe||typeof window<"u"&&window.addEventListener&&(window.addEventListener("visibilitychange",B,!1),window.addEventListener("focus",m,!1),window.addEventListener("online",D,!1),window.addEventListener("offline",h,!1),Oe=!0),()=>{window.removeEventListener("focus",m),window.removeEventListener("visibilitychange",B),window.removeEventListener("online",D),window.removeEventListener("offline",h),Oe=!1}}return t?t(e,{onFocus:ee,onFocusLost:pe,onOffline:ce,onOnline:te}):p()}function ie(e){return e.type==="query"}function Ze(e){return e.type==="mutation"}function ae(e){return e.type==="infinitequery"}function le(e){return ie(e)||ae(e)}function ge(e,t,p,m,y,D){return Ht(e)?e(t,p,m,y).filter(re).map(be).map(D):Array.isArray(e)?e.map(be).map(D):[]}function Ht(e){return typeof e=="function"}function be(e){return typeof e=="string"?{type:e}:e}var Pe=require("immer");var An=require("@reduxjs/toolkit");function et(e,t){return e.catch(t)}var fe=Symbol("forceQueryFn"),Qe=e=>typeof e[fe]=="function";function tt({serializeQueryArgs:e,queryThunk:t,infiniteQueryThunk:p,mutationThunk:m,api:y,context:D}){let h=new Map,B=new Map,{unsubscribeQueryResult:C,removeMutationResult:R,updateSubscriptionOptions:I}=y.internalActions;return{buildInitiateQuery:s,buildInitiateInfiniteQuery:x,buildInitiateMutation:l,getRunningQueryThunk:A,getRunningMutationThunk:S,getRunningQueriesThunk:o,getRunningMutationsThunk:g};function A(d,Q){return b=>{let E=D.endpointDefinitions[d],k=e({queryArgs:Q,endpointDefinition:E,endpointName:d});return h.get(b)?.[k]}}function S(d,Q){return b=>B.get(b)?.[Q]}function o(){return d=>Object.values(h.get(d)||{}).filter(re)}function g(){return d=>Object.values(B.get(d)||{}).filter(re)}function M(d){}function c(d,Q){let b=(E,{subscribe:k=!0,forceRefetch:T,subscriptionOptions:u,[fe]:n,...r}={})=>(a,f)=>{let P=e({queryArgs:E,endpointDefinition:Q,endpointName:d}),O,N={...r,type:"query",subscribe:k,forceRefetch:T,subscriptionOptions:u,endpointName:d,originalArgs:E,queryCacheKey:P,[fe]:n};if(ie(Q))O=t(N);else{let{direction:_,initialPageParam:K}=r;O=p({...N,direction:_,initialPageParam:K})}let F=y.endpoints[d].select(E),w=a(O),U=F(f());let{requestId:H,abort:W}=w,j=U.requestId!==H,q=h.get(a)?.[P],v=()=>F(f()),V=Object.assign(n?w.then(v):j&&!q?Promise.resolve(U):Promise.all([q,w]).then(v),{arg:E,requestId:H,subscriptionOptions:u,queryCacheKey:P,abort:W,async unwrap(){let _=await V;if(_.isError)throw _.error;return _.data},refetch:()=>a(b(E,{subscribe:!1,forceRefetch:!0})),unsubscribe(){k&&a(C({queryCacheKey:P,requestId:H}))},updateSubscriptionOptions(_){V.subscriptionOptions=_,a(I({endpointName:d,requestId:H,queryCacheKey:P,options:_}))}});if(!q&&!j&&!n){let _=ze(h,a,{});_[P]=V,V.then(()=>{delete _[P],J(_)||h.delete(a)})}return V};return b}function s(d,Q){return c(d,Q)}function x(d,Q){return c(d,Q)}function l(d){return(Q,{track:b=!0,fixedCacheKey:E}={})=>(k,T)=>{let u=m({type:"mutation",endpointName:d,originalArgs:Q,track:b,fixedCacheKey:E}),n=k(u);let{requestId:r,abort:a,unwrap:f}=n,P=et(n.unwrap().then(w=>({data:w})),w=>({error:w})),O=()=>{k(R({requestId:r,fixedCacheKey:E}))},N=Object.assign(P,{arg:n.arg,requestId:r,abort:a,unwrap:f,reset:O}),F=B.get(k)||{};return B.set(k,F),F[r]=N,N.then(()=>{delete F[r],J(F)||B.delete(k)}),E&&(F[E]=N,N.then(()=>{F[E]===N&&(delete F[E],J(F)||B.delete(k))})),N}}}var nt=require("@standard-schema/utils"),oe=class extends nt.SchemaError{constructor(p,m,y,D){super(p);this.value=m;this.schemaName=y;this._bqMeta=D}};async function ne(e,t,p,m){let y=await e["~standard"].validate(t);if(y.issues)throw new oe(y.issues,t,p,m);return y.value}function _t(e){return e}var Te=(e={})=>({...e,[i.SHOULD_AUTOBATCH]:!0});function rt({reducerPath:e,baseQuery:t,context:{endpointDefinitions:p},serializeQueryArgs:m,api:y,assertTagType:D,selectors:h,onSchemaFailure:B,catchSchemaFailure:C,skipSchemaValidation:R}){let I=(n,r,a,f)=>(P,O)=>{let N=p[n],F=m({queryArgs:r,endpointDefinition:N,endpointName:n});if(P(y.internalActions.queryResultPatched({queryCacheKey:F,patches:a})),!f)return;let w=y.endpoints[n].select(r)(O()),U=ge(N.providesTags,w.data,void 0,r,{},D);P(y.internalActions.updateProvidedBy([{queryCacheKey:F,providedTags:U}]))};function A(n,r,a=0){let f=[r,...n];return a&&f.length>a?f.slice(0,-1):f}function S(n,r,a=0){let f=[...n,r];return a&&f.length>a?f.slice(1):f}let o=(n,r,a,f=!0)=>(P,O)=>{let F=y.endpoints[n].select(r)(O()),w={patches:[],inversePatches:[],undo:()=>P(y.util.patchQueryData(n,r,w.inversePatches,f))};if(F.status==="uninitialized")return w;let U;if("data"in F)if((0,Pe.isDraftable)(F.data)){let[H,W,j]=(0,Pe.produceWithPatches)(F.data,a);w.patches.push(...W),w.inversePatches.push(...j),U=H}else U=a(F.data),w.patches.push({op:"replace",path:[],value:U}),w.inversePatches.push({op:"replace",path:[],value:F.data});return w.patches.length===0||P(y.util.patchQueryData(n,r,w.patches,f)),w},g=(n,r,a)=>f=>f(y.endpoints[n].initiate(r,{subscribe:!1,forceRefetch:!0,[fe]:()=>({data:a})})),M=(n,r)=>n.query&&n[r]?n[r]:_t,c=async(n,{signal:r,abort:a,rejectWithValue:f,fulfillWithValue:P,dispatch:O,getState:N,extra:F})=>{let w=p[n.endpointName],{metaSchema:U,skipSchemaValidation:H=R}=w;try{let W=M(w,"transformResponse"),j={signal:r,abort:a,dispatch:O,getState:N,extra:F,endpoint:n.endpointName,type:n.type,forced:n.type==="query"?s(n,N()):void 0,queryCacheKey:n.type==="query"?n.queryCacheKey:void 0},q=n.type==="query"?n[fe]:void 0,v,V=async(K,L,z,ue)=>{if(L==null&&K.pages.length)return Promise.resolve({data:K});let X={queryArg:n.originalArgs,pageParam:L},ye=await _(X),$=ue?A:S;return{data:{pages:$(K.pages,ye.data,z),pageParams:$(K.pageParams,L,z)},meta:ye.meta}};async function _(K){let L,{extraOptions:z,argSchema:ue,rawResponseSchema:X,responseSchema:ye}=w;if(ue&&!H&&(K=await ne(ue,K,"argSchema",{})),q?L=q():w.query?L=await t(w.query(K),j,z):L=await w.queryFn(K,j,z,de=>t(de,j,z)),typeof process<"u",L.error)throw new G(L.error,L.meta);let{data:$}=L;X&&!H&&($=await ne(X,L.data,"rawResponseSchema",L.meta));let Z=await W($,L.meta,K);return ye&&!H&&(Z=await ne(ye,Z,"responseSchema",L.meta)),{...L,data:Z}}if(n.type==="query"&&"infiniteQueryOptions"in w){let{infiniteQueryOptions:K}=w,{maxPages:L=1/0}=K,z,ue={pages:[],pageParams:[]},X=h.selectQueryEntry(N(),n.queryCacheKey)?.data,$=s(n,N())&&!n.direction||!X?ue:X;if("direction"in n&&n.direction&&$.pages.length){let Z=n.direction==="backward",xe=(Z?Ne:Ee)(K,$,n.originalArgs);z=await V($,xe,L,Z)}else{let{initialPageParam:Z=K.initialPageParam}=n,de=X?.pageParams??[],xe=de[0]??Z,Ke=de.length;z=await V($,xe,L),q&&(z={data:z.data.pages[0]});for(let Ue=1;Ue<Ke;Ue++){let kt=Ee(K,z.data,n.originalArgs);z=await V(z.data,kt,L)}}v=z}else v=await _(n.originalArgs);return U&&!H&&v.meta&&(v.meta=await ne(U,v.meta,"metaSchema",v.meta)),P(v.data,Te({fulfilledTimeStamp:Date.now(),baseQueryMeta:v.meta}))}catch(W){let j=W;if(j instanceof G){let q=M(w,"transformErrorResponse"),{rawErrorResponseSchema:v,errorResponseSchema:V}=w,{value:_,meta:K}=j;try{v&&!H&&(_=await ne(v,_,"rawErrorResponseSchema",K)),U&&!H&&(K=await ne(U,K,"metaSchema",K));let L=await q(_,K,n.originalArgs);return V&&!H&&(L=await ne(V,L,"errorResponseSchema",K)),f(L,Te({baseQueryMeta:K}))}catch(L){j=L}}try{if(j instanceof oe){let q={endpoint:n.endpointName,arg:n.originalArgs,type:n.type,queryCacheKey:n.type==="query"?n.queryCacheKey:void 0};w.onSchemaFailure?.(j,q),B?.(j,q);let{catchSchemaFailure:v=C}=w;if(v)return f(v(j,q),Te({baseQueryMeta:j._bqMeta}))}}catch(q){j=q}throw typeof process<"u",console.error(j),j}};function s(n,r){let a=h.selectQueryEntry(r,n.queryCacheKey),f=h.selectConfig(r).refetchOnMountOrArgChange,P=a?.fulfilledTimeStamp,O=n.forceRefetch??(n.subscribe&&f);return O?O===!0||(Number(new Date)-Number(P))/1e3>=O:!1}let x=()=>(0,i.createAsyncThunk)(`${e}/executeQuery`,c,{getPendingMeta({arg:r}){let a=p[r.endpointName];return Te({startedTimeStamp:Date.now(),...ae(a)?{direction:r.direction}:{}})},condition(r,{getState:a}){let f=a(),P=h.selectQueryEntry(f,r.queryCacheKey),O=P?.fulfilledTimeStamp,N=r.originalArgs,F=P?.originalArgs,w=p[r.endpointName],U=r.direction;return Qe(r)?!0:P?.status==="pending"?!1:s(r,f)||ie(w)&&w?.forceRefetch?.({currentArg:N,previousArg:F,endpointState:P,state:f})?!0:!(O&&!U)},dispatchConditionRejection:!0}),l=x(),d=x(),Q=(0,i.createAsyncThunk)(`${e}/executeMutation`,c,{getPendingMeta(){return Te({startedTimeStamp:Date.now()})}}),b=n=>"force"in n,E=n=>"ifOlderThan"in n,k=(n,r,a)=>(f,P)=>{let O=b(a)&&a.force,N=E(a)&&a.ifOlderThan,F=(U=!0)=>{let H={forceRefetch:U,isPrefetch:!0};return y.endpoints[n].initiate(r,H)},w=y.endpoints[n].select(r)(P());if(O)f(F());else if(N){let U=w?.fulfilledTimeStamp;if(!U){f(F());return}(Number(new Date)-Number(new Date(U)))/1e3>=N&&f(F())}else f(F(!1))};function T(n){return r=>r?.meta?.arg?.endpointName===n}function u(n,r){return{matchPending:(0,i.isAllOf)((0,i.isPending)(n),T(r)),matchFulfilled:(0,i.isAllOf)((0,i.isFulfilled)(n),T(r)),matchRejected:(0,i.isAllOf)((0,i.isRejected)(n),T(r))}}return{queryThunk:l,mutationThunk:Q,infiniteQueryThunk:d,prefetch:k,updateQueryData:o,upsertQueryData:g,patchQueryData:I,buildMatchThunkActions:u}}function Ee(e,{pages:t,pageParams:p},m){let y=t.length-1;return e.getNextPageParam(t[y],t,p[y],p,m)}function Ne(e,{pages:t,pageParams:p},m){return e.getPreviousPageParam?.(t[0],t,p[0],p,m)}function Ie(e,t,p,m){return ge(p[e.meta.arg.endpointName][t],(0,i.isFulfilled)(e)?e.payload:void 0,(0,i.isRejectedWithValue)(e)?e.payload:void 0,e.meta.arg.originalArgs,"baseQueryMeta"in e.meta?e.meta.baseQueryMeta:void 0,m)}var at=require("immer"),he=require("immer");function ke(e,t,p){let m=e[t];m&&p(m)}function se(e){return("arg"in e?e.arg.fixedCacheKey:e.fixedCacheKey)??e.requestId}function it(e,t,p){let m=e[se(t)];m&&p(m)}var Be={};function ot({reducerPath:e,queryThunk:t,mutationThunk:p,serializeQueryArgs:m,context:{endpointDefinitions:y,apiUid:D,extractRehydrationInfo:h,hasRehydrationInfo:B},assertTagType:C,config:R}){let I=(0,i.createAction)(`${e}/resetApiState`);function A(T,u,n,r){T[u.queryCacheKey]??={status:"uninitialized",endpointName:u.endpointName},ke(T,u.queryCacheKey,a=>{a.status="pending",a.requestId=n&&a.requestId?a.requestId:r.requestId,u.originalArgs!==void 0&&(a.originalArgs=u.originalArgs),a.startedTimeStamp=r.startedTimeStamp;let f=y[r.arg.endpointName];ae(f)&&"direction"in u&&(a.direction=u.direction)})}function S(T,u,n,r){ke(T,u.arg.queryCacheKey,a=>{if(a.requestId!==u.requestId&&!r)return;let{merge:f}=y[u.arg.endpointName];if(a.status="fulfilled",f)if(a.data!==void 0){let{fulfilledTimeStamp:P,arg:O,baseQueryMeta:N,requestId:F}=u,w=(0,i.createNextState)(a.data,U=>f(U,n,{arg:O.originalArgs,baseQueryMeta:N,fulfilledTimeStamp:P,requestId:F}));a.data=w}else a.data=n;else a.data=y[u.arg.endpointName].structuralSharing??!0?me((0,at.isDraft)(a.data)?(0,he.original)(a.data):a.data,n):n;delete a.error,a.fulfilledTimeStamp=u.fulfilledTimeStamp})}let o=(0,i.createSlice)({name:`${e}/queries`,initialState:Be,reducers:{removeQueryResult:{reducer(T,{payload:{queryCacheKey:u}}){delete T[u]},prepare:(0,i.prepareAutoBatched)()},cacheEntriesUpserted:{reducer(T,u){for(let n of u.payload){let{queryDescription:r,value:a}=n;A(T,r,!0,{arg:r,requestId:u.meta.requestId,startedTimeStamp:u.meta.timestamp}),S(T,{arg:r,requestId:u.meta.requestId,fulfilledTimeStamp:u.meta.timestamp,baseQueryMeta:{}},a,!0)}},prepare:T=>({payload:T.map(r=>{let{endpointName:a,arg:f,value:P}=r,O=y[a];return{queryDescription:{type:"query",endpointName:a,originalArgs:r.arg,queryCacheKey:m({queryArgs:f,endpointDefinition:O,endpointName:a})},value:P}}),meta:{[i.SHOULD_AUTOBATCH]:!0,requestId:(0,i.nanoid)(),timestamp:Date.now()}})},queryResultPatched:{reducer(T,{payload:{queryCacheKey:u,patches:n}}){ke(T,u,r=>{r.data=(0,he.applyPatches)(r.data,n.concat())})},prepare:(0,i.prepareAutoBatched)()}},extraReducers(T){T.addCase(t.pending,(u,{meta:n,meta:{arg:r}})=>{let a=Qe(r);A(u,r,a,n)}).addCase(t.fulfilled,(u,{meta:n,payload:r})=>{let a=Qe(n.arg);S(u,n,r,a)}).addCase(t.rejected,(u,{meta:{condition:n,arg:r,requestId:a},error:f,payload:P})=>{ke(u,r.queryCacheKey,O=>{if(!n){if(O.requestId!==a)return;O.status="rejected",O.error=P??f}})}).addMatcher(B,(u,n)=>{let{queries:r}=h(n);for(let[a,f]of Object.entries(r))(f?.status==="fulfilled"||f?.status==="rejected")&&(u[a]=f)})}}),g=(0,i.createSlice)({name:`${e}/mutations`,initialState:Be,reducers:{removeMutationResult:{reducer(T,{payload:u}){let n=se(u);n in T&&delete T[n]},prepare:(0,i.prepareAutoBatched)()}},extraReducers(T){T.addCase(p.pending,(u,{meta:n,meta:{requestId:r,arg:a,startedTimeStamp:f}})=>{a.track&&(u[se(n)]={requestId:r,status:"pending",endpointName:a.endpointName,startedTimeStamp:f})}).addCase(p.fulfilled,(u,{payload:n,meta:r})=>{r.arg.track&&it(u,r,a=>{a.requestId===r.requestId&&(a.status="fulfilled",a.data=n,a.fulfilledTimeStamp=r.fulfilledTimeStamp)})}).addCase(p.rejected,(u,{payload:n,error:r,meta:a})=>{a.arg.track&&it(u,a,f=>{f.requestId===a.requestId&&(f.status="rejected",f.error=n??r)})}).addMatcher(B,(u,n)=>{let{mutations:r}=h(n);for(let[a,f]of Object.entries(r))(f?.status==="fulfilled"||f?.status==="rejected")&&a!==f?.requestId&&(u[a]=f)})}}),M={tags:{},keys:{}},c=(0,i.createSlice)({name:`${e}/invalidation`,initialState:M,reducers:{updateProvidedBy:{reducer(T,u){for(let{queryCacheKey:n,providedTags:r}of u.payload){s(T,n);for(let{type:a,id:f}of r){let P=(T.tags[a]??={})[f||"__internal_without_id"]??=[];P.includes(n)||P.push(n)}T.keys[n]=r}},prepare:(0,i.prepareAutoBatched)()}},extraReducers(T){T.addCase(o.actions.removeQueryResult,(u,{payload:{queryCacheKey:n}})=>{s(u,n)}).addMatcher(B,(u,n)=>{let{provided:r}=h(n);for(let[a,f]of Object.entries(r))for(let[P,O]of Object.entries(f)){let N=(u.tags[a]??={})[P||"__internal_without_id"]??=[];for(let F of O)N.includes(F)||N.push(F)}}).addMatcher((0,i.isAnyOf)((0,i.isFulfilled)(t),(0,i.isRejectedWithValue)(t)),(u,n)=>{x(u,[n])}).addMatcher(o.actions.cacheEntriesUpserted.match,(u,n)=>{let r=n.payload.map(({queryDescription:a,value:f})=>({type:"UNKNOWN",payload:f,meta:{requestStatus:"fulfilled",requestId:"UNKNOWN",arg:a}}));x(u,r)})}});function s(T,u){let n=T.keys[u]??[];for(let r of n){let a=r.type,f=r.id??"__internal_without_id",P=T.tags[a]?.[f];P&&(T.tags[a][f]=P.filter(O=>O!==u))}delete T.keys[u]}function x(T,u){let n=u.map(r=>{let a=Ie(r,"providesTags",y,C),{queryCacheKey:f}=r.meta.arg;return{queryCacheKey:f,providedTags:a}});c.caseReducers.updateProvidedBy(T,c.actions.updateProvidedBy(n))}let l=(0,i.createSlice)({name:`${e}/subscriptions`,initialState:Be,reducers:{updateSubscriptionOptions(T,u){},unsubscribeQueryResult(T,u){},internal_getRTKQSubscriptions(){}}}),d=(0,i.createSlice)({name:`${e}/internalSubscriptions`,initialState:Be,reducers:{subscriptionsUpdated:{reducer(T,u){return(0,he.applyPatches)(T,u.payload)},prepare:(0,i.prepareAutoBatched)()}}}),Q=(0,i.createSlice)({name:`${e}/config`,initialState:{online:_e(),focused:He(),middlewareRegistered:!1,...R},reducers:{middlewareRegistered(T,{payload:u}){T.middlewareRegistered=T.middlewareRegistered==="conflict"||D!==u?"conflict":!0}},extraReducers:T=>{T.addCase(te,u=>{u.online=!0}).addCase(ce,u=>{u.online=!1}).addCase(ee,u=>{u.focused=!0}).addCase(pe,u=>{u.focused=!1}).addMatcher(B,u=>({...u}))}}),b=(0,i.combineReducers)({queries:o.reducer,mutations:g.reducer,provided:c.reducer,subscriptions:d.reducer,config:Q.reducer}),E=(T,u)=>b(I.match(u)?void 0:T,u),k={...Q.actions,...o.actions,...l.actions,...d.actions,...g.actions,...c.actions,resetApiState:I};return{reducer:E,actions:k}}var Ae=Symbol.for("RTKQ/skipToken"),yt={status:"uninitialized"},st=(0,i.createNextState)(yt,()=>{}),ut=(0,i.createNextState)(yt,()=>{});function dt({serializeQueryArgs:e,reducerPath:t,createSelector:p}){let m=l=>st,y=l=>ut;return{buildQuerySelector:S,buildInfiniteQuerySelector:o,buildMutationSelector:g,selectInvalidatedBy:M,selectCachedArgsForQuery:c,selectApiState:h,selectQueries:B,selectMutations:R,selectQueryEntry:C,selectConfig:I};function D(l){return{...l,...Fe(l.status)}}function h(l){return l[t]}function B(l){return h(l)?.queries}function C(l,d){return B(l)?.[d]}function R(l){return h(l)?.mutations}function I(l){return h(l)?.config}function A(l,d,Q){return b=>{if(b===Ae)return p(m,Q);let E=e({queryArgs:b,endpointDefinition:d,endpointName:l});return p(T=>C(T,E)??st,Q)}}function S(l,d){return A(l,d,D)}function o(l,d){let{infiniteQueryOptions:Q}=d;function b(E){let k={...E,...Fe(E.status)},{isLoading:T,isError:u,direction:n}=k,r=n==="forward",a=n==="backward";return{...k,hasNextPage:s(Q,k.data,k.originalArgs),hasPreviousPage:x(Q,k.data,k.originalArgs),isFetchingNextPage:T&&r,isFetchingPreviousPage:T&&a,isFetchNextPageError:u&&r,isFetchPreviousPageError:u&&a}}return A(l,d,b)}function g(){return l=>{let d;return typeof l=="object"?d=se(l)??Ae:d=l,p(d===Ae?y:E=>h(E)?.mutations?.[d]??ut,D)}}function M(l,d){let Q=l[t],b=new Set;for(let E of d.filter(re).map(be)){let k=Q.provided.tags[E.type];if(!k)continue;let T=(E.id!==void 0?k[E.id]:ve(Object.values(k)))??[];for(let u of T)b.add(u)}return ve(Array.from(b.values()).map(E=>{let k=Q.queries[E];return k?[{queryCacheKey:E,endpointName:k.endpointName,originalArgs:k.originalArgs}]:[]}))}function c(l,d){return Object.values(B(l)).filter(Q=>Q?.endpointName===d&&Q.status!=="uninitialized").map(Q=>Q.originalArgs)}function s(l,d,Q){return d?Ee(l,d,Q)!=null:!1}function x(l,d,Q){return!d||!l.getPreviousPageParam?!1:Ne(l,d,Q)!=null}}var ct=require("@reduxjs/toolkit");var pt=WeakMap?new WeakMap:void 0,Re=({endpointName:e,queryArgs:t})=>{let p="",m=pt?.get(t);if(typeof m=="string")p=m;else{let y=JSON.stringify(t,(D,h)=>(h=typeof h=="bigint"?{$bigint:h.toString()}:h,h=(0,i.isPlainObject)(h)?Object.keys(h).sort().reduce((B,C)=>(B[C]=h[C],B),{}):h,h));(0,i.isPlainObject)(t)&&pt?.set(t,y),p=y}return`${e}(${p})`};var qe=require("reselect");function Me(...e){return function(p){let m=(0,qe.weakMapMemoize)(R=>p.extractRehydrationInfo?.(R,{reducerPath:p.reducerPath??"api"})),y={reducerPath:"api",keepUnusedDataFor:60,refetchOnMountOrArgChange:!1,refetchOnFocus:!1,refetchOnReconnect:!1,invalidationBehavior:"delayed",...p,extractRehydrationInfo:m,serializeQueryArgs(R){let I=Re;if("serializeQueryArgs"in R.endpointDefinition){let A=R.endpointDefinition.serializeQueryArgs;I=S=>{let o=A(S);return typeof o=="string"?o:Re({...S,queryArgs:o})}}else p.serializeQueryArgs&&(I=p.serializeQueryArgs);return I(R)},tagTypes:[...p.tagTypes||[]]},D={endpointDefinitions:{},batch(R){R()},apiUid:(0,i.nanoid)(),extractRehydrationInfo:m,hasRehydrationInfo:(0,qe.weakMapMemoize)(R=>m(R)!=null)},h={injectEndpoints:C,enhanceEndpoints({addTagTypes:R,endpoints:I}){if(R)for(let A of R)y.tagTypes.includes(A)||y.tagTypes.push(A);if(I)for(let[A,S]of Object.entries(I))typeof S=="function"?S(D.endpointDefinitions[A]):Object.assign(D.endpointDefinitions[A]||{},S);return h}},B=e.map(R=>R.init(h,y,D));function C(R){let I=R.endpoints({query:A=>({...A,type:"query"}),mutation:A=>({...A,type:"mutation"}),infiniteQuery:A=>({...A,type:"infinitequery"})});for(let[A,S]of Object.entries(I)){if(R.overrideExisting!==!0&&A in D.endpointDefinitions){if(R.overrideExisting==="throw")throw new Error((0,ct.formatProdErrorMessage)(39));typeof process<"u";continue}typeof process<"u",D.endpointDefinitions[A]=S;for(let o of B)o.injectEndpoint(A,S)}return h}return h.injectEndpoints({endpoints:p.endpoints})}}var lt=require("@reduxjs/toolkit"),ft=Symbol();function mt(){return function(){throw new Error((0,lt.formatProdErrorMessage)(33))}}var Pt=require("immer");function Y(e,...t){return Object.assign(e,...t)}var gt=require("immer");var Qt=({api:e,queryThunk:t,internalState:p})=>{let m=`${e.reducerPath}/subscriptions`,y=null,D=null,{updateSubscriptionOptions:h,unsubscribeQueryResult:B}=e.internalActions,C=(o,g)=>{if(h.match(g)){let{queryCacheKey:c,requestId:s,options:x}=g.payload;return o?.[c]?.[s]&&(o[c][s]=x),!0}if(B.match(g)){let{queryCacheKey:c,requestId:s}=g.payload;return o[c]&&delete o[c][s],!0}if(e.internalActions.removeQueryResult.match(g))return delete o[g.payload.queryCacheKey],!0;if(t.pending.match(g)){let{meta:{arg:c,requestId:s}}=g,x=o[c.queryCacheKey]??={};return x[`${s}_running`]={},c.subscribe&&(x[s]=c.subscriptionOptions??x[s]??{}),!0}let M=!1;if(t.fulfilled.match(g)||t.rejected.match(g)){let c=o[g.meta.arg.queryCacheKey]||{},s=`${g.meta.requestId}_running`;M||=!!c[s],delete c[s]}if(t.rejected.match(g)){let{meta:{condition:c,arg:s,requestId:x}}=g;if(c&&s.subscribe){let l=o[s.queryCacheKey]??={};l[x]=s.subscriptionOptions??l[x]??{},M=!0}}return M},R=()=>p.currentSubscriptions,S={getSubscriptions:R,getSubscriptionCount:o=>{let M=R()[o]??{};return J(M)},isRequestSubscribed:(o,g)=>!!R()?.[o]?.[g]};return(o,g)=>{if(y||(y=JSON.parse(JSON.stringify(p.currentSubscriptions))),e.util.resetApiState.match(o))return y=p.currentSubscriptions={},D=null,[!0,!1];if(e.internalActions.internal_getRTKQSubscriptions.match(o))return[!1,S];let M=C(p.currentSubscriptions,o),c=!0;if(M){D||(D=setTimeout(()=>{let l=JSON.parse(JSON.stringify(p.currentSubscriptions)),[,d]=(0,gt.produceWithPatches)(y,()=>l);g.next(e.internalActions.subscriptionsUpdated(d)),y=l,D=null},500));let s=typeof o.type=="string"&&!!o.type.startsWith(m),x=t.rejected.match(o)&&o.meta.condition&&!!o.meta.arg.subscribe;c=!s&&!x}return[c,!1]}};function Vt(e){for(let t in e)return!1;return!0}var zt=2147483647/1e3-1,Tt=({reducerPath:e,api:t,queryThunk:p,context:m,internalState:y,selectors:{selectQueryEntry:D,selectConfig:h}})=>{let{removeQueryResult:B,unsubscribeQueryResult:C,cacheEntriesUpserted:R}=t.internalActions,I=(0,i.isAnyOf)(C.match,p.fulfilled,p.rejected,R.match);function A(c){let s=y.currentSubscriptions[c];return!!s&&!Vt(s)}let S={},o=(c,s,x)=>{let l=s.getState(),d=h(l);if(I(c)){let Q;if(R.match(c))Q=c.payload.map(b=>b.queryDescription.queryCacheKey);else{let{queryCacheKey:b}=C.match(c)?c.payload:c.meta.arg;Q=[b]}g(Q,s,d)}if(t.util.resetApiState.match(c))for(let[Q,b]of Object.entries(S))b&&clearTimeout(b),delete S[Q];if(m.hasRehydrationInfo(c)){let{queries:Q}=m.extractRehydrationInfo(c);g(Object.keys(Q),s,d)}};function g(c,s,x){let l=s.getState();for(let d of c){let Q=D(l,d);M(d,Q?.endpointName,s,x)}}function M(c,s,x,l){let Q=m.endpointDefinitions[s]?.keepUnusedDataFor??l.keepUnusedDataFor;if(Q===1/0)return;let b=Math.max(0,Math.min(Q,zt));if(!A(c)){let E=S[c];E&&clearTimeout(E),S[c]=setTimeout(()=>{A(c)||x.dispatch(B({queryCacheKey:c})),delete S[c]},b*1e3)}}return o};var ht=new Error("Promise never resolved before cacheEntryRemoved."),At=({api:e,reducerPath:t,context:p,queryThunk:m,mutationThunk:y,internalState:D,selectors:{selectQueryEntry:h,selectApiState:B}})=>{let C=(0,i.isAsyncThunkAction)(m),R=(0,i.isAsyncThunkAction)(y),I=(0,i.isFulfilled)(m,y),A={};function S(s,x,l){let d=A[s];d?.valueResolved&&(d.valueResolved({data:x,meta:l}),delete d.valueResolved)}function o(s){let x=A[s];x&&(delete A[s],x.cacheEntryRemoved())}let g=(s,x,l)=>{let d=M(s);function Q(b,E,k,T){let u=h(l,E),n=h(x.getState(),E);!u&&n&&c(b,T,E,x,k)}if(m.pending.match(s))Q(s.meta.arg.endpointName,d,s.meta.requestId,s.meta.arg.originalArgs);else if(e.internalActions.cacheEntriesUpserted.match(s))for(let{queryDescription:b,value:E}of s.payload){let{endpointName:k,originalArgs:T,queryCacheKey:u}=b;Q(k,u,s.meta.requestId,T),S(u,E,{})}else if(y.pending.match(s))x.getState()[t].mutations[d]&&c(s.meta.arg.endpointName,s.meta.arg.originalArgs,d,x,s.meta.requestId);else if(I(s))S(d,s.payload,s.meta.baseQueryMeta);else if(e.internalActions.removeQueryResult.match(s)||e.internalActions.removeMutationResult.match(s))o(d);else if(e.util.resetApiState.match(s))for(let b of Object.keys(A))o(b)};function M(s){return C(s)?s.meta.arg.queryCacheKey:R(s)?s.meta.arg.fixedCacheKey??s.meta.requestId:e.internalActions.removeQueryResult.match(s)?s.payload.queryCacheKey:e.internalActions.removeMutationResult.match(s)?se(s.payload):""}function c(s,x,l,d,Q){let b=p.endpointDefinitions[s],E=b?.onCacheEntryAdded;if(!E)return;let k={},T=new Promise(P=>{k.cacheEntryRemoved=P}),u=Promise.race([new Promise(P=>{k.valueResolved=P}),T.then(()=>{throw ht})]);u.catch(()=>{}),A[l]=k;let n=e.endpoints[s].select(le(b)?x:l),r=d.dispatch((P,O,N)=>N),a={...d,getCacheEntry:()=>n(d.getState()),requestId:Q,extra:r,updateCachedData:le(b)?P=>d.dispatch(e.util.updateQueryData(s,x,P)):void 0,cacheDataLoaded:u,cacheEntryRemoved:T},f=E(x,a);Promise.resolve(f).catch(P=>{if(P!==ht)throw P})}return g};var Rt=({api:e,context:{apiUid:t},reducerPath:p})=>(m,y)=>{e.util.resetApiState.match(m)&&y.dispatch(e.internalActions.middlewareRegistered(t)),typeof process<"u"};var St=({reducerPath:e,context:t,context:{endpointDefinitions:p},mutationThunk:m,queryThunk:y,api:D,assertTagType:h,refetchQuery:B,internalState:C})=>{let{removeQueryResult:R}=D.internalActions,I=(0,i.isAnyOf)((0,i.isFulfilled)(m),(0,i.isRejectedWithValue)(m)),A=(0,i.isAnyOf)((0,i.isFulfilled)(m,y),(0,i.isRejected)(m,y)),S=[],o=(c,s)=>{I(c)?M(Ie(c,"invalidatesTags",p,h),s):A(c)?M([],s):D.util.invalidateTags.match(c)&&M(ge(c.payload,void 0,void 0,void 0,void 0,h),s)};function g(c){let{queries:s,mutations:x}=c;for(let l of[s,x])for(let d in l)if(l[d]?.status==="pending")return!0;return!1}function M(c,s){let x=s.getState(),l=x[e];if(S.push(...c),l.config.invalidationBehavior==="delayed"&&g(l))return;let d=S;if(S=[],d.length===0)return;let Q=D.util.selectInvalidatedBy(x,d);t.batch(()=>{let b=Array.from(Q.values());for(let{queryCacheKey:E}of b){let k=l.queries[E],T=C.currentSubscriptions[E]??{};k&&(J(T)===0?s.dispatch(R({queryCacheKey:E})):k.status!=="uninitialized"&&s.dispatch(B(k)))}})}return o};var xt=({reducerPath:e,queryThunk:t,api:p,refetchQuery:m,internalState:y})=>{let D={},h=(o,g)=>{(p.internalActions.updateSubscriptionOptions.match(o)||p.internalActions.unsubscribeQueryResult.match(o))&&R(o.payload,g),(t.pending.match(o)||t.rejected.match(o)&&o.meta.condition)&&R(o.meta.arg,g),(t.fulfilled.match(o)||t.rejected.match(o)&&!o.meta.condition)&&C(o.meta.arg,g),p.util.resetApiState.match(o)&&A()};function B(o,g){let c=g.getState()[e].queries[o],s=y.currentSubscriptions[o];if(!(!c||c.status==="uninitialized"))return s}function C({queryCacheKey:o},g){let M=g.getState()[e],c=M.queries[o],s=y.currentSubscriptions[o];if(!c||c.status==="uninitialized")return;let{lowestPollingInterval:x,skipPollingIfUnfocused:l}=S(s);if(!Number.isFinite(x))return;let d=D[o];d?.timeout&&(clearTimeout(d.timeout),d.timeout=void 0);let Q=Date.now()+x;D[o]={nextPollTimestamp:Q,pollingInterval:x,timeout:setTimeout(()=>{(M.config.focused||!l)&&g.dispatch(m(c)),C({queryCacheKey:o},g)},x)}}function R({queryCacheKey:o},g){let c=g.getState()[e].queries[o],s=y.currentSubscriptions[o];if(!c||c.status==="uninitialized")return;let{lowestPollingInterval:x}=S(s);if(!Number.isFinite(x)){I(o);return}let l=D[o],d=Date.now()+x;(!l||d<l.nextPollTimestamp)&&C({queryCacheKey:o},g)}function I(o){let g=D[o];g?.timeout&&clearTimeout(g.timeout),delete D[o]}function A(){for(let o of Object.keys(D))I(o)}function S(o={}){let g=!1,M=Number.POSITIVE_INFINITY;for(let c in o)o[c].pollingInterval&&(M=Math.min(o[c].pollingInterval,M),g=o[c].skipPollingIfUnfocused||g);return{lowestPollingInterval:M,skipPollingIfUnfocused:g}}return h};var Dt=({api:e,context:t,queryThunk:p,mutationThunk:m})=>{let y=(0,i.isPending)(p,m),D=(0,i.isRejected)(p,m),h=(0,i.isFulfilled)(p,m),B={};return(R,I)=>{if(y(R)){let{requestId:A,arg:{endpointName:S,originalArgs:o}}=R.meta,g=t.endpointDefinitions[S],M=g?.onQueryStarted;if(M){let c={},s=new Promise((Q,b)=>{c.resolve=Q,c.reject=b});s.catch(()=>{}),B[A]=c;let x=e.endpoints[S].select(le(g)?o:A),l=I.dispatch((Q,b,E)=>E),d={...I,getCacheEntry:()=>x(I.getState()),requestId:A,extra:l,updateCachedData:le(g)?Q=>I.dispatch(e.util.updateQueryData(S,o,Q)):void 0,queryFulfilled:s};M(o,d)}}else if(h(R)){let{requestId:A,baseQueryMeta:S}=R.meta;B[A]?.resolve({data:R.payload,meta:S}),delete B[A]}else if(D(R)){let{requestId:A,rejectedWithValue:S,baseQueryMeta:o}=R.meta;B[A]?.reject({error:R.payload??R.error,isUnhandledError:!S,meta:o}),delete B[A]}}};var bt=({reducerPath:e,context:t,api:p,refetchQuery:m,internalState:y})=>{let{removeQueryResult:D}=p.internalActions,h=(C,R)=>{ee.match(C)&&B(R,"refetchOnFocus"),te.match(C)&&B(R,"refetchOnReconnect")};function B(C,R){let I=C.getState()[e],A=I.queries,S=y.currentSubscriptions;t.batch(()=>{for(let o of Object.keys(S)){let g=A[o],M=S[o];if(!M||!g)continue;(Object.values(M).some(s=>s[R]===!0)||Object.values(M).every(s=>s[R]===void 0)&&I.config[R])&&(J(M)===0?C.dispatch(D({queryCacheKey:o})):g.status!=="uninitialized"&&C.dispatch(m(g)))}})}return h};function Et(e){let{reducerPath:t,queryThunk:p,api:m,context:y}=e,{apiUid:D}=y,h={invalidateTags:(0,i.createAction)(`${t}/invalidateTags`)},B=A=>A.type.startsWith(`${t}/`),C=[Rt,Tt,St,xt,At,Dt];return{middleware:A=>{let S=!1,g={...e,internalState:{currentSubscriptions:{}},refetchQuery:I,isThisApiSliceAction:B},M=C.map(x=>x(g)),c=Qt(g),s=bt(g);return x=>l=>{if(!(0,i.isAction)(l))return x(l);S||(S=!0,A.dispatch(m.internalActions.middlewareRegistered(D)));let d={...A,next:x},Q=A.getState(),[b,E]=c(l,d,Q),k;if(b?k=x(l):k=E,A.getState()[t]&&(s(l,d,Q),B(l)||y.hasRehydrationInfo(l)))for(let T of M)T(l,d,Q);return k}},actions:h};function I(A){return e.api.endpoints[A.endpointName].initiate(A.originalArgs,{subscribe:!1,forceRefetch:!0})}}var Se=Symbol(),we=({createSelector:e=i.createSelector}={})=>({name:Se,init(t,{baseQuery:p,tagTypes:m,reducerPath:y,serializeQueryArgs:D,keepUnusedDataFor:h,refetchOnMountOrArgChange:B,refetchOnFocus:C,refetchOnReconnect:R,invalidationBehavior:I,onSchemaFailure:A,catchSchemaFailure:S,skipSchemaValidation:o},g){(0,Pt.enablePatches)();let M=v=>(typeof process<"u",v);Object.assign(t,{reducerPath:y,endpoints:{},internalActions:{onOnline:te,onOffline:ce,onFocus:ee,onFocusLost:pe},util:{}});let c=dt({serializeQueryArgs:D,reducerPath:y,createSelector:e}),{selectInvalidatedBy:s,selectCachedArgsForQuery:x,buildQuerySelector:l,buildInfiniteQuerySelector:d,buildMutationSelector:Q}=c;Y(t.util,{selectInvalidatedBy:s,selectCachedArgsForQuery:x});let{queryThunk:b,infiniteQueryThunk:E,mutationThunk:k,patchQueryData:T,updateQueryData:u,upsertQueryData:n,prefetch:r,buildMatchThunkActions:a}=rt({baseQuery:p,reducerPath:y,context:g,api:t,serializeQueryArgs:D,assertTagType:M,selectors:c,onSchemaFailure:A,catchSchemaFailure:S,skipSchemaValidation:o}),{reducer:f,actions:P}=ot({context:g,queryThunk:b,infiniteQueryThunk:E,mutationThunk:k,serializeQueryArgs:D,reducerPath:y,assertTagType:M,config:{refetchOnFocus:C,refetchOnReconnect:R,refetchOnMountOrArgChange:B,keepUnusedDataFor:h,reducerPath:y,invalidationBehavior:I}});Y(t.util,{patchQueryData:T,updateQueryData:u,upsertQueryData:n,prefetch:r,resetApiState:P.resetApiState,upsertQueryEntries:P.cacheEntriesUpserted}),Y(t.internalActions,P);let{middleware:O,actions:N}=Et({reducerPath:y,context:g,queryThunk:b,mutationThunk:k,infiniteQueryThunk:E,api:t,assertTagType:M,selectors:c});Y(t.util,N),Y(t,{reducer:f,middleware:O});let{buildInitiateQuery:F,buildInitiateInfiniteQuery:w,buildInitiateMutation:U,getRunningMutationThunk:H,getRunningMutationsThunk:W,getRunningQueriesThunk:j,getRunningQueryThunk:q}=tt({queryThunk:b,mutationThunk:k,infiniteQueryThunk:E,api:t,serializeQueryArgs:D,context:g});return Y(t.util,{getRunningMutationThunk:H,getRunningMutationsThunk:W,getRunningQueryThunk:q,getRunningQueriesThunk:j}),{name:Se,injectEndpoint(v,V){let _=t,K=_.endpoints[v]??={};ie(V)&&Y(K,{name:v,select:l(v,V),initiate:F(v,V)},a(b,v)),Ze(V)&&Y(K,{name:v,select:Q(),initiate:U(v)},a(k,v)),ae(V)&&Y(K,{name:v,select:d(v,V),initiate:w(v,V)},a(b,v))}}}});var It=Me(we());0&&(module.exports={NamedSchemaError,QueryStatus,_NEVER,buildCreateApi,copyWithStructuralSharing,coreModule,coreModuleName,createApi,defaultSerializeQueryArgs,fakeBaseQuery,fetchBaseQuery,retry,setupListeners,skipToken});
//# sourceMappingURL=rtk-query.production.min.cjs.map