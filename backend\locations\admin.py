from django.contrib import admin
from django.utils.html import format_html
from .models import Building, Shelf, Box, Kent, FileType, File
from .location_hierarchy_models import Country, Region, Zone, City, SubCity, Kebele, SpecialLocation


@admin.register(Building)
class BuildingAdmin(admin.ModelAdmin):
    """Building admin with barcode/QR code display"""

    list_display = ['name', 'code', 'organization', 'is_active', 'created_at']
    list_filter = ['organization', 'is_active', 'created_at']
    search_fields = ['name', 'code', 'description', 'address']
    readonly_fields = ['created_at', 'updated_at', 'barcode_preview', 'qr_code_preview']
    ordering = ['organization', 'code']

    fieldsets = (
        ('Basic Information', {
            'fields': ('organization', 'name', 'code', 'description', 'is_active')
        }),
        ('Location Details', {
            'fields': ('address',)
        }),
        ('Generated Codes', {
            'fields': ('barcode_preview', 'qr_code_preview'),
            'classes': ('collapse',)
        }),
        ('Audit Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def barcode_preview(self, obj):
        """Display barcode preview"""
        if obj.barcode_image:
            return format_html('<img src="{}" style="max-width: 200px;" />', obj.barcode_image.url)
        return "No barcode generated"
    barcode_preview.short_description = 'Barcode'

    def qr_code_preview(self, obj):
        """Display QR code preview"""
        if obj.qr_code_image:
            return format_html('<img src="{}" style="max-width: 100px;" />', obj.qr_code_image.url)
        return "No QR code generated"
    qr_code_preview.short_description = 'QR Code'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('organization')


@admin.register(Shelf)
class ShelfAdmin(admin.ModelAdmin):
    """Shelf admin with hierarchy display"""

    list_display = ['name', 'code', 'building', 'full_code', 'grid_size', 'capacity', 'is_active', 'created_at']
    list_filter = ['building__organization', 'building', 'is_active', 'created_at']
    search_fields = ['name', 'code', 'description', 'building__name']
    readonly_fields = ['created_at', 'updated_at', 'full_code', 'capacity', 'barcode_preview', 'qr_code_preview']
    ordering = ['building', 'code']

    fieldsets = (
        ('Basic Information', {
            'fields': ('building', 'name', 'code', 'full_code', 'description', 'is_active')
        }),
        ('Grid System', {
            'fields': ('rows', 'columns', 'capacity')
        }),
        ('Generated Codes', {
            'fields': ('barcode_preview', 'qr_code_preview'),
            'classes': ('collapse',)
        }),
        ('Audit Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def barcode_preview(self, obj):
        """Display barcode preview"""
        if obj.barcode_image:
            return format_html('<img src="{}" style="max-width: 200px;" />', obj.barcode_image.url)
        return "No barcode generated"
    barcode_preview.short_description = 'Barcode'

    def qr_code_preview(self, obj):
        """Display QR code preview"""
        if obj.qr_code_image:
            return format_html('<img src="{}" style="max-width: 100px;" />', obj.qr_code_image.url)
        return "No QR code generated"
    qr_code_preview.short_description = 'QR Code'

    def grid_size(self, obj):
        """Display grid size"""
        return f"{obj.rows} × {obj.columns}"
    grid_size.short_description = 'Grid Size'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('building__organization')


@admin.register(Box)
class BoxAdmin(admin.ModelAdmin):
    """Box (Position) admin with grid position display"""

    list_display = ['position_display', 'shelf', 'full_code', 'name', 'kent_count', 'file_count', 'is_active', 'created_at']
    list_filter = ['shelf__building__organization', 'shelf__building', 'shelf', 'is_active', 'created_at']
    search_fields = ['name', 'description', 'shelf__name', 'shelf__building__name']
    readonly_fields = ['created_at', 'updated_at', 'full_code', 'position_code', 'kent_count', 'file_count', 'document_count', 'barcode_preview', 'qr_code_preview']
    ordering = ['shelf__building', 'shelf', 'row', 'column']

    fieldsets = (
        ('Basic Information', {
            'fields': ('shelf', 'name', 'full_code', 'position_code', 'description', 'is_active')
        }),
        ('Position', {
            'fields': ('row', 'column')
        }),
        ('Physical Properties', {
            'fields': ('color', 'material', 'kent_count', 'file_count', 'document_count')
        }),
        ('Generated Codes', {
            'fields': ('barcode_preview', 'qr_code_preview'),
            'classes': ('collapse',)
        }),
        ('Audit Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def position_display(self, obj):
        """Display position in shelf"""
        return f"R{obj.row:02d}C{obj.column:02d}"
    position_display.short_description = 'Position'

    def kent_count(self, obj):
        """Display current kent count"""
        count = obj.get_kent_count()
        return f"{count} kents"
    kent_count.short_description = 'Kents'

    def file_count(self, obj):
        """Display current file count"""
        count = obj.get_file_count()
        return f"{count} files"
    file_count.short_description = 'Files'

    def document_count(self, obj):
        """Display current document count"""
        count = obj.get_document_count()
        return f"{count} documents"
    document_count.short_description = 'Documents'

    def barcode_preview(self, obj):
        """Display barcode preview"""
        if obj.barcode_image:
            return format_html('<img src="{}" style="max-width: 200px;" />', obj.barcode_image.url)
        return "No barcode generated"
    barcode_preview.short_description = 'Barcode'

    def qr_code_preview(self, obj):
        """Display QR code preview"""
        if obj.qr_code_image:
            return format_html('<img src="{}" style="max-width: 100px;" />', obj.qr_code_image.url)
        return "No QR code generated"
    qr_code_preview.short_description = 'QR Code'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('shelf__building__organization')


@admin.register(Kent)
class KentAdmin(admin.ModelAdmin):
    """Kent admin with full hierarchy display"""

    list_display = ['name', 'code', 'box', 'position_display', 'full_code', 'capacity', 'file_count', 'is_active', 'created_at']
    list_filter = ['box__shelf__building__organization', 'box__shelf__building', 'box__shelf', 'is_active', 'created_at']
    search_fields = ['name', 'code', 'description', 'box__shelf__name', 'box__shelf__building__name']
    readonly_fields = ['created_at', 'updated_at', 'full_code', 'location_path', 'position_code', 'file_count', 'document_count', 'barcode_preview', 'qr_code_preview']
    ordering = ['box__shelf__building', 'box__shelf', 'box__row', 'box__column', 'code']

    fieldsets = (
        ('Basic Information', {
            'fields': ('box', 'name', 'code', 'full_code', 'location_path', 'description', 'is_active')
        }),
        ('Position', {
            'fields': ('position_code',)
        }),
        ('Physical Properties', {
            'fields': ('capacity', 'color', 'material', 'file_count', 'document_count')
        }),
        ('Generated Codes', {
            'fields': ('barcode_preview', 'qr_code_preview'),
            'classes': ('collapse',)
        }),
        ('Audit Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def position_display(self, obj):
        """Display position in shelf"""
        return obj.position_code
    position_display.short_description = 'Position'

    def file_count(self, obj):
        """Display current file count"""
        count = obj.get_file_count()
        if obj.capacity:
            percentage = (count / obj.capacity) * 100
            color = 'red' if percentage > 90 else 'orange' if percentage > 75 else 'green'
            return format_html(
                '<span style="color: {};">{}/{} ({}%)</span>',
                color, count, obj.capacity, round(percentage, 1)
            )
        return f"{count} files"
    file_count.short_description = 'Files'

    def document_count(self, obj):
        """Display current document count"""
        count = obj.get_document_count()
        return f"{count} documents"
    document_count.short_description = 'Documents'

    def barcode_preview(self, obj):
        """Display barcode preview"""
        if obj.barcode_image:
            return format_html('<img src="{}" style="max-width: 200px;" />', obj.barcode_image.url)
        return "No barcode generated"
    barcode_preview.short_description = 'Barcode'

    def qr_code_preview(self, obj):
        """Display QR code preview"""
        if obj.qr_code_image:
            return format_html('<img src="{}" style="max-width: 100px;" />', obj.qr_code_image.url)
        return "No QR code generated"
    qr_code_preview.short_description = 'QR Code'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('box__shelf__building__organization')


@admin.register(FileType)
class FileTypeAdmin(admin.ModelAdmin):
    """FileType admin for managing file types"""

    list_display = ['name', 'code', 'color_display', 'file_count', 'requires_business_name', 'requires_tin_number', 'is_active', 'created_at']
    list_filter = ['is_active', 'requires_business_name', 'requires_tin_number', 'requires_license_number', 'requires_owner_name', 'created_at']
    search_fields = ['name', 'code', 'description']
    readonly_fields = ['created_at', 'updated_at', 'file_count']
    ordering = ['name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'code', 'description', 'is_active')
        }),
        ('Visual Properties', {
            'fields': ('color', 'icon')
        }),
        ('Required Fields', {
            'fields': ('requires_business_name', 'requires_tin_number', 'requires_license_number', 'requires_owner_name')
        }),
        ('Default Settings', {
            'fields': ('default_document_types',)
        }),
        ('Statistics', {
            'fields': ('file_count',),
            'classes': ('collapse',)
        }),
        ('Audit Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def color_display(self, obj):
        """Display color as a colored box"""
        return format_html(
            '<div style="width: 20px; height: 20px; background-color: {}; border: 1px solid #ccc; display: inline-block;"></div> {}',
            obj.color,
            obj.color
        )
    color_display.short_description = 'Color'

    def file_count(self, obj):
        """Display current file count"""
        count = obj.get_file_count()
        return f"{count} files"
    file_count.short_description = 'Files'


@admin.register(File)
class FileAdmin(admin.ModelAdmin):
    """File admin for document grouping"""

    list_display = ['name', 'file_number', 'file_type', 'kent', 'business_name', 'document_count', 'status', 'created_at']
    list_filter = ['file_type', 'status', 'kent__box__shelf__building__organization', 'kent__box__shelf__building', 'kent__box__shelf', 'created_at']
    search_fields = ['name', 'file_number', 'business_name', 'tin_number', 'vat_number', 'owner_name', 'description']
    readonly_fields = ['created_at', 'updated_at', 'full_code', 'location_path', 'document_count', 'document_types', 'barcode_preview', 'qr_code_preview']
    ordering = ['kent__box__shelf__building', 'kent__box__shelf', 'kent__box', 'kent', 'file_number']

    fieldsets = (
        ('Basic Information', {
            'fields': ('kent', 'name', 'file_number', 'full_code', 'location_path', 'file_type', 'description', 'status')
        }),
        ('Business Information', {
            'fields': ('business_name', 'tin_number', 'vat_number', 'owner_name', 'contact_phone', 'contact_email', 'address'),
            'classes': ('collapse',)
        }),
        ('Document Information', {
            'fields': ('document_count', 'document_types', 'tags')
        }),
        ('Dates', {
            'fields': ('created_date', 'last_activity_date')
        }),
        ('Generated Codes', {
            'fields': ('barcode_preview', 'qr_code_preview'),
            'classes': ('collapse',)
        }),
        ('Audit Information', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def document_count(self, obj):
        """Display current document count"""
        count = obj.get_document_count()
        return f"{count} documents"
    document_count.short_description = 'Documents'

    def document_types(self, obj):
        """Display document types in this file"""
        types = obj.get_document_types()
        if types:
            return ", ".join(types[:3]) + ("..." if len(types) > 3 else "")
        return "No documents"
    document_types.short_description = 'Document Types'

    def barcode_preview(self, obj):
        """Display barcode preview"""
        if obj.barcode_image:
            return format_html('<img src="{}" style="max-width: 200px;" />', obj.barcode_image.url)
        return "No barcode generated"
    barcode_preview.short_description = 'Barcode'

    def qr_code_preview(self, obj):
        """Display QR code preview"""
        if obj.qr_code_image:
            return format_html('<img src="{}" style="max-width: 100px;" />', obj.qr_code_image.url)
        return "No QR code generated"
    qr_code_preview.short_description = 'QR Code'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('kent__box__shelf__building__organization', 'created_by')


# Location Hierarchy Admin Classes

@admin.register(Country)
class CountryAdmin(admin.ModelAdmin):
    """Country admin"""
    list_display = ['name', 'code', 'phone_code', 'currency', 'is_active', 'region_count', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'code']
    ordering = ['name']

    def region_count(self, obj):
        return obj.regions.count()
    region_count.short_description = 'Regions'


@admin.register(Region)
class RegionAdmin(admin.ModelAdmin):
    """Region admin"""
    list_display = ['name', 'code', 'country', 'capital_city', 'population', 'is_active', 'zone_count', 'created_at']
    list_filter = ['country', 'is_active', 'created_at']
    search_fields = ['name', 'code', 'capital_city']
    ordering = ['country__name', 'name']

    def zone_count(self, obj):
        return obj.zones.count()
    zone_count.short_description = 'Zones'


@admin.register(Zone)
class ZoneAdmin(admin.ModelAdmin):
    """Zone admin"""
    list_display = ['name', 'code', 'region', 'population', 'is_active', 'city_count', 'created_at']
    list_filter = ['region__country', 'region', 'is_active', 'created_at']
    search_fields = ['name', 'code']
    ordering = ['region__name', 'name']

    def city_count(self, obj):
        return obj.cities.count()
    city_count.short_description = 'Cities'


@admin.register(City)
class CityAdmin(admin.ModelAdmin):
    """City admin"""
    list_display = ['name', 'code', 'zone', 'population', 'is_capital', 'is_active', 'subcity_count', 'created_at']
    list_filter = ['zone__region__country', 'zone__region', 'zone', 'is_capital', 'is_active', 'created_at']
    search_fields = ['name', 'code']
    ordering = ['zone__name', 'name']

    def subcity_count(self, obj):
        return obj.subcities.count()
    subcity_count.short_description = 'SubCities'


@admin.register(SubCity)
class SubCityAdmin(admin.ModelAdmin):
    """SubCity admin"""
    list_display = ['name', 'code', 'type', 'city', 'population', 'is_active', 'kebele_count', 'created_at']
    list_filter = ['city__zone__region__country', 'city__zone__region', 'city__zone', 'city', 'type', 'is_active', 'created_at']
    search_fields = ['name', 'code']
    ordering = ['city__name', 'name']

    def kebele_count(self, obj):
        return obj.kebeles.count()
    kebele_count.short_description = 'Kebeles'


@admin.register(Kebele)
class KebeleAdmin(admin.ModelAdmin):
    """Kebele admin"""
    list_display = ['display_name', 'code', 'subcity', 'population', 'is_active', 'created_at']
    list_filter = ['subcity__city__zone__region__country', 'subcity__city__zone__region', 'subcity__city__zone', 'subcity__city', 'subcity', 'is_active', 'created_at']
    search_fields = ['name', 'code', 'number']
    ordering = ['subcity__name', 'number', 'name']


@admin.register(SpecialLocation)
class SpecialLocationAdmin(admin.ModelAdmin):
    """Special Location admin"""

    list_display = ['name', 'kebele', 'is_active', 'created_at']
    list_filter = [
        'kebele__subcity__city__zone__region__country',
        'kebele__subcity__city__zone__region',
        'kebele__subcity__city__zone',
        'kebele__subcity__city',
        'kebele__subcity',
        'kebele',
        'is_active',
        'created_at'
    ]
    search_fields = ['name', 'kebele__name']
    readonly_fields = ['created_at', 'updated_at', 'location_path', 'full_name', 'display_name']
    ordering = ['kebele__subcity__name', 'kebele__number', 'name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('kebele', 'name', 'is_active')
        }),
        ('Computed Fields', {
            'fields': ('location_path', 'full_name', 'display_name'),
            'classes': ('collapse',)
        }),
        ('Audit Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'kebele__subcity__city__zone__region__country'
        )
