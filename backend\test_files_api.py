#!/usr/bin/env python
"""
Test script to check File model and API
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'arada_dms.settings')
django.setup()

from locations.models import Building, Shelf, Box, Kent, FileType, File
from locations.serializers import FileSerializer
from organizations.models import Organization

def test_file_model():
    print("=== Testing File Model ===")
    
    # Check if we have any files
    file_count = File.objects.count()
    print(f"Total files in database: {file_count}")
    
    if file_count == 0:
        print("No files found. Let's create some test data...")
        create_test_data()
    
    # Test serializer
    print("\n=== Testing FileSerializer ===")
    files = File.objects.all()[:5]  # Get first 5 files
    
    for file_obj in files:
        print(f"\nTesting file: {file_obj.name}")
        try:
            serializer = FileSerializer(file_obj)
            data = serializer.data
            print(f"  ✅ Serialization successful")
            print(f"  ID: {data.get('id')}")
            print(f"  Name: {data.get('name')}")
            print(f"  File Number: {data.get('file_number')}")
            print(f"  Kent Location: {data.get('kent_location')}")
            print(f"  Document Count: {data.get('document_count')}")
            print(f"  Document Types: {data.get('document_types')}")
        except Exception as e:
            print(f"  ❌ Serialization failed: {e}")
            import traceback
            traceback.print_exc()

def create_test_data():
    print("Creating test data...")
    
    # Get or create organization
    org, created = Organization.objects.get_or_create(
        name="Test Organization",
        defaults={
            'short_name': 'TEST',
            'description': 'Test organization for file testing',
            'is_default': True
        }
    )
    
    # Get or create building
    building, created = Building.objects.get_or_create(
        name="Test Building",
        organization=org,
        defaults={
            'code': 'TB01',
            'description': 'Test building for file testing'
        }
    )
    
    # Get or create shelf
    shelf, created = Shelf.objects.get_or_create(
        name="Test Shelf",
        building=building,
        defaults={
            'code': 'TS01',
            'rows': 2,
            'columns': 2,
            'description': 'Test shelf for file testing'
        }
    )
    
    # Get or create box
    box, created = Box.objects.get_or_create(
        shelf=shelf,
        row=1,
        column=1,
        defaults={
            'capacity': 10,
            'description': 'Test box for file testing'
        }
    )
    
    # Get or create kent
    kent, created = Kent.objects.get_or_create(
        box=box,
        name="Test Kent",
        defaults={
            'code': 'TK01',
            'description': 'Test kent for file testing',
            'capacity': 20
        }
    )
    
    # Get or create file type
    file_type, created = FileType.objects.get_or_create(
        name="Business File",
        defaults={
            'code': 'BUS',
            'description': 'Business file type for testing',
            'color': '#2196f3',
            'icon': 'business',
            'requires_business_name': True
        }
    )
    
    # Create test files
    for i in range(3):
        file_obj, created = File.objects.get_or_create(
            kent=kent,
            file_number=f"F{i+1:03d}",
            defaults={
                'name': f"Test Business File {i+1}",
                'file_type': file_type,
                'description': f'Test file {i+1} for testing purposes',
                'business_name': f'Test Business {i+1}',
                'tin_number': f'TIN{i+1:06d}',
                'status': 'active'
            }
        )
        if created:
            print(f"  Created file: {file_obj.name}")
        else:
            print(f"  File already exists: {file_obj.name}")

def test_api_endpoint():
    print("\n=== Testing API Endpoint Logic ===")

    from locations.views import FileListCreateView
    from django.test import RequestFactory
    from django.contrib.auth import get_user_model

    User = get_user_model()

    # Create a test user
    user, created = User.objects.get_or_create(
        username='testuser',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User'
        }
    )

    # Create a mock request
    factory = RequestFactory()
    request = factory.get('/api/locations/files/')
    request.user = user

    # Test the view
    view = FileListCreateView()
    view.request = request

    try:
        # Test base queryset first
        base_queryset = File.objects.all()
        print(f"  Base File.objects.all() count: {base_queryset.count()}")

        active_queryset = File.objects.filter(is_active=True)
        print(f"  Active files count: {active_queryset.count()}")

        # Check if files have the required relationships
        for file_obj in File.objects.all()[:3]:
            print(f"  File: {file_obj.name}")
            print(f"    is_active: {file_obj.is_active}")
            print(f"    kent: {file_obj.kent}")
            print(f"    kent.box: {file_obj.kent.box if file_obj.kent else None}")
            print(f"    kent.box.shelf: {file_obj.kent.box.shelf if file_obj.kent and file_obj.kent.box else None}")
            print(f"    kent.box.shelf.building: {file_obj.kent.box.shelf.building if file_obj.kent and file_obj.kent.box and file_obj.kent.box.shelf else None}")

        queryset = view.get_queryset()
        print(f"  ✅ get_queryset() successful, returned {queryset.count()} files")

        if queryset.count() == 0:
            print("  Debugging empty queryset...")
            # Test the select_related query manually
            test_queryset = File.objects.select_related(
                'kent__box__shelf__building', 'file_type', 'created_by'
            ).filter(is_active=True)
            print(f"    Manual select_related query count: {test_queryset.count()}")

        serializer_class = view.get_serializer_class()
        print(f"  ✅ get_serializer_class() returned: {serializer_class}")

        # Test serialization of queryset
        serializer = serializer_class(queryset, many=True)
        data = serializer.data
        print(f"  ✅ Serialization of queryset successful, {len(data)} items")

    except Exception as e:
        print(f"  ❌ API endpoint test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_file_model()
    test_api_endpoint()
    print("\n=== Test Complete ===")
