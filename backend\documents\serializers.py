from rest_framework import serializers
from django.utils import timezone
from .models import DocumentType, Document, DocumentVersion, DocumentTag


class DocumentTypeSerializer(serializers.ModelSerializer):
    """Document type serializer"""
    
    organization_name = serializers.CharField(source='organization.name', read_only=True)
    document_count = serializers.SerializerMethodField()
    retention_days_display = serializers.SerializerMethodField()
    
    class Meta:
        model = DocumentType
        fields = [
            'id', 'organization', 'organization_name', 'name', 'code', 'description',
            'retention_days', 'retention_days_display', 'confidentiality_level',
            'requires_expiry_date', 'requires_approval', 'allowed_file_extensions',
            'max_file_size_mb', 'is_active', 'document_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_document_count(self, obj):
        """Get number of documents of this type"""
        return obj.documents.filter(is_active=True).count()
    
    def get_retention_days_display(self, obj):
        """Get retention days with fallback to organization default"""
        return obj.get_retention_days()


class DocumentTypeCreateSerializer(serializers.ModelSerializer):
    """Document type creation serializer"""
    
    class Meta:
        model = DocumentType
        fields = [
            'organization', 'name', 'code', 'description', 'retention_days',
            'confidentiality_level', 'requires_expiry_date', 'requires_approval',
            'allowed_file_extensions', 'max_file_size_mb'
        ]
    
    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class DocumentSerializer(serializers.ModelSerializer):
    """Document serializer"""
    
    document_type_name = serializers.CharField(source='document_type.name', read_only=True)
    organization_name = serializers.CharField(source='document_type.organization.name', read_only=True)

    # File information
    file_name = serializers.ReadOnlyField()
    file_number = serializers.ReadOnlyField()

    # Location information
    kent_location = serializers.CharField(source='kent.location_path', read_only=True)
    kent_code = serializers.CharField(source='kent.full_code', read_only=True)
    full_location_path = serializers.ReadOnlyField()

    created_by_name = serializers.CharField(source='created_by.full_name', read_only=True)
    location_display = serializers.ReadOnlyField()
    is_expired = serializers.ReadOnlyField()
    is_physical = serializers.ReadOnlyField()
    is_digital = serializers.ReadOnlyField()
    can_be_requested = serializers.ReadOnlyField()
    retention_date = serializers.SerializerMethodField()
    
    class Meta:
        model = Document
        fields = [
            'id', 'title', 'description', 'document_type', 'document_type_name',
            'organization_name', 'mode', 'status', 'tags', 'reference_number',
            'document_date', 'expiry_date', 'document_file', 'file_name', 'file_number',
            'kent', 'kent_location', 'kent_code', 'full_location_path',
            'file', 'file_size', 'number_of_pages', 'barcode_image', 'qr_code_image',
            'location_display', 'is_expired', 'is_physical', 'is_digital',
            'can_be_requested', 'retention_date', 'is_active',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'file_size', 'barcode_image', 'qr_code_image', 
            'created_at', 'updated_at'
        ]
    
    def get_retention_date(self, obj):
        """Get document retention expiry date"""
        return obj.get_retention_date()


class DocumentCreateSerializer(serializers.ModelSerializer):
    """Document creation serializer"""
    
    class Meta:
        model = Document
        fields = [
            'title', 'description', 'document_type', 'mode', 'tags', 'number_of_pages',
            'reference_number', 'document_date', 'expiry_date', 'document_file', 'file', 'kent'
        ]
    
    def validate(self, attrs):
        document_type = attrs.get('document_type')
        mode = attrs.get('mode')
        kent = attrs.get('kent')
        document_file = attrs.get('document_file')  # File grouping (ForeignKey)
        digital_file = attrs.get('file')  # Digital file upload (FileField)
        expiry_date = attrs.get('expiry_date')

        # Validate expiry date requirement
        if document_type and document_type.requires_expiry_date and not expiry_date:
            raise serializers.ValidationError(
                f"Expiry date is required for {document_type.name} documents"
            )

        # Validate physical document location (document_file or kent)
        if mode in ['physical', 'hybrid']:
            if not document_file and not kent:
                raise serializers.ValidationError(
                    "Physical documents must belong to a file or have a direct kent location"
                )

        # Validate digital document file
        if mode in ['digital', 'hybrid'] and not digital_file:
            raise serializers.ValidationError(
                "Digital documents must have a file upload"
            )
        
        # Validate file extension
        if digital_file and document_type and document_type.allowed_file_extensions:
            file_ext = digital_file.name.split('.')[-1].lower()
            if file_ext not in document_type.allowed_file_extensions:
                raise serializers.ValidationError(
                    f"File type '{file_ext}' not allowed. Allowed types: {document_type.allowed_file_extensions}"
                )
        
        return attrs
    
    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class DocumentUpdateSerializer(serializers.ModelSerializer):
    """Document update serializer"""
    
    class Meta:
        model = Document
        fields = [
            'title', 'description', 'mode', 'status', 'tags',
            'reference_number', 'document_date', 'expiry_date', 'kent', 'file'
        ]


class DocumentVersionSerializer(serializers.ModelSerializer):
    """Document version serializer"""
    
    document_title = serializers.CharField(source='document.title', read_only=True)
    created_by_name = serializers.CharField(source='created_by.full_name', read_only=True)
    
    class Meta:
        model = DocumentVersion
        fields = [
            'id', 'document', 'document_title', 'version_number', 'file',
            'file_size', 'change_summary', 'created_by', 'created_by_name', 'created_at'
        ]
        read_only_fields = ['id', 'file_size', 'created_at']
    
    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class DocumentTagSerializer(serializers.ModelSerializer):
    """Document tag serializer"""
    
    organization_name = serializers.CharField(source='organization.name', read_only=True)
    usage_count = serializers.SerializerMethodField()
    
    class Meta:
        model = DocumentTag
        fields = [
            'id', 'organization', 'organization_name', 'name', 'color',
            'description', 'is_active', 'usage_count', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']
    
    def get_usage_count(self, obj):
        """Get number of documents using this tag"""
        return Document.objects.filter(tags__contains=[obj.name], is_active=True).count()


class DocumentSearchSerializer(serializers.Serializer):
    """Document search serializer"""
    
    query = serializers.CharField(max_length=255, required=False)
    document_type = serializers.IntegerField(required=False)
    mode = serializers.ChoiceField(
        choices=Document.DocumentMode.choices,
        required=False
    )
    status = serializers.ChoiceField(
        choices=Document.Status.choices,
        required=False
    )
    kent = serializers.IntegerField(required=False)
    tags = serializers.ListField(
        child=serializers.CharField(max_length=50),
        required=False
    )
    date_from = serializers.DateField(required=False)
    date_to = serializers.DateField(required=False)
    organization = serializers.IntegerField(required=False)


class DocumentSummarySerializer(serializers.ModelSerializer):
    """Document summary serializer for listings"""
    
    document_type_name = serializers.CharField(source='document_type.name', read_only=True)
    location_display = serializers.ReadOnlyField()
    
    class Meta:
        model = Document
        fields = [
            'id', 'title', 'document_type_name', 'mode', 'status',
            'location_display', 'created_at'
        ]


class DocumentStatsSerializer(serializers.Serializer):
    """Document statistics serializer"""
    
    total_documents = serializers.IntegerField()
    physical_documents = serializers.IntegerField()
    digital_documents = serializers.IntegerField()
    hybrid_documents = serializers.IntegerField()
    active_documents = serializers.IntegerField()
    checked_out_documents = serializers.IntegerField()
    expired_documents = serializers.IntegerField()
    documents_by_type = serializers.DictField()
    recent_documents = DocumentSummarySerializer(many=True)
