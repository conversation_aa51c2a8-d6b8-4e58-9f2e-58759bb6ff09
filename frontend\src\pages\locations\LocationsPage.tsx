import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Avatar,
  Fade,
  Zoom,
  Badge,
  Chip,
  CircularProgress,
} from '@mui/material';
import {
  Business,
  Shelves,
  Inventory,
  TrendingUp,
  Archive,
  ViewModule,
} from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import locationService from '../../services/locationService';

// Dashboard card interface
interface DashboardCard {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  count: number;
  badge?: string;
  gradient: string;
  route: string;
}

const LocationsPage: React.FC = () => {
  const navigate = useNavigate();
  const { showNotification } = useNotification();

  const [stats, setStats] = useState({
    buildings: 0,
    shelves: 0,
    boxes: 0,
    kents: 0,
    documents: 0,
    utilization: 0,
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      setLoading(true);

      // Load basic counts (these should work)
      const [buildingsData, shelvesData, boxesData, kentsData] = await Promise.all([
        locationService.getBuildings({ page_size: 1 }),
        locationService.getShelves({ page_size: 1 }),
        locationService.getBoxes({ page_size: 1 }),
        locationService.getKents({ page_size: 1 }),
      ]);

      // Try to load summary data separately with error handling
      let summaryData = null;
      try {
        summaryData = await locationService.getLocationSummary();
      } catch (summaryError) {
        console.warn('Summary API failed, using fallback data:', summaryError);
        // Continue without summary data
      }

      setStats({
        buildings: buildingsData.count || 0,
        shelves: shelvesData.count || 0,
        boxes: boxesData.count || 0,
        kents: kentsData.count || 0,
        documents: 0, // Will be loaded from documents API when available
        utilization: summaryData?.utilization_percentage || 0,
      });
    } catch (error) {
      console.error('Failed to load stats:', error);
      // Set fallback stats
      setStats({
        buildings: 0,
        shelves: 0,
        boxes: 0,
        kents: 0,
        documents: 0,
        utilization: 0,
      });
      showNotification('Failed to load location statistics', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Dashboard cards configuration
  const dashboardCards: DashboardCard[] = [
    {
      id: 'buildings',
      title: 'Buildings',
      description: 'Manage physical buildings and structures',
      icon: <Business sx={{ fontSize: 40 }} />,
      color: '#1976d2',
      count: stats.buildings,
      badge: 'Physical',
      gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      route: '/locations/buildings',
    },
    {
      id: 'shelves',
      title: 'Shelves',
      description: 'Organize shelves within buildings',
      icon: <Shelves sx={{ fontSize: 40 }} />,
      color: '#7b1fa2',
      count: stats.shelves,
      badge: 'Storage',
      gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      route: '/locations/shelves',
    },

    {
      id: 'boxes',
      title: 'Box Positions',
      description: 'View automatically created shelf positions (Read-only)',
      icon: <Archive sx={{ fontSize: 40 }} />,
      color: '#388e3c',
      count: stats.boxes,
      badge: 'Auto-created',
      gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      route: '/locations/boxes',
    },
    {
      id: 'kents',
      title: 'Kents',
      description: 'Manage individual storage containers within boxes',
      icon: <Inventory sx={{ fontSize: 40 }} />,
      color: '#f57c00',
      count: stats.kents,
      badge: 'Containers',
      gradient: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
      route: '/locations/kents',
    },
    {
      id: 'visualization',
      title: 'Shelf Visualization',
      description: 'Interactive visual navigation through storage hierarchy',
      icon: <ViewModule sx={{ fontSize: 40 }} />,
      color: '#1976d2',
      count: stats.shelves,
      badge: 'Interactive',
      gradient: 'linear-gradient(135deg, #74b9ff 0%, #0984e3 100%)',
      route: '/locations/visualization',
    },
    {
      id: 'analytics',
      title: 'Analytics',
      description: 'View utilization and performance metrics',
      icon: <TrendingUp sx={{ fontSize: 40 }} />,
      color: '#d32f2f',
      count: Math.round(stats.utilization),
      badge: `${Math.round(stats.utilization)}%`,
      gradient: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
      route: '/locations/analytics',
    },
  ];

  const handleCardClick = (cardId: string) => {
    const card = dashboardCards.find(c => c.id === cardId);
    if (card) {
      navigate(card.route);
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Fade in timeout={800}>
        <Box sx={{ mb: 6, textAlign: 'center' }}>
          <Typography
            variant="h3"
            component="h1"
            gutterBottom
            sx={{
              fontWeight: 700,
              background: 'linear-gradient(45deg, #1976d2, #7b1fa2)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              mb: 2
            }}
          >
            Physical Location Center
          </Typography>
          <Typography variant="h6" color="text.secondary" sx={{ maxWidth: 600, mx: 'auto' }}>
            Manage all aspects of your physical document storage infrastructure
          </Typography>
        </Box>
      </Fade>

      {/* Dashboard Cards */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
          <CircularProgress size={60} />
        </Box>
      ) : (
        <Box sx={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
          gap: 4,
          mb: 4
        }}>
          {dashboardCards.map((card, index) => (
            <Zoom in timeout={600 + index * 100} key={card.id}>
              <Card
                sx={{
                  position: 'relative',
                  overflow: 'hidden',
                  cursor: 'pointer',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
                  },
                  '&:active': {
                    transform: 'translateY(-4px)',
                  },
                }}
                onClick={() => handleCardClick(card.id)}
              >
                {/* Background Gradient */}
                <Box
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    height: '120px',
                    background: card.gradient,
                    opacity: 0.1,
                  }}
                />

                {/* Badge */}
                {card.badge && (
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 16,
                      right: 16,
                      zIndex: 2,
                    }}
                  >
                    <Chip
                      label={card.badge}
                      size="small"
                      sx={{
                        bgcolor: card.color,
                        color: 'white',
                        fontWeight: 600,
                        fontSize: '0.75rem',
                      }}
                    />
                  </Box>
                )}

                <CardContent sx={{ p: 4, position: 'relative', zIndex: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 3 }}>
                    {/* Icon */}
                    <Avatar
                      sx={{
                        width: 80,
                        height: 80,
                        background: card.gradient,
                        color: 'white',
                        boxShadow: '0 8px 24px rgba(0,0,0,0.15)',
                      }}
                    >
                      {card.icon}
                    </Avatar>

                    {/* Content */}
                    <Box sx={{ flex: 1, minWidth: 0 }}>
                      <Typography
                        variant="h5"
                        sx={{
                          fontWeight: 600,
                          mb: 1,
                          color: 'text.primary'
                        }}
                      >
                        {card.title}
                      </Typography>
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{ mb: 3, lineHeight: 1.6 }}
                      >
                        {card.description}
                      </Typography>

                      {/* Stats */}
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Badge
                          badgeContent={card.id === 'analytics' ? `${card.count}%` : card.count}
                          color="primary"
                          max={999}
                          sx={{
                            '& .MuiBadge-badge': {
                              fontSize: '0.875rem',
                              height: '24px',
                              minWidth: '24px',
                              fontWeight: 600,
                            }
                          }}
                        >
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1,
                              px: 2,
                              py: 1,
                              borderRadius: 2,
                              bgcolor: 'action.hover',
                            }}
                          >
                            <TrendingUp sx={{ fontSize: 20, color: card.color }} />
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              Manage
                            </Typography>
                          </Box>
                        </Badge>
                      </Box>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Zoom>
          ))}
        </Box>
      )}
    </Container>
  );
};

export default LocationsPage;
