from django.contrib import admin
from django.utils.html import format_html
from django.utils import timezone
from .models import DocumentRequest, RequestApproval, AuditLog, Notification


@admin.register(DocumentRequest)
class DocumentRequestAdmin(admin.ModelAdmin):
    """Document Request admin with workflow management"""

    list_display = ['request_number', 'requested_by', 'status', 'priority', 'required_date', 'due_date', 'is_overdue', 'requested_date']
    list_filter = ['status', 'priority', 'requested_date', 'required_date', 'due_date']
    search_fields = ['request_number', 'requested_by__username', 'purpose']
    readonly_fields = ['id', 'request_number', 'requested_date', 'created_at', 'updated_at', 'is_overdue', 'days_until_due']
    ordering = ['-requested_date']
    filter_horizontal = ['documents']

    fieldsets = (
        ('Request Information', {
            'fields': ('request_number', 'requested_by', 'documents', 'purpose', 'priority')
        }),
        ('Dates', {
            'fields': ('requested_date', 'required_date', 'due_date', 'days_until_due', 'is_overdue')
        }),
        ('Workflow Status', {
            'fields': ('status', 'approved_by', 'approved_date', 'rejection_reason')
        }),
        ('Checkout/Return', {
            'fields': ('checked_out_date', 'checked_out_by', 'returned_date', 'returned_to')
        }),
        ('Notes', {
            'fields': ('notes',)
        }),
        ('Audit Information', {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def is_overdue(self, obj):
        """Display overdue status"""
        if obj.is_overdue:
            return format_html('<span style="color: red; font-weight: bold;">OVERDUE</span>')
        return format_html('<span style="color: green;">No</span>')
    is_overdue.short_description = 'Overdue'

    def days_until_due(self, obj):
        """Display days until due"""
        days = obj.days_until_due
        if days is not None:
            if days < 0:
                return format_html('<span style="color: red;">{} days overdue</span>', abs(days))
            elif days <= 3:
                return format_html('<span style="color: orange;">{} days left</span>', days)
            else:
                return format_html('<span style="color: green;">{} days left</span>', days)
        return "N/A"
    days_until_due.short_description = 'Days Until Due'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'requested_by', 'approved_by', 'checked_out_by', 'returned_to'
        ).prefetch_related('documents')


@admin.register(RequestApproval)
class RequestApprovalAdmin(admin.ModelAdmin):
    """Request Approval admin"""

    list_display = ['request', 'approver', 'action', 'created_at']
    list_filter = ['action', 'created_at']
    search_fields = ['request__request_number', 'approver__username', 'comments']
    readonly_fields = ['created_at']
    ordering = ['-created_at']

    fieldsets = (
        ('Approval Information', {
            'fields': ('request', 'approver', 'action')
        }),
        ('Comments', {
            'fields': ('comments',)
        }),
        ('Audit Information', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('request', 'approver')


@admin.register(AuditLog)
class AuditLogAdmin(admin.ModelAdmin):
    """Audit Log admin for compliance tracking"""

    list_display = ['user', 'action', 'object_repr', 'timestamp', 'ip_address']
    list_filter = ['action', 'timestamp', 'content_type']
    search_fields = ['user__username', 'object_repr', 'description', 'ip_address']
    readonly_fields = ['timestamp', 'user', 'action', 'content_type', 'object_id', 'object_repr', 'description', 'ip_address', 'user_agent', 'changes']
    ordering = ['-timestamp']

    fieldsets = (
        ('Action Information', {
            'fields': ('user', 'action', 'timestamp')
        }),
        ('Object Information', {
            'fields': ('content_type', 'object_id', 'object_repr', 'description')
        }),
        ('Technical Details', {
            'fields': ('ip_address', 'user_agent'),
            'classes': ('collapse',)
        }),
        ('Changes', {
            'fields': ('changes',),
            'classes': ('collapse',)
        }),
    )

    def has_add_permission(self, request):
        """Prevent manual creation of audit logs"""
        return False

    def has_change_permission(self, request, obj=None):
        """Prevent modification of audit logs"""
        return False

    def has_delete_permission(self, request, obj=None):
        """Prevent deletion of audit logs"""
        return False

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'content_type')


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    """Notification admin"""

    list_display = ['recipient', 'title', 'notification_type', 'is_read', 'email_sent', 'created_at']
    list_filter = ['notification_type', 'is_read', 'email_sent', 'created_at']
    search_fields = ['recipient__username', 'title', 'message']
    readonly_fields = ['created_at', 'read_at', 'email_sent_at']
    ordering = ['-created_at']

    fieldsets = (
        ('Notification Information', {
            'fields': ('recipient', 'title', 'message', 'notification_type')
        }),
        ('Status', {
            'fields': ('is_read', 'read_at', 'email_sent', 'email_sent_at')
        }),
        ('Related Object', {
            'fields': ('content_type', 'object_id'),
            'classes': ('collapse',)
        }),
        ('Audit Information', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('recipient', 'content_type')
