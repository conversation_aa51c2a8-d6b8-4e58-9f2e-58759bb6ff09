import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Tabs,
  Tab,
  Paper,
  Breadcrumbs,
  Link,
} from '@mui/material';
import {
  Assessment,
  Dashboard,
  TrendingUp,
  Notifications,
  Add,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import LevelUpgradeDashboard from '../../components/taxpayers/LevelUpgradeDashboard';
import PendingUpgradesList from '../../components/taxpayers/PendingUpgradesList';
import UpgradeNotifications from '../../components/taxpayers/UpgradeNotifications';
import IncomeAnalysisForm from '../../components/taxpayers/IncomeAnalysisForm';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`level-assessment-tabpanel-${index}`}
      aria-labelledby={`level-assessment-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `level-assessment-tab-${index}`,
    'aria-controls': `level-assessment-tabpanel-${index}`,
  };
}

const LevelAssessmentPage: React.FC = () => {
  const navigate = useNavigate();
  const [currentTab, setCurrentTab] = useState(0);
  const [analysisFormOpen, setAnalysisFormOpen] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  const handleViewPendingUpgrades = () => {
    setCurrentTab(1);
  };

  const handleViewNotifications = () => {
    setCurrentTab(2);
  };

  const handleCreateAnalysis = () => {
    setAnalysisFormOpen(true);
  };

  const handleAnalysisSuccess = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  const handleUpgradeProcessed = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  const handleNotificationProcessed = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 3 }}>
        <Link
          color="inherit"
          href="#"
          onClick={() => navigate('/taxpayers')}
          sx={{ display: 'flex', alignItems: 'center', gap: 0.5, textDecoration: 'none' }}
        >
          Taxpayers
        </Link>
        <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
          <Assessment fontSize="small" />
          Level Assessment
        </Typography>
      </Breadcrumbs>

      {/* Page Header */}
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Assessment color="primary" sx={{ fontSize: 40 }} />
          Taxpayer Level Assessment System
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage annual income analysis and taxpayer level upgrades based on daily income thresholds.
        </Typography>
      </Box>

      {/* Tabs */}
      <Paper sx={{ width: '100%' }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs 
            value={currentTab} 
            onChange={handleTabChange} 
            aria-label="level assessment tabs"
            variant="fullWidth"
          >
            <Tab 
              label="Dashboard" 
              icon={<Dashboard />} 
              iconPosition="start"
              {...a11yProps(0)} 
            />
            <Tab 
              label="Pending Upgrades" 
              icon={<TrendingUp />} 
              iconPosition="start"
              {...a11yProps(1)} 
            />
            <Tab 
              label="Notifications" 
              icon={<Notifications />} 
              iconPosition="start"
              {...a11yProps(2)} 
            />
          </Tabs>
        </Box>

        {/* Dashboard Tab */}
        <TabPanel value={currentTab} index={0}>
          <LevelUpgradeDashboard
            key={refreshTrigger}
            onViewPendingUpgrades={handleViewPendingUpgrades}
            onViewNotifications={handleViewNotifications}
            onCreateAnalysis={handleCreateAnalysis}
          />
        </TabPanel>

        {/* Pending Upgrades Tab */}
        <TabPanel value={currentTab} index={1}>
          <PendingUpgradesList
            key={refreshTrigger}
            onUpgradeProcessed={handleUpgradeProcessed}
          />
        </TabPanel>

        {/* Notifications Tab */}
        <TabPanel value={currentTab} index={2}>
          <UpgradeNotifications
            key={refreshTrigger}
            onNotificationProcessed={handleNotificationProcessed}
          />
        </TabPanel>
      </Paper>

      {/* Income Analysis Form Dialog */}
      <IncomeAnalysisForm
        open={analysisFormOpen}
        onClose={() => setAnalysisFormOpen(false)}
        onSuccess={handleAnalysisSuccess}
      />
    </Container>
  );
};

export default LevelAssessmentPage;
