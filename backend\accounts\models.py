from django.contrib.auth.models import AbstractUser
from django.db import models
from django.core.validators import RegexValidator


class User(AbstractUser):
    """
    Custom User model extending Django's AbstractUser
    Supports role-based access control for the document management system
    """

    class Role(models.TextChoices):
        ADMIN = 'admin', 'Administrator'
        MANAGER = 'manager', 'Manager'
        CLERK = 'clerk', 'Clerk'
        AUDITOR = 'auditor', 'Auditor'
        EXPERT = 'expert', 'Expert'

    # Additional fields
    role = models.CharField(
        max_length=20,
        choices=Role.choices,
        default=Role.CLERK,
        help_text="User role determines system permissions"
    )

    employee_id = models.CharField(
        max_length=20,
        unique=True,
        null=True,
        blank=True,
        help_text="Employee identification number"
    )

    phone_number = models.CharField(
        max_length=15,
        validators=[RegexValidator(
            regex=r'^\+?1?\d{9,15}$',
            message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed."
        )],
        null=True,
        blank=True
    )

    department = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        help_text="Department or division within the organization"
    )

    profile_picture = models.ImageField(
        upload_to='profiles/',
        null=True,
        blank=True,
        help_text="User profile picture"
    )

    # Organization relationship (will be added after organizations app)
    organization = models.ForeignKey(
        'organizations.Organization',
        on_delete=models.CASCADE,
        related_name='users',
        null=True,
        blank=True,
        help_text="Organization this user belongs to"
    )

    # Audit fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_login_ip = models.GenericIPAddressField(null=True, blank=True)

    class Meta:
        db_table = 'accounts_user'
        verbose_name = 'User'
        verbose_name_plural = 'Users'
        ordering = ['username']

    def __str__(self):
        return f"{self.get_full_name()} ({self.username})"

    @property
    def full_name(self):
        """Return the user's full name"""
        return f"{self.first_name} {self.last_name}".strip()

    def has_role(self, role):
        """Check if user has a specific role"""
        return self.role == role

    def can_approve_requests(self):
        """Check if user can approve document requests"""
        return self.role in [self.Role.ADMIN, self.Role.MANAGER]

    def can_manage_documents(self):
        """Check if user can create/edit documents"""
        return self.role in [self.Role.ADMIN, self.Role.MANAGER, self.Role.CLERK]

    def is_read_only(self):
        """Check if user has read-only access"""
        return self.role == self.Role.AUDITOR


class UserSession(models.Model):
    """
    Track user sessions for security and audit purposes
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sessions')
    session_key = models.CharField(max_length=500, unique=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    login_time = models.DateTimeField(auto_now_add=True)
    logout_time = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'accounts_user_session'
        verbose_name = 'User Session'
        verbose_name_plural = 'User Sessions'
        ordering = ['-login_time']

    def __str__(self):
        return f"{self.user.username} - {self.login_time}"
