#!/usr/bin/env python3
"""
Test script to verify the tax collection settings API endpoint
"""

import os
import sys
import django
from django.conf import settings

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + '/backend')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from organizations.models import Organization
import json

def test_tax_settings_api():
    """Test the tax collection settings API endpoint"""
    
    print("🔧 Testing Tax Collection Settings API")
    print("=" * 50)
    
    # Get the organization
    try:
        org = Organization.objects.get(id=2)
        print(f"✅ Found organization: {org.name}")
    except Organization.DoesNotExist:
        print("❌ Organization with ID 2 not found")
        return
    
    # Show current values
    print(f"\n📊 Current Tax Collection Settings:")
    print(f"   Individual Penalty Rate: {org.individual_penalty_rate}%")
    print(f"   Individual Interest Rate: {org.individual_interest_rate}%")
    print(f"   Organization Penalty Rate: {org.organization_penalty_rate}%")
    print(f"   Organization Interest Rate: {org.organization_interest_rate}%")
    
    # Create test client
    client = Client()
    
    # Test data - new tax rates
    test_data = {
        'individual_penalty_rate': 6.5,
        'individual_interest_rate': 2.8,
        'organization_penalty_rate': 13.0,
        'organization_interest_rate': 4.2
    }
    
    print(f"\n🧪 Testing API Update with:")
    for key, value in test_data.items():
        print(f"   {key}: {value}%")
    
    # Test PATCH request
    response = client.patch(
        f'/api/organizations/{org.id}/',
        data=json.dumps(test_data),
        content_type='application/json'
    )
    
    print(f"\n📡 API Response:")
    print(f"   Status Code: {response.status_code}")
    
    if response.status_code == 200:
        print("   ✅ API request successful!")
        
        # Parse response
        response_data = json.loads(response.content)
        
        print(f"\n📋 API Response Data:")
        print(f"   Individual Penalty Rate: {response_data.get('individual_penalty_rate')}%")
        print(f"   Individual Interest Rate: {response_data.get('individual_interest_rate')}%")
        print(f"   Organization Penalty Rate: {response_data.get('organization_penalty_rate')}%")
        print(f"   Organization Interest Rate: {response_data.get('organization_interest_rate')}%")
        
        # Verify database update
        org.refresh_from_db()
        print(f"\n💾 Database Verification:")
        print(f"   Individual Penalty Rate: {org.individual_penalty_rate}%")
        print(f"   Individual Interest Rate: {org.individual_interest_rate}%")
        print(f"   Organization Penalty Rate: {org.organization_penalty_rate}%")
        print(f"   Organization Interest Rate: {org.organization_interest_rate}%")
        
        # Check if values match
        success = (
            float(org.individual_penalty_rate) == test_data['individual_penalty_rate'] and
            float(org.individual_interest_rate) == test_data['individual_interest_rate'] and
            float(org.organization_penalty_rate) == test_data['organization_penalty_rate'] and
            float(org.organization_interest_rate) == test_data['organization_interest_rate']
        )
        
        if success:
            print("\n🎉 SUCCESS: Tax collection settings updated successfully!")
            print("   ✅ API endpoint is working correctly")
            print("   ✅ Database values match test data")
            print("   ✅ The issue is likely in the frontend FormData handling")
        else:
            print("\n❌ FAILURE: Database values don't match test data")
            print("   🔍 There might be an issue with the serializer or model")
            
    else:
        print(f"   ❌ API request failed!")
        print(f"   Error: {response.content.decode()}")
    
    print("\n" + "=" * 50)
    print("🔍 CONCLUSION:")
    if response.status_code == 200:
        print("   • Backend API is working correctly")
        print("   • The issue is in the frontend FormData submission")
        print("   • Check browser console for FormData contents")
        print("   • Verify penalty/interest fields are included in FormData")
    else:
        print("   • Backend API has issues")
        print("   • Check Django logs for errors")
        print("   • Verify serializer configuration")

if __name__ == '__main__':
    test_tax_settings_api()
