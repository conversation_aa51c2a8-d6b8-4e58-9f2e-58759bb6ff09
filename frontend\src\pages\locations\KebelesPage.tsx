import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  <PERSON><PERSON><PERSON>,
  Card,
  CardContent,
  Button,
  TextField,
  FormControlLabel,
  Switch,
  InputAdornment,
  Avatar,
  Divider,
  Stack,
  Fade,
  Alert,
  CircularProgress,
  IconButton,
  Breadcrumbs,
  Link,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TablePagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Home as HomeIcon,
  ArrowBack,
  Home,
  Add,
  Edit,
  Delete,
  CheckCircle,
  Cancel,
  Business,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useNotification } from '../../contexts/NotificationContext';
import locationHierarchyService from '../../services/locationHierarchyService';
import ConfirmationDialog from '../../components/common/ConfirmationDialog';

interface Kebele {
  id: number;
  name: string;
  code: string;
  subcity: number;
  subcity_name?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface SubCity {
  id: number;
  name: string;
  code: string;
  city_name?: string;
}

const KebelesPage: React.FC = () => {
  const navigate = useNavigate();
  const { showNotification } = useNotification();

  const [kebeles, setKebeles] = useState<Kebele[]>([]);
  const [subcities, setSubcities] = useState<SubCity[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingKebele, setEditingKebele] = useState<Kebele | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    subcity: '',
    is_active: true,
  });
  const [formErrors, setFormErrors] = useState<any>({});
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [kebeleToDelete, setKebeleToDelete] = useState<Kebele | null>(null);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    loadKebeles();
    loadSubcities();
  }, [page, rowsPerPage]);

  const loadKebeles = async () => {
    try {
      setLoading(true);
      const response = await locationHierarchyService.getKebeles({
        page: page + 1,
        page_size: rowsPerPage,
      });
      setKebeles(response.results);
      setTotalCount(response.count);
    } catch (error) {
      console.error('Error loading kebeles:', error);
      showNotification('Failed to load kebeles', 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadSubcities = async () => {
    try {
      const response = await locationHierarchyService.getSubCities({ page_size: 100 });
      setSubcities(response.results);
    } catch (error) {
      console.error('Error loading subcities:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setSubmitting(true);
      setFormErrors({});

      if (editingKebele) {
        await locationHierarchyService.updateKebele(editingKebele.id, formData);
        showNotification('Kebele updated successfully', 'success');
      } else {
        await locationHierarchyService.createKebele(formData);
        showNotification('Kebele created successfully', 'success');
      }

      resetForm();
      loadKebeles();
    } catch (error: any) {
      console.error('Error submitting form:', error);
      if (error.response?.data) {
        setFormErrors(error.response.data);
      }
      showNotification('Failed to save kebele', 'error');
    } finally {
      setSubmitting(false);
    }
  };

  const handleEdit = (kebele: Kebele) => {
    setEditingKebele(kebele);
    setFormData({
      name: kebele.name,
      code: kebele.code,
      subcity: kebele.subcity.toString(),
      is_active: kebele.is_active,
    });
    setShowForm(true);
  };

  const handleDelete = (kebele: Kebele) => {
    setKebeleToDelete(kebele);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!kebeleToDelete) return;

    try {
      setDeleting(true);
      await locationHierarchyService.deleteKebele(kebeleToDelete.id);
      showNotification('Kebele deleted successfully', 'success');
      loadKebeles();
      handleCloseDeleteDialog();
    } catch (error) {
      console.error('Error deleting kebele:', error);
      showNotification('Failed to delete kebele', 'error');
    } finally {
      setDeleting(false);
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setKebeleToDelete(null);
    setDeleting(false);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      code: '',
      subcity: '',
      is_active: true,
    });
    setFormErrors({});
    setEditingKebele(null);
    setShowForm(false);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Fade in timeout={800}>
        <Box sx={{ mb: 4 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs sx={{ mb: 2 }}>
            <Link
              component="button"
              variant="body1"
              onClick={() => navigate('/locations/hierarchy')}
              sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
            >
              <Home fontSize="small" />
              Location Hierarchy
            </Link>
            <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <HomeIcon fontSize="small" />
              Kebeles
            </Typography>
          </Breadcrumbs>

          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'between', mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <IconButton onClick={() => navigate('/locations/hierarchy')} sx={{ mr: 1 }}>
                <ArrowBack />
              </IconButton>
              <Avatar sx={{ bgcolor: 'error.main' }}>
                <HomeIcon />
              </Avatar>
              <Box>
                <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
                  Kebeles Management
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Manage kebeles within subcities and woredas
                </Typography>
              </Box>
            </Box>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setShowForm(true)}
              size="large"
              sx={{ ml: 'auto' }}
            >
              Add Kebele
            </Button>
          </Box>
        </Box>
      </Fade>

      {/* Form Section */}
      {showForm && (
        <Fade in timeout={600}>
          <Card sx={{ mb: 4 }}>
            <CardContent sx={{ p: 4 }}>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                {editingKebele ? 'Edit Kebele' : 'Add New Kebele'}
              </Typography>
              <Divider sx={{ mb: 3 }} />

              <form onSubmit={handleSubmit}>
                <Stack spacing={3}>
                  <TextField
                    label="Kebele Name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    error={!!formErrors.name}
                    helperText={formErrors.name || 'Enter the full kebele name'}
                    fullWidth
                    required
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <HomeIcon color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />

                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                    <TextField
                      label="Kebele Code"
                      value={formData.code}
                      onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
                      error={!!formErrors.code}
                      helperText={formErrors.code || 'e.g., K01, K02, K03'}
                      required
                      inputProps={{ maxLength: 10 }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <HomeIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />

                    <FormControl fullWidth required error={!!formErrors.subcity}>
                      <InputLabel>SubCity/Woreda</InputLabel>
                      <Select
                        value={formData.subcity}
                        onChange={(e) => setFormData({ ...formData, subcity: e.target.value })}
                        label="SubCity/Woreda"
                        startAdornment={
                          <InputAdornment position="start">
                            <Business color="action" />
                          </InputAdornment>
                        }
                      >
                        {subcities.map((subcity) => (
                          <MenuItem key={subcity.id} value={subcity.id.toString()}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Chip label={subcity.code} size="small" />
                              {subcity.name}
                              {subcity.city_name && (
                                <Typography variant="caption" color="text.secondary">
                                  ({subcity.city_name})
                                </Typography>
                              )}
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                      {formErrors.subcity && (
                        <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 2 }}>
                          {formErrors.subcity}
                        </Typography>
                      )}
                    </FormControl>
                  </Box>

                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.is_active}
                        onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                        color="primary"
                      />
                    }
                    label="Active Status"
                  />

                  <Box sx={{ display: 'flex', gap: 2, pt: 2 }}>
                    <Button
                      type="submit"
                      variant="contained"
                      disabled={submitting}
                      startIcon={submitting ? <CircularProgress size={20} /> : <Add />}
                      size="large"
                    >
                      {submitting ? 'Saving...' : editingKebele ? 'Update Kebele' : 'Create Kebele'}
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={resetForm}
                      size="large"
                    >
                      Cancel
                    </Button>
                  </Box>
                </Stack>
              </form>
            </CardContent>
          </Card>
        </Fade>
      )}

      {/* Kebeles Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
            Kebeles List
          </Typography>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : kebeles.length === 0 ? (
            <Alert severity="info">
              No kebeles found. Click "Add Kebele" to create your first kebele.
            </Alert>
          ) : (
            <>
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Kebele</TableCell>
                      <TableCell>Code</TableCell>
                      <TableCell>SubCity/Woreda</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Created</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {kebeles.map((kebele) => (
                      <TableRow key={kebele.id} hover>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <Avatar sx={{ bgcolor: 'error.main', width: 32, height: 32 }}>
                              <HomeIcon fontSize="small" />
                            </Avatar>
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {kebele.name}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip label={kebele.code} size="small" color="error" />
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Business fontSize="small" color="action" />
                            {kebele.subcity_name || `SubCity ${kebele.subcity}`}
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={kebele.is_active ? 'Active' : 'Inactive'}
                            color={kebele.is_active ? 'success' : 'error'}
                            size="small"
                            icon={kebele.is_active ? <CheckCircle /> : <Cancel />}
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {new Date(kebele.created_at).toLocaleDateString()}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                            <IconButton
                              size="small"
                              onClick={() => handleEdit(kebele)}
                              color="primary"
                            >
                              <Edit fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleDelete(kebele)}
                              color="error"
                            >
                              <Delete fontSize="small" />
                            </IconButton>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              <TablePagination
                component="div"
                count={totalCount}
                page={page}
                onPageChange={handleChangePage}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                rowsPerPageOptions={[5, 10, 25, 50]}
              />
            </>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        title="Delete Kebele"
        itemName={kebeleToDelete?.name}
        itemType="Kebele"
        message={`Are you sure you want to delete "${kebeleToDelete?.name}"? This action cannot be undone.`}
        confirmText="Delete Kebele"
        severity="error"
        loading={deleting}
      />
    </Container>
  );
};

export default KebelesPage;
