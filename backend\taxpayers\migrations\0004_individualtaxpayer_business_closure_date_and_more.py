# Generated by Django 5.2.3 on 2025-07-23 16:27

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "taxpayers",
            "0003_individualtaxpayer_city_individualtaxpayer_country_and_more",
        ),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="individualtaxpayer",
            name="business_closure_date",
            field=models.DateField(
                blank=True,
                help_text="Date when the business was officially closed",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="individualtaxpayer",
            name="business_closure_reason",
            field=models.TextField(
                blank=True,
                help_text="Reason for business closure (e.g., Bankruptcy, Voluntary closure, Relocation, etc.)",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="individualtaxpayer",
            name="closed_by",
            field=models.ForeignKey(
                blank=True,
                help_text="User who processed the business closure",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_closed_businesses",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="individualtaxpayer",
            name="is_business_closed",
            field=models.BooleanField(
                default=False,
                help_text="Whether the business has been permanently closed",
            ),
        ),
        migrations.AddField(
            model_name="organizationtaxpayer",
            name="business_closure_date",
            field=models.DateField(
                blank=True,
                help_text="Date when the business was officially closed",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="organizationtaxpayer",
            name="business_closure_reason",
            field=models.TextField(
                blank=True,
                help_text="Reason for business closure (e.g., Bankruptcy, Voluntary closure, Relocation, etc.)",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="organizationtaxpayer",
            name="closed_by",
            field=models.ForeignKey(
                blank=True,
                help_text="User who processed the business closure",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_closed_businesses",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="organizationtaxpayer",
            name="is_business_closed",
            field=models.BooleanField(
                default=False,
                help_text="Whether the business has been permanently closed",
            ),
        ),
    ]
