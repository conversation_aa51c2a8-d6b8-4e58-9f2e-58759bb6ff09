import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Avatar,
  Fade,
  Zoom,
  Badge,
  Chip,
  CircularProgress,
  Alert,
  Grid,
} from '@mui/material';
import {
  Description,
  Folder,
  Category,
  TrendingUp,
  CloudUpload,
  Schedule,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useNotification } from '../../contexts/NotificationContext';
import documentService from '../../services/documentService';
import fileService from '../../services/fileService';

interface DashboardCard {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  count: number;
  badge: string;
  gradient: string;
  route: string;
}

const DocumentCenterPage: React.FC = () => {
  const navigate = useNavigate();
  const { showNotification } = useNotification();

  const [stats, setStats] = useState({
    documents: 0,
    files: 0,
    documentTypes: 0,
    fileTypes: 0,
    recentUploads: 0,
    expiringSoon: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      setLoading(true);

      // Load stats individually to handle errors gracefully
      const statsData = {
        documents: 0,
        files: 0,
        fileTypes: 0,
        documentTypes: 0,
        recentUploads: 12, // Mock data for now
        expiringSoon: 5, // Mock data for now
      };

      // Try to load documents count
      try {
        const documentsRes = await documentService.getDocuments({ page_size: 1 });
        statsData.documents = documentsRes.count || 0;
      } catch (error) {
        console.warn('Failed to load documents count:', error);
      }

      // Try to load files count
      try {
        const filesRes = await fileService.getFiles({ page_size: 1 });
        statsData.files = filesRes.count || 0;
      } catch (error) {
        console.warn('Failed to load files count:', error);
      }

      // Try to load file types count
      try {
        const fileTypesRes = await fileService.getFileTypes({ page_size: 1 });
        statsData.fileTypes = fileTypesRes.count || 0;
      } catch (error) {
        console.warn('Failed to load file types count:', error);
      }

      // Try to load document types count
      try {
        const documentTypesRes = await documentService.getDocumentTypes({ page_size: 1 });
        statsData.documentTypes = documentTypesRes.count || 0;
      } catch (error) {
        console.warn('Failed to load document types count:', error);
      }

      setStats(statsData);
    } catch (error) {
      console.error('Failed to load stats:', error);
      showNotification('Failed to load document statistics', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Dashboard cards configuration
  const dashboardCards: DashboardCard[] = [
    {
      id: 'documents',
      title: 'Documents',
      description: 'Manage individual documents and files',
      icon: <Description sx={{ fontSize: 40 }} />,
      color: '#1976d2',
      count: stats.documents,
      badge: 'Content',
      gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      route: '/document-center/documents',
    },
    {
      id: 'files',
      title: 'Business Files',
      description: 'Organize business files within storage containers',
      icon: <Folder sx={{ fontSize: 40 }} />,
      color: '#7b1fa2',
      count: stats.files,
      badge: 'Business',
      gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      route: '/document-center/files',
    },
    {
      id: 'document-types',
      title: 'Document Types',
      description: 'Configure document categories and types',
      icon: <Category sx={{ fontSize: 40 }} />,
      color: '#388e3c',
      count: stats.documentTypes,
      badge: 'Types',
      gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      route: '/document-center/document-types',
    },
    {
      id: 'file-types',
      title: 'File Types',
      description: 'Manage business file type configurations',
      icon: <Category sx={{ fontSize: 40 }} />,
      color: '#f57c00',
      count: stats.fileTypes,
      badge: 'Categories',
      gradient: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
      route: '/document-center/file-types',
    },
    {
      id: 'recent-uploads',
      title: 'Recent Uploads',
      description: 'View recently uploaded documents',
      icon: <CloudUpload sx={{ fontSize: 40 }} />,
      color: '#0288d1',
      count: stats.recentUploads,
      badge: 'Recent',
      gradient: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
      route: '/document-center/recent-uploads',
    },
    {
      id: 'analytics',
      title: 'Analytics',
      description: 'View document statistics and insights',
      icon: <TrendingUp sx={{ fontSize: 40 }} />,
      color: '#d32f2f',
      count: stats.expiringSoon,
      badge: 'Insights',
      gradient: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
      route: '/document-center/analytics',
    },
  ];

  const handleCardClick = (route: string) => {
    navigate(route);
  };

  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ py: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
          <CircularProgress size={60} />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Fade in timeout={800}>
        <Box sx={{ mb: 6, textAlign: 'center' }}>
          <Typography
            variant="h3"
            component="h1"
            sx={{
              fontWeight: 700,
              background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              mb: 2,
            }}
          >
            Document Management Center
          </Typography>
          <Typography
            variant="h6"
            color="text.secondary"
            sx={{ maxWidth: 600, mx: 'auto', lineHeight: 1.6 }}
          >
            Comprehensive document and file management system for organizing, storing, and tracking business documents
          </Typography>
        </Box>
      </Fade>

      {/* Dashboard Cards */}
      <Box sx={{
        display: 'grid',
        gridTemplateColumns: {
          xs: '1fr',
          sm: 'repeat(2, 1fr)',
          md: 'repeat(3, 1fr)',
        },
        gap: 3,
        mb: 4
      }}>
        {dashboardCards.map((card, index) => (
          <Zoom in timeout={600 + index * 100} key={card.id}>
            <Card
              sx={{
                position: 'relative',
                overflow: 'hidden',
                cursor: 'pointer',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
                },
                '&:active': {
                  transform: 'translateY(-4px)',
                },
              }}
              onClick={() => handleCardClick(card.route)}
            >
              {/* Background Gradient */}
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '120px',
                  background: card.gradient,
                  opacity: 0.1,
                }}
              />

              {/* Badge */}
              {card.badge && (
                <Box
                  sx={{
                    position: 'absolute',
                    top: 16,
                    right: 16,
                    zIndex: 2,
                  }}
                >
                  <Chip
                    label={card.badge}
                    size="small"
                    sx={{
                      bgcolor: card.color,
                      color: 'white',
                      fontWeight: 600,
                      fontSize: '0.75rem',
                    }}
                  />
                </Box>
              )}

              <CardContent sx={{ p: 4, position: 'relative', zIndex: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 3 }}>
                  {/* Icon */}
                  <Avatar
                    sx={{
                      width: 80,
                      height: 80,
                      background: card.gradient,
                      color: 'white',
                      boxShadow: '0 8px 24px rgba(0,0,0,0.15)',
                    }}
                  >
                    {card.icon}
                  </Avatar>

                  {/* Content */}
                  <Box sx={{ flex: 1, minWidth: 0 }}>
                    <Typography
                      variant="h5"
                      sx={{
                        fontWeight: 600,
                        mb: 1,
                        color: 'text.primary'
                      }}
                    >
                      {card.title}
                    </Typography>
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{ mb: 3, lineHeight: 1.6 }}
                    >
                      {card.description}
                    </Typography>

                    {/* Stats */}
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Badge
                        badgeContent={card.id === 'analytics' ? `${card.count}%` : card.count}
                        color="primary"
                        max={999}
                        sx={{
                          '& .MuiBadge-badge': {
                            fontSize: '0.875rem',
                            height: '24px',
                            minWidth: '24px',
                            fontWeight: 600,
                          },
                        }}
                      >
                        <Box
                          sx={{
                            width: 12,
                            height: 12,
                            borderRadius: '50%',
                            bgcolor: card.color,
                          }}
                        />
                      </Badge>
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{ fontWeight: 500 }}
                      >
                        {card.id === 'analytics' ? 'Utilization' : 'Total Items'}
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Zoom>
        ))}
      </Box>

      {/* Quick Stats */}
      <Fade in timeout={1400}>
        <Box sx={{ mt: 6 }}>
          <Typography variant="h5" sx={{ mb: 3, fontWeight: 600 }}>
            Quick Overview
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                    Document Distribution
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                    <Avatar sx={{ bgcolor: 'primary.main' }}>
                      <Description />
                    </Avatar>
                    <Box>
                      <Typography variant="body1" sx={{ fontWeight: 500 }}>
                        Total Documents: {stats.documents.toLocaleString()}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Across {stats.files.toLocaleString()} business files
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                    Configuration Status
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                    <Avatar sx={{ bgcolor: 'success.main' }}>
                      <Category />
                    </Avatar>
                    <Box>
                      <Typography variant="body1" sx={{ fontWeight: 500 }}>
                        {stats.documentTypes + stats.fileTypes} Types Configured
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {stats.documentTypes} document types, {stats.fileTypes} file types
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      </Fade>
    </Container>
  );
};

export default DocumentCenterPage;
