import React, { useState, useRef, useCallback } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Box,
  Typography,
  IconButton,
  Alert,
  Paper,
} from '@mui/material';
import {
  CameraAlt,
  Close,
  Refresh,
  FlipCameraAndroid,
} from '@mui/icons-material';

interface CameraCaptureProps {
  open: boolean;
  onClose: () => void;
  onCapture: (file: File) => void;
  title?: string;
}

const CameraCapture: React.FC<CameraCaptureProps> = ({
  open,
  onClose,
  onCapture,
  title = 'Capture Document'
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [isStreaming, setIsStreaming] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [facingMode, setFacingMode] = useState<'user' | 'environment'>('environment');

  const startCamera = useCallback(async () => {
    try {
      setError(null);

      // Stop existing stream if any
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }

      const constraints: MediaStreamConstraints = {
        video: {
          facingMode: facingMode,
          width: { ideal: 1920 },
          height: { ideal: 1080 },
        },
        audio: false,
      };

      const mediaStream = await navigator.mediaDevices.getUserMedia(constraints);
      setStream(mediaStream);

      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;

        // Handle play promise to avoid interruption errors
        const playPromise = videoRef.current.play();
        if (playPromise !== undefined) {
          playPromise
            .then(() => {
              setIsStreaming(true);
            })
            .catch((error) => {
              console.warn('Video play was interrupted:', error);
              // Don't set error state for play interruptions as they're usually harmless
              setIsStreaming(true);
            });
        } else {
          setIsStreaming(true);
        }
      }
    } catch (err: any) {
      console.error('Error accessing camera:', err);
      setError(
        err.name === 'NotAllowedError'
          ? 'Camera access denied. Please allow camera permissions and try again.'
          : err.name === 'NotFoundError'
          ? 'No camera found. Please connect a camera and try again.'
          : 'Failed to access camera. Please check your camera settings.'
      );
    }
  }, [facingMode]); // Removed stream dependency to prevent restart loop

  const stopCamera = useCallback(() => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }
    setIsStreaming(false);
    setCapturedImage(null);
  }, [stream]);

  const capturePhoto = useCallback(() => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    if (!context) return;

    // Set canvas dimensions to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Draw the video frame to canvas
    context.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Convert canvas to blob
    canvas.toBlob((blob) => {
      if (blob) {
        // Create a file from the blob
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const file = new File([blob], `document-capture-${timestamp}.jpg`, {
          type: 'image/jpeg',
        });

        // Show preview
        const imageUrl = URL.createObjectURL(blob);
        setCapturedImage(imageUrl);

        // Call the onCapture callback with the file
        onCapture(file);
      }
    }, 'image/jpeg', 0.9);
  }, [onCapture]);

  const retakePhoto = useCallback(() => {
    setCapturedImage(null);
    if (capturedImage) {
      URL.revokeObjectURL(capturedImage);
    }
  }, [capturedImage]);

  const switchCamera = useCallback(() => {
    setFacingMode(prev => prev === 'user' ? 'environment' : 'user');
  }, []);

  const handleClose = useCallback(() => {
    stopCamera();
    if (capturedImage) {
      URL.revokeObjectURL(capturedImage);
      setCapturedImage(null);
    }
    setError(null);
    onClose();
  }, [stopCamera, capturedImage, onClose]);

  // Start camera when dialog opens
  React.useEffect(() => {
    if (open && !isStreaming && !capturedImage) {
      startCamera();
    }
  }, [open, isStreaming, capturedImage, startCamera]);

  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      stopCamera();
      if (capturedImage) {
        URL.revokeObjectURL(capturedImage);
      }
    };
  }, [stopCamera, capturedImage]);

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { height: '80vh' }
      }}
    >
      <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        {title}
        <IconButton onClick={handleClose}>
          <Close />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 2 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Box sx={{ position: 'relative', width: '100%', height: '100%' }}>
          {!capturedImage ? (
            // Camera view
            <Box sx={{ position: 'relative', width: '100%', height: '400px' }}>
              <video
                ref={videoRef}
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover',
                  borderRadius: 8,
                  backgroundColor: '#000',
                  display: 'block',
                }}
                playsInline
                muted
                autoPlay
              />
              
              {isStreaming && (
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: 16,
                    left: '50%',
                    transform: 'translateX(-50%)',
                    display: 'flex',
                    gap: 2,
                  }}
                >
                  <IconButton
                    onClick={switchCamera}
                    sx={{
                      backgroundColor: 'rgba(0,0,0,0.5)',
                      color: 'white',
                      '&:hover': { backgroundColor: 'rgba(0,0,0,0.7)' }
                    }}
                  >
                    <FlipCameraAndroid />
                  </IconButton>
                  
                  <IconButton
                    onClick={capturePhoto}
                    sx={{
                      backgroundColor: 'primary.main',
                      color: 'white',
                      width: 64,
                      height: 64,
                      '&:hover': { backgroundColor: 'primary.dark' }
                    }}
                  >
                    <CameraAlt sx={{ fontSize: 32 }} />
                  </IconButton>
                </Box>
              )}
            </Box>
          ) : (
            // Preview captured image
            <Box sx={{ textAlign: 'center' }}>
              <Paper sx={{ p: 2, mb: 2 }}>
                <img
                  src={capturedImage}
                  alt="Captured document"
                  style={{
                    maxWidth: '100%',
                    maxHeight: '400px',
                    objectFit: 'contain',
                    borderRadius: 8,
                  }}
                />
              </Paper>
              
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Document captured successfully! You can retake the photo or use this image.
              </Typography>
            </Box>
          )}

          {/* Hidden canvas for image capture */}
          <canvas
            ref={canvasRef}
            style={{ display: 'none' }}
          />
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 2, gap: 1 }}>
        {!isStreaming && !capturedImage && !error && (
          <Button
            variant="outlined"
            startIcon={<CameraAlt />}
            onClick={startCamera}
          >
            Start Camera
          </Button>
        )}
        
        {capturedImage && (
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={retakePhoto}
          >
            Retake
          </Button>
        )}
        
        <Button variant="outlined" onClick={handleClose}>
          Cancel
        </Button>
        
        {capturedImage && (
          <Button
            variant="contained"
            onClick={handleClose}
          >
            Use This Photo
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default CameraCapture;
