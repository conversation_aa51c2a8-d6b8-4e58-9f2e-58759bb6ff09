import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Typography,
  Card,
  CardContent,
  CardActions,
  Grid,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  Pagination,
} from '@mui/material';
import {
  Search,
  Add,
  Edit,
  Delete,
  Business,
  Phone,
  Email,
  LocationOn,
  FilterList,
  Refresh,
  AccountBalance,
  Receipt,
  Visibility,
} from '@mui/icons-material';
import { useNotification } from '../../../contexts/NotificationContext';
// Import the service
import taxpayerService from '../../../services/taxpayerService';

// Define interfaces locally to avoid import issues
interface OrganizationTaxPayer {
  id: string;
  tin: string;
  business_name: string;
  trade_name: string;
  display_name: string;
  organization_business_type: string;
  organization_business_type_name: string;
  tax_payer_level: string;
  tax_payer_level_name: string;
  business_sector: string;
  business_sector_name: string;
  business_sub_sector: string;
  business_sub_sector_name: string;
  business_registration_date: string;
  business_license_number: string;
  capital_amount?: number;
  number_of_employees?: number;
  manager_first_name: string;
  manager_middle_name: string;
  manager_last_name: string;
  manager_title: string;
  manager_full_name: string;
  vat_registration_date?: string;
  vat_number?: string;
  phone: string;
  phone_secondary: string;
  email: string;
  subcity: string;
  subcity_name: string;
  kebele: string;
  kebele_name: string;
  house_number: string;
  street_address: string;
  postal_code: string;
  location_display: string;
  tax_file?: string;
  tax_file_name?: string;
  is_active: boolean;
  registration_date: string;
  last_updated: string;
}

interface BusinessSector {
  id: string;
  code: string;
  name: string;
  description: string;
  is_active: boolean;
  sub_sectors_count: number;
  created_at: string;
  updated_at: string;
}

interface TaxPayerLevel {
  id: string;
  name: string;
  code: string;
  description: string;
  minimum_annual_turnover?: number;
  maximum_annual_turnover?: number;
  turnover_range: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface OrganizationBusinessType {
  id: string;
  code: string;
  name: string;
  description: string;
  requires_vat_registration: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface ListResponse<T> {
  count: number;
  next?: string;
  previous?: string;
  results: T[];
}
import locationService from '../../../services/locationService';
import locationHierarchyService from '../../../services/locationHierarchyService';

// Define location hierarchy interfaces locally
interface SubCity {
  id: number;
  city: number;
  city_name: string;
  zone_name: string;
  region_name: string;
  country_name: string;
  name: string;
  code: string;
  type: 'subcity' | 'woreda' | 'district';
  type_display: string;
  population?: number;
  area_km2?: number;
  is_active: boolean;
  full_name: string;
  kebele_count: number;
  created_at: string;
  updated_at: string;
}

interface Kebele {
  id: number;
  subcity: number;
  subcity_name: string;
  city_name: string;
  zone_name: string;
  region_name: string;
  country_name: string;
  name: string;
  code: string;
  number: number;
  population?: number;
  area_km2?: number;
  is_active: boolean;
  full_name: string;
  display_name: string;
  created_at: string;
  updated_at: string;
}
import OrganizationTaxPayerForm from '../forms/OrganizationTaxPayerForm';

interface OrganizationTaxPayersTabProps {
  onDataChange?: () => void;
}

const OrganizationTaxPayersTab: React.FC<OrganizationTaxPayersTabProps> = ({ onDataChange }) => {
  const { showNotification } = useNotification();
  const navigate = useNavigate();

  // State
  const [taxpayers, setTaxpayers] = useState<OrganizationTaxPayer[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(12);

  // Filters
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLevel, setSelectedLevel] = useState('');
  const [selectedSector, setSelectedSector] = useState('');
  const [selectedBusinessType, setSelectedBusinessType] = useState('');
  const [selectedSubcity, setSelectedSubcity] = useState('');

  // Form state
  const [formOpen, setFormOpen] = useState(false);
  const [editingTaxpayer, setEditingTaxpayer] = useState<OrganizationTaxPayer | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [taxpayerToDelete, setTaxpayerToDelete] = useState<OrganizationTaxPayer | null>(null);

  // Filter options
  const [levels, setLevels] = useState<TaxPayerLevel[]>([]);
  const [sectors, setSectors] = useState<BusinessSector[]>([]);
  const [businessTypes, setBusinessTypes] = useState<OrganizationBusinessType[]>([]);
  const [subcities, setSubcities] = useState<SubCity[]>([]);

  useEffect(() => {
    loadTaxpayers();
    loadFilterOptions();
  }, [currentPage, searchTerm, selectedLevel, selectedSector, selectedBusinessType, selectedSubcity]);

  const loadTaxpayers = async () => {
    try {
      setLoading(true);
      const params: any = {
        page: currentPage,
        search: searchTerm || undefined,
        tax_payer_level: selectedLevel || undefined,
        business_sector: selectedSector || undefined,
        organization_business_type: selectedBusinessType || undefined,
        subcity: selectedSubcity || undefined,
        ordering: '-registration_date',
      };

      const response: ListResponse<OrganizationTaxPayer> = await taxpayerService.getOrganizationTaxPayers(params);
      setTaxpayers(response.results);
      setTotalCount(response.count);
    } catch (error) {
      console.error('Failed to load organization taxpayers:', error);
      showNotification('Failed to load organization taxpayers', 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadFilterOptions = async () => {
    try {
      const [levelsData, sectorsData, businessTypesData, subcitiesData] = await Promise.all([
        taxpayerService.getTaxPayerLevelsSimple(),
        taxpayerService.getBusinessSectorsSimple(),
        taxpayerService.getOrganizationBusinessTypesSimple(),
        locationHierarchyService.getSubCities(),
      ]);

      setLevels(levelsData);
      setSectors(sectorsData);
      setBusinessTypes(businessTypesData);
      setSubcities(subcitiesData.results);
    } catch (error) {
      console.error('Failed to load filter options:', error);
    }
  };

  const handleCreate = () => {
    setEditingTaxpayer(null);
    setFormOpen(true);
  };

  const handleEdit = (taxpayer: OrganizationTaxPayer) => {
    setEditingTaxpayer(taxpayer);
    setFormOpen(true);
  };

  const handleDelete = (taxpayer: OrganizationTaxPayer) => {
    setTaxpayerToDelete(taxpayer);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!taxpayerToDelete) return;

    try {
      await taxpayerService.deleteOrganizationTaxPayer(taxpayerToDelete.id);
      showNotification('Organization taxpayer deleted successfully', 'success');
      loadTaxpayers();
      if (onDataChange) onDataChange();
    } catch (error) {
      console.error('Failed to delete taxpayer:', error);
      showNotification('Failed to delete taxpayer', 'error');
    } finally {
      setDeleteDialogOpen(false);
      setTaxpayerToDelete(null);
    }
  };

  const handleFormSubmit = async () => {
    setFormOpen(false);
    loadTaxpayers();
    if (onDataChange) onDataChange();
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedLevel('');
    setSelectedSector('');
    setSelectedBusinessType('');
    setSelectedSubcity('');
    setCurrentPage(1);
  };

  const TaxPayerCard = ({ taxpayer }: { taxpayer: OrganizationTaxPayer }) => (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <CardContent sx={{ flexGrow: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <AccountBalance sx={{ fontSize: 40, mr: 2, color: 'primary.main' }} />
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h6" component="div" noWrap>
              {taxpayer.business_name}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              TIN: {taxpayer.tin}
            </Typography>
          </Box>
        </Box>

        {taxpayer.trade_name && taxpayer.trade_name !== taxpayer.business_name && (
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Business sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
            <Typography variant="body2" noWrap>
              Trade: {taxpayer.trade_name}
            </Typography>
          </Box>
        )}

        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Phone sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
          <Typography variant="body2">
            {taxpayer.phone}
          </Typography>
        </Box>

        {taxpayer.email && (
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Email sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
            <Typography variant="body2" noWrap>
              {taxpayer.email}
            </Typography>
          </Box>
        )}

        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <LocationOn sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
          <Typography variant="body2" noWrap>
            {taxpayer.location_display}
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 1 }}>
          <Chip
            label={taxpayer.tax_payer_level_name}
            size="small"
            color="primary"
            variant="outlined"
          />
          <Chip
            label={taxpayer.organization_business_type_name}
            size="small"
            color="secondary"
            variant="outlined"
          />
          {taxpayer.vat_number && (
            <Chip
              label="VAT Registered"
              size="small"
              color="success"
              variant="outlined"
              icon={<Receipt />}
            />
          )}
        </Box>

        <Typography variant="caption" color="text.secondary">
          Sector: {taxpayer.business_sector_name}
        </Typography>
        <br />
        <Typography variant="caption" color="text.secondary">
          Manager: {taxpayer.manager_full_name}
        </Typography>
      </CardContent>

      <CardActions>
        <Button
          size="small"
          startIcon={<Visibility />}
          onClick={() => navigate(`/taxpayers/organizations/${taxpayer.id}`)}
        >
          View
        </Button>
        <Button
          size="small"
          startIcon={<Edit />}
          onClick={() => handleEdit(taxpayer)}
        >
          Edit
        </Button>
        <Button
          size="small"
          color="error"
          startIcon={<Delete />}
          onClick={() => handleDelete(taxpayer)}
        >
          Delete
        </Button>
      </CardActions>
    </Card>
  );

  const totalPages = Math.ceil(totalCount / pageSize);

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" component="h2">
          Organization Tax Payers ({totalCount.toLocaleString()})
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={loadTaxpayers}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={handleCreate}
          >
            Add Organization
          </Button>
        </Box>
      </Box>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid size={{ xs: 12, md: 3 }}>
              <TextField
                fullWidth
                placeholder="Search by name, TIN, VAT, phone..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 2 }}>
              <FormControl fullWidth>
                <InputLabel>Tax Payer Level</InputLabel>
                <Select
                  value={selectedLevel}
                  onChange={(e) => setSelectedLevel(e.target.value)}
                  label="Tax Payer Level"
                >
                  <MenuItem value="">All Levels</MenuItem>
                  {levels.map((level) => (
                    <MenuItem key={level.id} value={level.id}>
                      {level.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 2 }}>
              <FormControl fullWidth>
                <InputLabel>Business Sector</InputLabel>
                <Select
                  value={selectedSector}
                  onChange={(e) => setSelectedSector(e.target.value)}
                  label="Business Sector"
                >
                  <MenuItem value="">All Sectors</MenuItem>
                  {sectors.map((sector) => (
                    <MenuItem key={sector.id} value={sector.id}>
                      {sector.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 2 }}>
              <FormControl fullWidth>
                <InputLabel>Business Type</InputLabel>
                <Select
                  value={selectedBusinessType}
                  onChange={(e) => setSelectedBusinessType(e.target.value)}
                  label="Business Type"
                >
                  <MenuItem value="">All Types</MenuItem>
                  {businessTypes.map((type) => (
                    <MenuItem key={type.id} value={type.id}>
                      {type.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 2 }}>
              <FormControl fullWidth>
                <InputLabel>SubCity</InputLabel>
                <Select
                  value={selectedSubcity}
                  onChange={(e) => setSelectedSubcity(e.target.value)}
                  label="SubCity"
                >
                  <MenuItem value="">All SubCities</MenuItem>
                  {subcities.map((subcity) => (
                    <MenuItem key={subcity.id} value={subcity.id}>
                      {subcity.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid size={{ xs: 12, md: 1 }}>
              <Button
                fullWidth
                variant="outlined"
                onClick={clearFilters}
                sx={{ height: 56 }}
              >
                Clear
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Content */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
          <CircularProgress />
        </Box>
      ) : taxpayers.length === 0 ? (
        <Alert severity="info">
          No organization taxpayers found. {searchTerm && 'Try adjusting your search criteria or '}
          <Button onClick={handleCreate}>add the first one</Button>.
        </Alert>
      ) : (
        <>
          <Grid container spacing={3}>
            {taxpayers.map((taxpayer) => (
              <Grid key={taxpayer.id} size={{ xs: 12, sm: 6, md: 4 }}>
                <TaxPayerCard taxpayer={taxpayer} />
              </Grid>
            ))}
          </Grid>

          {/* Pagination */}
          {totalPages > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <Pagination
                count={totalPages}
                page={currentPage}
                onChange={(event, page) => setCurrentPage(page)}
                color="primary"
                size="large"
              />
            </Box>
          )}
        </>
      )}

      {/* Form Dialog */}
      <Dialog
        open={formOpen}
        onClose={() => setFormOpen(false)}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          {editingTaxpayer ? 'Edit Organization Tax Payer' : 'Add Organization Tax Payer'}
        </DialogTitle>
        <DialogContent>
          <OrganizationTaxPayerForm
            taxpayer={editingTaxpayer}
            onSubmit={handleFormSubmit}
            onCancel={() => setFormOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the organization "{taxpayerToDelete?.business_name}"?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default OrganizationTaxPayersTab;
