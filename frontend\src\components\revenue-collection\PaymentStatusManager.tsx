/**
 * Payment Status Manager Component
 * 
 * Comprehensive payment status management with penalty/interest tracking
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  Button,
  Box,
  LinearProgress,
  Alert,
  Divider,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
} from '@mui/material';
import {
  Payment,
  Warning,
  CheckCircle,
  Schedule,
  Calculate,
  Receipt,
  Refresh,
  TrendingUp,
  AccountBalance,
  Info,
} from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import revenueCollectionService from '../../services/revenueCollectionService';

interface PaymentStatusManagerProps {
  collections: any[];
  onRefresh: () => void;
}

interface PaymentSummary {
  total_collections: number;
  pending_count: number;
  partial_count: number;
  overdue_count: number;
  paid_count: number;
  total_assessed: number;
  total_paid: number;
  total_outstanding: number;
  total_overdue: number;
  total_penalty: number;
  total_interest: number;
}

const PaymentStatusManager: React.FC<PaymentStatusManagerProps> = ({
  collections,
  onRefresh,
}) => {
  const [summary, setSummary] = useState<PaymentSummary | null>(null);
  const [bulkUpdateOpen, setBulkUpdateOpen] = useState(false);
  const [bulkUpdating, setBulkUpdating] = useState(false);
  const { showNotification } = useNotification();

  useEffect(() => {
    calculateSummary();
  }, [collections]);

  const calculateSummary = () => {
    if (!collections.length) {
      setSummary(null);
      return;
    }

    const summary: PaymentSummary = {
      total_collections: collections.length,
      pending_count: collections.filter(c => c.payment_status === 'PENDING').length,
      partial_count: collections.filter(c => c.payment_status === 'PARTIAL').length,
      overdue_count: collections.filter(c => c.payment_status === 'OVERDUE').length,
      paid_count: collections.filter(c => c.payment_status === 'PAID').length,
      total_assessed: collections.reduce((sum, c) => sum + (c.amount || 0), 0),
      total_paid: collections.reduce((sum, c) => sum + (c.paid_amount || 0), 0),
      total_outstanding: collections.reduce((sum, c) => sum + ((c.amount || 0) - (c.paid_amount || 0)), 0),
      total_overdue: collections.filter(c => c.payment_status === 'OVERDUE').reduce((sum, c) => sum + ((c.amount || 0) - (c.paid_amount || 0)), 0),
      total_penalty: collections.reduce((sum, c) => sum + (c.penalty_amount || 0), 0),
      total_interest: collections.reduce((sum, c) => sum + (c.interest_amount || 0), 0),
    };

    setSummary(summary);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PAID': return 'success';
      case 'PARTIAL': return 'warning';
      case 'OVERDUE': return 'error';
      default: return 'default';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'ETB',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const handleBulkUpdateOverdue = async () => {
    try {
      setBulkUpdating(true);
      const result = await revenueCollectionService.bulkUpdateOverdueCollections();
      
      showNotification(
        `Updated ${result.updated_count} overdue collections. Added ${formatCurrency(result.total_penalty_added)} in penalties and ${formatCurrency(result.total_interest_added)} in interest.`,
        'success'
      );
      
      setBulkUpdateOpen(false);
      onRefresh();
    } catch (error) {
      console.error('Error updating overdue collections:', error);
      showNotification('Failed to update overdue collections', 'error');
    } finally {
      setBulkUpdating(false);
    }
  };

  if (!summary) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Payment Status Overview
          </Typography>
          <Alert severity="info">
            No collections data available
          </Alert>
        </CardContent>
      </Card>
    );
  }

  const paymentCompletionRate = summary.total_assessed > 0 
    ? (summary.total_paid / summary.total_assessed) * 100 
    : 0;

  return (
    <>
      <Card>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h6">
              Payment Status Overview
            </Typography>
            <Box display="flex" gap={1}>
              <Tooltip title="Refresh data">
                <IconButton onClick={onRefresh} size="small">
                  <Refresh />
                </IconButton>
              </Tooltip>
              <Button
                variant="outlined"
                size="small"
                onClick={() => setBulkUpdateOpen(true)}
                startIcon={<Calculate />}
                disabled={summary.overdue_count === 0}
              >
                Update Overdue ({summary.overdue_count})
              </Button>
            </Box>
          </Box>

          {/* Payment Status Summary */}
          <Grid container spacing={2} mb={3}>
            <Grid size={{ xs: 6, sm: 3 }}>
              <Box textAlign="center" p={2} bgcolor="grey.50" borderRadius={1}>
                <Typography variant="h4" color="text.primary">
                  {summary.pending_count}
                </Typography>
                <Chip label="PENDING" size="small" color="default" />
              </Box>
            </Grid>
            <Grid size={{ xs: 6, sm: 3 }}>
              <Box textAlign="center" p={2} bgcolor="warning.50" borderRadius={1}>
                <Typography variant="h4" color="warning.main">
                  {summary.partial_count}
                </Typography>
                <Chip label="PARTIAL" size="small" color="warning" />
              </Box>
            </Grid>
            <Grid size={{ xs: 6, sm: 3 }}>
              <Box textAlign="center" p={2} bgcolor="error.50" borderRadius={1}>
                <Typography variant="h4" color="error.main">
                  {summary.overdue_count}
                </Typography>
                <Chip label="OVERDUE" size="small" color="error" />
              </Box>
            </Grid>
            <Grid size={{ xs: 6, sm: 3 }}>
              <Box textAlign="center" p={2} bgcolor="success.50" borderRadius={1}>
                <Typography variant="h4" color="success.main">
                  {summary.paid_count}
                </Typography>
                <Chip label="PAID" size="small" color="success" />
              </Box>
            </Grid>
          </Grid>

          {/* Payment Progress */}
          <Box mb={3}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
              <Typography variant="subtitle2">
                Payment Completion Rate
              </Typography>
              <Typography variant="subtitle2" color="primary">
                {paymentCompletionRate.toFixed(1)}%
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={paymentCompletionRate}
              sx={{ height: 8, borderRadius: 4 }}
            />
          </Box>

          {/* Financial Summary */}
          <Grid container spacing={2}>
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Box>
                <Typography variant="caption" color="text.secondary">
                  Total Assessed
                </Typography>
                <Typography variant="h6">
                  {formatCurrency(summary.total_assessed)}
                </Typography>
              </Box>
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Box>
                <Typography variant="caption" color="text.secondary">
                  Total Paid
                </Typography>
                <Typography variant="h6" color="success.main">
                  {formatCurrency(summary.total_paid)}
                </Typography>
              </Box>
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Box>
                <Typography variant="caption" color="text.secondary">
                  Outstanding
                </Typography>
                <Typography variant="h6" color="warning.main">
                  {formatCurrency(summary.total_outstanding)}
                </Typography>
              </Box>
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Box>
                <Typography variant="caption" color="text.secondary">
                  Overdue
                </Typography>
                <Typography variant="h6" color="error.main">
                  {formatCurrency(summary.total_overdue)}
                </Typography>
              </Box>
            </Grid>
          </Grid>

          {/* Penalty and Interest Summary */}
          {(summary.total_penalty > 0 || summary.total_interest > 0) && (
            <Box mt={2}>
              <Divider sx={{ mb: 2 }} />
              <Grid container spacing={2}>
                <Grid size={{ xs: 6 }}>
                  <Box>
                    <Typography variant="caption" color="text.secondary">
                      Total Penalties
                    </Typography>
                    <Typography variant="h6" color="error.main">
                      {formatCurrency(summary.total_penalty)}
                    </Typography>
                  </Box>
                </Grid>
                <Grid size={{ xs: 6 }}>
                  <Box>
                    <Typography variant="caption" color="text.secondary">
                      Total Interest
                    </Typography>
                    <Typography variant="h6" color="error.main">
                      {formatCurrency(summary.total_interest)}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Box>
          )}

          {/* Overdue Alert */}
          {summary.overdue_count > 0 && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              <Typography variant="body2">
                <strong>{summary.overdue_count} collections are overdue</strong> with total overdue amount of{' '}
                <strong>{formatCurrency(summary.total_overdue)}</strong>. 
                Consider processing payments or updating penalty calculations.
              </Typography>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Bulk Update Dialog */}
      <Dialog open={bulkUpdateOpen} onClose={() => setBulkUpdateOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          <Box display="flex" alignItems="center" gap={1}>
            <Calculate color="primary" />
            Update Overdue Collections
          </Box>
        </DialogTitle>
        <DialogContent>
          <Alert severity="info" sx={{ mb: 2 }}>
            This will recalculate penalties and interest for all overdue collections based on current
            organization settings and days overdue.
          </Alert>
          
          <Typography variant="body2" gutterBottom>
            <strong>Collections to update:</strong> {summary.overdue_count}
          </Typography>
          <Typography variant="body2" gutterBottom>
            <strong>Current overdue amount:</strong> {formatCurrency(summary.total_overdue)}
          </Typography>
          <Typography variant="body2">
            <strong>Current penalties:</strong> {formatCurrency(summary.total_penalty)}
          </Typography>
          <Typography variant="body2">
            <strong>Current interest:</strong> {formatCurrency(summary.total_interest)}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setBulkUpdateOpen(false)} disabled={bulkUpdating}>
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleBulkUpdateOverdue}
            disabled={bulkUpdating}
            startIcon={bulkUpdating ? <CircularProgress size={20} /> : <Calculate />}
          >
            {bulkUpdating ? 'Updating...' : 'Update Overdue Collections'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default PaymentStatusManager;
