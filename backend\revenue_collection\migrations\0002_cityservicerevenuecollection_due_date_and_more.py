# Generated by Django 5.2.3 on 2025-07-15 18:30

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("revenue_collection", "0001_initial"),
        (
            "taxpayers",
            "0003_individualtaxpayer_city_individualtaxpayer_country_and_more",
        ),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="cityservicerevenuecollection",
            name="due_date",
            field=models.DateField(blank=True, help_text="Payment due date", null=True),
        ),
        migrations.AddField(
            model_name="cityservicerevenuecollection",
            name="paid_amount",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text="Amount actually paid",
                max_digits=15,
                validators=[django.core.validators.MinValueValidator(0)],
            ),
        ),
        migrations.AddField(
            model_name="cityservicerevenuecollection",
            name="payment_date",
            field=models.DateField(
                blank=True, help_text="Date when payment was actually made", null=True
            ),
        ),
        migrations.AddField(
            model_name="cityservicerevenuecollection",
            name="payment_status",
            field=models.CharField(
                choices=[
                    ("PENDING", "Pending Payment"),
                    ("PAID", "Paid"),
                    ("OVERDUE", "Overdue"),
                    ("PARTIAL", "Partially Paid"),
                    ("CANCELLED", "Cancelled"),
                ],
                default="PENDING",
                help_text="Current payment status",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="regionalrevenuecollection",
            name="due_date",
            field=models.DateField(blank=True, help_text="Payment due date", null=True),
        ),
        migrations.AddField(
            model_name="regionalrevenuecollection",
            name="paid_amount",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text="Amount actually paid",
                max_digits=15,
                validators=[django.core.validators.MinValueValidator(0)],
            ),
        ),
        migrations.AddField(
            model_name="regionalrevenuecollection",
            name="payment_date",
            field=models.DateField(
                blank=True, help_text="Date when payment was actually made", null=True
            ),
        ),
        migrations.AddField(
            model_name="regionalrevenuecollection",
            name="payment_status",
            field=models.CharField(
                choices=[
                    ("PENDING", "Pending Payment"),
                    ("PAID", "Paid"),
                    ("OVERDUE", "Overdue"),
                    ("PARTIAL", "Partially Paid"),
                    ("CANCELLED", "Cancelled"),
                ],
                default="PENDING",
                help_text="Current payment status",
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name="cityservicerevenuecollection",
            name="amount",
            field=models.DecimalField(
                decimal_places=2,
                help_text="Total amount to be collected",
                max_digits=15,
                validators=[django.core.validators.MinValueValidator(0)],
            ),
        ),
        migrations.AlterField(
            model_name="cityservicerevenuecollection",
            name="collection_date",
            field=models.DateField(help_text="Date when revenue was assessed/due"),
        ),
        migrations.AlterField(
            model_name="regionalrevenuecollection",
            name="amount",
            field=models.DecimalField(
                decimal_places=2,
                help_text="Total amount to be collected",
                max_digits=15,
                validators=[django.core.validators.MinValueValidator(0)],
            ),
        ),
        migrations.AlterField(
            model_name="regionalrevenuecollection",
            name="collection_date",
            field=models.DateField(help_text="Date when revenue was assessed/due"),
        ),
        migrations.CreateModel(
            name="TaxpayerPaymentSummary",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "total_assessed",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Total amount assessed for this period",
                        max_digits=15,
                    ),
                ),
                (
                    "total_paid",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Total amount paid for this period",
                        max_digits=15,
                    ),
                ),
                (
                    "total_outstanding",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Total outstanding amount",
                        max_digits=15,
                    ),
                ),
                (
                    "total_overdue",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Total overdue amount",
                        max_digits=15,
                    ),
                ),
                (
                    "total_collections",
                    models.IntegerField(
                        default=0, help_text="Total number of collections"
                    ),
                ),
                (
                    "paid_collections",
                    models.IntegerField(
                        default=0, help_text="Number of fully paid collections"
                    ),
                ),
                (
                    "overdue_collections",
                    models.IntegerField(
                        default=0, help_text="Number of overdue collections"
                    ),
                ),
                (
                    "last_payment_date",
                    models.DateField(
                        blank=True, help_text="Date of last payment", null=True
                    ),
                ),
                (
                    "next_due_date",
                    models.DateField(
                        blank=True, help_text="Next payment due date", null=True
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        help_text="User who created this summary",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="taxpayer_summaries_created",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "individual_taxpayer",
                    models.ForeignKey(
                        blank=True,
                        help_text="Individual taxpayer",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="taxpayers.individualtaxpayer",
                    ),
                ),
                (
                    "organization_taxpayer",
                    models.ForeignKey(
                        blank=True,
                        help_text="Organization taxpayer",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="taxpayers.organizationtaxpayer",
                    ),
                ),
                (
                    "period",
                    models.ForeignKey(
                        help_text="Revenue period",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="revenue_collection.revenueperiod",
                    ),
                ),
            ],
            options={
                "ordering": ["-period__start_date", "-updated_at"],
                "indexes": [
                    models.Index(
                        fields=["individual_taxpayer", "period"],
                        name="revenue_col_individ_d8d5cd_idx",
                    ),
                    models.Index(
                        fields=["organization_taxpayer", "period"],
                        name="revenue_col_organiz_e04f1f_idx",
                    ),
                    models.Index(
                        fields=["period", "total_overdue"],
                        name="revenue_col_period__a8571b_idx",
                    ),
                ],
                "unique_together": {
                    ("individual_taxpayer", "period"),
                    ("organization_taxpayer", "period"),
                },
            },
        ),
    ]
