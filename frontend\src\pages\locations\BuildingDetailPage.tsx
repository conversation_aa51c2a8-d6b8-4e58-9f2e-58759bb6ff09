import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Link,
} from '@mui/material';
import {
  Business,
  Edit,
  Delete,
  Home,
  LocationOn,
  Info,
  Phone,
  Email,
  Person,
  Apartment,
  Storage,
} from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import locationService from '../../services/locationService';
import type { Building } from '../../services/locationService';
import DetailPageTemplate from '../../components/common/DetailPageTemplate';
import ConfirmationDialog from '../../components/common/ConfirmationDialog';

const BuildingDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showNotification } = useNotification();

  const [building, setBuilding] = useState<Building | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    if (id) {
      loadBuilding();
    }
  }, [id]);

  const loadBuilding = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await locationService.getBuilding(Number(id));
      setBuilding(data);
    } catch (error) {
      console.error('Error loading building:', error);
      setError('Failed to load building');
      showNotification('Failed to load building', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    // Navigate back to buildings page with edit state
    navigate('/locations/buildings', {
      state: {
        editBuilding: building,
        showForm: true
      }
    });
  };

  const handleDelete = () => {
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!building) return;

    try {
      setDeleting(true);
      await locationService.deleteBuilding(building.id);
      showNotification('Building deleted successfully', 'success');
      navigate('/locations/buildings');
    } catch (error) {
      console.error('Error deleting building:', error);
      showNotification('Failed to delete building', 'error');
    } finally {
      setDeleting(false);
      setDeleteDialogOpen(false);
    }
  };

  const handleBack = () => {
    navigate('/locations/buildings');
  };

  if (!building && !loading && !error) {
    setError('Building not found');
  }

  const breadcrumbs = [
    { label: 'Dashboard', path: '/dashboard', icon: <Home fontSize="small" /> },
    { label: 'Locations', path: '/locations', icon: <LocationOn fontSize="small" /> },
    { label: 'Buildings', path: '/locations/buildings', icon: <Business fontSize="small" /> },
    { label: building?.name || 'Building', path: undefined, icon: <Apartment fontSize="small" /> },
  ];

  const actions = [
    {
      label: 'Edit',
      icon: <Edit />,
      onClick: handleEdit,
      color: 'primary' as const,
    },
    {
      label: 'Delete',
      icon: <Delete />,
      onClick: handleDelete,
      color: 'error' as const,
    },
  ];

  const chips = building ? [
    {
      label: building.is_active ? 'Active' : 'Inactive',
      color: building.is_active ? 'success' as const : 'error' as const,
    },
    ...(building.code ? [{
      label: `Code: ${building.code}`,
      color: 'info' as const,
    }] : []),
    ...(building.organization_name ? [{
      label: building.organization_name,
      color: 'primary' as const,
    }] : []),
  ] : [];

  const sections = building ? [
    {
      title: 'Basic Information',
      icon: <Info />,
      content: (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            p: 2,
            bgcolor: 'primary.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'primary.200'
          }}>
            <Apartment sx={{ color: 'primary.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="primary.main" sx={{ fontWeight: 600 }}>
                Building Name
              </Typography>
              <Typography variant="body2" color="primary.dark">
                {building.name}
              </Typography>
            </Box>
          </Box>

          {building.code && (
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              p: 2,
              bgcolor: 'info.50',
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'info.200'
            }}>
              <Business sx={{ color: 'info.main', mr: 2 }} />
              <Box>
                <Typography variant="subtitle2" color="info.main" sx={{ fontWeight: 600 }}>
                  Building Code
                </Typography>
                <Typography variant="body2" color="info.dark">
                  {building.code}
                </Typography>
              </Box>
            </Box>
          )}

          {building.description && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" color="text.secondary" sx={{ fontWeight: 600, mb: 1 }}>
                Description
              </Typography>
              <Typography variant="body2" sx={{
                p: 2,
                bgcolor: 'grey.50',
                borderRadius: 1,
                border: '1px solid',
                borderColor: 'grey.200'
              }}>
                {building.description}
              </Typography>
            </Box>
          )}

          {building.full_address && (
            <Box sx={{
              display: 'flex',
              alignItems: 'flex-start',
              p: 2,
              bgcolor: 'secondary.50',
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'secondary.200'
            }}>
              <LocationOn sx={{ color: 'secondary.main', mr: 2, mt: 0.5 }} />
              <Box>
                <Typography variant="subtitle2" color="secondary.main" sx={{ fontWeight: 600 }}>
                  Address
                </Typography>
                <Typography variant="body2" color="secondary.dark">
                  {building.full_address}
                </Typography>
              </Box>
            </Box>
          )}
        </Box>
      ),
    },
    {
      title: 'Contact & Management',
      icon: <Person />,
      content: (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          {building.manager_name && (
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              p: 2,
              bgcolor: 'success.50',
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'success.200'
            }}>
              <Person sx={{ color: 'success.main', mr: 2 }} />
              <Box>
                <Typography variant="subtitle2" color="success.main" sx={{ fontWeight: 600 }}>
                  Building Manager
                </Typography>
                <Typography variant="body2" color="success.dark">
                  {building.manager_name}
                </Typography>
              </Box>
            </Box>
          )}

          {building.contact_phone && (
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              p: 2,
              bgcolor: 'info.50',
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'info.200'
            }}>
              <Phone sx={{ color: 'info.main', mr: 2 }} />
              <Box>
                <Typography variant="subtitle2" color="info.main" sx={{ fontWeight: 600 }}>
                  Contact Phone
                </Typography>
                <Link
                  href={`tel:${building.contact_phone}`}
                  sx={{
                    color: 'info.dark',
                    textDecoration: 'none',
                    '&:hover': { textDecoration: 'underline' }
                  }}
                >
                  {building.contact_phone}
                </Link>
              </Box>
            </Box>
          )}

          {building.contact_email && (
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              p: 2,
              bgcolor: 'warning.50',
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'warning.200'
            }}>
              <Email sx={{ color: 'warning.main', mr: 2 }} />
              <Box>
                <Typography variant="subtitle2" color="warning.main" sx={{ fontWeight: 600 }}>
                  Contact Email
                </Typography>
                <Link
                  href={`mailto:${building.contact_email}`}
                  sx={{
                    color: 'warning.dark',
                    textDecoration: 'none',
                    '&:hover': { textDecoration: 'underline' }
                  }}
                >
                  {building.contact_email}
                </Link>
              </Box>
            </Box>
          )}

          {building.organization_name && (
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              p: 2,
              bgcolor: 'primary.50',
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'primary.200'
            }}>
              <Business sx={{ color: 'primary.main', mr: 2 }} />
              <Box>
                <Typography variant="subtitle2" color="primary.main" sx={{ fontWeight: 600 }}>
                  Organization
                </Typography>
                <Typography variant="body2" color="primary.dark">
                  {building.organization_name}
                </Typography>
              </Box>
            </Box>
          )}
        </Box>
      ),
    },
    {
      title: 'Storage Statistics',
      icon: <Storage />,
      content: (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            p: 2,
            bgcolor: 'success.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'success.200'
          }}>
            <Storage sx={{ color: 'success.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="success.main" sx={{ fontWeight: 600 }}>
                Shelves
              </Typography>
              <Typography variant="body2" color="success.dark">
                {building.shelf_count || 0} shelves
              </Typography>
            </Box>
          </Box>

          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            p: 2,
            bgcolor: 'warning.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'warning.200'
          }}>
            <Storage sx={{ color: 'warning.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="warning.main" sx={{ fontWeight: 600 }}>
                Total Kents
              </Typography>
              <Typography variant="body2" color="warning.dark">
                {building.kent_count || 0} kents
              </Typography>
            </Box>
          </Box>

          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            p: 2,
            bgcolor: 'info.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'info.200'
          }}>
            <Business sx={{ color: 'info.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="info.main" sx={{ fontWeight: 600 }}>
                Status
              </Typography>
              <Typography variant="body2" color="info.dark">
                {building.is_active ? 'Active and operational' : 'Inactive - not in use'}
              </Typography>
            </Box>
          </Box>
        </Box>
      ),
    },
  ] : [];

  return (
    <>
      <DetailPageTemplate
        loading={loading}
        error={error}
        breadcrumbs={breadcrumbs}
        title={building?.name || 'Building'}
        subtitle={building?.code ? `Building Code: ${building.code}` : building?.full_address}
        avatar={{
          fallbackIcon: <Apartment sx={{ fontSize: 40 }} />,
          alt: building?.name || 'Building',
        }}
        chips={chips}
        actions={actions}
        sections={sections}
        onBack={handleBack}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleConfirmDelete}
        title="Delete Building"
        itemName={building?.name}
        itemType="Building"
        message={`Are you sure you want to delete "${building?.name}"? This will also delete all shelves, boxes, and kents within this building.`}
        confirmText="Delete Building"
        severity="error"
        loading={deleting}
      />
    </>
  );
};

export default BuildingDetailPage;
