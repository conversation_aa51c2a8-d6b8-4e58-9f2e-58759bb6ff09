import apiClient from './api';

// Types
export interface BusinessSector {
  id: string;
  code: string;
  name: string;
  description: string;
  is_active: boolean;
  sub_sectors_count: number;
  created_at: string;
  updated_at: string;
}

export interface BusinessSubSector {
  id: string;
  business_sector: string;
  business_sector_name: string;
  business_sector_code: string;
  code: string;
  name: string;
  description: string;
  full_code: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface TaxPayerLevel {
  id: string;
  name: string;
  code: string;
  description: string;
  minimum_annual_turnover?: number;
  maximum_annual_turnover?: number;
  turnover_range: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface OrganizationBusinessType {
  id: string;
  code: string;
  name: string;
  description: string;
  requires_vat_registration: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface IndividualTaxPayer {
  id: string;
  tin: string;
  first_name: string;
  middle_name: string;
  last_name: string;
  full_name: string;
  display_name: string;
  nationality: string;
  gender: string;
  date_of_birth: string;
  tax_payer_level: string;
  tax_payer_level_name: string;
  business_sector: string;
  business_sector_name: string;
  business_sub_sector: string;
  business_sub_sector_name: string;
  business_registration_date: string;
  business_name: string;
  business_license_number: string;
  phone: string;
  phone_secondary: string;
  email: string;
  subcity: string;
  subcity_name: string;
  kebele: string;
  kebele_name: string;
  house_number: string;
  street_address: string;
  postal_code: string;
  location_display: string;
  profile_picture?: string;
  tax_file?: string;
  tax_file_name?: string;
  is_active: boolean;
  registration_date: string;
  last_updated: string;
}

export interface OrganizationTaxPayer {
  id: string;
  tin: string;
  business_name: string;
  trade_name: string;
  display_name: string;
  organization_business_type: string;
  organization_business_type_name: string;
  tax_payer_level: string;
  tax_payer_level_name: string;
  business_sector: string;
  business_sector_name: string;
  business_sub_sector: string;
  business_sub_sector_name: string;
  business_registration_date: string;
  business_license_number: string;
  capital_amount?: number;
  number_of_employees?: number;
  manager_first_name: string;
  manager_middle_name: string;
  manager_last_name: string;
  manager_title: string;
  manager_full_name: string;
  vat_registration_date?: string;
  vat_number?: string;
  phone: string;
  phone_secondary: string;
  email: string;
  subcity: string;
  subcity_name: string;
  kebele: string;
  kebele_name: string;
  house_number: string;
  street_address: string;
  postal_code: string;
  location_display: string;
  tax_file?: string;
  tax_file_name?: string;
  is_active: boolean;
  registration_date: string;
  last_updated: string;
}

export interface BusinessSectorCreate {
  code: string;
  name: string;
  description?: string;
}

export interface BusinessSubSectorCreate {
  business_sector: string;
  code: string;
  name: string;
  description?: string;
}

export interface IndividualTaxPayerCreate {
  tin: string;
  first_name: string;
  middle_name?: string;
  last_name: string;
  nationality: string;
  gender: string;
  date_of_birth: string;
  tax_payer_level: string;
  business_sector: string;
  business_sub_sector: string;
  business_registration_date: string;
  business_name?: string;
  business_license_number?: string;
  phone: string;
  phone_secondary?: string;
  email?: string;
  subcity?: string;
  kebele?: string;
  house_number?: string;
  street_address?: string;
  postal_code?: string;
  profile_picture?: File;
  tax_file?: string;
}

export interface OrganizationTaxPayerCreate {
  tin: string;
  business_name: string;
  trade_name?: string;
  organization_business_type: string;
  tax_payer_level: string;
  business_sector: string;
  business_sub_sector: string;
  business_registration_date: string;
  business_license_number?: string;
  capital_amount?: number;
  number_of_employees?: number;
  manager_first_name: string;
  manager_middle_name?: string;
  manager_last_name: string;
  manager_title?: string;
  vat_registration_date?: string;
  vat_number?: string;
  phone: string;
  phone_secondary?: string;
  email?: string;
  subcity?: string;
  kebele?: string;
  house_number?: string;
  street_address?: string;
  postal_code?: string;
  tax_file?: string;
}

export interface ListResponse<T> {
  count: number;
  next?: string;
  previous?: string;
  results: T[];
}

export interface TaxPayerStatistics {
  individual: {
    total: number;
    by_level: Array<{ tax_payer_level__name: string; tax_payer_level__code: string; count: number }>;
    by_sector: Array<{ business_sector__name: string; business_sector__code: string; count: number }>;
    by_gender: Array<{ gender: string; count: number }>;
  };
  organization: {
    total: number;
    by_level: Array<{ tax_payer_level__name: string; tax_payer_level__code: string; count: number }>;
    by_sector: Array<{ business_sector__name: string; business_sector__code: string; count: number }>;
    by_type: Array<{ organization_business_type__name: string; organization_business_type__code: string; count: number }>;
    with_vat: number;
  };
  overall: {
    total_taxpayers: number;
    total_sectors: number;
    total_sub_sectors: number;
  };
}

export interface SearchResult {
  id: string;
  type: 'individual' | 'organization';
  tin: string;
  name: string;
  business_name?: string;
  trade_name?: string;
  vat_number?: string;
  sector: string;
  level: string;
}

class TaxPayerService {
  private baseUrl = '/taxpayers';

  // Business Sectors
  async getBusinessSectors(params?: { search?: string; ordering?: string }): Promise<ListResponse<BusinessSector>> {
    const response = await apiClient.get(`${this.baseUrl}/business-sectors/`, { params });
    return response.data;
  }

  async getBusinessSector(id: string): Promise<BusinessSector> {
    const response = await apiClient.get(`${this.baseUrl}/business-sectors/${id}/`);
    return response.data;
  }

  async createBusinessSector(data: BusinessSectorCreate): Promise<BusinessSector> {
    const response = await apiClient.post(`${this.baseUrl}/business-sectors/`, data);
    return response.data;
  }

  async updateBusinessSector(id: string, data: Partial<BusinessSectorCreate>): Promise<BusinessSector> {
    const response = await apiClient.patch(`${this.baseUrl}/business-sectors/${id}/`, data);
    return response.data;
  }

  async deleteBusinessSector(id: string): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/business-sectors/${id}/`);
  }

  async getBusinessSectorsSimple(): Promise<BusinessSector[]> {
    const response = await apiClient.get(`${this.baseUrl}/business-sectors/simple/`);
    return response.data;
  }

  // Business Sub-Sectors
  async getBusinessSubSectors(params?: { 
    business_sector?: string; 
    search?: string; 
    ordering?: string 
  }): Promise<ListResponse<BusinessSubSector>> {
    const response = await apiClient.get(`${this.baseUrl}/business-sub-sectors/`, { params });
    return response.data;
  }

  async getBusinessSubSector(id: string): Promise<BusinessSubSector> {
    const response = await apiClient.get(`${this.baseUrl}/business-sub-sectors/${id}/`);
    return response.data;
  }

  async createBusinessSubSector(data: BusinessSubSectorCreate): Promise<BusinessSubSector> {
    const response = await apiClient.post(`${this.baseUrl}/business-sub-sectors/`, data);
    return response.data;
  }

  async updateBusinessSubSector(id: string, data: Partial<BusinessSubSectorCreate>): Promise<BusinessSubSector> {
    const response = await apiClient.patch(`${this.baseUrl}/business-sub-sectors/${id}/`, data);
    return response.data;
  }

  async deleteBusinessSubSector(id: string): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/business-sub-sectors/${id}/`);
  }

  async getBusinessSubSectorsSimple(businessSector?: string): Promise<BusinessSubSector[]> {
    const params = businessSector ? { business_sector: businessSector } : {};
    const response = await apiClient.get(`${this.baseUrl}/business-sub-sectors/simple/`, { params });
    return response.data;
  }

  // Tax Payer Levels
  async getTaxPayerLevels(): Promise<ListResponse<TaxPayerLevel>> {
    const response = await apiClient.get(`${this.baseUrl}/tax-payer-levels/`);
    return response.data;
  }

  async getTaxPayerLevelsSimple(): Promise<TaxPayerLevel[]> {
    const response = await apiClient.get(`${this.baseUrl}/tax-payer-levels/simple/`);
    return response.data;
  }

  // Organization Business Types
  async getOrganizationBusinessTypes(): Promise<ListResponse<OrganizationBusinessType>> {
    const response = await apiClient.get(`${this.baseUrl}/organization-business-types/`);
    return response.data;
  }

  async getOrganizationBusinessTypesSimple(): Promise<OrganizationBusinessType[]> {
    const response = await apiClient.get(`${this.baseUrl}/organization-business-types/simple/`);
    return response.data;
  }

  // Individual Tax Payers
  async getIndividualTaxPayers(params?: {
    page?: number;
    search?: string;
    tax_payer_level?: string;
    business_sector?: string;
    business_sub_sector?: string;
    nationality?: string;
    gender?: string;
    subcity?: string;
    kebele?: string;
    ordering?: string;
  }): Promise<ListResponse<IndividualTaxPayer>> {
    const response = await apiClient.get(`${this.baseUrl}/individuals/`, { params });
    return response.data;
  }

  async getIndividualTaxPayer(id: string): Promise<IndividualTaxPayer> {
    const response = await apiClient.get(`${this.baseUrl}/individuals/${id}/`);
    return response.data;
  }

  async createIndividualTaxPayer(data: IndividualTaxPayerCreate): Promise<IndividualTaxPayer> {
    const formData = new FormData();
    
    // Add all fields to FormData
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (value instanceof File) {
          formData.append(key, value);
        } else {
          formData.append(key, value.toString());
        }
      }
    });

    const response = await apiClient.post(`${this.baseUrl}/individuals/`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  async updateIndividualTaxPayer(id: string, data: Partial<IndividualTaxPayerCreate>): Promise<IndividualTaxPayer> {
    const formData = new FormData();
    
    // Add all fields to FormData
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (value instanceof File) {
          formData.append(key, value);
        } else {
          formData.append(key, value.toString());
        }
      }
    });

    const response = await apiClient.patch(`${this.baseUrl}/individuals/${id}/`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  async deleteIndividualTaxPayer(id: string): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/individuals/${id}/`);
  }

  // Organization Tax Payers
  async getOrganizationTaxPayers(params?: {
    page?: number;
    search?: string;
    tax_payer_level?: string;
    business_sector?: string;
    business_sub_sector?: string;
    organization_business_type?: string;
    subcity?: string;
    kebele?: string;
    ordering?: string;
  }): Promise<ListResponse<OrganizationTaxPayer>> {
    const response = await apiClient.get(`${this.baseUrl}/organizations/`, { params });
    return response.data;
  }

  async getOrganizationTaxPayer(id: string): Promise<OrganizationTaxPayer> {
    const response = await apiClient.get(`${this.baseUrl}/organizations/${id}/`);
    return response.data;
  }

  async createOrganizationTaxPayer(data: OrganizationTaxPayerCreate): Promise<OrganizationTaxPayer> {
    const response = await apiClient.post(`${this.baseUrl}/organizations/`, data);
    return response.data;
  }

  async updateOrganizationTaxPayer(id: string, data: Partial<OrganizationTaxPayerCreate>): Promise<OrganizationTaxPayer> {
    const response = await apiClient.patch(`${this.baseUrl}/organizations/${id}/`, data);
    return response.data;
  }

  async deleteIndividualTaxPayer(id: string): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/individuals/${id}/`);
  }

  async deleteOrganizationTaxPayer(id: string): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/organizations/${id}/`);
  }

  // Business closure operations
  async closeIndividualBusiness(id: string, closureData: { closure_date: string; closure_reason: string }): Promise<{ message: string }> {
    const response = await apiClient.post(`${this.baseUrl}/individuals/${id}/close-business/`, closureData);
    return response.data;
  }

  async reopenIndividualBusiness(id: string): Promise<{ message: string }> {
    const response = await apiClient.post(`${this.baseUrl}/individuals/${id}/reopen-business/`);
    return response.data;
  }

  async closeOrganizationBusiness(id: string, closureData: { closure_date: string; closure_reason: string }): Promise<{ message: string }> {
    const response = await apiClient.post(`${this.baseUrl}/organizations/${id}/close-business/`, closureData);
    return response.data;
  }

  async reopenOrganizationBusiness(id: string): Promise<{ message: string }> {
    const response = await apiClient.post(`${this.baseUrl}/organizations/${id}/reopen-business/`);
    return response.data;
  }

  async getAnalytics(): Promise<any> {
    const response = await apiClient.get(`${this.baseUrl}/analytics/`);
    return response.data;
  }

  // Income Analysis Operations
  async getIncomeAnalyses(params?: any): Promise<any> {
    const response = await apiClient.get(`${this.baseUrl}/income-analyses/`, { params });
    return response.data;
  }

  async createIncomeAnalysis(data: any): Promise<any> {
    const response = await apiClient.post(`${this.baseUrl}/income-analyses/`, data);
    return response.data;
  }

  async getIncomeAnalysis(id: string): Promise<any> {
    const response = await apiClient.get(`${this.baseUrl}/income-analyses/${id}/`);
    return response.data;
  }

  async updateIncomeAnalysis(id: string, data: any): Promise<any> {
    const response = await apiClient.patch(`${this.baseUrl}/income-analyses/${id}/`, data);
    return response.data;
  }

  async deleteIncomeAnalysis(id: string): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/income-analyses/${id}/`);
  }

  async approveLevelUpgrade(id: string): Promise<{ message: string }> {
    const response = await apiClient.post(`${this.baseUrl}/income-analyses/${id}/approve/`);
    return response.data;
  }

  async rejectLevelUpgrade(id: string, reviewNotes?: string): Promise<{ message: string }> {
    const response = await apiClient.post(`${this.baseUrl}/income-analyses/${id}/reject/`, {
      review_notes: reviewNotes || ''
    });
    return response.data;
  }

  // Level Upgrade Notifications
  async getUpgradeNotifications(params?: any): Promise<any> {
    const response = await apiClient.get(`${this.baseUrl}/upgrade-notifications/`, { params });
    return response.data;
  }

  async markNotificationRead(id: string): Promise<{ message: string }> {
    const response = await apiClient.post(`${this.baseUrl}/upgrade-notifications/${id}/read/`);
    return response.data;
  }

  async dismissNotification(id: string): Promise<{ message: string }> {
    const response = await apiClient.post(`${this.baseUrl}/upgrade-notifications/${id}/dismiss/`);
    return response.data;
  }

  // Dashboard Statistics
  async getLevelUpgradeStats(): Promise<any> {
    const response = await apiClient.get(`${this.baseUrl}/level-upgrade-stats/`);
    return response.data;
  }

  // Tax Payer Levels CRUD
  async createTaxPayerLevel(data: any): Promise<TaxPayerLevel> {
    const response = await apiClient.post(`${this.baseUrl}/tax-payer-levels/`, data);
    return response.data;
  }

  async updateTaxPayerLevel(id: string, data: any): Promise<TaxPayerLevel> {
    const response = await apiClient.patch(`${this.baseUrl}/tax-payer-levels/${id}/`, data);
    return response.data;
  }

  async deleteTaxPayerLevel(id: string): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/tax-payer-levels/${id}/`);
  }

  // Organization Business Types CRUD
  async createOrganizationBusinessType(data: any): Promise<OrganizationBusinessType> {
    const response = await apiClient.post(`${this.baseUrl}/organization-business-types/`, data);
    return response.data;
  }

  async updateOrganizationBusinessType(id: string, data: any): Promise<OrganizationBusinessType> {
    const response = await apiClient.patch(`${this.baseUrl}/organization-business-types/${id}/`, data);
    return response.data;
  }

  async deleteOrganizationBusinessType(id: string): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/organization-business-types/${id}/`);
  }



  // Statistics and Search
  async getStatistics(): Promise<TaxPayerStatistics> {
    const response = await apiClient.get(`${this.baseUrl}/statistics/`);
    return response.data;
  }

  async searchTaxPayers(query: string): Promise<{ results: SearchResult[] }> {
    const response = await apiClient.get(`${this.baseUrl}/search/`, { params: { q: query } });
    return response.data;
  }
}

export default new TaxPayerService();
