import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  InputAdornment,
  Typography,
  Menu,
  Alert,
  Grid,
} from '@mui/material';
import {
  Add,
  Search,
  Edit,
  Delete,
  MoreVert,
  Business,
  LocationOn,
  Public,
} from '@mui/icons-material';
import { useNotification } from '../../../contexts/NotificationContext';
import locationHierarchyService from '../../../services/locationHierarchyService';
import type {
  Zone,
  ZoneCreate,
  RegionSelect
} from '../../../services/locationHierarchyService';

interface ZonesTabProps {
  onStatsChange: () => void;
}

const ZonesTab: React.FC<ZonesTabProps> = ({ onStatsChange }) => {
  const { showSuccess, showError } = useNotification();

  const [zones, setZones] = useState<Zone[]>([]);
  const [regions, setRegions] = useState<RegionSelect[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRegion, setSelectedRegion] = useState<number | ''>('');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingZone, setEditingZone] = useState<Zone | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedZone, setSelectedZone] = useState<Zone | null>(null);
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);

  const [formData, setFormData] = useState<ZoneCreate>({
    region: 0,
    name: '',
    code: '',
    population: undefined,
    area_km2: undefined,
    is_active: true,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    loadRegions();
    loadZones();
  }, []);

  useEffect(() => {
    loadZones();
  }, [searchTerm, selectedRegion]);

  const loadRegions = async () => {
    try {
      const response = await locationHierarchyService.getRegionsSelect();
      setRegions(response);
    } catch (error) {
      showError('Failed to load regions');
    }
  };

  const loadZones = async () => {
    try {
      setLoading(true);
      const response = await locationHierarchyService.getZones({
        search: searchTerm,
        region: selectedRegion || undefined,
        page_size: 100,
      });
      setZones(response.results);
    } catch (error) {
      showError('Failed to load zones');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (zone?: Zone) => {
    if (zone) {
      setEditingZone(zone);
      setFormData({
        region: zone.region,
        name: zone.name,
        code: zone.code,
        population: zone.population,
        area_km2: zone.area_km2,
        is_active: zone.is_active,
      });
    } else {
      setEditingZone(null);
      setFormData({
        region: selectedRegion as number || 2,
        name: '',
        code: '',
        population: undefined,
        area_km2: undefined,
        is_active: true,
      });
    }
    setErrors({});
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingZone(null);
    setErrors({});
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.region) {
      newErrors.region = 'Region is required';
    }
    if (!formData.name.trim()) {
      newErrors.name = 'Zone name is required';
    }
    if (!formData.code.trim()) {
      newErrors.code = 'Zone code is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      if (editingZone) {
        await locationHierarchyService.updateZone(editingZone.id, formData);
        showSuccess('Zone updated successfully');
      } else {
        await locationHierarchyService.createZone(formData);
        showSuccess('Zone created successfully');
      }

      handleCloseDialog();
      loadZones();
      onStatsChange();
    } catch (error: any) {
      showError(error.response?.data?.detail || 'Failed to save zone');
    }
  };

  const handleDelete = async () => {
    if (!selectedZone) return;

    try {
      await locationHierarchyService.deleteZone(selectedZone.id);
      showSuccess('Zone deleted successfully');
      setDeleteDialogOpen(false);
      setSelectedZone(null);
      loadZones();
      onStatsChange();
    } catch (error: any) {
      showError(error.response?.data?.detail || 'Failed to delete zone');
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, zone: Zone) => {
    setMenuAnchor(event.currentTarget);
    setSelectedZone(zone);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
    setSelectedZone(null);
  };

  const handleInputChange = (field: keyof ZoneCreate) => (
    event: React.ChangeEvent<HTMLInputElement> | { target: { value: unknown } }
  ) => {
    const value = event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">Zones Management</Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => handleOpenDialog()}
          disabled={regions.length === 0}
        >
          Add Zone
        </Button>
      </Box>

      {regions.length === 0 && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          No regions available. Please create regions first before adding zones.
        </Alert>
      )}

      {/* Search and Filters */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid size={{ xs: 12, md: 4 }}>
          <TextField
            fullWidth
            placeholder="Search zones..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
          />
        </Grid>
        <Grid size={{ xs: 12, md: 4 }}>
          <FormControl fullWidth>
            <InputLabel>Filter by Region</InputLabel>
            <Select
              value={selectedRegion}
              onChange={(e) => setSelectedRegion(e.target.value as number)}
              label="Filter by Region"
            >
              <MenuItem value="">All Regions</MenuItem>
              {regions.map((region) => (
                <MenuItem key={region.id} value={region.id}>
                  {region.name} ({region.code})
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
      </Grid>

      {/* Zones Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>Code</TableCell>
              <TableCell>Region</TableCell>
              <TableCell>Population</TableCell>
              <TableCell>Cities</TableCell>
              <TableCell>Status</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {zones.map((zone) => (
              <TableRow key={zone.id}>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Business fontSize="small" />
                    {zone.name}
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip label={zone.code} size="small" />
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <LocationOn fontSize="small" />
                    {zone.region_name}
                  </Box>
                </TableCell>
                <TableCell>
                  {zone.population ? zone.population.toLocaleString() : '-'}
                </TableCell>
                <TableCell>
                  <Chip label={zone.city_count} size="small" color="primary" />
                </TableCell>
                <TableCell>
                  <Chip
                    label={zone.is_active ? 'Active' : 'Inactive'}
                    color={zone.is_active ? 'success' : 'default'}
                    size="small"
                  />
                </TableCell>
                <TableCell align="right">
                  <IconButton
                    onClick={(e) => handleMenuOpen(e, zone)}
                    size="small"
                  >
                    <MoreVert />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Action Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
      >
        <MenuItem
          onClick={() => {
            handleOpenDialog(selectedZone!);
            handleMenuClose();
          }}
        >
          <Edit fontSize="small" sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        <MenuItem
          onClick={() => {
            setDeleteDialogOpen(true);
            handleMenuClose();
          }}
          sx={{ color: 'error.main' }}
        >
          <Delete fontSize="small" sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>

      {/* Create/Edit Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingZone ? 'Edit Zone' : 'Add New Zone'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required error={!!errors.region}>
                <InputLabel>Region</InputLabel>
                <Select
                  value={formData.region || ''}
                  onChange={handleInputChange('region')}
                  label="Region"
                >
                  {regions.map((region) => (
                    <MenuItem key={region.id} value={region.id}>
                      {region.name} ({region.code})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Zone Name"
                value={formData.name}
                onChange={handleInputChange('name')}
                error={!!errors.name}
                helperText={errors.name}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Zone Code"
                value={formData.code}
                onChange={handleInputChange('code')}
                error={!!errors.code}
                helperText={errors.code}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Population"
                type="number"
                value={formData.population || ''}
                onChange={handleInputChange('population')}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Area (km²)"
                type="number"
                value={formData.area_km2 || ''}
                onChange={handleInputChange('area_km2')}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.is_active}
                    onChange={handleInputChange('is_active')}
                  />
                }
                label="Active"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSubmit} variant="contained">
            {editingZone ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 2 }}>
            Are you sure you want to delete "{selectedZone?.name}"? This action cannot be undone.
          </Alert>
          {selectedZone && selectedZone.city_count > 0 && (
            <Alert severity="error">
              This zone has {selectedZone.city_count} cities.
              Please delete or reassign them first.
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleDelete}
            color="error"
            variant="contained"
            disabled={selectedZone ? selectedZone.city_count > 0 : false}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ZonesTab;
