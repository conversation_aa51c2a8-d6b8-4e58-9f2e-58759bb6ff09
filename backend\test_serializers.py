#!/usr/bin/env python
"""
Test script to verify the serializers work correctly
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'arada_dms.settings')
django.setup()

from locations.models import Building, Shelf, Box, Kent, File
from locations.serializers import (
    BuildingSerializer, ShelfSerializer, BoxSerializer, 
    KentSerializer, FileSerializer
)

def test_serializers():
    print("Testing serializers for the new hierarchy")
    print("=" * 60)
    
    # Test Building serializer
    print("1. Testing Building Serializer")
    buildings = Building.objects.all()[:1]
    if buildings:
        building = buildings[0]
        serializer = BuildingSerializer(building)
        data = serializer.data
        print(f"   Building: {data['name']} ({data['code']})")
        print(f"   Shelf count: {data['shelf_count']}")
        print(f"   Kent count: {data['kent_count']}")
        print("   ✅ Building serializer working")
    else:
        print("   ❌ No buildings found")
    
    print()
    
    # Test Shelf serializer
    print("2. Testing Shelf Serializer")
    shelves = Shelf.objects.all()[:1]
    if shelves:
        shelf = shelves[0]
        serializer = ShelfSerializer(shelf)
        data = serializer.data
        print(f"   Shelf: {data['name']} ({data['code']})")
        print(f"   Grid: {data.get('rows', 'N/A')}x{data.get('columns', 'N/A')}")
        print(f"   Box count: {data.get('box_count', 'N/A')}")
        print(f"   Kent count: {data['kent_count']}")
        print("   ✅ Shelf serializer working")
    else:
        print("   ❌ No shelves found")
    
    print()
    
    # Test Box serializer
    print("3. Testing Box Serializer")
    boxes = Box.objects.all()[:1]
    if boxes:
        box = boxes[0]
        serializer = BoxSerializer(box)
        data = serializer.data
        print(f"   Box: {data['position_code']} - {data.get('name', 'Unnamed')}")
        print(f"   Position: Row {data['row']}, Column {data['column']}")
        print(f"   Full code: {data['full_code']}")
        print(f"   Kent count: {data['kent_count']}")
        print(f"   File count: {data['file_count']}")
        print(f"   Document count: {data['document_count']}")
        print("   ✅ Box serializer working")
    else:
        print("   ❌ No boxes found")
    
    print()
    
    # Test Kent serializer
    print("4. Testing Kent Serializer")
    kents = Kent.objects.all()[:1]
    if kents:
        kent = kents[0]
        serializer = KentSerializer(kent)
        data = serializer.data
        print(f"   Kent: {data['name']} ({data['code']})")
        print(f"   Box position: {data['box_position']}")
        print(f"   Full code: {data['full_code']}")
        print(f"   Location path: {data['location_path']}")
        print(f"   File count: {data['file_count']}")
        print(f"   Document count: {data['document_count']}")
        print("   ✅ Kent serializer working")
    else:
        print("   ❌ No kents found")
    
    print()
    
    # Test File serializer
    print("5. Testing File Serializer")
    files = File.objects.all()[:1]
    if files:
        file_obj = files[0]
        serializer = FileSerializer(file_obj)
        data = serializer.data
        print(f"   File: {data['name']} ({data['file_number']})")
        print(f"   Full code: {data['full_code']}")
        print(f"   Location path: {data['location_path']}")
        print(f"   Document count: {data['document_count']}")
        print("   ✅ File serializer working")
    else:
        print("   ❌ No files found")
    
    print()
    
    # Test relationships
    print("6. Testing Relationships")
    
    # Building -> Shelves -> Boxes -> Kents -> Files
    for building in Building.objects.all()[:1]:
        print(f"   Building: {building.name}")
        
        for shelf in building.shelves.all()[:1]:
            print(f"     Shelf: {shelf.name} ({shelf.rows}x{shelf.columns})")
            
            for box in shelf.boxes.all()[:2]:
                print(f"       Box: {box.position_code}")
                
                for kent in box.kents.all()[:1]:
                    print(f"         Kent: {kent.name}")
                    
                    for file_obj in kent.files.all()[:1]:
                        print(f"           File: {file_obj.name}")
                        print(f"           Documents: {file_obj.get_document_count()}")
    
    print()
    print("✅ Serializer testing completed!")

if __name__ == '__main__':
    test_serializers()
