# Generated by Django 5.2.3 on 2025-07-14 20:09

import datetime
import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("locations", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Organization",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Official organization name",
                        max_length=200,
                        unique=True,
                    ),
                ),
                (
                    "short_name",
                    models.Char<PERSON>ield(
                        help_text="Abbreviated organization name",
                        max_length=50,
                        unique=True,
                    ),
                ),
                (
                    "logo",
                    models.ImageField(
                        blank=True,
                        help_text="Organization logo",
                        null=True,
                        upload_to="organizations/logos/",
                    ),
                ),
                (
                    "motto",
                    models.Char<PERSON>ield(
                        blank=True,
                        help_text="Organization motto or mission statement",
                        max_length=500,
                        null=True,
                    ),
                ),
                (
                    "tagline",
                    models.CharField(
                        blank=True,
                        help_text="Brief tagline or slogan",
                        max_length=200,
                        null=True,
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Detailed description of the organization",
                        null=True,
                    ),
                ),
                (
                    "website",
                    models.URLField(
                        blank=True, help_text="Organization website URL", null=True
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        blank=True,
                        help_text="Primary contact email",
                        max_length=254,
                        null=True,
                    ),
                ),
                (
                    "phone",
                    models.CharField(
                        blank=True,
                        help_text="Primary contact phone",
                        max_length=15,
                        null=True,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.",
                                regex="^\\+?1?\\d{9,15}$",
                            )
                        ],
                    ),
                ),
                (
                    "fax",
                    models.CharField(
                        blank=True,
                        help_text="Fax number",
                        max_length=15,
                        null=True,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Fax number must be entered in the format: '+999999999'. Up to 15 digits allowed.",
                                regex="^\\+?1?\\d{9,15}$",
                            )
                        ],
                    ),
                ),
                (
                    "address_line1",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "address_line2",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("postal_code", models.CharField(blank=True, max_length=20, null=True)),
                (
                    "office_hours_start",
                    models.TimeField(
                        default=datetime.time(8, 0), help_text="Office opening time"
                    ),
                ),
                (
                    "office_hours_end",
                    models.TimeField(
                        default=datetime.time(17, 0), help_text="Office closing time"
                    ),
                ),
                (
                    "established_date",
                    models.DateField(
                        blank=True,
                        help_text="Date when the organization was established",
                        null=True,
                    ),
                ),
                (
                    "registration_number",
                    models.CharField(
                        blank=True,
                        help_text="Official registration number",
                        max_length=100,
                        null=True,
                    ),
                ),
                (
                    "tax_id",
                    models.CharField(
                        blank=True,
                        help_text="Tax identification number",
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "license_number",
                    models.CharField(
                        blank=True,
                        help_text="Business license number",
                        max_length=100,
                        null=True,
                    ),
                ),
                (
                    "primary_color",
                    models.CharField(
                        default="#1976d2",
                        help_text="Primary brand color (hex format)",
                        max_length=7,
                    ),
                ),
                (
                    "secondary_color",
                    models.CharField(
                        default="#1565c0",
                        help_text="Secondary brand color (hex format)",
                        max_length=7,
                    ),
                ),
                (
                    "accent_color",
                    models.CharField(
                        default="#ff9800",
                        help_text="Accent brand color (hex format)",
                        max_length=7,
                    ),
                ),
                (
                    "social_media",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Social media links (JSON format)",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, help_text="Whether this organization is active"
                    ),
                ),
                (
                    "is_default",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this is the default organization for system branding",
                    ),
                ),
                (
                    "document_retention_days",
                    models.PositiveIntegerField(
                        default=2555,
                        help_text="Default document retention period in days",
                    ),
                ),
                (
                    "max_file_size_mb",
                    models.PositiveIntegerField(
                        default=50, help_text="Maximum file upload size in MB"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "city",
                    models.ForeignKey(
                        blank=True,
                        help_text="City from location hierarchy",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="organizations",
                        to="locations.city",
                    ),
                ),
                (
                    "country",
                    models.ForeignKey(
                        blank=True,
                        help_text="Country from location hierarchy",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="organizations",
                        to="locations.country",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_organizations",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "kebele",
                    models.ForeignKey(
                        blank=True,
                        help_text="Kebele from location hierarchy",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="organizations",
                        to="locations.kebele",
                    ),
                ),
                (
                    "state_province",
                    models.ForeignKey(
                        blank=True,
                        help_text="Region/State/Province from location hierarchy",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="organizations",
                        to="locations.region",
                    ),
                ),
                (
                    "subcity",
                    models.ForeignKey(
                        blank=True,
                        help_text="SubCity/Woreda from location hierarchy",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="organizations",
                        to="locations.subcity",
                    ),
                ),
            ],
            options={
                "verbose_name": "Organization",
                "verbose_name_plural": "Organizations",
                "db_table": "organizations_organization",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="Department",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                (
                    "code",
                    models.CharField(
                        help_text="Department code (e.g., HR, FIN, IT)", max_length=10
                    ),
                ),
                ("description", models.TextField(blank=True, null=True)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "head",
                    models.ForeignKey(
                        blank=True,
                        help_text="Department head/manager",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="headed_departments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="departments",
                        to="organizations.organization",
                    ),
                ),
            ],
            options={
                "verbose_name": "Department",
                "verbose_name_plural": "Departments",
                "db_table": "organizations_department",
                "ordering": ["organization", "name"],
                "unique_together": {("organization", "code")},
            },
        ),
    ]
