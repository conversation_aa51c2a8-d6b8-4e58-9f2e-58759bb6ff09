import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  TextField,
  FormControlLabel,
  Switch,
  InputAdornment,
  Avatar,
  Divider,
  Stack,
  Fade,
  Alert,
  CircularProgress,
  IconButton,
  Breadcrumbs,
  Link,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TablePagination,
} from '@mui/material';
import {
  Public,
  Flag,
  Phone,
  AttachMoney,
  ArrowBack,
  Add,
  Edit,
  Delete,
  Visibility,
  CheckCircle,
  Cancel,
  Home,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useNotification } from '../../contexts/NotificationContext';
import locationHierarchyService from '../../services/locationHierarchyService';
import { normalizeCode, createCodeBlurHandler } from '../../utils/codeUtils';
import ConfirmationDialog from '../../components/common/ConfirmationDialog';

interface Country {
  id: number;
  name: string;
  code: string;
  phone_code?: string;
  currency?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

const CountriesPage: React.FC = () => {
  const navigate = useNavigate();
  const { showNotification } = useNotification();

  const [countries, setCountries] = useState<Country[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingCountry, setEditingCountry] = useState<Country | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    phone_code: '',
    currency: '',
    is_active: true,
  });
  const [formErrors, setFormErrors] = useState<any>({});
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [countryToDelete, setCountryToDelete] = useState<Country | null>(null);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    loadCountries();
  }, [page, rowsPerPage]);

  const loadCountries = async () => {
    try {
      setLoading(true);
      const response = await locationHierarchyService.getCountries({
        page: page + 1,
        page_size: rowsPerPage,
      });
      setCountries(response.results);
      setTotalCount(response.count);
    } catch (error) {
      console.error('Error loading countries:', error);
      showNotification('Failed to load countries', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setSubmitting(true);
      setFormErrors({});

      if (editingCountry) {
        await locationHierarchyService.updateCountry(editingCountry.id, formData);
        showNotification('Country updated successfully', 'success');
      } else {
        await locationHierarchyService.createCountry(formData);
        showNotification('Country created successfully', 'success');
      }

      resetForm();
      loadCountries();
    } catch (error: any) {
      console.error('Error submitting form:', error);
      if (error.response?.data) {
        setFormErrors(error.response.data);
      }
      showNotification('Failed to save country', 'error');
    } finally {
      setSubmitting(false);
    }
  };

  const handleEdit = (country: Country) => {
    setEditingCountry(country);
    setFormData({
      name: country.name,
      code: country.code,
      phone_code: country.phone_code || '',
      currency: country.currency || '',
      is_active: country.is_active,
    });
    setShowForm(true);
  };

  const handleDelete = (country: Country) => {
    setCountryToDelete(country);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!countryToDelete) return;

    try {
      setDeleting(true);
      await locationHierarchyService.deleteCountry(countryToDelete.id);
      showNotification('Country deleted successfully', 'success');
      loadCountries();
      handleCloseDeleteDialog();
    } catch (error) {
      console.error('Error deleting country:', error);
      showNotification('Failed to delete country', 'error');
    } finally {
      setDeleting(false);
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setCountryToDelete(null);
    setDeleting(false);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      code: '',
      phone_code: '',
      currency: '',
      is_active: true,
    });
    setFormErrors({});
    setEditingCountry(null);
    setShowForm(false);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Fade in timeout={800}>
        <Box sx={{ mb: 4 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs sx={{ mb: 2 }}>
            <Link
              component="button"
              variant="body1"
              onClick={() => navigate('/locations/hierarchy')}
              sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
            >
              <Home fontSize="small" />
              Location Hierarchy
            </Link>
            <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <Public fontSize="small" />
              Countries
            </Typography>
          </Breadcrumbs>

          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'between', mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <IconButton onClick={() => navigate('/locations/hierarchy')} sx={{ mr: 1 }}>
                <ArrowBack />
              </IconButton>
              <Avatar sx={{ bgcolor: 'primary.main' }}>
                <Public />
              </Avatar>
              <Box>
                <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
                  Countries Management
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Manage countries in the location hierarchy
                </Typography>
              </Box>
            </Box>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setShowForm(true)}
              size="large"
              sx={{ ml: 'auto' }}
            >
              Add Country
            </Button>
          </Box>
        </Box>
      </Fade>

      {/* Form Section */}
      {showForm && (
        <Fade in timeout={600}>
          <Card sx={{ mb: 4 }}>
            <CardContent sx={{ p: 4 }}>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                {editingCountry ? 'Edit Country' : 'Add New Country'}
              </Typography>
              <Divider sx={{ mb: 3 }} />
              
              <form onSubmit={handleSubmit}>
                <Stack spacing={3}>
                  <TextField
                    label="Country Name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    error={!!formErrors.name}
                    helperText={formErrors.name || 'Enter the full country name'}
                    fullWidth
                    required
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Public color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />
                  
                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                    <TextField
                      label="Country Code"
                      value={formData.code}
                      onChange={(e) => setFormData({ ...formData, code: e.target.value })}
                      onBlur={createCodeBlurHandler(
                        (field, value) => setFormData(prev => ({ ...prev, [field]: value })),
                        'code'
                      )}
                      error={!!formErrors.code}
                      helperText={formErrors.code || 'Code will be automatically converted to uppercase (e.g., ET, US, GB)'}
                      required
                      inputProps={{ maxLength: 3 }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Flag color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                    
                    <TextField
                      label="Phone Code"
                      value={formData.phone_code}
                      onChange={(e) => setFormData({ ...formData, phone_code: e.target.value })}
                      error={!!formErrors.phone_code}
                      helperText={formErrors.phone_code || 'e.g., +251, +1, +44'}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Phone color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>

                  <TextField
                    label="Currency Code"
                    value={formData.currency}
                    onChange={(e) => setFormData({ ...formData, currency: e.target.value.toUpperCase() })}
                    error={!!formErrors.currency}
                    helperText={formErrors.currency || 'e.g., ETB, USD, GBP'}
                    inputProps={{ maxLength: 3 }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <AttachMoney color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />

                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.is_active}
                        onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                        color="primary"
                      />
                    }
                    label="Active Status"
                  />

                  <Box sx={{ display: 'flex', gap: 2, pt: 2 }}>
                    <Button
                      type="submit"
                      variant="contained"
                      disabled={submitting}
                      startIcon={submitting ? <CircularProgress size={20} /> : <Add />}
                      size="large"
                    >
                      {submitting ? 'Saving...' : editingCountry ? 'Update Country' : 'Create Country'}
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={resetForm}
                      size="large"
                    >
                      Cancel
                    </Button>
                  </Box>
                </Stack>
              </form>
            </CardContent>
          </Card>
        </Fade>
      )}

      {/* Countries Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
            Countries List
          </Typography>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : countries.length === 0 ? (
            <Alert severity="info">
              No countries found. Click "Add Country" to create your first country.
            </Alert>
          ) : (
            <>
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Country</TableCell>
                      <TableCell>Code</TableCell>
                      <TableCell>Phone Code</TableCell>
                      <TableCell>Currency</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Created</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {countries.map((country) => (
                      <TableRow key={country.id} hover>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <Avatar sx={{ bgcolor: 'primary.main', width: 32, height: 32 }}>
                              <Public fontSize="small" />
                            </Avatar>
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {country.name}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip label={country.code} size="small" color="primary" />
                        </TableCell>
                        <TableCell>{country.phone_code || '-'}</TableCell>
                        <TableCell>{country.currency || '-'}</TableCell>
                        <TableCell>
                          <Chip
                            label={country.is_active ? 'Active' : 'Inactive'}
                            color={country.is_active ? 'success' : 'error'}
                            size="small"
                            icon={country.is_active ? <CheckCircle /> : <Cancel />}
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {new Date(country.created_at).toLocaleDateString()}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                            <IconButton
                              size="small"
                              onClick={() => handleEdit(country)}
                              color="primary"
                            >
                              <Edit fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleDelete(country)}
                              color="error"
                            >
                              <Delete fontSize="small" />
                            </IconButton>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              <TablePagination
                component="div"
                count={totalCount}
                page={page}
                onPageChange={handleChangePage}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                rowsPerPageOptions={[5, 10, 25, 50]}
              />
            </>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        title="Delete Country"
        itemName={countryToDelete?.name}
        itemType="Country"
        message={`Are you sure you want to delete "${countryToDelete?.name}"? This will also delete all regions, zones, cities, subcities, and kebeles within this country. This action cannot be undone.`}
        confirmText="Delete Country"
        severity="error"
        loading={deleting}
      />
    </Container>
  );
};

export default CountriesPage;
