from rest_framework import generics, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from .models import Organization, Department
from .serializers import (
    OrganizationSerializer, OrganizationCreateSerializer, OrganizationUpdateSerializer,
    DepartmentSerializer, DepartmentCreateSerializer
)


class OrganizationListCreateView(generics.ListCreateAPIView):
    """List and create organizations"""
    queryset = Organization.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return OrganizationCreateSerializer
        return OrganizationSerializer


class OrganizationDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, and delete organization"""
    queryset = Organization.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return OrganizationUpdateSerializer
        return OrganizationSerializer

    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)


class DepartmentListCreateView(generics.ListCreateAPIView):
    """List and create departments"""
    queryset = Department.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return DepartmentCreateSerializer
        return DepartmentSerializer


class DepartmentDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, and delete department"""
    queryset = Department.objects.all()
    serializer_class = DepartmentSerializer
    permission_classes = [permissions.IsAuthenticated]


class OrganizationDepartmentListView(generics.ListAPIView):
    """List departments for a specific organization"""
    serializer_class = DepartmentSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        org_pk = self.kwargs['org_pk']
        return Department.objects.filter(organization=org_pk)


class DefaultOrganizationView(APIView):
    """Get the default organization"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        default_org = Organization.get_default()
        if default_org:
            serializer = OrganizationSerializer(default_org)
            return Response(serializer.data)
        return Response(
            {"detail": "No default organization set"},
            status=status.HTTP_404_NOT_FOUND
        )


class DefaultOrganizationPublicView(APIView):
    """Get the default organization (public access for branding)"""
    permission_classes = []  # No authentication required
    authentication_classes = []  # No authentication required

    def get(self, request):
        default_org = Organization.get_default()
        if default_org:
            # Return only public information for branding
            try:
                data = {
                    'id': default_org.id,
                    'name': default_org.name,
                    'short_name': default_org.short_name,
                    'logo': default_org.logo.url if default_org.logo else None,
                    'motto': default_org.motto if hasattr(default_org, 'motto') else None,
                    'tagline': default_org.tagline if hasattr(default_org, 'tagline') else None,
                    'description': default_org.description if hasattr(default_org, 'description') else None,
                    'website': default_org.website if hasattr(default_org, 'website') else None,
                    'email': default_org.email,
                    'phone': default_org.phone,
                    'fax': default_org.fax if hasattr(default_org, 'fax') else None,
                    'full_address': default_org.full_address,
                    'office_hours_display': default_org.get_office_hours_display(),
                    'primary_color': default_org.primary_color if hasattr(default_org, 'primary_color') else '#1976d2',
                    'secondary_color': default_org.secondary_color if hasattr(default_org, 'secondary_color') else '#1565c0',
                    'accent_color': default_org.accent_color if hasattr(default_org, 'accent_color') else '#ff9800',
                    'social_media': default_org.social_media if hasattr(default_org, 'social_media') else {},
                }
                return Response(data)
            except Exception as e:
                return Response(
                    {"detail": f"Error retrieving organization data: {str(e)}"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
        return Response(
            {"detail": "No default organization set"},
            status=status.HTTP_404_NOT_FOUND
        )


class SetDefaultOrganizationView(APIView):
    """Set an organization as default"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk):
        try:
            organization = Organization.objects.get(pk=pk)
            organization.set_as_default()
            serializer = OrganizationSerializer(organization)
            return Response(serializer.data)
        except Organization.DoesNotExist:
            return Response(
                {"detail": "Organization not found"},
                status=status.HTTP_404_NOT_FOUND
            )


class OrganizationLogoUploadView(APIView):
    """Upload logo for an organization"""
    permission_classes = [permissions.IsAuthenticated]

    def patch(self, request, pk):
        try:
            organization = Organization.objects.get(pk=pk)
            if 'logo' in request.FILES:
                organization.logo = request.FILES['logo']
                organization.save()
                serializer = OrganizationSerializer(organization)
                return Response(serializer.data)
            return Response(
                {"detail": "No logo file provided"},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Organization.DoesNotExist:
            return Response(
                {"detail": "Organization not found"},
                status=status.HTTP_404_NOT_FOUND
            )
