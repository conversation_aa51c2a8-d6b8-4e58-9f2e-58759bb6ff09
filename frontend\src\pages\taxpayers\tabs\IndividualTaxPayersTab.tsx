import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Avatar,
  Typography,
  Card,
  CardContent,
  CardActions,
  Grid,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  Pagination,
} from '@mui/material';
import {
  Search,
  Add,
  Edit,
  Delete,
  Person,
  Business,
  Phone,
  Email,
  LocationOn,
  FilterList,
  Refresh,
  Visibility,
} from '@mui/icons-material';
import { useNotification } from '../../../contexts/NotificationContext';
// Import the service
import taxpayerService from '../../../services/taxpayerService';

// Define interfaces locally to avoid import issues
interface IndividualTaxPayer {
  id: string;
  tin: string;
  first_name: string;
  middle_name: string;
  last_name: string;
  full_name: string;
  display_name: string;
  nationality: string;
  gender: string;
  date_of_birth: string;
  tax_payer_level: string;
  tax_payer_level_name: string;
  business_sector: string;
  business_sector_name: string;
  business_sub_sector: string;
  business_sub_sector_name: string;
  business_registration_date: string;
  business_name: string;
  business_license_number: string;
  phone: string;
  phone_secondary: string;
  email: string;
  subcity: string;
  subcity_name: string;
  kebele: string;
  kebele_name: string;
  house_number: string;
  street_address: string;
  postal_code: string;
  location_display: string;
  profile_picture?: string;
  tax_file?: string;
  tax_file_name?: string;
  is_active: boolean;
  registration_date: string;
  last_updated: string;
}

interface BusinessSector {
  id: string;
  code: string;
  name: string;
  description: string;
  is_active: boolean;
  sub_sectors_count: number;
  created_at: string;
  updated_at: string;
}

interface TaxPayerLevel {
  id: string;
  name: string;
  code: string;
  description: string;
  minimum_annual_turnover?: number;
  maximum_annual_turnover?: number;
  turnover_range: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface ListResponse<T> {
  count: number;
  next?: string;
  previous?: string;
  results: T[];
}
import locationService from '../../../services/locationService';
import locationHierarchyService from '../../../services/locationHierarchyService';

// Define location hierarchy interfaces locally
interface SubCity {
  id: number;
  city: number;
  city_name: string;
  zone_name: string;
  region_name: string;
  country_name: string;
  name: string;
  code: string;
  type: 'subcity' | 'woreda' | 'district';
  type_display: string;
  population?: number;
  area_km2?: number;
  is_active: boolean;
  full_name: string;
  kebele_count: number;
  created_at: string;
  updated_at: string;
}

interface Kebele {
  id: number;
  subcity: number;
  subcity_name: string;
  city_name: string;
  zone_name: string;
  region_name: string;
  country_name: string;
  name: string;
  code: string;
  number: number;
  population?: number;
  area_km2?: number;
  is_active: boolean;
  full_name: string;
  display_name: string;
  created_at: string;
  updated_at: string;
}
import IndividualTaxPayerForm from '../forms/IndividualTaxPayerForm';

interface IndividualTaxPayersTabProps {
  onDataChange?: () => void;
}

const IndividualTaxPayersTab: React.FC<IndividualTaxPayersTabProps> = ({ onDataChange }) => {
  const { showNotification } = useNotification();
  const navigate = useNavigate();

  // State
  const [taxpayers, setTaxpayers] = useState<IndividualTaxPayer[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(12);
  
  // Filters
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLevel, setSelectedLevel] = useState('');
  const [selectedSector, setSelectedSector] = useState('');
  const [selectedGender, setSelectedGender] = useState('');
  const [selectedSubcity, setSelectedSubcity] = useState('');
  
  // Form state
  const [formOpen, setFormOpen] = useState(false);
  const [editingTaxpayer, setEditingTaxpayer] = useState<IndividualTaxPayer | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [taxpayerToDelete, setTaxpayerToDelete] = useState<IndividualTaxPayer | null>(null);
  
  // Filter options
  const [levels, setLevels] = useState<TaxPayerLevel[]>([]);
  const [sectors, setSectors] = useState<BusinessSector[]>([]);
  const [subcities, setSubcities] = useState<SubCity[]>([]);

  useEffect(() => {
    loadTaxpayers();
    loadFilterOptions();
  }, [currentPage, searchTerm, selectedLevel, selectedSector, selectedGender, selectedSubcity]);

  const loadTaxpayers = async () => {
    try {
      setLoading(true);
      const params: any = {
        page: currentPage,
        search: searchTerm || undefined,
        tax_payer_level: selectedLevel || undefined,
        business_sector: selectedSector || undefined,
        gender: selectedGender || undefined,
        subcity: selectedSubcity || undefined,
        ordering: '-registration_date',
      };

      const response: ListResponse<IndividualTaxPayer> = await taxpayerService.getIndividualTaxPayers(params);
      setTaxpayers(response.results);
      setTotalCount(response.count);
    } catch (error) {
      console.error('Failed to load individual taxpayers:', error);
      showNotification('Failed to load individual taxpayers', 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadFilterOptions = async () => {
    try {
      const [levelsData, sectorsData, subcitiesData] = await Promise.all([
        taxpayerService.getTaxPayerLevelsSimple(),
        taxpayerService.getBusinessSectorsSimple(),
        locationHierarchyService.getSubCities(),
      ]);
      
      setLevels(levelsData);
      setSectors(sectorsData);
      setSubcities(subcitiesData.results);
    } catch (error) {
      console.error('Failed to load filter options:', error);
    }
  };

  const handleCreate = () => {
    setEditingTaxpayer(null);
    setFormOpen(true);
  };

  const handleEdit = (taxpayer: IndividualTaxPayer) => {
    setEditingTaxpayer(taxpayer);
    setFormOpen(true);
  };

  const handleDelete = (taxpayer: IndividualTaxPayer) => {
    setTaxpayerToDelete(taxpayer);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!taxpayerToDelete) return;

    try {
      await taxpayerService.deleteIndividualTaxPayer(taxpayerToDelete.id);
      showNotification('Individual taxpayer deleted successfully', 'success');
      loadTaxpayers();
      if (onDataChange) onDataChange();
    } catch (error) {
      console.error('Failed to delete taxpayer:', error);
      showNotification('Failed to delete taxpayer', 'error');
    } finally {
      setDeleteDialogOpen(false);
      setTaxpayerToDelete(null);
    }
  };

  const handleFormSubmit = async () => {
    setFormOpen(false);
    loadTaxpayers();
    if (onDataChange) onDataChange();
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedLevel('');
    setSelectedSector('');
    setSelectedGender('');
    setSelectedSubcity('');
    setCurrentPage(1);
  };

  const TaxPayerCard = ({ taxpayer }: { taxpayer: IndividualTaxPayer }) => (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <CardContent sx={{ flexGrow: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Avatar
            src={taxpayer.profile_picture}
            sx={{ width: 56, height: 56, mr: 2, bgcolor: 'primary.main' }}
          >
            <Person />
          </Avatar>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h6" component="div" noWrap>
              {taxpayer.full_name}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              TIN: {taxpayer.tin}
            </Typography>
          </Box>
        </Box>

        {taxpayer.business_name && (
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Business sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
            <Typography variant="body2" noWrap>
              {taxpayer.business_name}
            </Typography>
          </Box>
        )}

        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Phone sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
          <Typography variant="body2">
            {taxpayer.phone}
          </Typography>
        </Box>

        {taxpayer.email && (
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Email sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
            <Typography variant="body2" noWrap>
              {taxpayer.email}
            </Typography>
          </Box>
        )}

        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <LocationOn sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
          <Typography variant="body2" noWrap>
            {taxpayer.location_display}
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 1 }}>
          <Chip 
            label={taxpayer.tax_payer_level_name} 
            size="small" 
            color="primary" 
            variant="outlined"
          />
          <Chip 
            label={taxpayer.gender === 'M' ? 'Male' : taxpayer.gender === 'F' ? 'Female' : 'Other'} 
            size="small" 
            color="secondary" 
            variant="outlined"
          />
        </Box>

        <Typography variant="caption" color="text.secondary">
          Sector: {taxpayer.business_sector_name}
        </Typography>
      </CardContent>

      <CardActions>
        <Button
          size="small"
          startIcon={<Visibility />}
          onClick={() => navigate(`/taxpayers/individuals/${taxpayer.id}`)}
        >
          View
        </Button>
        <Button
          size="small"
          startIcon={<Edit />}
          onClick={() => handleEdit(taxpayer)}
        >
          Edit
        </Button>
        <Button
          size="small"
          color="error"
          startIcon={<Delete />}
          onClick={() => handleDelete(taxpayer)}
        >
          Delete
        </Button>
      </CardActions>
    </Card>
  );

  const totalPages = Math.ceil(totalCount / pageSize);

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" component="h2" sx={{ fontWeight: 600 }}>
          Individual Tax Payers ({totalCount.toLocaleString()})
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={loadTaxpayers}
            disabled={loading}
            sx={{ borderRadius: 2 }}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={handleCreate}
            sx={{
              borderRadius: 2,
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
              }
            }}
          >
            Add Individual
          </Button>
        </Box>
      </Box>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid size={{ xs: 12, md: 3 }}>
              <TextField
                fullWidth
                placeholder="Search by name, TIN, phone..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 2 }}>
              <FormControl fullWidth>
                <InputLabel>Tax Payer Level</InputLabel>
                <Select
                  value={selectedLevel}
                  onChange={(e) => setSelectedLevel(e.target.value)}
                  label="Tax Payer Level"
                >
                  <MenuItem value="">All Levels</MenuItem>
                  {levels.map((level) => (
                    <MenuItem key={level.id} value={level.id}>
                      {level.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 2 }}>
              <FormControl fullWidth>
                <InputLabel>Business Sector</InputLabel>
                <Select
                  value={selectedSector}
                  onChange={(e) => setSelectedSector(e.target.value)}
                  label="Business Sector"
                >
                  <MenuItem value="">All Sectors</MenuItem>
                  {sectors.map((sector) => (
                    <MenuItem key={sector.id} value={sector.id}>
                      {sector.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 2 }}>
              <FormControl fullWidth>
                <InputLabel>Gender</InputLabel>
                <Select
                  value={selectedGender}
                  onChange={(e) => setSelectedGender(e.target.value)}
                  label="Gender"
                >
                  <MenuItem value="">All Genders</MenuItem>
                  <MenuItem value="M">Male</MenuItem>
                  <MenuItem value="F">Female</MenuItem>
                  <MenuItem value="O">Other</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 2 }}>
              <FormControl fullWidth>
                <InputLabel>SubCity</InputLabel>
                <Select
                  value={selectedSubcity}
                  onChange={(e) => setSelectedSubcity(e.target.value)}
                  label="SubCity"
                >
                  <MenuItem value="">All SubCities</MenuItem>
                  {subcities.map((subcity) => (
                    <MenuItem key={subcity.id} value={subcity.id}>
                      {subcity.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid size={{ xs: 12, md: 1 }}>
              <Button
                fullWidth
                variant="outlined"
                onClick={clearFilters}
                sx={{ height: 56 }}
              >
                Clear
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Content */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
          <CircularProgress />
        </Box>
      ) : taxpayers.length === 0 ? (
        <Alert severity="info">
          No individual taxpayers found. {searchTerm && 'Try adjusting your search criteria or '}
          <Button onClick={handleCreate}>add the first one</Button>.
        </Alert>
      ) : (
        <>
          <Grid container spacing={3}>
            {taxpayers.map((taxpayer) => (
              <Grid key={taxpayer.id} size={{ xs: 12, sm: 6, md: 4 }}>
                <TaxPayerCard taxpayer={taxpayer} />
              </Grid>
            ))}
          </Grid>

          {/* Pagination */}
          {totalPages > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <Pagination
                count={totalPages}
                page={currentPage}
                onChange={(event, page) => setCurrentPage(page)}
                color="primary"
                size="large"
              />
            </Box>
          )}
        </>
      )}

      {/* Form Dialog */}
      <Dialog
        open={formOpen}
        onClose={() => setFormOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {editingTaxpayer ? 'Edit Individual Tax Payer' : 'Add Individual Tax Payer'}
        </DialogTitle>
        <DialogContent>
          <IndividualTaxPayerForm
            taxpayer={editingTaxpayer}
            onSubmit={handleFormSubmit}
            onCancel={() => setFormOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the taxpayer "{taxpayerToDelete?.full_name}"?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default IndividualTaxPayersTab;
