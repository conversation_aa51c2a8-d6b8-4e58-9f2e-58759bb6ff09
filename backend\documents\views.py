from rest_framework import generics, permissions, status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from django.http import FileResponse, Http404
from django.shortcuts import get_object_or_404
from .models import DocumentType, Document, DocumentVersion, DocumentTag
from .serializers import (
    DocumentTypeSerializer, DocumentSerializer, DocumentCreateSerializer,
    DocumentVersionSerializer, DocumentTagSerializer
)


# Placeholder views - will be implemented in next phase
class DocumentTypeListCreateView(generics.ListCreateAPIView):
    queryset = DocumentType.objects.all()
    serializer_class = DocumentTypeSerializer
    permission_classes = [permissions.IsAuthenticated]

class DocumentTypeDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = DocumentType.objects.all()
    serializer_class = DocumentTypeSerializer
    permission_classes = [permissions.IsAuthenticated]

class DocumentListCreateView(generics.ListCreateAPIView):
    queryset = Document.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return DocumentCreateSerializer
        return DocumentSerializer

class DocumentDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Document.objects.all()
    serializer_class = DocumentSerializer
    permission_classes = [permissions.IsAuthenticated]

class DocumentVersionListCreateView(generics.ListCreateAPIView):
    queryset = DocumentVersion.objects.all()
    serializer_class = DocumentVersionSerializer
    permission_classes = [permissions.IsAuthenticated]

class DocumentVersionDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = DocumentVersion.objects.all()
    serializer_class = DocumentVersionSerializer
    permission_classes = [permissions.IsAuthenticated]

class DocumentTagListCreateView(generics.ListCreateAPIView):
    queryset = DocumentTag.objects.all()
    serializer_class = DocumentTagSerializer
    permission_classes = [permissions.IsAuthenticated]

class DocumentTagDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = DocumentTag.objects.all()
    serializer_class = DocumentTagSerializer
    permission_classes = [permissions.IsAuthenticated]

class DocumentSearchView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    def get(self, request):
        return Response({'message': 'Document search endpoint'})

class DocumentStatsView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    def get(self, request):
        return Response({'message': 'Document stats endpoint'})

class DocumentDownloadView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, pk):
        try:
            document = get_object_or_404(Document, pk=pk)

            if not document.file:
                return Response(
                    {'error': 'No file attached to this document'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Create HTTP response with file for download
            response = FileResponse(
                document.file.open('rb'),
                as_attachment=True,
                filename=document.file.name.split('/')[-1]
            )

            return response

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class DocumentViewView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, pk):
        try:
            document = get_object_or_404(Document, pk=pk)

            if not document.file:
                return Response(
                    {'error': 'No file attached to this document'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Create HTTP response for viewing (not as attachment)
            response = FileResponse(
                document.file.open('rb'),
                as_attachment=False,
                filename=document.file.name.split('/')[-1]
            )

            # Set content type for proper viewing
            file_extension = document.file.name.split('.')[-1].lower()
            content_types = {
                'pdf': 'application/pdf',
                'jpg': 'image/jpeg',
                'jpeg': 'image/jpeg',
                'png': 'image/png',
                'gif': 'image/gif',
                'txt': 'text/plain',
                'doc': 'application/msword',
                'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'xls': 'application/vnd.ms-excel',
                'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            }

            if file_extension in content_types:
                response['Content-Type'] = content_types[file_extension]

            return response

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )




class DocumentVersionDownloadView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, pk):
        try:
            version = get_object_or_404(DocumentVersion, pk=pk)

            if not version.file:
                return Response(
                    {'error': 'No file attached to this document version'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Create HTTP response with file for download
            response = FileResponse(
                version.file.open('rb'),
                as_attachment=True,
                filename=version.file.name.split('/')[-1]
            )

            return response

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class DocumentUploadFileView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    def patch(self, request, pk):
        try:
            document = Document.objects.get(pk=pk)

            # Check permissions
            if not request.user.is_staff and document.created_by != request.user:
                return Response(
                    {'error': 'You do not have permission to upload files to this document'},
                    status=status.HTTP_403_FORBIDDEN
                )

            file = request.FILES.get('file')
            if not file:
                return Response(
                    {'error': 'No file provided'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Update document with new file
            document.file = file
            document.save()

            # Return updated document
            serializer = DocumentSerializer(document)
            return Response(serializer.data)

        except Document.DoesNotExist:
            return Response(
                {'error': 'Document not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class DocumentGenerateBarcodeView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk):
        try:
            document = get_object_or_404(Document, pk=pk)

            # Force regenerate barcode
            document.barcode_image = None
            document.save()  # This will trigger barcode generation

            serializer = DocumentSerializer(document)
            return Response(serializer.data)

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class DocumentGenerateQRView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk):
        try:
            document = get_object_or_404(Document, pk=pk)

            # Force regenerate QR code
            document.qr_code_image = None
            document.save()  # This will trigger QR code generation

            serializer = DocumentSerializer(document)
            return Response(serializer.data)

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
