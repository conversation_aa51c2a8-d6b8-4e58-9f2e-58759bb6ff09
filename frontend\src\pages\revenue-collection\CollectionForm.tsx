/**
 * Revenue Collection Form
 * 
 * Form for creating and editing revenue collections
 * Supports both Regional and City Service collections
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Alert,
  CircularProgress,
  Autocomplete,
  InputAdornment,
  Divider,
  Paper,
  Chip,
  Container,
  Breadcrumbs,
  Link,
  Stepper,
  Step,
  StepLabel,
  Fade,
  Zoom,
} from '@mui/material';
import {
  Save,
  Cancel,
  Person,
  Business,
  Receipt,
  AccountBalance,
  Home,
  NavigateNext,
  MonetizationOn,
  CalendarToday,
  Description,
  CheckCircle,
  Assignment,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { useNavigate, useParams } from 'react-router-dom';
import { useNotification } from '../../contexts/NotificationContext';
import revenueCollectionService from '../../services/revenueCollectionService';
import taxpayerService from '../../services/taxpayerService';
import type {
  RegionalRevenueSource,
  CityServiceRevenueSource,
  RevenuePeriod,
  RegionalRevenueCollectionCreate,
  CityServiceRevenueCollectionCreate,
} from '../../services/revenueCollectionService';
import type { IndividualTaxPayer, OrganizationTaxPayer } from '../../services/taxpayerService';

interface CollectionFormProps {
  mode: 'create' | 'edit';
  type: 'regional' | 'city-service';
}

const CollectionForm: React.FC<CollectionFormProps> = ({ mode, type }) => {
  const navigate = useNavigate();
  const { id } = useParams();
  const { showSuccess, showError } = useNotification();
  
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  
  // Form data
  const [formData, setFormData] = useState({
    taxpayer_type: 'individual' as 'individual' | 'organization',
    individual_taxpayer: '',
    organization_taxpayer: '',
    revenue_source: '',
    period: '',
    amount: '',
    collection_date: new Date(),
    receipt_number: '',
    notes: '',
  });

  // Options data
  const [revenueSources, setRevenueSources] = useState<(RegionalRevenueSource | CityServiceRevenueSource)[]>([]);
  const [periods, setPeriods] = useState<RevenuePeriod[]>([]);
  const [individualTaxpayers, setIndividualTaxpayers] = useState<IndividualTaxPayer[]>([]);
  const [organizationTaxpayers, setOrganizationTaxpayers] = useState<OrganizationTaxPayer[]>([]);

  useEffect(() => {
    loadOptions();
    if (mode === 'edit' && id) {
      loadCollection();
    }
  }, [mode, id, type]);

  const loadOptions = async () => {
    try {
      setLoading(true);
      const [
        revenueSourcesResponse,
        periodsResponse,
        individualTaxpayersResponse,
        organizationTaxpayersResponse,
      ] = await Promise.all([
        type === 'regional'
          ? revenueCollectionService.getRegionalRevenueSources({ is_active: true })
          : revenueCollectionService.getCityServiceRevenueSources({ is_active: true }),
        revenueCollectionService.getRevenuePeriods({ is_closed: false }),
        taxpayerService.getIndividualTaxPayers({ page_size: 1000 }),
        taxpayerService.getOrganizationTaxPayers({ page_size: 1000 }),
      ]);

      setRevenueSources(revenueSourcesResponse.results);
      setPeriods(periodsResponse.results);
      setIndividualTaxpayers(individualTaxpayersResponse.results);
      setOrganizationTaxpayers(organizationTaxpayersResponse.results);

      // Auto-select current period if creating new collection
      if (mode === 'create' && periodsResponse.results.length > 0) {
        const currentPeriod = periodsResponse.results.find(p => p.is_current);
        if (currentPeriod) {
          setFormData(prev => ({ ...prev, period: currentPeriod.id }));
        }
      }
    } catch (error) {
      console.error('Error loading options:', error);
      showError('Failed to load form options');
    } finally {
      setLoading(false);
    }
  };

  const loadCollection = async () => {
    if (!id) return;
    
    try {
      const collection = type === 'regional'
        ? await revenueCollectionService.getRegionalRevenueCollection(id)
        : await revenueCollectionService.getCityServiceRevenueCollection(id);
      
      setFormData({
        taxpayer_type: collection.individual_taxpayer ? 'individual' : 'organization',
        individual_taxpayer: collection.individual_taxpayer || '',
        organization_taxpayer: collection.organization_taxpayer || '',
        revenue_source: collection.revenue_source,
        period: collection.period,
        amount: collection.amount.toString(),
        collection_date: new Date(collection.collection_date),
        receipt_number: collection.receipt_number || '',
        notes: collection.notes || '',
      });
    } catch (error) {
      console.error('Error loading collection:', error);
      showError('Failed to load collection');
      navigate('/revenue-collection/collections');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.revenue_source || !formData.period || !formData.amount) {
      showError('Please fill in all required fields');
      return;
    }

    if (!formData.individual_taxpayer && !formData.organization_taxpayer) {
      showError('Please select a taxpayer');
      return;
    }

    try {
      setSubmitting(true);
      
      const submitData = {
        individual_taxpayer: formData.taxpayer_type === 'individual' ? formData.individual_taxpayer : undefined,
        organization_taxpayer: formData.taxpayer_type === 'organization' ? formData.organization_taxpayer : undefined,
        revenue_source: formData.revenue_source,
        period: formData.period,
        amount: parseFloat(formData.amount),
        collection_date: formData.collection_date.toISOString().split('T')[0],
        receipt_number: formData.receipt_number || undefined,
        notes: formData.notes || undefined,
      };

      if (mode === 'create') {
        if (type === 'regional') {
          await revenueCollectionService.createRegionalRevenueCollection(submitData as RegionalRevenueCollectionCreate);
          showSuccess('Regional revenue collection created successfully');
        } else {
          await revenueCollectionService.createCityServiceRevenueCollection(submitData as CityServiceRevenueCollectionCreate);
          showSuccess('City service revenue collection created successfully');
        }
      } else if (id) {
        if (type === 'regional') {
          await revenueCollectionService.updateRegionalRevenueCollection(id, submitData);
          showSuccess('Regional revenue collection updated successfully');
        } else {
          await revenueCollectionService.updateCityServiceRevenueCollection(id, submitData);
          showSuccess('City service revenue collection updated successfully');
        }
      }
      
      navigate('/revenue-collection/collections');
    } catch (error) {
      console.error('Error saving collection:', error);
      showError('Failed to save collection');
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate('/revenue-collection/collections');
  };

  const formatCurrency = (amount: string) => {
    const num = parseFloat(amount);
    if (isNaN(num)) return '';
    return new Intl.NumberFormat('en-ET', {
      style: 'currency',
      currency: 'ETB',
      minimumFractionDigits: 2,
    }).format(num);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  const selectedTaxpayers = formData.taxpayer_type === 'individual' ? individualTaxpayers : organizationTaxpayers;

  const steps = ['Taxpayer Information', 'Revenue Details', 'Collection Information'];
  const currentStep = 0; // You can implement step logic if needed

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Header Section */}
        <Fade in timeout={600}>
          <Box mb={4}>
            {/* Breadcrumbs */}
            <Breadcrumbs
              separator={<NavigateNext fontSize="small" />}
              sx={{ mb: 2 }}
            >
              <Link
                color="inherit"
                href="/dashboard"
                sx={{ display: 'flex', alignItems: 'center', textDecoration: 'none' }}
              >
                <Home sx={{ mr: 0.5 }} fontSize="inherit" />
                Dashboard
              </Link>
              <Link
                color="inherit"
                href="/revenue-collection/collections"
                sx={{ textDecoration: 'none' }}
              >
                Revenue Collections
              </Link>
              <Typography color="text.primary">
                {mode === 'create' ? 'Create' : 'Edit'} {type === 'regional' ? 'Regional' : 'City Service'} Collection
              </Typography>
            </Breadcrumbs>

            {/* Page Title */}
            <Box display="flex" alignItems="center" gap={2} mb={2}>
              <Paper
                sx={{
                  p: 1.5,
                  bgcolor: type === 'regional' ? 'primary.main' : 'secondary.main',
                  color: 'white',
                  borderRadius: 2
                }}
              >
                {type === 'regional' ? <AccountBalance /> : <Business />}
              </Paper>
              <Box>
                <Typography variant="h4" fontWeight="bold" color="text.primary">
                  {mode === 'create' ? 'Create New' : 'Edit'} {type === 'regional' ? 'Regional' : 'City Service'} Collection
                </Typography>
                <Typography variant="subtitle1" color="text.secondary">
                  {mode === 'create'
                    ? `Add a new ${type === 'regional' ? 'regional revenue' : 'city service'} collection record`
                    : `Update ${type === 'regional' ? 'regional revenue' : 'city service'} collection information`
                  }
                </Typography>
              </Box>
            </Box>

            {/* Collection Type Chip */}
            <Chip
              icon={type === 'regional' ? <AccountBalance /> : <Business />}
              label={`${type === 'regional' ? 'Regional Revenue' : 'City Service'} Collection`}
              color={type === 'regional' ? 'primary' : 'secondary'}
              variant="outlined"
              size="medium"
              sx={{ fontWeight: 'medium' }}
            />
          </Box>
        </Fade>

        {/* Progress Stepper */}
        <Fade in timeout={800}>
          <Card sx={{ mb: 4, overflow: 'visible' }}>
            <CardContent sx={{ pb: 3 }}>
              <Stepper activeStep={currentStep} alternativeLabel>
                {steps.map((label, index) => (
                  <Step key={label}>
                    <StepLabel
                      StepIconProps={{
                        sx: {
                          '&.Mui-active': {
                            color: type === 'regional' ? 'primary.main' : 'secondary.main',
                          },
                          '&.Mui-completed': {
                            color: type === 'regional' ? 'primary.main' : 'secondary.main',
                          },
                        },
                      }}
                    >
                      {label}
                    </StepLabel>
                  </Step>
                ))}
              </Stepper>
            </CardContent>
          </Card>
        </Fade>
        {/* Main Form */}
        <Zoom in timeout={1000}>
          <form onSubmit={handleSubmit}>
            <Grid container spacing={4}>
              {/* Taxpayer Information Section */}
              <Grid item xs={12}>
                <Card
                  sx={{
                    borderRadius: 3,
                    boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                    border: '1px solid',
                    borderColor: 'divider',
                    overflow: 'visible'
                  }}
                >
                  <CardContent sx={{ p: 4 }}>
                    <Box display="flex" alignItems="center" gap={2} mb={3}>
                      <Paper
                        sx={{
                          p: 1,
                          bgcolor: 'primary.50',
                          border: '1px solid',
                          borderColor: 'primary.200',
                          borderRadius: 2
                        }}
                      >
                        <Person color="primary" />
                      </Paper>
                      <Box>
                        <Typography variant="h6" fontWeight="bold" color="primary.main">
                          Taxpayer Information
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Select the taxpayer for this collection
                        </Typography>
                      </Box>
                    </Box>
                  
                    <Grid container spacing={3}>
                      {/* Taxpayer Type Selection */}
                      <Grid item xs={12} md={6}>
                        <FormControl fullWidth required>
                          <InputLabel>Taxpayer Type</InputLabel>
                          <Select
                            value={formData.taxpayer_type}
                            onChange={(e) => {
                              const newType = e.target.value as 'individual' | 'organization';
                              setFormData({
                                ...formData,
                                taxpayer_type: newType,
                                individual_taxpayer: '',
                                organization_taxpayer: '',
                              });
                            }}
                            label="Taxpayer Type"
                            sx={{
                              '& .MuiSelect-select': {
                                display: 'flex',
                                alignItems: 'center',
                              }
                            }}
                          >
                            <MenuItem value="individual">
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Person color="primary" />
                                <Box>
                                  <Typography variant="body1">Individual Taxpayer</Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    Personal tax collection
                                  </Typography>
                                </Box>
                              </Box>
                            </MenuItem>
                            <MenuItem value="organization">
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Business color="secondary" />
                                <Box>
                                  <Typography variant="body1">Organization Taxpayer</Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    Business tax collection
                                  </Typography>
                                </Box>
                              </Box>
                            </MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      {/* Taxpayer Selection Status */}
                      <Grid item xs={12} md={6}>
                        <Alert
                          severity={formData.taxpayer_type === 'individual' ? 'info' : 'warning'}
                          sx={{ height: '100%', display: 'flex', alignItems: 'center' }}
                        >
                          <Typography variant="body2">
                            {formData.taxpayer_type === 'individual'
                              ? 'Individual taxpayer collection - Personal tax revenue'
                              : 'Organization taxpayer collection - Business tax revenue'
                            }
                          </Typography>
                        </Alert>
                      </Grid>

                      {/* Taxpayer Selection */}
                      <Grid item xs={12}>
                        <Autocomplete
                          options={selectedTaxpayers}
                          getOptionLabel={(option) =>
                            formData.taxpayer_type === 'individual'
                              ? `${(option as IndividualTaxPayer).full_name} (TIN: ${option.tin})`
                              : `${(option as OrganizationTaxPayer).business_name} (TIN: ${option.tin})`
                          }
                          value={selectedTaxpayers.find(t =>
                            t.id === (formData.taxpayer_type === 'individual' ? formData.individual_taxpayer : formData.organization_taxpayer)
                          ) || null}
                          onChange={(_, value) => {
                            if (formData.taxpayer_type === 'individual') {
                              setFormData({ ...formData, individual_taxpayer: value?.id || '' });
                            } else {
                              setFormData({ ...formData, organization_taxpayer: value?.id || '' });
                            }
                          }}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Select Taxpayer"
                              required
                              helperText="Search by name or TIN number"
                              InputProps={{
                                ...params.InputProps,
                                startAdornment: (
                                  <InputAdornment position="start">
                                    {formData.taxpayer_type === 'individual' ? <Person /> : <Business />}
                                  </InputAdornment>
                                ),
                              }}
                            />
                          )}
                          renderOption={(props, option) => (
                            <Box component="li" {...props} sx={{ display: 'flex', alignItems: 'center', gap: 2, p: 2 }}>
                              <Paper sx={{ p: 1, bgcolor: 'background.default' }}>
                                {formData.taxpayer_type === 'individual' ? <Person /> : <Business />}
                              </Paper>
                              <Box>
                                <Typography variant="body1">
                                  {formData.taxpayer_type === 'individual'
                                    ? (option as IndividualTaxPayer).full_name
                                    : (option as OrganizationTaxPayer).business_name
                                  }
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  TIN: {option.tin}
                                </Typography>
                              </Box>
                            </Box>
                          )}
                        />
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>

              {/* Revenue Details Section */}
              <Grid item xs={12}>
                <Card
                  sx={{
                    borderRadius: 3,
                    boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                    border: '1px solid',
                    borderColor: 'divider',
                  }}
                >
                  <CardContent sx={{ p: 4 }}>
                    <Box display="flex" alignItems="center" gap={2} mb={3}>
                      <Paper
                        sx={{
                          p: 1,
                          bgcolor: 'secondary.50',
                          border: '1px solid',
                          borderColor: 'secondary.200',
                          borderRadius: 2
                        }}
                      >
                        <MonetizationOn color="secondary" />
                      </Paper>
                      <Box>
                        <Typography variant="h6" fontWeight="bold" color="secondary.main">
                          Revenue Details
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Configure the revenue source and collection period
                        </Typography>
                      </Box>
                    </Box>

                    <Grid container spacing={3}>

                      {/* Revenue Source */}
                      <Grid item xs={12} md={6}>
                        <FormControl fullWidth required>
                          <InputLabel>Revenue Source</InputLabel>
                          <Select
                            value={formData.revenue_source}
                            onChange={(e) => setFormData({ ...formData, revenue_source: e.target.value })}
                            label="Revenue Source"
                            startAdornment={
                              <InputAdornment position="start">
                                <AccountBalance />
                              </InputAdornment>
                            }
                          >
                            {revenueSources.map((source) => (
                              <MenuItem key={source.id} value={source.id}>
                                <Box>
                                  <Typography variant="body1">
                                    {source.code} - {source.name}
                                  </Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    {type === 'regional' ? 'Regional Revenue' : 'City Service'}
                                  </Typography>
                                </Box>
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </Grid>

                      {/* Collection Period */}
                      <Grid item xs={12} md={6}>
                        <FormControl fullWidth required>
                          <InputLabel>Collection Period</InputLabel>
                          <Select
                            value={formData.period}
                            onChange={(e) => setFormData({ ...formData, period: e.target.value })}
                            label="Collection Period"
                            startAdornment={
                              <InputAdornment position="start">
                                <CalendarToday />
                              </InputAdornment>
                            }
                          >
                            {periods.map((period) => (
                              <MenuItem key={period.id} value={period.id}>
                                <Box>
                                  <Typography variant="body1">
                                    {period.name}
                                  </Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    Revenue collection period
                                  </Typography>
                                </Box>
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </Grid>

                      {/* Collection Amount */}
                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="Collection Amount"
                          type="number"
                          value={formData.amount}
                          onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                          required
                          InputProps={{
                            startAdornment: (
                              <InputAdornment position="start">
                                <MonetizationOn color="success" />
                                ETB
                              </InputAdornment>
                            ),
                          }}
                          helperText={formData.amount ? `Preview: ${formatCurrency(formData.amount)}` : 'Enter the collection amount'}
                          sx={{
                            '& .MuiInputBase-input': {
                              fontSize: '1.1rem',
                              fontWeight: 'medium',
                            }
                          }}
                        />
                      </Grid>

                      {/* Collection Date */}
                      <Grid item xs={12} md={6}>
                        <DatePicker
                          label="Collection Date"
                          value={formData.collection_date}
                          onChange={(date) => setFormData({ ...formData, collection_date: date || new Date() })}
                          slotProps={{
                            textField: {
                              fullWidth: true,
                              required: true,
                              helperText: 'Date when the revenue was collected',
                              InputProps: {
                                startAdornment: (
                                  <InputAdornment position="start">
                                    <CalendarToday />
                                  </InputAdornment>
                                ),
                              },
                            },
                          }}
                        />
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>

              {/* Collection Information Section */}
              <Grid item xs={12}>
                <Card
                  sx={{
                    borderRadius: 3,
                    boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                    border: '1px solid',
                    borderColor: 'divider',
                  }}
                >
                  <CardContent sx={{ p: 4 }}>
                    <Box display="flex" alignItems="center" gap={2} mb={3}>
                      <Paper
                        sx={{
                          p: 1,
                          bgcolor: 'success.50',
                          border: '1px solid',
                          borderColor: 'success.200',
                          borderRadius: 2
                        }}
                      >
                        <Assignment color="success" />
                      </Paper>
                      <Box>
                        <Typography variant="h6" fontWeight="bold" color="success.main">
                          Collection Information
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Additional details and documentation
                        </Typography>
                      </Box>
                    </Box>

                    <Grid container spacing={3}>

                      {/* Receipt Number */}
                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="Receipt Number"
                          value={formData.receipt_number}
                          onChange={(e) => setFormData({ ...formData, receipt_number: e.target.value })}
                          InputProps={{
                            startAdornment: (
                              <InputAdornment position="start">
                                <Receipt color="primary" />
                              </InputAdornment>
                            ),
                          }}
                          helperText="Optional receipt or reference number"
                          placeholder="e.g., RCP-2024-001"
                        />
                      </Grid>

                      {/* Collection Status */}
                      <Grid item xs={12} md={6}>
                        <Alert severity="success" sx={{ height: '100%', display: 'flex', alignItems: 'center' }}>
                          <Box display="flex" alignItems="center" gap={1}>
                            <CheckCircle />
                            <Typography variant="body2">
                              Collection will be recorded as completed
                            </Typography>
                          </Box>
                        </Alert>
                      </Grid>

                      {/* Notes */}
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Additional Notes"
                          value={formData.notes}
                          onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                          multiline
                          rows={4}
                          InputProps={{
                            startAdornment: (
                              <InputAdornment position="start" sx={{ alignSelf: 'flex-start', mt: 2 }}>
                                <Description />
                              </InputAdornment>
                            ),
                          }}
                          helperText="Optional additional notes, comments, or special instructions"
                          placeholder="Enter any additional information about this collection..."
                          sx={{
                            '& .MuiInputBase-root': {
                              alignItems: 'flex-start',
                            }
                          }}
                        />
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>

              {/* Form Actions */}
              <Grid item xs={12}>
                <Card
                  sx={{
                    borderRadius: 3,
                    boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                    border: '1px solid',
                    borderColor: 'divider',
                    bgcolor: 'background.paper',
                  }}
                >
                  <CardContent sx={{ p: 4 }}>
                    {/* Collection Summary */}
                    <Box mb={4}>
                      <Typography variant="h6" fontWeight="bold" color="text.primary" mb={3}>
                        Collection Summary
                      </Typography>

                      <Grid container spacing={3}>
                        <Grid item xs={12} sm={6} md={3}>
                          <Paper sx={{ p: 2, bgcolor: 'primary.50', border: '1px solid', borderColor: 'primary.200' }}>
                            <Typography variant="caption" color="primary.main" fontWeight="medium">
                              Collection Type
                            </Typography>
                            <Typography variant="body1" fontWeight="bold" color="primary.main">
                              {type === 'regional' ? 'Regional Revenue' : 'City Service Revenue'}
                            </Typography>
                          </Paper>
                        </Grid>

                        <Grid item xs={12} sm={6} md={3}>
                          <Paper sx={{ p: 2, bgcolor: 'success.50', border: '1px solid', borderColor: 'success.200' }}>
                            <Typography variant="caption" color="success.main" fontWeight="medium">
                              Amount
                            </Typography>
                            <Typography variant="h6" fontWeight="bold" color="success.main">
                              {formData.amount ? formatCurrency(formData.amount) : 'ETB 0.00'}
                            </Typography>
                          </Paper>
                        </Grid>

                        <Grid item xs={12} sm={6} md={3}>
                          <Paper sx={{ p: 2, bgcolor: 'info.50', border: '1px solid', borderColor: 'info.200' }}>
                            <Typography variant="caption" color="info.main" fontWeight="medium">
                              Collection Date
                            </Typography>
                            <Typography variant="body1" fontWeight="bold" color="info.main">
                              {formData.collection_date.toLocaleDateString('en-ET', {
                                month: 'short',
                                day: 'numeric',
                                year: 'numeric',
                              })}
                            </Typography>
                          </Paper>
                        </Grid>

                        <Grid item xs={12} sm={6} md={3}>
                          <Paper sx={{ p: 2, bgcolor: 'secondary.50', border: '1px solid', borderColor: 'secondary.200' }}>
                            <Typography variant="caption" color="secondary.main" fontWeight="medium">
                              Status
                            </Typography>
                            <Typography variant="body1" fontWeight="bold" color="secondary.main">
                              {mode === 'create' ? 'New Collection' : 'Updating'}
                            </Typography>
                          </Paper>
                        </Grid>
                      </Grid>
                    </Box>

                    <Divider sx={{ my: 3 }} />

                    {/* Action Buttons */}
                    <Box display="flex" gap={2} justifyContent="flex-end" flexWrap="wrap">
                      <Button
                        variant="outlined"
                        startIcon={<Cancel />}
                        onClick={handleCancel}
                        disabled={submitting}
                        size="large"
                        sx={{ minWidth: 140 }}
                      >
                        Cancel
                      </Button>
                      <Button
                        type="submit"
                        variant="contained"
                        startIcon={submitting ? <CircularProgress size={20} /> : <Save />}
                        disabled={submitting}
                        size="large"
                        sx={{
                          minWidth: 180,
                          bgcolor: type === 'regional' ? 'primary.main' : 'secondary.main',
                          '&:hover': {
                            bgcolor: type === 'regional' ? 'primary.dark' : 'secondary.dark',
                          }
                        }}
                      >
                        {submitting
                          ? 'Saving...'
                          : (mode === 'create' ? 'Create Collection' : 'Update Collection')
                        }
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </form>
        </Zoom>
      </Container>
    </LocalizationProvider>
  );
};

export default CollectionForm;
