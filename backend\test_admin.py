#!/usr/bin/env python
"""
Test script to verify Django admin is working correctly
Tests admin interface for all registered models
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'arada_dms.settings')
django.setup()

from django.contrib import admin
from django.apps import apps

def test_admin_registration():
    """Test that all models are properly registered in admin"""
    print("🧪 Testing Django Admin Registration...")
    
    # Get all models from our apps
    our_apps = ['accounts', 'organizations', 'locations', 'documents', 'requests']
    
    for app_name in our_apps:
        print(f"\n📱 Testing {app_name} app:")
        
        try:
            app_config = apps.get_app_config(app_name)
            models = app_config.get_models()
            
            for model in models:
                model_name = model.__name__
                
                # Check if model is registered in admin
                if model in admin.site._registry:
                    admin_class = admin.site._registry[model]
                    print(f"  ✅ {model_name} - Registered with {admin_class.__class__.__name__}")
                    
                    # Test admin class configuration
                    try:
                        # Test list_display
                        if hasattr(admin_class, 'list_display'):
                            for field in admin_class.list_display:
                                if hasattr(admin_class, field):
                                    # It's a method, test if it's callable
                                    method = getattr(admin_class, field)
                                    if callable(method):
                                        print(f"    📋 Custom method: {field}")
                                elif hasattr(model, field):
                                    print(f"    🔗 Model field: {field}")
                                else:
                                    print(f"    ⚠️  Unknown field/method: {field}")
                        
                        # Test readonly_fields
                        if hasattr(admin_class, 'readonly_fields'):
                            for field in admin_class.readonly_fields:
                                if hasattr(admin_class, field) or hasattr(model, field):
                                    print(f"    🔒 Readonly field: {field}")
                                else:
                                    print(f"    ⚠️  Unknown readonly field: {field}")
                        
                        # Test fieldsets
                        if hasattr(admin_class, 'fieldsets'):
                            print(f"    📝 Fieldsets configured: {len(admin_class.fieldsets)} sections")
                            
                    except Exception as e:
                        print(f"    ❌ Error in admin configuration: {e}")
                        
                else:
                    print(f"  ❌ {model_name} - NOT REGISTERED")
                    
        except Exception as e:
            print(f"  ❌ Error testing {app_name}: {e}")
    
    print(f"\n📊 Admin Registration Summary:")
    total_registered = len(admin.site._registry)
    print(f"  Total models registered: {total_registered}")
    
    # List all registered models
    print(f"\n📋 All Registered Models:")
    for model, admin_class in admin.site._registry.items():
        app_label = model._meta.app_label
        model_name = model.__name__
        admin_name = admin_class.__class__.__name__
        print(f"  {app_label}.{model_name} -> {admin_name}")

def test_admin_urls():
    """Test admin URLs are working"""
    print(f"\n🔗 Testing Admin URLs...")
    
    from django.urls import reverse
    from django.test import Client
    
    client = Client()
    
    # Test admin index
    try:
        response = client.get('/admin/')
        if response.status_code == 302:  # Redirect to login
            print("  ✅ Admin index accessible (redirects to login)")
        else:
            print(f"  ⚠️  Admin index returned status: {response.status_code}")
    except Exception as e:
        print(f"  ❌ Error accessing admin index: {e}")
    
    # Test admin login
    try:
        response = client.get('/admin/login/')
        if response.status_code == 200:
            print("  ✅ Admin login page accessible")
        else:
            print(f"  ⚠️  Admin login returned status: {response.status_code}")
    except Exception as e:
        print(f"  ❌ Error accessing admin login: {e}")

if __name__ == '__main__':
    test_admin_registration()
    test_admin_urls()
    print("\n🎉 Admin testing completed!")
