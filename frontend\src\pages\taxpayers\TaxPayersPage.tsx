import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Tabs,
  Tab,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Tooltip,
  Button,
  TextField,
  InputAdornment,
  Alert,
  CircularProgress,
  Paper,
  Divider,
  Breadcrumbs,
  Link,
  Fade,
  Skeleton,
} from '@mui/material';
import {
  Person,
  Business,
  Category,
  Assessment,
  Add,
  Search,
  TrendingUp,
  Groups,
  AccountBalance,
  Receipt,
  Home,
  Dashboard,
  Analytics,
  PieChart,
} from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
// Import the service
import taxpayerService from '../../services/taxpayerService';

// Define interface locally to avoid import issues
interface TaxPayerStatistics {
  individual: {
    total: number;
    by_level: Array<{ tax_payer_level__name: string; tax_payer_level__code: string; count: number }>;
    by_sector: Array<{ business_sector__name: string; business_sector__code: string; count: number }>;
    by_gender: Array<{ gender: string; count: number }>;
  };
  organization: {
    total: number;
    by_level: Array<{ tax_payer_level__name: string; tax_payer_level__code: string; count: number }>;
    by_sector: Array<{ business_sector__name: string; business_sector__code: string; count: number }>;
    by_type: Array<{ organization_business_type__name: string; organization_business_type__code: string; count: number }>;
    with_vat: number;
  };
  overall: {
    total_taxpayers: number;
    total_sectors: number;
    total_sub_sectors: number;
  };
}
import IndividualTaxPayersTab from './tabs/IndividualTaxPayersTab';
import OrganizationTaxPayersTab from './tabs/OrganizationTaxPayersTab';
import BusinessSectorsTab from './tabs/BusinessSectorsTab';
import BusinessSubSectorsTab from './tabs/BusinessSubSectorsTab';
import TaxPayerLevelsTab from './tabs/TaxPayerLevelsTab';
import OrganizationBusinessTypesTab from './tabs/OrganizationBusinessTypesTab';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`taxpayer-tabpanel-${index}`}
      aria-labelledby={`taxpayer-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

const TaxPayersPage: React.FC = () => {
  const { showNotification } = useNotification();
  const navigate = useNavigate();
  const [currentTab, setCurrentTab] = useState(0);
  const [statistics, setStatistics] = useState<TaxPayerStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [showSearchResults, setShowSearchResults] = useState(false);

  useEffect(() => {
    loadStatistics();
  }, []);

  const loadStatistics = async () => {
    try {
      setLoading(true);
      const stats = await taxpayerService.getStatistics();
      setStatistics(stats);
    } catch (error) {
      console.error('Failed to load statistics:', error);
      showNotification('Failed to load statistics', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) return;

    try {
      setSearchLoading(true);
      const results = await taxpayerService.searchTaxPayers(searchQuery);
      setSearchResults(results.results);
      setShowSearchResults(true);
      if (results.results.length === 0) {
        showNotification('No taxpayers found matching your search', 'info');
      }
    } catch (error) {
      console.error('Search failed:', error);
      showNotification('Search failed', 'error');
    } finally {
      setSearchLoading(false);
    }
  };

  const handleSearchResultClick = (result: any) => {
    if (result.type === 'individual') {
      navigate(`/taxpayers/individuals/${result.id}`);
    } else {
      navigate(`/taxpayers/organizations/${result.id}`);
    }
  };

  const clearSearch = () => {
    setSearchQuery('');
    setSearchResults([]);
    setShowSearchResults(false);
  };

  const StatisticsCard = ({
    title,
    value,
    icon,
    color = 'primary',
    gradient = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
  }: {
    title: string;
    value: number | string;
    icon: React.ReactNode;
    color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
    gradient?: string;
  }) => (
    <Card
      sx={{
        height: '100%',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: '0 12px 24px rgba(0,0,0,0.15)'
        },
        background: gradient,
        color: 'white',
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(255,255,255,0.1)',
          opacity: 0,
          transition: 'opacity 0.3s ease',
        },
        '&:hover::before': {
          opacity: 1,
        }
      }}
    >
      <CardContent sx={{ position: 'relative', zIndex: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography variant="h3" component="div" sx={{ fontWeight: 'bold', mb: 1 }}>
              {typeof value === 'number' ? value.toLocaleString() : value}
            </Typography>
            <Typography variant="body1" sx={{ opacity: 0.9, fontWeight: 500 }}>
              {title}
            </Typography>
          </Box>
          <Box sx={{ opacity: 0.8, fontSize: '3rem' }}>
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ py: 3 }}>
        <Box sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: 500,
          gap: 3
        }}>
          <CircularProgress
            size={60}
            thickness={4}
            sx={{
              color: 'primary.main',
              '& .MuiCircularProgress-circle': {
                strokeLinecap: 'round',
              }
            }}
          />
          <Typography variant="h6" color="text.secondary" sx={{ fontWeight: 500 }}>
            Loading Tax Payers Management System...
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Breadcrumbs */}
      <Fade in timeout={600}>
        <Box sx={{ mb: 3 }}>
          <Breadcrumbs sx={{ mb: 2 }}>
            <Link
              component="button"
              variant="body1"
              onClick={() => window.location.href = '/dashboard'}
              sx={{ display: 'flex', alignItems: 'center', gap: 0.5, textDecoration: 'none' }}
            >
              <Home fontSize="small" />
              Dashboard
            </Link>
            <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <Receipt fontSize="small" />
              Tax Payers Management
            </Typography>
          </Breadcrumbs>
        </Box>
      </Fade>

      {/* Header */}
      <Fade in timeout={800}>
        <Paper
          elevation={0}
          sx={{
            p: 4,
            mb: 4,
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            borderRadius: 3
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box>
              <Typography variant="h3" component="h1" sx={{ fontWeight: 'bold', mb: 1 }}>
                Tax Payers Management
              </Typography>
              <Typography variant="h6" sx={{ opacity: 0.9 }}>
                Comprehensive tax payer registration and management system
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Button
                variant="contained"
                startIcon={<Analytics />}
                onClick={() => navigate('/taxpayers/analytics')}
                sx={{
                  bgcolor: 'rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  '&:hover': {
                    bgcolor: 'rgba(255, 255, 255, 0.3)',
                  },
                  backdropFilter: 'blur(10px)',
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                }}
              >
                Analytics Dashboard
              </Button>
              <Button
                variant="contained"
                startIcon={<Assessment />}
                onClick={() => navigate('/taxpayers/level-assessment')}
                sx={{
                  bgcolor: 'rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  '&:hover': {
                    bgcolor: 'rgba(255, 255, 255, 0.3)',
                  },
                  backdropFilter: 'blur(10px)',
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                }}
              >
                Level Assessment
              </Button>
              <TrendingUp sx={{ fontSize: '4rem', opacity: 0.7 }} />
            </Box>
          </Box>
        </Paper>
      </Fade>

      {/* Statistics Cards */}
      <Fade in timeout={1000}>
        <Box sx={{ mb: 4 }}>
          {statistics ? (
            <Grid container spacing={3}>
              <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                <StatisticsCard
                  title="Total Tax Payers"
                  value={statistics.overall.total_taxpayers}
                  icon={<Groups />}
                  gradient="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
                />
              </Grid>
              <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                <StatisticsCard
                  title="Individual Tax Payers"
                  value={statistics.individual.total}
                  icon={<Person />}
                  gradient="linear-gradient(135deg, #f093fb 0%, #f5576c 100%)"
                />
              </Grid>
              <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                <StatisticsCard
                  title="Organization Tax Payers"
                  value={statistics.organization.total}
                  icon={<Business />}
                  gradient="linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)"
                />
              </Grid>
              <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                <StatisticsCard
                  title="VAT Registered Organizations"
                  value={statistics.organization.with_vat}
                  icon={<Receipt />}
                  gradient="linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)"
                />
              </Grid>
            </Grid>
          ) : (
            <Grid container spacing={3}>
              {[1, 2, 3, 4].map((item) => (
                <Grid key={item} size={{ xs: 12, sm: 6, md: 3 }}>
                  <Card sx={{ height: 140 }}>
                    <CardContent>
                      <Skeleton variant="text" width="60%" height={40} />
                      <Skeleton variant="text" width="40%" height={24} />
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          )}
        </Box>
      </Fade>

      {/* Search Bar */}
      <Fade in timeout={1200}>
        <Paper
          elevation={2}
          sx={{
            mb: 4,
            borderRadius: 3,
            overflow: 'hidden',
            border: '1px solid',
            borderColor: 'divider'
          }}
        >
          <Box sx={{ p: 3, background: 'linear-gradient(90deg, #f8f9fa 0%, #ffffff 100%)' }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'text.primary' }}>
              Search Tax Payers
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
              <TextField
                fullWidth
                placeholder="Search by TIN, name, business name, VAT number..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search color="action" />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    backgroundColor: 'white',
                    '&:hover': {
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'primary.main',
                      },
                    },
                  },
                }}
              />
              <Button
                variant="contained"
                onClick={handleSearch}
                disabled={!searchQuery.trim() || searchLoading}
                sx={{
                  minWidth: 120,
                  height: 56,
                  borderRadius: 2,
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                  }
                }}
              >
                {searchLoading ? <CircularProgress size={20} color="inherit" /> : 'Search'}
              </Button>
              {showSearchResults && (
                <Button
                  variant="outlined"
                  onClick={clearSearch}
                  sx={{ minWidth: 100, height: 56 }}
                >
                  Clear
                </Button>
              )}
            </Box>
          </Box>
        </Paper>
      </Fade>

      {/* Search Results */}
      {showSearchResults && (
        <Fade in timeout={800}>
          <Paper
            elevation={2}
            sx={{
              mb: 4,
              borderRadius: 3,
              border: '1px solid',
              borderColor: 'divider'
            }}
          >
            <Box sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'text.primary' }}>
                Search Results ({searchResults.length} found)
              </Typography>

              {searchResults.length > 0 ? (
                <Grid container spacing={2}>
                  {searchResults.map((result) => (
                    <Grid size={{ xs: 12, sm: 6, md: 4 }} key={result.id}>
                      <Card
                        sx={{
                          cursor: 'pointer',
                          transition: 'all 0.2s',
                          '&:hover': {
                            transform: 'translateY(-2px)',
                            boxShadow: 4,
                          }
                        }}
                        onClick={() => handleSearchResultClick(result)}
                      >
                        <CardContent>
                          <Box display="flex" alignItems="center" gap={2} mb={1}>
                            {result.type === 'individual' ? (
                              <Person color="primary" />
                            ) : (
                              <Business color="secondary" />
                            )}
                            <Chip
                              label={result.type === 'individual' ? 'Individual' : 'Organization'}
                              color={result.type === 'individual' ? 'primary' : 'secondary'}
                              size="small"
                            />
                          </Box>
                          <Typography variant="h6" noWrap>
                            {result.name}
                          </Typography>
                          {result.business_name && (
                            <Typography variant="body2" color="text.secondary" noWrap>
                              {result.business_name}
                            </Typography>
                          )}
                          <Typography variant="body2" color="text.secondary">
                            TIN: {result.tin}
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              ) : (
                <Alert severity="info">
                  No taxpayers found matching "{searchQuery}". Try different search terms.
                </Alert>
              )}
            </Box>
          </Paper>
        </Fade>
      )}

      {/* Main Content Tabs */}
      <Fade in timeout={1400}>
        <Paper
          elevation={3}
          sx={{
            borderRadius: 3,
            overflow: 'hidden',
            border: '1px solid',
            borderColor: 'divider'
          }}
        >
          <Box sx={{
            borderBottom: 1,
            borderColor: 'divider',
            background: 'linear-gradient(90deg, #f8f9fa 0%, #ffffff 100%)'
          }}>
            <Tabs
              value={currentTab}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons="auto"
              sx={{
                '& .MuiTab-root': {
                  minHeight: 64,
                  minWidth: 160,
                  textTransform: 'none',
                  fontSize: '0.95rem',
                  fontWeight: 600,
                  py: 2,
                  '&.Mui-selected': {
                    color: 'primary.main',
                    background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',
                  },
                },
                '& .MuiTabs-indicator': {
                  height: 3,
                  borderRadius: '3px 3px 0 0',
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                },
              }}
            >
              <Tab
                icon={<Person />}
                label="Individual Tax Payers"
                iconPosition="start"
              />
              <Tab
                icon={<Business />}
                label="Organization Tax Payers"
                iconPosition="start"
              />
              <Tab
                icon={<Category />}
                label="Business Sectors"
                iconPosition="start"
              />
              <Tab
                icon={<Category />}
                label="Business Sub-Sectors"
                iconPosition="start"
              />
              <Tab
                icon={<Assessment />}
                label="Tax Payer Levels"
                iconPosition="start"
              />
              <Tab
                icon={<AccountBalance />}
                label="Organization Types"
                iconPosition="start"
              />
            </Tabs>
          </Box>

          <TabPanel value={currentTab} index={0}>
            <IndividualTaxPayersTab onDataChange={loadStatistics} />
          </TabPanel>

          <TabPanel value={currentTab} index={1}>
            <OrganizationTaxPayersTab onDataChange={loadStatistics} />
          </TabPanel>

          <TabPanel value={currentTab} index={2}>
            <BusinessSectorsTab onDataChange={loadStatistics} />
          </TabPanel>

          <TabPanel value={currentTab} index={3}>
            <BusinessSubSectorsTab onDataChange={loadStatistics} />
          </TabPanel>

          <TabPanel value={currentTab} index={4}>
            <TaxPayerLevelsTab onDataChange={loadStatistics} />
          </TabPanel>

          <TabPanel value={currentTab} index={5}>
            <OrganizationBusinessTypesTab onDataChange={loadStatistics} />
          </TabPanel>
        </Paper>
      </Fade>

      {/* Quick Actions */}
      <Box sx={{ position: 'fixed', bottom: 24, right: 24, display: 'flex', flexDirection: 'column', gap: 1 }}>
        <Tooltip title="Add Individual Tax Payer" placement="left">
          <IconButton
            color="primary"
            sx={{
              bgcolor: 'primary.main',
              color: 'white',
              width: 56,
              height: 56,
              '&:hover': { bgcolor: 'primary.dark' }
            }}
            onClick={() => setCurrentTab(0)}
          >
            <Person />
          </IconButton>
        </Tooltip>
        <Tooltip title="Add Organization Tax Payer" placement="left">
          <IconButton
            color="secondary"
            sx={{
              bgcolor: 'secondary.main',
              color: 'white',
              width: 56,
              height: 56,
              '&:hover': { bgcolor: 'secondary.dark' }
            }}
            onClick={() => setCurrentTab(1)}
          >
            <Business />
          </IconButton>
        </Tooltip>
      </Box>
    </Container>
  );
};

export default TaxPayersPage;
