import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Ty<PERSON><PERSON>,
  Button,
  Chip,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  Avatar,
  IconButton,
  Tooltip,
  Badge,
  Divider,
} from '@mui/material';
import {
  Notifications,
  TrendingUp,
  Person,
  Business,
  CheckCircle,
  Close,
  Refresh,
  MarkEmailRead,
} from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import taxpayerService from '../../services/taxpayerService';

interface UpgradeNotification {
  id: string;
  title: string;
  message: string;
  taxpayer_name: string;
  analysis_year: number;
  current_level_code: string;
  recommended_level_code: string;
  average_daily_income: number;
  is_read: boolean;
  is_dismissed: boolean;
  created_at: string;
  analysis: string;
}

interface UpgradeNotificationsProps {
  onNotificationProcessed: () => void;
}

const UpgradeNotifications: React.FC<UpgradeNotificationsProps> = ({
  onNotificationProcessed,
}) => {
  const { showNotification } = useNotification();
  const [notifications, setNotifications] = useState<UpgradeNotification[]>([]);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState<string | null>(null);

  const loadNotifications = async () => {
    try {
      setLoading(true);
      const data = await taxpayerService.getUpgradeNotifications({
        ordering: '-created_at',
      });
      setNotifications(data.results);
    } catch (error: any) {
      console.error('Failed to load notifications:', error);
      showNotification('Failed to load notifications', 'error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadNotifications();
  }, []);

  const handleMarkAsRead = async (notificationId: string) => {
    setProcessing(notificationId);
    try {
      await taxpayerService.markNotificationRead(notificationId);
      loadNotifications();
      onNotificationProcessed();
    } catch (error: any) {
      console.error('Failed to mark notification as read:', error);
      showNotification('Failed to mark notification as read', 'error');
    } finally {
      setProcessing(null);
    }
  };

  const handleDismiss = async (notificationId: string) => {
    setProcessing(notificationId);
    try {
      await taxpayerService.dismissNotification(notificationId);
      loadNotifications();
      onNotificationProcessed();
      showNotification('Notification dismissed', 'success');
    } catch (error: any) {
      console.error('Failed to dismiss notification:', error);
      showNotification('Failed to dismiss notification', 'error');
    } finally {
      setProcessing(null);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const unreadCount = notifications.filter(n => !n.is_read).length;

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
        <CircularProgress />
      </Box>
    );
  }

  if (notifications.length === 0) {
    return (
      <Alert severity="info">
        <Typography variant="body2">
          No upgrade notifications at this time. Notifications will appear here when taxpayers are recommended for level upgrades.
        </Typography>
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h6" component="h3" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Badge badgeContent={unreadCount} color="error">
            <Notifications color="primary" />
          </Badge>
          Upgrade Notifications ({notifications.length})
        </Typography>
        <Tooltip title="Refresh Notifications">
          <IconButton onClick={loadNotifications}>
            <Refresh />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Unread Count Alert */}
      {unreadCount > 0 && (
        <Alert severity="warning" sx={{ mb: 2 }}>
          <Typography variant="body2">
            You have <strong>{unreadCount} unread notification{unreadCount !== 1 ? 's' : ''}</strong> 
            requiring your attention.
          </Typography>
        </Alert>
      )}

      {/* Notifications List */}
      <Card>
        <List disablePadding>
          {notifications.map((notification, index) => (
            <React.Fragment key={notification.id}>
              <ListItem
                sx={{
                  bgcolor: notification.is_read ? 'transparent' : 'action.hover',
                  borderLeft: notification.is_read ? 'none' : '4px solid',
                  borderLeftColor: 'primary.main',
                }}
              >
                <ListItemAvatar>
                  <Avatar 
                    sx={{ 
                      bgcolor: notification.is_read ? 'grey.400' : 'primary.main',
                      opacity: notification.is_dismissed ? 0.5 : 1,
                    }}
                  >
                    <TrendingUp />
                  </Avatar>
                </ListItemAvatar>

                <ListItemText
                  primary={
                    <Box display="flex" alignItems="center" gap={1}>
                      <Typography 
                        variant="subtitle2" 
                        sx={{ 
                          fontWeight: notification.is_read ? 'normal' : 'bold',
                          opacity: notification.is_dismissed ? 0.6 : 1,
                        }}
                      >
                        {notification.title}
                      </Typography>
                      {!notification.is_read && (
                        <Chip label="New" color="primary" size="small" />
                      )}
                      {notification.is_dismissed && (
                        <Chip label="Dismissed" color="default" size="small" variant="outlined" />
                      )}
                    </Box>
                  }
                  secondary={
                    <Box sx={{ opacity: notification.is_dismissed ? 0.6 : 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        {notification.message}
                      </Typography>
                      <Box display="flex" alignItems="center" gap={2} flexWrap="wrap">
                        <Typography variant="caption" color="text.secondary">
                          Year: {notification.analysis_year}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Daily Income: {formatCurrency(notification.average_daily_income)}
                        </Typography>
                        <Box display="flex" alignItems="center" gap={0.5}>
                          <Chip 
                            label={`Level ${notification.current_level_code}`} 
                            size="small" 
                            color="default"
                            variant="outlined"
                          />
                          <TrendingUp fontSize="small" color="success" />
                          <Chip 
                            label={`Level ${notification.recommended_level_code}`} 
                            size="small" 
                            color="success"
                            variant="outlined"
                          />
                        </Box>
                        <Typography variant="caption" color="text.secondary">
                          {formatDate(notification.created_at)}
                        </Typography>
                      </Box>
                    </Box>
                  }
                />

                <ListItemSecondaryAction>
                  <Box display="flex" alignItems="center" gap={1}>
                    {!notification.is_read && (
                      <Tooltip title="Mark as Read">
                        <IconButton
                          size="small"
                          onClick={() => handleMarkAsRead(notification.id)}
                          disabled={processing === notification.id}
                        >
                          {processing === notification.id ? (
                            <CircularProgress size={16} />
                          ) : (
                            <MarkEmailRead fontSize="small" />
                          )}
                        </IconButton>
                      </Tooltip>
                    )}
                    
                    {!notification.is_dismissed && (
                      <Tooltip title="Dismiss">
                        <IconButton
                          size="small"
                          onClick={() => handleDismiss(notification.id)}
                          disabled={processing === notification.id}
                        >
                          {processing === notification.id ? (
                            <CircularProgress size={16} />
                          ) : (
                            <Close fontSize="small" />
                          )}
                        </IconButton>
                      </Tooltip>
                    )}
                  </Box>
                </ListItemSecondaryAction>
              </ListItem>
              
              {index < notifications.length - 1 && <Divider />}
            </React.Fragment>
          ))}
        </List>
      </Card>

      {/* Summary */}
      <Box mt={2}>
        <Typography variant="caption" color="text.secondary">
          Showing {notifications.length} notification{notifications.length !== 1 ? 's' : ''}
          {unreadCount > 0 && ` • ${unreadCount} unread`}
        </Typography>
      </Box>
    </Box>
  );
};

export default UpgradeNotifications;
