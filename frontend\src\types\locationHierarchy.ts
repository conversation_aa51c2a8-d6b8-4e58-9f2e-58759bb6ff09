/**
 * Location Hierarchy Type Definitions
 * Centralized type definitions for location hierarchy system
 */

// Base interfaces for location entities
export interface Country {
  id: number;
  name: string;
  code: string;
  iso_code: string;
  population?: number;
  area_km2?: number;
  capital_city?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Region {
  id: number;
  country: number;
  country_name: string;
  name: string;
  code: string;
  population?: number;
  area_km2?: number;
  capital_city?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Zone {
  id: number;
  region: number;
  region_name: string;
  name: string;
  code: string;
  population?: number;
  area_km2?: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface City {
  id: number;
  zone: number;
  zone_name: string;
  name: string;
  code: string;
  population?: number;
  area_km2?: number;
  is_capital?: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface SubCity {
  id: number;
  city: number;
  city_name: string;
  name: string;
  code: string;
  type: 'subcity' | 'woreda' | 'district';
  population?: number;
  area_km2?: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Kebele {
  id: number;
  subcity: number;
  subcity_name: string;
  name: string;
  code: string;
  number: number;
  population?: number;
  area_km2?: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  display_name: string;
  full_name: string;
}

// Create interfaces
export interface CountryCreate {
  name: string;
  code: string;
  iso_code: string;
  population?: number;
  area_km2?: number;
  capital_city?: string;
  is_active?: boolean;
}

export interface RegionCreate {
  country: number;
  name: string;
  code: string;
  population?: number;
  area_km2?: number;
  capital_city?: string;
  is_active?: boolean;
}

export interface ZoneCreate {
  region: number;
  name: string;
  code: string;
  population?: number;
  area_km2?: number;
  is_active?: boolean;
}

export interface CityCreate {
  zone: number;
  name: string;
  code: string;
  population?: number;
  area_km2?: number;
  is_capital?: boolean;
  is_active?: boolean;
}

export interface SubCityCreate {
  city: number;
  name: string;
  code: string;
  type: 'subcity' | 'woreda' | 'district';
  population?: number;
  area_km2?: number;
  is_active?: boolean;
}

export interface KebeleCreate {
  subcity: number;
  name: string;
  code: string;
  number: number;
  population?: number;
  area_km2?: number;
  is_active?: boolean;
}

// Select interfaces for dropdowns
export interface CountrySelect {
  id: number;
  name: string;
  code: string;
  iso_code: string;
}

export interface RegionSelect {
  id: number;
  name: string;
  code: string;
  country: number;
}

export interface ZoneSelect {
  id: number;
  name: string;
  code: string;
  region: number;
}

export interface CitySelect {
  id: number;
  name: string;
  code: string;
  type: string;
  city: number;
}

export interface SubCitySelect {
  id: number;
  name: string;
  code: string;
  type: string;
  city: number;
}

export interface KebeleSelect {
  id: number;
  name: string;
  code: string;
  number: number;
  display_name: string;
  subcity: number;
}

// Special Location interfaces
export interface SpecialLocation {
  id: number;
  kebele: number;
  kebele_name: string;
  kebele_full_name: string;
  name: string;
  is_active: boolean;
  location_path: string;
  display_name: string;
  full_name: string;
  created_at: string;
  updated_at: string;
}

export interface SpecialLocationCreate {
  kebele: number;
  name: string;
  is_active: boolean;
}

export interface SpecialLocationSelect {
  id: number;
  name: string;
  display_name: string;
  kebele: number;
}

// Response interfaces
export interface LocationHierarchyListResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}
