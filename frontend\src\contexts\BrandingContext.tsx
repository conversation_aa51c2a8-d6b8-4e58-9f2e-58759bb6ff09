import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import organizationService from '../services/organizationService';
import type { Organization } from '../services/types';

interface BrandingContextType {
  defaultOrganization: Organization | null;
  systemLogo: string | null;
  systemTitle: string;
  loading: boolean;
  error: string | null;
  refreshBranding: () => Promise<void>;
  getThemeColors: () => {
    primary: string;
    secondary: string;
    accent: string;
  };
  getBrandingInfo: () => {
    name: string;
    shortName: string;
    logo?: string;
    motto?: string;
    tagline?: string;
  };
  getContactInfo: () => {
    email?: string;
    phone?: string;
    fax?: string;
    address?: string;
    website?: string;
    officeHours?: string;
    socialMedia?: Organization['social_media'];
  };
}

const BrandingContext = createContext<BrandingContextType | undefined>(undefined);

interface BrandingProviderProps {
  children: ReactNode;
}

export const BrandingProvider: React.FC<BrandingProviderProps> = ({ children }) => {
  const [defaultOrganization, setDefaultOrganization] = useState<Organization | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDefaultOrganization = async () => {
    try {
      setLoading(true);
      setError(null);

      // Try to get default organization (use public API first to avoid auth errors)
      let org = null;
      try {
        org = await organizationService.getDefaultOrganizationPublic();
        // If public API fails, try authenticated API
        if (!org) {
          org = await organizationService.getDefaultOrganization();
        }
      } catch (error) {
        console.log('Failed to fetch default organization:', error);
      }

      setDefaultOrganization(org);
    } catch (error: any) {
      console.error('Failed to fetch default organization:', error);
      setError('Failed to load branding information');

      // Fallback to default branding
      setDefaultOrganization({
        id: 0,
        name: 'Arada DMS',
        short_name: 'ADMS',
        logo: undefined,
        motto: 'Efficient Document Management',
        tagline: 'Streamlining Your Document Workflow',
        description: 'Professional Document Management System',
        website: '',
        email: '<EMAIL>',
        phone: '+251-11-123-4567',
        fax: '',
        address_line1: '',
        address_line2: '',
        city: 'Addis Ababa',
        state_province: 'Addis Ababa',
        postal_code: '',
        country: 'Ethiopia',
        full_address: 'Addis Ababa, Ethiopia',
        office_hours_start: '08:00',
        office_hours_end: '17:00',
        office_hours_display: '8:00 AM - 5:00 PM',
        established_date: '',
        registration_number: '',
        tax_id: '',
        license_number: '',
        primary_color: '#1976d2',
        secondary_color: '#1565c0',
        accent_color: '#ff9800',
        social_media: {
          facebook: '',
          twitter: '',
          linkedin: '',
          instagram: '',
          youtube: '',
        },
        is_active: true,
        is_default: true,
        document_retention_days: 2555,
        max_file_size_mb: 50,
        user_count: 0,
        department_count: 0,
        created_at: '',
        updated_at: '',
      } as Organization);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDefaultOrganization();
  }, []);

  const refreshBranding = async () => {
    await fetchDefaultOrganization();
  };

  const systemLogo = defaultOrganization?.logo || null;
  const systemTitle = defaultOrganization?.name || 'Document Management System';

  // Update document title when system title changes
  useEffect(() => {
    document.title = systemTitle;
  }, [systemTitle]);

  const getThemeColors = () => {
    return {
      primary: defaultOrganization?.primary_color || '#1976d2',
      secondary: defaultOrganization?.secondary_color || '#1565c0',
      accent: defaultOrganization?.accent_color || '#ff9800',
    };
  };

  const getBrandingInfo = () => {
    // Construct full logo URL if logo exists
    let logoUrl = undefined;
    if (defaultOrganization?.logo) {
      if (defaultOrganization.logo.startsWith('http')) {
        logoUrl = defaultOrganization.logo;
      } else {
        // Remove /api from the base URL for media files
        const baseUrl = (import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000').replace('/api', '');
        // Ensure the logo path starts with /
        const logoPath = defaultOrganization.logo.startsWith('/')
          ? defaultOrganization.logo
          : `/${defaultOrganization.logo}`;
        logoUrl = `${baseUrl}${logoPath}`;
      }
      console.log('Logo URL constructed:', logoUrl);
    }

    return {
      name: defaultOrganization?.name || 'Arada DMS',
      shortName: defaultOrganization?.short_name || 'ADMS',
      logo: logoUrl,
      motto: defaultOrganization?.motto,
      tagline: defaultOrganization?.tagline,
    };
  };

  const getContactInfo = () => {
    return {
      email: defaultOrganization?.email,
      phone: defaultOrganization?.phone,
      fax: defaultOrganization?.fax,
      address: defaultOrganization?.full_address,
      website: defaultOrganization?.website,
      officeHours: defaultOrganization?.office_hours_display,
      socialMedia: defaultOrganization?.social_media,
    };
  };

  const value: BrandingContextType = {
    defaultOrganization,
    systemLogo,
    systemTitle,
    loading,
    error,
    refreshBranding,
    getThemeColors,
    getBrandingInfo,
    getContactInfo,
  };

  return (
    <BrandingContext.Provider value={value}>
      {children}
    </BrandingContext.Provider>
  );
};

export const useBranding = (): BrandingContextType => {
  const context = useContext(BrandingContext);
  if (context === undefined) {
    throw new Error('useBranding must be used within a BrandingProvider');
  }
  return context;
};

// Hook for theme colors
export const useThemeColors = () => {
  const { getThemeColors } = useBranding();
  return getThemeColors();
};

// Hook for branding info
export const useBrandingInfo = () => {
  const { getBrandingInfo } = useBranding();
  return getBrandingInfo();
};

// Hook for contact info
export const useContactInfo = () => {
  const { getContactInfo } = useBranding();
  return getContactInfo();
};
