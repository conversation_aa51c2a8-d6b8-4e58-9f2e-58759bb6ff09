BEGIN;
--
-- <PERSON>reate model Country
--
CREATE TABLE "locations_country" ("id" bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY, "name" varchar(100) NOT NULL UNIQUE, "code" varchar(3) NOT NULL UNIQUE, "phone_code" varchar(10) NOT NULL, "currency" varchar(10) NOT NULL, "is_active" boolean NOT NULL, "created_at" timestamp with time zone NOT NULL, "updated_at" timestamp with time zone NOT NULL);

--
-- Create model Region
--
CREATE TABLE "locations_region" ("id" bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY, "name" varchar(100) NOT NULL, "code" varchar(20) NOT NULL, "population" integer NULL CHECK ("population" >= 0), "area_km2" numeric(10, 2) NULL, "capital_city" varchar(100) NOT NULL, "is_active" boolean NOT NULL, "created_at" timestamp with time zone NOT NULL, "updated_at" timestamp with time zone NOT NULL, "country_id" bigint NOT NULL);

--
-- Create model Zone
--
CREATE TABLE "locations_zone" ("id" bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY, "name" varchar(100) NOT NULL, "code" varchar(20) NOT NULL, "population" integer NULL CHECK ("population" >= 0), "area_km2" numeric(10, 2) NULL, "is_active" boolean NOT NULL, "created_at" timestamp with time zone NOT NULL, "updated_at" timestamp with time zone NOT NULL, "region_id" bigint NOT NULL);

--
-- Create model City
--
CREATE TABLE "locations_city" ("id" bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY, "name" varchar(100) NOT NULL, "code" varchar(20) NOT NULL, "population" integer NULL CHECK ("population" >= 0), "area_km2" numeric(10, 2) NULL, "is_capital" boolean NOT NULL, "is_active" boolean NOT NULL, "created_at" timestamp with time zone NOT NULL, "updated_at" timestamp with time zone NOT NULL, "zone_id" bigint NOT NULL);

--
-- Create model SubCity
--
CREATE TABLE "locations_subcity" ("id" bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY, "name" varchar(100) NOT NULL, "code" varchar(20) NOT NULL, "type" varchar(20) NOT NULL, "population" integer NULL CHECK ("population" >= 0), "area_km2" numeric(10, 2) NULL, "is_active" boolean NOT NULL, "created_at" timestamp with time zone NOT NULL, "updated_at" timestamp with time zone NOT NULL, "city_id" bigint NOT NULL);

--
-- Create model Kebele
--
CREATE TABLE "locations_kebele" ("id" bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY, "name" varchar(100) NOT NULL, "code" varchar(20) NOT NULL, "number" integer NOT NULL CHECK ("number" >= 0), "population" integer NULL CHECK ("population" >= 0), "area_km2" numeric(10, 2) NULL, "is_active" boolean NOT NULL, "created_at" timestamp with time zone NOT NULL, "updated_at" timestamp with time zone NOT NULL, "subcity_id" bigint NOT NULL);

-- Add constraints and indexes
CREATE INDEX "locations_country_name_822b110c_like" ON "locations_country" ("name" varchar_pattern_ops);
CREATE INDEX "locations_country_code_bacab364_like" ON "locations_country" ("code" varchar_pattern_ops);

ALTER TABLE "locations_region" ADD CONSTRAINT "locations_region_country_id_code_bb707df4_uniq" UNIQUE ("country_id", "code");
ALTER TABLE "locations_region" ADD CONSTRAINT "locations_region_country_id_9aae9082_fk_locations_country_id" FOREIGN KEY ("country_id") REFERENCES "locations_country" ("id") DEFERRABLE INITIALLY DEFERRED;
CREATE INDEX "locations_region_country_id_9aae9082" ON "locations_region" ("country_id");

ALTER TABLE "locations_zone" ADD CONSTRAINT "locations_zone_region_id_code_8ad61dbb_uniq" UNIQUE ("region_id", "code");
ALTER TABLE "locations_zone" ADD CONSTRAINT "locations_zone_region_id_adb23be6_fk_locations_region_id" FOREIGN KEY ("region_id") REFERENCES "locations_region" ("id") DEFERRABLE INITIALLY DEFERRED;
CREATE INDEX "locations_zone_region_id_adb23be6" ON "locations_zone" ("region_id");

ALTER TABLE "locations_city" ADD CONSTRAINT "locations_city_zone_id_code_f4c2406f_uniq" UNIQUE ("zone_id", "code");
ALTER TABLE "locations_city" ADD CONSTRAINT "locations_city_zone_id_7699a0e2_fk_locations_zone_id" FOREIGN KEY ("zone_id") REFERENCES "locations_zone" ("id") DEFERRABLE INITIALLY DEFERRED;
CREATE INDEX "locations_city_zone_id_7699a0e2" ON "locations_city" ("zone_id");

ALTER TABLE "locations_subcity" ADD CONSTRAINT "locations_subcity_city_id_code_92e23d7a_uniq" UNIQUE ("city_id", "code");
ALTER TABLE "locations_subcity" ADD CONSTRAINT "locations_subcity_city_id_45db6df1_fk_locations_city_id" FOREIGN KEY ("city_id") REFERENCES "locations_city" ("id") DEFERRABLE INITIALLY DEFERRED;
CREATE INDEX "locations_subcity_city_id_45db6df1" ON "locations_subcity" ("city_id");

ALTER TABLE "locations_kebele" ADD CONSTRAINT "locations_kebele_subcity_id_code_182ce6ea_uniq" UNIQUE ("subcity_id", "code");
ALTER TABLE "locations_kebele" ADD CONSTRAINT "locations_kebele_subcity_id_6a0b2e11_fk_locations_subcity_id" FOREIGN KEY ("subcity_id") REFERENCES "locations_subcity" ("id") DEFERRABLE INITIALLY DEFERRED;
CREATE INDEX "locations_kebele_subcity_id_6a0b2e11" ON "locations_kebele" ("subcity_id");

COMMIT;
