import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  TextField,
  InputAdornment,
  Avatar,
  Divider,
  Stack,
  Fade,
  Alert,
  CircularProgress,
  IconButton,
  Breadcrumbs,
  Link,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TablePagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Inventory,
  ArrowBack,
  Home,
  Add,
  Edit,
  Delete,
  Visibility,
  Archive,
  QrCode,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useNotification } from '../../contexts/NotificationContext';
import locationService from '../../services/locationService';
import ConfirmationDialog from '../../components/common/ConfirmationDialog';
import type { Kent, KentCreate, Box as BoxType } from '../../services/types';

const KentsPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { showNotification } = useNotification();

  const [kents, setKents] = useState<Kent[]>([]);
  const [boxes, setBoxes] = useState<BoxType[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingKent, setEditingKent] = useState<Kent | null>(null);
  const [formData, setFormData] = useState({
    box: '',
    name: '',
    code: '',
    description: '',
    color: '#2196f3',
    material: 'cardboard',
    capacity: 50,
  });
  const [formErrors, setFormErrors] = useState<any>({});
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [kentToDelete, setKentToDelete] = useState<Kent | null>(null);
  const [deleting, setDeleting] = useState(false);

  const materialOptions = [
    { value: 'cardboard', label: 'Cardboard' },
    { value: 'plastic', label: 'Plastic' },
    { value: 'metal', label: 'Metal' },
    { value: 'wood', label: 'Wood' },
    { value: 'other', label: 'Other' },
  ];

  const colorOptions = [
    { value: '#2196f3', label: 'Blue' },
    { value: '#4caf50', label: 'Green' },
    { value: '#ff9800', label: 'Orange' },
    { value: '#f44336', label: 'Red' },
    { value: '#9c27b0', label: 'Purple' },
    { value: '#607d8b', label: 'Blue Grey' },
    { value: '#795548', label: 'Brown' },
    { value: '#000000', label: 'Black' },
  ];

  useEffect(() => {
    loadKents();
    loadBoxes();
  }, [page, rowsPerPage]);

  // Handle navigation state from detail page
  useEffect(() => {
    const state = location.state as any;
    if (state?.editKent && state?.showForm) {
      handleEdit(state.editKent);
      // Clear the state to prevent re-triggering
      navigate(location.pathname, { replace: true });
    }
  }, [location.state]);

  const loadKents = async () => {
    try {
      setLoading(true);
      const response = await locationService.getKents({
        page: page + 1,
        page_size: rowsPerPage,
      });
      setKents(response.results);
      setTotalCount(response.count);
    } catch (error) {
      console.error('Error loading kents:', error);
      showNotification('Failed to load kents', 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadBoxes = async () => {
    try {
      const response = await locationService.getBoxes({ page_size: 100 });
      setBoxes(response.results);
    } catch (error) {
      console.error('Error loading boxes:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setSubmitting(true);
      setFormErrors({});

      if (editingKent) {
        await locationService.updateKent(editingKent.id, formData);
        showNotification('Kent updated successfully', 'success');
      } else {
        await locationService.createKent(formData);
        showNotification('Kent created successfully', 'success');
      }

      resetForm();
      loadKents();
    } catch (error: any) {
      console.error('Error submitting form:', error);
      console.log('Error response:', error.response);

      if (error.response?.status === 400 && error.response?.data) {
        // Handle validation errors
        setFormErrors(error.response.data);
        showNotification('Please fix the validation errors', 'error');
      } else if (error.response?.status === 500) {
        // Handle server errors
        console.error('Server error:', error.response.data);
        showNotification('Server error occurred. Please try again.', 'error');
      } else {
        // Handle other errors
        showNotification('Failed to save kent', 'error');
      }
    } finally {
      setSubmitting(false);
    }
  };

  const handleViewDetails = (kent: Kent) => {
    navigate(`/locations/kents/${kent.id}`);
  };

  const handleEdit = (kent: Kent) => {
    setEditingKent(kent);
    setFormData({
      box: kent.box,
      name: kent.name,
      code: kent.code,
      description: kent.description || '',
      color: kent.color || '#2196f3',
      material: kent.material || 'cardboard',
      capacity: kent.capacity || 50,
    });
    setShowForm(true);
  };

  const handleDelete = (kent: Kent) => {
    setKentToDelete(kent);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!kentToDelete) return;
    
    try {
      setDeleting(true);
      await locationService.deleteKent(kentToDelete.id);
      showNotification('Kent deleted successfully', 'success');
      loadKents();
      handleCloseDeleteDialog();
    } catch (error) {
      console.error('Error deleting kent:', error);
      showNotification('Failed to delete kent', 'error');
    } finally {
      setDeleting(false);
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setKentToDelete(null);
    setDeleting(false);
  };

  const generateUniqueCode = () => {
    // Generate a unique code based on timestamp
    const timestamp = Date.now().toString().slice(-4);
    return `K${timestamp}`;
  };

  const resetForm = () => {
    setFormData({
      box: boxes.length > 0 ? boxes[0].id : '',
      name: '',
      code: generateUniqueCode(),
      description: '',
      color: '#2196f3',
      material: 'cardboard',
      capacity: 50,
    });
    setFormErrors({});
    setEditingKent(null);
    setShowForm(false);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Fade in timeout={800}>
        <Box sx={{ mb: 4 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs sx={{ mb: 2 }}>
            <Link
              component="button"
              variant="body1"
              onClick={() => navigate('/locations')}
              sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
            >
              <Home fontSize="small" />
              Physical Location Center
            </Link>
            <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <Inventory fontSize="small" />
              Kents
            </Typography>
          </Breadcrumbs>

          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'between', mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <IconButton onClick={() => navigate('/locations')} sx={{ mr: 1 }}>
                <ArrowBack />
              </IconButton>
              <Avatar sx={{ bgcolor: 'warning.main' }}>
                <Inventory />
              </Avatar>
              <Box>
                <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
                  Kents Management
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Manage individual storage containers within boxes
                </Typography>
              </Box>
            </Box>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setShowForm(true)}
              size="large"
              sx={{ ml: 'auto' }}
            >
              Add Kent
            </Button>
          </Box>
        </Box>
      </Fade>

      {/* Form Section */}
      {showForm && (
        <Fade in timeout={600}>
          <Card sx={{ mb: 4 }}>
            <CardContent sx={{ p: 4 }}>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                {editingKent ? 'Edit Kent' : 'Add New Kent'}
              </Typography>
              <Divider sx={{ mb: 3 }} />
              
              <form onSubmit={handleSubmit}>
                <Stack spacing={3}>
                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                    <TextField
                      label="Kent Name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      error={!!formErrors.name}
                      helperText={formErrors.name || 'Enter the kent name or identifier'}
                      required
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Inventory color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />

                    <TextField
                      label="Kent Code"
                      value={formData.code}
                      onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
                      error={!!formErrors.code}
                      helperText={formErrors.code || 'e.g., K1, K2, KT01'}
                      required
                      inputProps={{ maxLength: 10 }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Inventory color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>
                  
                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                    <FormControl fullWidth required error={!!formErrors.box}>
                      <InputLabel>Box Position</InputLabel>
                      <Select
                        value={formData.box}
                        onChange={(e) => setFormData({ ...formData, box: Number(e.target.value) })}
                        label="Box Position"
                        startAdornment={
                          <InputAdornment position="start">
                            <Archive color="action" />
                          </InputAdornment>
                        }
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: 300,
                            },
                          },
                        }}
                      >
                        {boxes.map((box) => (
                          <MenuItem key={box.id} value={box.id}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                              <Chip
                                label={`R${box.row}C${box.column}`}
                                size="small"
                                color="primary"
                                variant="outlined"
                              />
                              <Box sx={{ flexGrow: 1 }}>
                                <Typography variant="body2" fontWeight="medium">
                                  {box.shelf_name ? `${box.shelf_name} - Position ${box.row}${box.column}` : `Position R${box.row}C${box.column}`}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {box.building_name && `${box.building_name} • `}
                                  {box.kent_count || 0} kents • {box.available_space || 0} available
                                </Typography>
                              </Box>
                              {box.is_full && (
                                <Chip label="Full" size="small" color="error" />
                              )}
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                      {formErrors.box && (
                        <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 2 }}>
                          {formErrors.box}
                        </Typography>
                      )}
                    </FormControl>
                    
                    <TextField
                      label="Capacity"
                      type="number"
                      value={formData.capacity}
                      onChange={(e) => setFormData({ ...formData, capacity: parseInt(e.target.value) || 0 })}
                      error={!!formErrors.capacity}
                      helperText={formErrors.capacity || 'Maximum files this kent can hold'}
                      required
                      inputProps={{ min: 1, max: 1000 }}
                    />
                  </Box>

                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                    <FormControl fullWidth error={!!formErrors.material}>
                      <InputLabel>Material</InputLabel>
                      <Select
                        value={formData.material}
                        onChange={(e) => setFormData({ ...formData, material: e.target.value })}
                        label="Material"
                      >
                        {materialOptions.map((option) => (
                          <MenuItem key={option.value} value={option.value}>
                            {option.label}
                          </MenuItem>
                        ))}
                      </Select>
                      {formErrors.material && (
                        <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 2 }}>
                          {formErrors.material}
                        </Typography>
                      )}
                    </FormControl>

                    <FormControl fullWidth error={!!formErrors.color}>
                      <InputLabel>Color</InputLabel>
                      <Select
                        value={formData.color}
                        onChange={(e) => setFormData({ ...formData, color: e.target.value })}
                        label="Color"
                      >
                        {colorOptions.map((option) => (
                          <MenuItem key={option.value} value={option.value}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Box
                                sx={{
                                  width: 16,
                                  height: 16,
                                  borderRadius: '50%',
                                  bgcolor: option.value,
                                  border: '1px solid #ccc',
                                }}
                              />
                              {option.label}
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                      {formErrors.color && (
                        <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 2 }}>
                          {formErrors.color}
                        </Typography>
                      )}
                    </FormControl>
                  </Box>

                  <TextField
                    label="Description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    error={!!formErrors.description}
                    helperText={formErrors.description || 'Optional description for this kent'}
                    multiline
                    rows={3}
                    fullWidth
                  />

                  <Box sx={{ display: 'flex', gap: 2, pt: 2 }}>
                    <Button
                      type="submit"
                      variant="contained"
                      disabled={submitting}
                      startIcon={submitting ? <CircularProgress size={20} /> : <Add />}
                      size="large"
                    >
                      {submitting ? 'Saving...' : editingKent ? 'Update Kent' : 'Create Kent'}
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={resetForm}
                      size="large"
                    >
                      Cancel
                    </Button>
                  </Box>
                </Stack>
              </form>
            </CardContent>
          </Card>
        </Fade>
      )}

      {/* Kents Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
            Kents List
          </Typography>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : kents.length === 0 ? (
            <Alert severity="info">
              No kents found. Click "Add Kent" to create your first kent.
            </Alert>
          ) : (
            <>
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Kent</TableCell>
                      <TableCell>Location</TableCell>
                      <TableCell>Capacity</TableCell>
                      <TableCell>Material</TableCell>
                      <TableCell>QR Code</TableCell>
                      <TableCell>Created</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {kents.map((kent) => (
                      <TableRow key={kent.id} hover>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <Avatar
                              sx={{
                                bgcolor: kent.color || '#2196f3',
                                width: 32,
                                height: 32
                              }}
                            >
                              <Inventory fontSize="small" />
                            </Avatar>
                            <Box>
                              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                {kent.name}
                              </Typography>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Chip label={kent.code} size="small" color="primary" />
                                <Typography variant="caption" color="text.secondary">
                                  {kent.description || 'No description'}
                                </Typography>
                              </Box>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {kent.location_path || `Box ${kent.box}`}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Box>
                            <Typography variant="body2">
                              {kent.file_count || 0} / {kent.capacity || 0} files
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {kent.capacity ? Math.round(((kent.file_count || 0) / kent.capacity) * 100) : 0}% full
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={kent.material || 'cardboard'}
                            size="small"
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell>
                          <IconButton size="small" color="primary">
                            <QrCode fontSize="small" />
                          </IconButton>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {new Date(kent.created_at).toLocaleDateString()}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                            <IconButton
                              size="small"
                              onClick={() => handleViewDetails(kent)}
                              color="info"
                              title="View Details"
                            >
                              <Visibility fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleEdit(kent)}
                              color="primary"
                              title="Edit"
                            >
                              <Edit fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleDelete(kent)}
                              color="error"
                              title="Delete"
                            >
                              <Delete fontSize="small" />
                            </IconButton>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              <TablePagination
                component="div"
                count={totalCount}
                page={page}
                onPageChange={handleChangePage}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                rowsPerPageOptions={[5, 10, 25, 50]}
              />
            </>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        title="Delete Kent"
        itemName={kentToDelete?.name}
        itemType="Kent"
        message={`Are you sure you want to delete "${kentToDelete?.name}"? This will also delete all files within this kent. This action cannot be undone.`}
        confirmText="Delete Kent"
        severity="error"
        loading={deleting}
      />
    </Container>
  );
};

export default KentsPage;
