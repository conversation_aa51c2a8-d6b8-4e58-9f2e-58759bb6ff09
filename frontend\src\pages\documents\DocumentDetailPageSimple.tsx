import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Avatar,
  Chip,
  Grid,
  Card,
  CardContent,
  Divider,
  IconButton,
  Alert,
  CircularProgress,
  Breadcrumbs,
  Link,
} from '@mui/material';
import {
  ArrowBack,
  Edit,
  Delete,
  Download,
  Visibility,
  Print,
  Description,
  Home,
  Folder,
  CalendarToday,
  Person,
  Business,
  Tag,
  Schedule,
  Warning,
  CheckCircle,
  Info,
  QrCode,
  CropFree,
  CloudDownload,
} from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import documentService from '../../services/documentService';
import type { Document } from '../../services/documentService';
import ConfirmationDialog from '../../components/common/ConfirmationDialog';

const DocumentDetailPageSimple: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const { showSuccess, showError } = useNotification();

  // Determine the back navigation path based on current route
  const getBackPath = () => {
    if (location.pathname.includes('/document-center/')) {
      return '/document-center/documents';
    }
    return '/documents';
  };

  const [document, setDocument] = useState<Document | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    loadDocument();
  }, [id]);

  const loadDocument = async () => {
    if (!id) return;

    try {
      setLoading(true);
      setError(null);
      const data = await documentService.getDocument(id);
      setDocument(data);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to load document');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    // Navigate to the documents list page with edit state
    const backPath = getBackPath();
    navigate(backPath, {
      state: {
        editDocument: document,
        showForm: true
      }
    });
  };

  const handleDelete = () => {
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!document) return;

    try {
      setDeleting(true);
      await documentService.deleteDocument(document.id);
      showSuccess('Document deleted successfully');
      navigate(getBackPath());
    } catch (error) {
      console.error('Error deleting document:', error);
      showError('Failed to delete document');
    } finally {
      setDeleting(false);
      setDeleteDialogOpen(false);
    }
  };

  const handleView = async () => {
    if (!document || !document.file) {
      showError('No file available for viewing');
      return;
    }

    try {
      const url = await documentService.viewDocument(document.id);
      window.open(url, '_blank');
    } catch (err: any) {
      console.error('View failed:', err);
      showError(`Failed to view document: ${err.message || 'Unknown error'}`);
    }
  };

  const handleDownload = async () => {
    if (!document || !document.file) {
      showError('No file available for download');
      return;
    }

    try {
      const blob = await documentService.downloadDocument(document.id);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${document.title}.${document.file.split('.').pop()}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      showSuccess('Document downloaded successfully');
    } catch (err: any) {
      console.error('Download failed:', err);
      showError(`Failed to download document: ${err.message || 'Unknown error'}`);
    }
  };

  const handlePrint = async () => {
    if (!document || !document.file) {
      showError('No file available for printing');
      return;
    }

    try {
      await documentService.printDocument(document.id);
      showSuccess('Document sent to printer');
    } catch (err: any) {
      console.error('Print failed:', err);
      showError(`Failed to print document: ${err.message || 'Unknown error'}`);
    }
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (!document) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error">Document not found</Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 3 }}>
        <Link
          component="button"
          variant="body2"
          onClick={() => navigate('/dashboard')}
          sx={{ display: 'flex', alignItems: 'center', gap: 0.5, textDecoration: 'none' }}
        >
          <Home fontSize="small" />
          Dashboard
        </Link>
        <Link
          component="button"
          variant="body2"
          onClick={() => navigate(getBackPath())}
          sx={{ display: 'flex', alignItems: 'center', gap: 0.5, textDecoration: 'none' }}
        >
          <Description fontSize="small" />
          Documents
        </Link>
        <Typography variant="body2" color="text.primary">
          {document.title}
        </Typography>
      </Breadcrumbs>

      {/* Header */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<ArrowBack />}
              onClick={() => navigate(getBackPath())}
            >
              Back to Documents
            </Button>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            {document.file && (
              <>
                <Button
                  variant="outlined"
                  startIcon={<Visibility />}
                  onClick={handleView}
                  color="info"
                >
                  View
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<Print />}
                  onClick={handlePrint}
                  color="secondary"
                >
                  Print
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<Download />}
                  onClick={handleDownload}
                  color="success"
                >
                  Download
                </Button>
              </>
            )}
            <Button
              variant="outlined"
              startIcon={<Edit />}
              onClick={handleEdit}
              color="primary"
            >
              Edit
            </Button>
            <Button
              variant="outlined"
              startIcon={<Delete />}
              onClick={handleDelete}
              color="error"
            >
              Delete
            </Button>
          </Box>
        </Box>

        {/* Document Title and Status */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
          <Avatar sx={{ bgcolor: 'primary.main', width: 64, height: 64 }}>
            <Description fontSize="large" />
          </Avatar>
          <Box sx={{ flex: 1 }}>
            <Typography variant="h4" component="h1" sx={{ fontWeight: 600, mb: 1 }}>
              {document.title}
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Chip
                label={document.mode?.charAt(0).toUpperCase() + document.mode?.slice(1)}
                color={document.mode === 'digital' ? 'info' : document.mode === 'hybrid' ? 'warning' : 'default'}
                size="small"
              />
              <Chip
                label={document.status?.charAt(0).toUpperCase() + document.status?.slice(1)}
                color={document.status === 'active' ? 'success' : 'default'}
                size="small"
              />
              <Chip
                label={document.document_type_name}
                color="primary"
                variant="outlined"
                size="small"
              />
              {document.is_expired && (
                <Chip
                  label="Expired"
                  color="error"
                  size="small"
                />
              )}
            </Box>
          </Box>
        </Box>
      </Paper>

      {/* Content */}
      <Grid container spacing={3}>
        {/* Main Information */}
        <Grid item xs={12} md={8}>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Info color="primary" />
                Document Information
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <Grid container spacing={2}>
                {document.description && (
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {document.description}
                    </Typography>
                  </Grid>
                )}

                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <Description color="action" />
                    <Box>
                      <Typography variant="caption" color="text.secondary">
                        Document Type
                      </Typography>
                      <Typography variant="body2">
                        {document.document_type_name}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                {document.reference_number && (
                  <Grid item xs={12} sm={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                      <Tag color="action" />
                      <Box>
                        <Typography variant="caption" color="text.secondary">
                          Reference Number
                        </Typography>
                        <Typography variant="body2">
                          {document.reference_number}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>
                )}

                {document.number_of_pages && (
                  <Grid item xs={12} sm={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                      <Description color="action" />
                      <Box>
                        <Typography variant="caption" color="text.secondary">
                          Number of Pages
                        </Typography>
                        <Typography variant="body2">
                          {document.number_of_pages}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>
                )}

                {document.file_name && (
                  <Grid item xs={12} sm={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                      <Folder color="action" />
                      <Box>
                        <Typography variant="caption" color="text.secondary">
                          Business File
                        </Typography>
                        <Typography variant="body2">
                          {document.file_name}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>
                )}

                {document.kent_location && (
                  <Grid item xs={12} sm={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                      <Business color="action" />
                      <Box>
                        <Typography variant="caption" color="text.secondary">
                          Physical Location
                        </Typography>
                        <Typography variant="body2">
                          {document.kent_location}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>
                )}
              </Grid>
            </CardContent>
          </Card>

          {/* File Information */}
          {document.file && (
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <CloudDownload color="primary" />
                  Digital File
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="caption" color="text.secondary">
                      File Size
                    </Typography>
                    <Typography variant="body2">
                      {documentService.formatFileSize(document.file_size)}
                    </Typography>
                  </Grid>

                  <Grid item xs={12}>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Button
                        variant="contained"
                        startIcon={<Visibility />}
                        onClick={handleView}
                        size="small"
                      >
                        View Document
                      </Button>
                      <Button
                        variant="outlined"
                        startIcon={<Print />}
                        onClick={handlePrint}
                        size="small"
                      >
                        Print
                      </Button>
                      <Button
                        variant="outlined"
                        startIcon={<Download />}
                        onClick={handleDownload}
                        size="small"
                      >
                        Download
                      </Button>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          )}
        </Grid>

        {/* Sidebar */}
        <Grid item xs={12} md={4}>
          {/* Dates */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CalendarToday color="primary" />
                Important Dates
              </Typography>
              <Divider sx={{ mb: 2 }} />

              {document.document_date && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" color="text.secondary">
                    Document Date
                  </Typography>
                  <Typography variant="body2">
                    {new Date(document.document_date).toLocaleDateString()}
                  </Typography>
                </Box>
              )}

              {document.expiry_date && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" color="text.secondary">
                    Expiry Date
                  </Typography>
                  <Typography variant="body2" color={document.is_expired ? 'error' : 'text.primary'}>
                    {new Date(document.expiry_date).toLocaleDateString()}
                    {document.is_expired && ' (Expired)'}
                  </Typography>
                </Box>
              )}

              <Box sx={{ mb: 2 }}>
                <Typography variant="caption" color="text.secondary">
                  Created
                </Typography>
                <Typography variant="body2">
                  {new Date(document.created_at).toLocaleDateString()}
                </Typography>
              </Box>

              <Box>
                <Typography variant="caption" color="text.secondary">
                  Last Updated
                </Typography>
                <Typography variant="body2">
                  {new Date(document.updated_at).toLocaleDateString()}
                </Typography>
              </Box>
            </CardContent>
          </Card>

          {/* Creator */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Person color="primary" />
                Created By
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <Typography variant="body2">
                {document.created_by_name || 'Unknown'}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleConfirmDelete}
        title="Delete Document"
        itemName={document.title}
        itemType="Document"
        message={`Are you sure you want to delete "${document.title}"? This action cannot be undone.`}
        confirmText="Delete Document"
        severity="error"
        loading={deleting}
      />
    </Container>
  );
};

export default DocumentDetailPageSimple;
