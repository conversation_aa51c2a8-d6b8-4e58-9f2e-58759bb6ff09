/**
 * Revenue Periods Management Page
 * 
 * Manages revenue collection periods with CRUD operations
 * and period status tracking.
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControlLabel,
  Switch,
  CircularProgress,
  Alert,
  Tooltip,
  Grid,
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  DateRange,
  TrendingUp,
  CheckCircle,
  Schedule,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { useNotification } from '../../contexts/NotificationContext';
import revenueCollectionService from '../../services/revenueCollectionService';
import type {
  RevenuePeriod,
  RevenuePeriodCreate,
} from '../../services/revenueCollectionService';

const PeriodsPage: React.FC = () => {
  const { showSuccess, showError } = useNotification();
  
  const [loading, setLoading] = useState(true);
  const [periods, setPeriods] = useState<RevenuePeriod[]>([]);
  const [currentPeriod, setCurrentPeriod] = useState<RevenuePeriod | null>(null);
  
  // Dialog states
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogMode, setDialogMode] = useState<'create' | 'edit'>('create');
  const [editingItem, setEditingItem] = useState<RevenuePeriod | null>(null);
  
  // Form states
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    start_date: null as Date | null,
    end_date: null as Date | null,
    is_closed: false,
  });

  useEffect(() => {
    loadPeriods();
    loadCurrentPeriod();
  }, []);

  const loadPeriods = async () => {
    try {
      setLoading(true);
      const response = await revenueCollectionService.getRevenuePeriods({
        ordering: '-start_date',
      });
      setPeriods(response.results);
    } catch (error) {
      console.error('Error loading periods:', error);
      showError('Failed to load periods');
    } finally {
      setLoading(false);
    }
  };

  const loadCurrentPeriod = async () => {
    try {
      const current = await revenueCollectionService.getCurrentPeriod();
      setCurrentPeriod(current);
    } catch (error) {
      console.warn('No current period found');
      setCurrentPeriod(null);
    }
  };

  const handleOpenDialog = (mode: 'create' | 'edit', item?: RevenuePeriod) => {
    setDialogMode(mode);
    setEditingItem(item || null);
    
    if (mode === 'edit' && item) {
      setFormData({
        name: item.name,
        code: item.code,
        start_date: new Date(item.start_date),
        end_date: new Date(item.end_date),
        is_closed: item.is_closed,
      });
    } else {
      setFormData({
        name: '',
        code: '',
        start_date: null,
        end_date: null,
        is_closed: false,
      });
    }
    
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingItem(null);
    setFormData({
      name: '',
      code: '',
      start_date: null,
      end_date: null,
      is_closed: false,
    });
  };

  const handleSubmit = async () => {
    if (!formData.start_date || !formData.end_date) {
      showError('Please select both start and end dates');
      return;
    }

    try {
      const submitData: RevenuePeriodCreate = {
        name: formData.name,
        code: formData.code,
        start_date: formData.start_date.toISOString().split('T')[0],
        end_date: formData.end_date.toISOString().split('T')[0],
        is_closed: formData.is_closed,
      };

      if (dialogMode === 'create') {
        await revenueCollectionService.createRevenuePeriod(submitData);
        showSuccess('Revenue period created successfully');
      } else if (editingItem) {
        await revenueCollectionService.updateRevenuePeriod(editingItem.id, submitData);
        showSuccess('Revenue period updated successfully');
      }
      
      handleCloseDialog();
      loadPeriods();
      loadCurrentPeriod();
    } catch (error) {
      console.error('Error saving period:', error);
      showError('Failed to save period');
    }
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this period?')) {
      return;
    }

    try {
      await revenueCollectionService.deleteRevenuePeriod(id);
      showSuccess('Revenue period deleted successfully');
      loadPeriods();
      loadCurrentPeriod();
    } catch (error) {
      console.error('Error deleting period:', error);
      showError('Failed to delete period');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-ET', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ET', {
      style: 'currency',
      currency: 'ETB',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getPeriodStatus = (period: RevenuePeriod) => {
    if (period.is_closed) {
      return { label: 'Closed', color: 'default' as const };
    }
    if (period.is_current) {
      return { label: 'Current', color: 'success' as const };
    }
    const today = new Date();
    const startDate = new Date(period.start_date);
    if (startDate > today) {
      return { label: 'Future', color: 'info' as const };
    }
    return { label: 'Past', color: 'warning' as const };
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box sx={{ flexGrow: 1, p: 3 }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Revenue Periods
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Manage revenue collection periods and their status
          </Typography>
        </Box>

        {/* Current Period Alert */}
        {currentPeriod && (
          <Alert severity="info" sx={{ mb: 3 }}>
            <Typography variant="body2">
              Current Active Period: <strong>{currentPeriod.name}</strong> 
              ({formatDate(currentPeriod.start_date)} - {formatDate(currentPeriod.end_date)})
            </Typography>
          </Alert>
        )}

        {/* Statistics Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <DateRange color="primary" sx={{ mr: 1 }} />
                  <Typography variant="h6" component="div">
                    Total Periods
                  </Typography>
                </Box>
                <Typography variant="h4" component="div" color="primary">
                  {periods.length}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <CheckCircle color="success" sx={{ mr: 1 }} />
                  <Typography variant="h6" component="div">
                    Closed Periods
                  </Typography>
                </Box>
                <Typography variant="h4" component="div" color="success.main">
                  {periods.filter(p => p.is_closed).length}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Schedule color="warning" sx={{ mr: 1 }} />
                  <Typography variant="h6" component="div">
                    Active Periods
                  </Typography>
                </Box>
                <Typography variant="h4" component="div" color="warning.main">
                  {periods.filter(p => !p.is_closed).length}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <TrendingUp color="secondary" sx={{ mr: 1 }} />
                  <Typography variant="h6" component="div">
                    Current Period
                  </Typography>
                </Box>
                <Typography variant="h6" component="div" color="secondary">
                  {currentPeriod ? currentPeriod.name : 'None'}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Periods Table */}
        <Card>
          <CardContent>
            <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="h6">
                Revenue Periods ({periods.length})
              </Typography>
              <Button
                variant="contained"
                startIcon={<Add />}
                onClick={() => handleOpenDialog('create')}
              >
                Add Period
              </Button>
            </Box>

            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Code</TableCell>
                    <TableCell>Name</TableCell>
                    <TableCell>Start Date</TableCell>
                    <TableCell>End Date</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Collections</TableCell>
                    <TableCell>Total Revenue</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {periods.map((period) => {
                    const status = getPeriodStatus(period);
                    return (
                      <TableRow key={period.id}>
                        <TableCell>
                          <Typography variant="body2" fontWeight="medium">
                            {period.code}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {period.name}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {formatDate(period.start_date)}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {formatDate(period.end_date)}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={status.label}
                            color={status.color}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {period.collections_count.total.toLocaleString()}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            R: {period.collections_count.regional} | C: {period.collections_count.city_service}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" fontWeight="medium">
                            {formatCurrency(period.total_revenue.grand_total)}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            R: {formatCurrency(period.total_revenue.regional)} | C: {formatCurrency(period.total_revenue.city_service)}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Tooltip title="Edit">
                            <IconButton
                              size="small"
                              onClick={() => handleOpenDialog('edit', period)}
                            >
                              <Edit />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete">
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => handleDelete(period.id)}
                            >
                              <Delete />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                  {periods.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={8} align="center">
                        <Typography variant="body2" color="text.secondary">
                          No periods found
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>

        {/* Create/Edit Dialog */}
        <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
          <DialogTitle>
            {dialogMode === 'create' ? 'Create' : 'Edit'} Revenue Period
          </DialogTitle>
          <DialogContent>
            <Box sx={{ pt: 1 }}>
              <TextField
                fullWidth
                label="Period Name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                margin="normal"
                required
                helperText="e.g., Q1 2025, January 2025"
              />
              <TextField
                fullWidth
                label="Period Code"
                value={formData.code}
                onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
                margin="normal"
                required
                helperText="Unique code for this period (e.g., 2025Q1, 2025-01)"
              />
              <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
                <DatePicker
                  label="Start Date"
                  value={formData.start_date}
                  onChange={(date) => setFormData({ ...formData, start_date: date })}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      required: true,
                    },
                  }}
                />
                <DatePicker
                  label="End Date"
                  value={formData.end_date}
                  onChange={(date) => setFormData({ ...formData, end_date: date })}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      required: true,
                    },
                  }}
                />
              </Box>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.is_closed}
                    onChange={(e) => setFormData({ ...formData, is_closed: e.target.checked })}
                  />
                }
                label="Closed for new entries"
                sx={{ mt: 2 }}
              />
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Cancel</Button>
            <Button
              onClick={handleSubmit}
              variant="contained"
              disabled={!formData.name || !formData.code || !formData.start_date || !formData.end_date}
            >
              {dialogMode === 'create' ? 'Create' : 'Update'}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </LocalizationProvider>
  );
};

export default PeriodsPage;
