"""
Revenue Collection Serializers

This module provides DRF serializers for all revenue collection models
with proper validation, nested relationships, and read/write capabilities.
"""

from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    RegionalCategory, CityServiceCategory,
    RegionalRevenueSource, CityServiceRevenueSource,
    RevenuePeriod,
    RegionalRevenueCollection, CityServiceRevenueCollection,
    RevenueSummary, TaxpayerPaymentSummary
)
from taxpayers.serializers import IndividualTaxPayerSerializer, OrganizationTaxPayerSerializer

User = get_user_model()


class RegionalCategorySerializer(serializers.ModelSerializer):
    """Serializer for Regional Categories"""
    revenue_sources_count = serializers.SerializerMethodField()
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    
    class Meta:
        model = RegionalCategory
        fields = [
            'id', 'name', 'code', 'description', 'is_active',
            'revenue_sources_count', 'created_by', 'created_by_name',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'created_by']
    
    def get_revenue_sources_count(self, obj):
        return obj.revenue_sources.count()


class CityServiceCategorySerializer(serializers.ModelSerializer):
    """Serializer for City Service Categories"""
    revenue_sources_count = serializers.SerializerMethodField()
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    
    class Meta:
        model = CityServiceCategory
        fields = [
            'id', 'name', 'code', 'description', 'is_active',
            'revenue_sources_count', 'created_by', 'created_by_name',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'created_by']
    
    def get_revenue_sources_count(self, obj):
        return obj.revenue_sources.count()


class RegionalRevenueSourceSerializer(serializers.ModelSerializer):
    """Serializer for Regional Revenue Sources"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    category_code = serializers.CharField(source='category.code', read_only=True)
    collections_count = serializers.SerializerMethodField()
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    
    class Meta:
        model = RegionalRevenueSource
        fields = [
            'id', 'name', 'code', 'description', 'is_active',
            'category', 'category_name', 'category_code',
            'collections_count', 'created_by', 'created_by_name',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'created_by']
    
    def get_collections_count(self, obj):
        return obj.regionalrevenuecollection_set.count()


class CityServiceRevenueSourceSerializer(serializers.ModelSerializer):
    """Serializer for City Service Revenue Sources"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    category_code = serializers.CharField(source='category.code', read_only=True)
    collections_count = serializers.SerializerMethodField()
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    
    class Meta:
        model = CityServiceRevenueSource
        fields = [
            'id', 'name', 'code', 'description', 'is_active',
            'category', 'category_name', 'category_code',
            'collections_count', 'created_by', 'created_by_name',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'created_by']
    
    def get_collections_count(self, obj):
        return obj.cityservicerevenuecollection_set.count()


class RevenuePeriodSerializer(serializers.ModelSerializer):
    """Serializer for Revenue Periods"""
    is_current = serializers.BooleanField(read_only=True)
    collections_count = serializers.SerializerMethodField()
    total_revenue = serializers.SerializerMethodField()
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    
    class Meta:
        model = RevenuePeriod
        fields = [
            'id', 'name', 'code', 'start_date', 'end_date', 'is_closed',
            'is_current', 'collections_count', 'total_revenue',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'created_by']
    
    def get_collections_count(self, obj):
        regional_count = obj.regionalrevenuecollection_set.count()
        city_count = obj.cityservicerevenuecollection_set.count()
        return {
            'regional': regional_count,
            'city_service': city_count,
            'total': regional_count + city_count
        }
    
    def get_total_revenue(self, obj):
        from django.db.models import Sum
        regional_total = obj.regionalrevenuecollection_set.aggregate(
            total=Sum('amount')
        )['total'] or 0
        city_total = obj.cityservicerevenuecollection_set.aggregate(
            total=Sum('amount')
        )['total'] or 0
        return {
            'regional': float(regional_total),
            'city_service': float(city_total),
            'grand_total': float(regional_total + city_total)
        }


class BaseRevenueCollectionSerializer(serializers.ModelSerializer):
    """Base serializer for revenue collections"""
    taxpayer_name = serializers.CharField(read_only=True)
    taxpayer_tin = serializers.CharField(read_only=True)
    taxpayer_type = serializers.SerializerMethodField()
    recorded_by_name = serializers.CharField(source='recorded_by.username', read_only=True)
    last_modified_by_name = serializers.CharField(source='last_modified_by.username', read_only=True)
    period_name = serializers.CharField(source='period.name', read_only=True)
    
    def get_taxpayer_type(self, obj):
        if obj.individual_taxpayer:
            return 'individual'
        elif obj.organization_taxpayer:
            return 'organization'
        return None
    
    def validate(self, data):
        """Ensure exactly one taxpayer type is selected"""
        individual = data.get('individual_taxpayer')
        organization = data.get('organization_taxpayer')
        
        if not individual and not organization:
            raise serializers.ValidationError(
                "Either individual_taxpayer or organization_taxpayer must be specified."
            )
        
        if individual and organization:
            raise serializers.ValidationError(
                "Cannot specify both individual_taxpayer and organization_taxpayer."
            )
        
        return data


class RegionalRevenueCollectionSerializer(BaseRevenueCollectionSerializer):
    """Serializer for Regional Revenue Collections"""
    revenue_source_name = serializers.CharField(source='revenue_source.name', read_only=True)
    revenue_source_code = serializers.CharField(source='revenue_source.code', read_only=True)
    category_name = serializers.CharField(source='revenue_source.category.name', read_only=True)
    
    class Meta:
        model = RegionalRevenueCollection
        fields = [
            'id', 'individual_taxpayer', 'organization_taxpayer',
            'taxpayer_name', 'taxpayer_tin', 'taxpayer_type',
            'revenue_source', 'revenue_source_name', 'revenue_source_code', 'category_name',
            'period', 'period_name', 'amount', 'collection_date', 'receipt_number',
            'due_date', 'payment_date', 'paid_amount', 'payment_status',
            'penalty_amount', 'interest_amount', 'penalty_rate', 'interest_rate',
            'penalty_calculated_date', 'interest_calculated_date',
            'notes', 'recorded_by', 'recorded_by_name', 'recorded_date',
            'last_modified_by', 'last_modified_by_name', 'last_modified'
        ]
        read_only_fields = [
            'id', 'recorded_date', 'last_modified', 'recorded_by', 'last_modified_by'
        ]


class CityServiceRevenueCollectionSerializer(BaseRevenueCollectionSerializer):
    """Serializer for City Service Revenue Collections"""
    revenue_source_name = serializers.CharField(source='revenue_source.name', read_only=True)
    revenue_source_code = serializers.CharField(source='revenue_source.code', read_only=True)
    category_name = serializers.CharField(source='revenue_source.category.name', read_only=True)
    
    class Meta:
        model = CityServiceRevenueCollection
        fields = [
            'id', 'individual_taxpayer', 'organization_taxpayer',
            'taxpayer_name', 'taxpayer_tin', 'taxpayer_type',
            'revenue_source', 'revenue_source_name', 'revenue_source_code', 'category_name',
            'period', 'period_name', 'amount', 'collection_date', 'receipt_number',
            'due_date', 'payment_date', 'paid_amount', 'payment_status',
            'penalty_amount', 'interest_amount', 'penalty_rate', 'interest_rate',
            'penalty_calculated_date', 'interest_calculated_date',
            'notes', 'recorded_by', 'recorded_by_name', 'recorded_date',
            'last_modified_by', 'last_modified_by_name', 'last_modified'
        ]
        read_only_fields = [
            'id', 'recorded_date', 'last_modified', 'recorded_by', 'last_modified_by'
        ]


class RevenueSummarySerializer(serializers.ModelSerializer):
    """Serializer for Revenue Summaries"""
    period_name = serializers.CharField(source='period.name', read_only=True)
    location_display = serializers.SerializerMethodField()
    location_hierarchy = serializers.SerializerMethodField()
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    
    class Meta:
        model = RevenueSummary
        fields = [
            'id', 'period', 'period_name',
            'country', 'region', 'zone', 'city', 'subcity', 'kebele',
            'location_display', 'location_hierarchy',
            'regional_total', 'city_service_total', 'grand_total',
            'summary_date', 'last_updated', 'created_by', 'created_by_name'
        ]
        read_only_fields = [
            'id', 'summary_date', 'last_updated', 'created_by',
            'regional_total', 'city_service_total', 'grand_total'
        ]
    
    def get_location_display(self, obj):
        parts = []
        if obj.kebele:
            parts.append(f"Kebele {obj.kebele.name}")
        if obj.subcity:
            parts.append(f"SubCity {obj.subcity.name}")
        if obj.city:
            parts.append(f"City {obj.city.name}")
        if obj.region:
            parts.append(f"Region {obj.region.name}")
        
        return " → ".join(reversed(parts)) if parts else "All Locations"
    
    def get_location_hierarchy(self, obj):
        hierarchy = {}
        if obj.country:
            hierarchy['country'] = {'id': obj.country.id, 'name': obj.country.name}
        if obj.region:
            hierarchy['region'] = {'id': obj.region.id, 'name': obj.region.name}
        if obj.zone:
            hierarchy['zone'] = {'id': obj.zone.id, 'name': obj.zone.name}
        if obj.city:
            hierarchy['city'] = {'id': obj.city.id, 'name': obj.city.name}
        if obj.subcity:
            hierarchy['subcity'] = {'id': obj.subcity.id, 'name': obj.subcity.name}
        if obj.kebele:
            hierarchy['kebele'] = {'id': obj.kebele.id, 'name': obj.kebele.name}
        
        return hierarchy


# Create/Update serializers for simplified forms
class RegionalCategoryCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = RegionalCategory
        fields = ['name', 'code', 'description', 'is_active']


class CityServiceCategoryCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = CityServiceCategory
        fields = ['name', 'code', 'description', 'is_active']


class RegionalRevenueSourceCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = RegionalRevenueSource
        fields = ['category', 'name', 'code', 'description', 'is_active']


class CityServiceRevenueSourceCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = CityServiceRevenueSource
        fields = ['category', 'name', 'code', 'description', 'is_active']


class RevenuePeriodCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = RevenuePeriod
        fields = ['name', 'code', 'start_date', 'end_date', 'is_closed']


class RegionalRevenueCollectionCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = RegionalRevenueCollection
        fields = [
            'individual_taxpayer', 'organization_taxpayer', 'revenue_source',
            'period', 'amount', 'collection_date', 'receipt_number', 'notes'
        ]
    
    def validate(self, data):
        """Ensure exactly one taxpayer type is selected"""
        individual = data.get('individual_taxpayer')
        organization = data.get('organization_taxpayer')
        
        if not individual and not organization:
            raise serializers.ValidationError(
                "Either individual_taxpayer or organization_taxpayer must be specified."
            )
        
        if individual and organization:
            raise serializers.ValidationError(
                "Cannot specify both individual_taxpayer and organization_taxpayer."
            )
        
        return data


class CityServiceRevenueCollectionCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = CityServiceRevenueCollection
        fields = [
            'individual_taxpayer', 'organization_taxpayer', 'revenue_source',
            'period', 'amount', 'collection_date', 'receipt_number', 'notes'
        ]
    
    def validate(self, data):
        """Ensure exactly one taxpayer type is selected"""
        individual = data.get('individual_taxpayer')
        organization = data.get('organization_taxpayer')
        
        if not individual and not organization:
            raise serializers.ValidationError(
                "Either individual_taxpayer or organization_taxpayer must be specified."
            )
        
        if individual and organization:
            raise serializers.ValidationError(
                "Cannot specify both individual_taxpayer and organization_taxpayer."
            )
        
        return data


class TaxpayerPaymentSummarySerializer(serializers.ModelSerializer):
    """Serializer for Taxpayer Payment Summaries"""
    taxpayer_name = serializers.SerializerMethodField()
    taxpayer_tin = serializers.SerializerMethodField()
    taxpayer_type = serializers.SerializerMethodField()
    period_name = serializers.CharField(source='period.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)

    class Meta:
        model = TaxpayerPaymentSummary
        fields = [
            'id', 'individual_taxpayer', 'organization_taxpayer',
            'taxpayer_name', 'taxpayer_tin', 'taxpayer_type',
            'period', 'period_name',
            'total_assessed', 'total_paid', 'total_outstanding', 'total_overdue',
            'total_collections', 'paid_collections', 'overdue_collections',
            'last_payment_date', 'next_due_date', 'payment_completion_rate',
            'created_at', 'updated_at', 'created_by', 'created_by_name'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'created_by']

    def get_taxpayer_name(self, obj):
        """Get taxpayer name"""
        return obj.taxpayer_name

    def get_taxpayer_tin(self, obj):
        """Get taxpayer TIN"""
        return obj.taxpayer_tin

    def get_taxpayer_type(self, obj):
        """Get taxpayer type"""
        if obj.individual_taxpayer:
            return 'individual'
        elif obj.organization_taxpayer:
            return 'organization'
        return None
