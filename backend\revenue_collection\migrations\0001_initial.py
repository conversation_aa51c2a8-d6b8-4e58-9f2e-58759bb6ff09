# Generated by Django 5.2.3 on 2025-07-15 12:41

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("locations", "0002_initial"),
        (
            "taxpayers",
            "0003_individualtaxpayer_city_individualtaxpayer_country_and_more",
        ),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="CityServiceCategory",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(help_text="Category name", max_length=100)),
                (
                    "code",
                    models.CharField(
                        help_text="Unique category code", max_length=20, unique=True
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True, help_text="Detailed description of the category"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, help_text="Whether this category is active"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who created this category",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "City Service Category",
                "verbose_name_plural": "City Service Categories",
                "ordering": ["code", "name"],
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="CityServiceRevenueSource",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(help_text="Revenue source name", max_length=100),
                ),
                (
                    "code",
                    models.CharField(help_text="Unique source code", max_length=20),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Detailed description of the revenue source",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, help_text="Whether this source is active"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "category",
                    models.ForeignKey(
                        help_text="City service category this source belongs to",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="revenue_sources",
                        to="revenue_collection.cityservicecategory",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who created this source",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "City Service Revenue Source",
                "verbose_name_plural": "City Service Revenue Sources",
                "ordering": ["code", "name"],
                "abstract": False,
                "unique_together": {("category", "code")},
            },
        ),
        migrations.CreateModel(
            name="RegionalCategory",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(help_text="Category name", max_length=100)),
                (
                    "code",
                    models.CharField(
                        help_text="Unique category code", max_length=20, unique=True
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True, help_text="Detailed description of the category"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, help_text="Whether this category is active"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who created this category",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Regional Category",
                "verbose_name_plural": "Regional Categories",
                "ordering": ["code", "name"],
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="RegionalRevenueSource",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(help_text="Revenue source name", max_length=100),
                ),
                (
                    "code",
                    models.CharField(help_text="Unique source code", max_length=20),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Detailed description of the revenue source",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, help_text="Whether this source is active"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "category",
                    models.ForeignKey(
                        help_text="Regional category this source belongs to",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="revenue_sources",
                        to="revenue_collection.regionalcategory",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who created this source",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Regional Revenue Source",
                "verbose_name_plural": "Regional Revenue Sources",
                "ordering": ["code", "name"],
                "abstract": False,
                "unique_together": {("category", "code")},
            },
        ),
        migrations.CreateModel(
            name="RevenuePeriod",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Period name (e.g., 'Q1 2024', 'January 2024')",
                        max_length=100,
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        help_text="Period code (e.g., '2024Q1', '2024-01')",
                        max_length=20,
                        unique=True,
                    ),
                ),
                ("start_date", models.DateField(help_text="Period start date")),
                ("end_date", models.DateField(help_text="Period end date")),
                (
                    "is_closed",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this period is closed for new entries",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who created this period",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Revenue Period",
                "verbose_name_plural": "Revenue Periods",
                "ordering": ["-start_date"],
            },
        ),
        migrations.CreateModel(
            name="RegionalRevenueCollection",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Revenue amount collected",
                        max_digits=15,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "collection_date",
                    models.DateField(help_text="Date when revenue was collected"),
                ),
                ("recorded_date", models.DateTimeField(auto_now_add=True)),
                ("last_modified", models.DateTimeField(auto_now=True)),
                (
                    "notes",
                    models.TextField(
                        blank=True, help_text="Additional notes or comments"
                    ),
                ),
                (
                    "receipt_number",
                    models.CharField(
                        blank=True,
                        help_text="Receipt or reference number",
                        max_length=50,
                    ),
                ),
                (
                    "individual_taxpayer",
                    models.ForeignKey(
                        blank=True,
                        help_text="Individual taxpayer (if applicable)",
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to="taxpayers.individualtaxpayer",
                    ),
                ),
                (
                    "last_modified_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who last modified this record",
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)s_modifications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization_taxpayer",
                    models.ForeignKey(
                        blank=True,
                        help_text="Organization taxpayer (if applicable)",
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to="taxpayers.organizationtaxpayer",
                    ),
                ),
                (
                    "recorded_by",
                    models.ForeignKey(
                        help_text="User who recorded this collection",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)s_collections",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "revenue_source",
                    models.ForeignKey(
                        help_text="Regional revenue source",
                        on_delete=django.db.models.deletion.PROTECT,
                        to="revenue_collection.regionalrevenuesource",
                    ),
                ),
                (
                    "period",
                    models.ForeignKey(
                        help_text="Revenue collection period",
                        on_delete=django.db.models.deletion.PROTECT,
                        to="revenue_collection.revenueperiod",
                    ),
                ),
            ],
            options={
                "verbose_name": "Regional Revenue Collection",
                "verbose_name_plural": "Regional Revenue Collections",
                "indexes": [
                    models.Index(
                        fields=["collection_date"],
                        name="revenue_col_collect_de890e_idx",
                    ),
                    models.Index(
                        fields=["period", "revenue_source"],
                        name="revenue_col_period__7df57d_idx",
                    ),
                    models.Index(
                        fields=["individual_taxpayer"],
                        name="revenue_col_individ_bb988f_idx",
                    ),
                    models.Index(
                        fields=["organization_taxpayer"],
                        name="revenue_col_organiz_253460_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="CityServiceRevenueCollection",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Revenue amount collected",
                        max_digits=15,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "collection_date",
                    models.DateField(help_text="Date when revenue was collected"),
                ),
                ("recorded_date", models.DateTimeField(auto_now_add=True)),
                ("last_modified", models.DateTimeField(auto_now=True)),
                (
                    "notes",
                    models.TextField(
                        blank=True, help_text="Additional notes or comments"
                    ),
                ),
                (
                    "receipt_number",
                    models.CharField(
                        blank=True,
                        help_text="Receipt or reference number",
                        max_length=50,
                    ),
                ),
                (
                    "individual_taxpayer",
                    models.ForeignKey(
                        blank=True,
                        help_text="Individual taxpayer (if applicable)",
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to="taxpayers.individualtaxpayer",
                    ),
                ),
                (
                    "last_modified_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who last modified this record",
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)s_modifications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization_taxpayer",
                    models.ForeignKey(
                        blank=True,
                        help_text="Organization taxpayer (if applicable)",
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to="taxpayers.organizationtaxpayer",
                    ),
                ),
                (
                    "recorded_by",
                    models.ForeignKey(
                        help_text="User who recorded this collection",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)s_collections",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "revenue_source",
                    models.ForeignKey(
                        help_text="City service revenue source",
                        on_delete=django.db.models.deletion.PROTECT,
                        to="revenue_collection.cityservicerevenuesource",
                    ),
                ),
                (
                    "period",
                    models.ForeignKey(
                        help_text="Revenue collection period",
                        on_delete=django.db.models.deletion.PROTECT,
                        to="revenue_collection.revenueperiod",
                    ),
                ),
            ],
            options={
                "verbose_name": "City Service Revenue Collection",
                "verbose_name_plural": "City Service Revenue Collections",
                "indexes": [
                    models.Index(
                        fields=["collection_date"],
                        name="revenue_col_collect_23453b_idx",
                    ),
                    models.Index(
                        fields=["period", "revenue_source"],
                        name="revenue_col_period__0268df_idx",
                    ),
                    models.Index(
                        fields=["individual_taxpayer"],
                        name="revenue_col_individ_e0c278_idx",
                    ),
                    models.Index(
                        fields=["organization_taxpayer"],
                        name="revenue_col_organiz_819bd9_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="RevenueSummary",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "regional_total",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Total regional revenue for this period/location",
                        max_digits=15,
                    ),
                ),
                (
                    "city_service_total",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Total city service revenue for this period/location",
                        max_digits=15,
                    ),
                ),
                (
                    "grand_total",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Grand total revenue for this period/location",
                        max_digits=15,
                    ),
                ),
                ("summary_date", models.DateField(auto_now_add=True)),
                ("last_updated", models.DateTimeField(auto_now=True)),
                (
                    "city",
                    models.ForeignKey(
                        blank=True,
                        help_text="City for this summary",
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to="locations.city",
                    ),
                ),
                (
                    "country",
                    models.ForeignKey(
                        blank=True,
                        help_text="Country for this summary",
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to="locations.country",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who created this summary",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "kebele",
                    models.ForeignKey(
                        blank=True,
                        help_text="Kebele for this summary",
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to="locations.kebele",
                    ),
                ),
                (
                    "period",
                    models.ForeignKey(
                        help_text="Revenue period for this summary",
                        on_delete=django.db.models.deletion.PROTECT,
                        to="revenue_collection.revenueperiod",
                    ),
                ),
                (
                    "region",
                    models.ForeignKey(
                        blank=True,
                        help_text="Region for this summary",
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to="locations.region",
                    ),
                ),
                (
                    "subcity",
                    models.ForeignKey(
                        blank=True,
                        help_text="SubCity/Woreda for this summary",
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to="locations.subcity",
                    ),
                ),
                (
                    "zone",
                    models.ForeignKey(
                        blank=True,
                        help_text="Zone for this summary",
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to="locations.zone",
                    ),
                ),
            ],
            options={
                "verbose_name": "Revenue Summary",
                "verbose_name_plural": "Revenue Summaries",
                "ordering": ["-period__start_date", "-summary_date"],
                "indexes": [
                    models.Index(
                        fields=["period"], name="revenue_col_period__033ff7_idx"
                    ),
                    models.Index(
                        fields=["region", "period"],
                        name="revenue_col_region__c3beb4_idx",
                    ),
                    models.Index(
                        fields=["city", "period"], name="revenue_col_city_id_44ed86_idx"
                    ),
                    models.Index(
                        fields=["subcity", "period"],
                        name="revenue_col_subcity_a09aa9_idx",
                    ),
                    models.Index(
                        fields=["kebele", "period"],
                        name="revenue_col_kebele__5e7ab5_idx",
                    ),
                ],
            },
        ),
    ]
