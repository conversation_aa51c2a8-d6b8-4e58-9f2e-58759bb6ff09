import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  InputAdornment,
  Typography,
  Menu,
  Alert,
  Grid,
} from '@mui/material';
import {
  Add,
  Search,
  Edit,
  Delete,
  MoreVert,
  LocationOn,
  Public,
} from '@mui/icons-material';
import { useNotification } from '../../../contexts/NotificationContext';
import locationHierarchyService from '../../../services/locationHierarchyService';
import type { 
  Region, 
  RegionCreate, 
  CountrySelect 
} from '../../../services/locationHierarchyService';

interface RegionsTabProps {
  onStatsChange: () => void;
}

const RegionsTab: React.FC<RegionsTabProps> = ({ onStatsChange }) => {
  const { showSuccess, showError } = useNotification();
  
  const [regions, setRegions] = useState<Region[]>([]);
  const [countries, setCountries] = useState<CountrySelect[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCountry, setSelectedCountry] = useState<number | ''>('');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingRegion, setEditingRegion] = useState<Region | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedRegion, setSelectedRegion] = useState<Region | null>(null);
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);

  const [formData, setFormData] = useState<RegionCreate>({
    country: 0,
    name: '',
    code: '',
    population: undefined,
    area_km2: undefined,
    capital_city: '',
    is_active: true,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    loadCountries();
    loadRegions();
  }, []);

  useEffect(() => {
    loadRegions();
  }, [searchTerm, selectedCountry]);

  const loadCountries = async () => {
    try {
      const response = await locationHierarchyService.getCountriesSelect();
      setCountries(response);
    } catch (error) {
      showError('Failed to load countries');
    }
  };

  const loadRegions = async () => {
    try {
      setLoading(true);
      const response = await locationHierarchyService.getRegions({
        search: searchTerm,
        country: selectedCountry || undefined,
        page_size: 100,
      });
      setRegions(response.results);
    } catch (error) {
      showError('Failed to load regions');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (region?: Region) => {
    if (region) {
      setEditingRegion(region);
      setFormData({
        country: region.country,
        name: region.name,
        code: region.code,
        population: region.population,
        area_km2: region.area_km2,
        capital_city: region.capital_city || '',
        is_active: region.is_active,
      });
    } else {
      setEditingRegion(null);
      setFormData({
        country: selectedCountry as number || 2,
        name: '',
        code: '',
        population: undefined,
        area_km2: undefined,
        capital_city: '',
        is_active: true,
      });
    }
    setErrors({});
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingRegion(null);
    setErrors({});
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.country) {
      newErrors.country = 'Country is required';
    }
    if (!formData.name.trim()) {
      newErrors.name = 'Region name is required';
    }
    if (!formData.code.trim()) {
      newErrors.code = 'Region code is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      if (editingRegion) {
        await locationHierarchyService.updateRegion(editingRegion.id, formData);
        showSuccess('Region updated successfully');
      } else {
        await locationHierarchyService.createRegion(formData);
        showSuccess('Region created successfully');
      }
      
      handleCloseDialog();
      loadRegions();
      onStatsChange();
    } catch (error: any) {
      showError(error.response?.data?.detail || 'Failed to save region');
    }
  };

  const handleDelete = async () => {
    if (!selectedRegion) return;

    try {
      await locationHierarchyService.deleteRegion(selectedRegion.id);
      showSuccess('Region deleted successfully');
      setDeleteDialogOpen(false);
      setSelectedRegion(null);
      loadRegions();
      onStatsChange();
    } catch (error: any) {
      showError(error.response?.data?.detail || 'Failed to delete region');
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, region: Region) => {
    setMenuAnchor(event.currentTarget);
    setSelectedRegion(region);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
    setSelectedRegion(null);
  };

  const handleInputChange = (field: keyof RegionCreate) => (
    event: React.ChangeEvent<HTMLInputElement> | { target: { value: unknown } }
  ) => {
    const value = event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">Regions Management</Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => handleOpenDialog()}
          disabled={countries.length === 0}
        >
          Add Region
        </Button>
      </Box>

      {countries.length === 0 && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          No countries available. Please create countries first before adding regions.
        </Alert>
      )}

      {/* Search and Filters */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid size={{ xs: 12, md: 4 }}>
          <TextField
            fullWidth
            placeholder="Search regions..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
          />
        </Grid>
        <Grid size={{ xs: 12, md: 4 }}>
          <FormControl fullWidth>
            <InputLabel>Filter by Country</InputLabel>
            <Select
              value={selectedCountry}
              onChange={(e) => setSelectedCountry(e.target.value as number)}
              label="Filter by Country"
            >
              <MenuItem value="">All Countries</MenuItem>
              {countries.map((country) => (
                <MenuItem key={country.id} value={country.id}>
                  {country.name} ({country.code})
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
      </Grid>

      {/* Regions Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>Code</TableCell>
              <TableCell>Country</TableCell>
              <TableCell>Capital</TableCell>
              <TableCell>Population</TableCell>
              <TableCell>Zones</TableCell>
              <TableCell>Status</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {regions.map((region) => (
              <TableRow key={region.id}>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <LocationOn fontSize="small" />
                    {region.name}
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip label={region.code} size="small" />
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Public fontSize="small" />
                    {region.country_name}
                  </Box>
                </TableCell>
                <TableCell>{region.capital_city || '-'}</TableCell>
                <TableCell>
                  {region.population ? region.population.toLocaleString() : '-'}
                </TableCell>
                <TableCell>
                  <Chip label={region.zone_count} size="small" color="primary" />
                </TableCell>
                <TableCell>
                  <Chip
                    label={region.is_active ? 'Active' : 'Inactive'}
                    color={region.is_active ? 'success' : 'default'}
                    size="small"
                  />
                </TableCell>
                <TableCell align="right">
                  <IconButton
                    onClick={(e) => handleMenuOpen(e, region)}
                    size="small"
                  >
                    <MoreVert />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Action Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
      >
        <MenuItem
          onClick={() => {
            handleOpenDialog(selectedRegion!);
            handleMenuClose();
          }}
        >
          <Edit fontSize="small" sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        <MenuItem
          onClick={() => {
            setDeleteDialogOpen(true);
            handleMenuClose();
          }}
          sx={{ color: 'error.main' }}
        >
          <Delete fontSize="small" sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>

      {/* Create/Edit Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingRegion ? 'Edit Region' : 'Add New Region'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required error={!!errors.country}>
                <InputLabel>Country</InputLabel>
                <Select
                  value={formData.country || ''}
                  onChange={handleInputChange('country')}
                  label="Country"
                >
                  {countries.map((country) => (
                    <MenuItem key={country.id} value={country.id}>
                      {country.name} ({country.code})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Region Name"
                value={formData.name}
                onChange={handleInputChange('name')}
                error={!!errors.name}
                helperText={errors.name}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Region Code"
                value={formData.code}
                onChange={handleInputChange('code')}
                error={!!errors.code}
                helperText={errors.code}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Capital City"
                value={formData.capital_city}
                onChange={handleInputChange('capital_city')}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Population"
                type="number"
                value={formData.population || ''}
                onChange={handleInputChange('population')}
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="Area (km²)"
                type="number"
                value={formData.area_km2 || ''}
                onChange={handleInputChange('area_km2')}
              />
            </Grid>
            <Grid size={{ xs: 12 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.is_active}
                    onChange={handleInputChange('is_active')}
                  />
                }
                label="Active"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSubmit} variant="contained">
            {editingRegion ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 2 }}>
            Are you sure you want to delete "{selectedRegion?.name}"? This action cannot be undone.
          </Alert>
          {selectedRegion && selectedRegion.zone_count > 0 && (
            <Alert severity="error">
              This region has {selectedRegion.zone_count} zones. 
              Please delete or reassign them first.
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleDelete} 
            color="error" 
            variant="contained"
            disabled={selectedRegion ? selectedRegion.zone_count > 0 : false}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default RegionsTab;
