"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = symlog;
exports.symlogish = symlogish;
var _linear = require("./linear.js");
var _continuous = require("./continuous.js");
var _init = require("./init.js");
function transformSymlog(c) {
  return function (x) {
    return Math.sign(x) * Math.log1p(Math.abs(x / c));
  };
}
function transformSymexp(c) {
  return function (x) {
    return Math.sign(x) * Math.expm1(Math.abs(x)) * c;
  };
}
function symlogish(transform) {
  var c = 1,
    scale = transform(transformSymlog(c), transformSymexp(c));
  scale.constant = function (_) {
    return arguments.length ? transform(transformSymlog(c = +_), transformSymexp(c)) : c;
  };
  return (0, _linear.linearish)(scale);
}
function symlog() {
  var scale = symlogish((0, _continuous.transformer)());
  scale.copy = function () {
    return (0, _continuous.copy)(scale, symlog()).constant(scale.constant());
  };
  return _init.initRange.apply(scale, arguments);
}