import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  TextField,
  InputAdornment,
  Avatar,
  Divider,
  Stack,
  Fade,
  Alert,
  CircularProgress,
  IconButton,
  Breadcrumbs,
  Link,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TablePagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Archive,
  ArrowBack,
  Home,
  Add,
  Edit,
  Delete,
  Visibility,
  Shelves,
  GridView,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useNotification } from '../../contexts/NotificationContext';
import locationService from '../../services/locationService';
import ConfirmationDialog from '../../components/common/ConfirmationDialog';
import type { Box as BoxType, BoxCreate, Shelf } from '../../services/types';

const BoxesPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { showNotification } = useNotification();

  const [boxes, setBoxes] = useState<BoxType[]>([]);
  const [shelves, setShelves] = useState<Shelf[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [showForm, setShowForm] = useState(false); // Disabled - boxes are created automatically
  const [editingBox, setEditingBox] = useState<BoxType | null>(null);
  const [formData, setFormData] = useState<BoxCreate>({
    shelf: 0,
    row: 1,
    column: 1,
    description: '',
  });
  const [formErrors, setFormErrors] = useState<any>({});
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [boxToDelete, setBoxToDelete] = useState<BoxType | null>(null);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    loadBoxes();
    loadShelves();
  }, [page, rowsPerPage]);

  // Handle navigation state from detail page
  useEffect(() => {
    const state = location.state as any;
    if (state?.editBox && state?.showForm) {
      handleEdit(state.editBox);
      // Clear the state to prevent re-triggering
      navigate(location.pathname, { replace: true });
    }
  }, [location.state]);

  const loadBoxes = async () => {
    try {
      setLoading(true);
      const response = await locationService.getBoxes({
        page: page + 1,
        page_size: rowsPerPage,
      });
      setBoxes(response.results);
      setTotalCount(response.count);
    } catch (error) {
      console.error('Error loading boxes:', error);
      showNotification('Failed to load boxes', 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadShelves = async () => {
    try {
      const response = await locationService.getShelves({ page_size: 100 });
      setShelves(response.results);
    } catch (error) {
      console.error('Error loading shelves:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setSubmitting(true);
      setFormErrors({});

      if (editingBox) {
        await locationService.updateBox(editingBox.id, formData);
        showNotification('Box updated successfully', 'success');
      } else {
        await locationService.createBox(formData);
        showNotification('Box created successfully', 'success');
      }

      resetForm();
      loadBoxes();
    } catch (error: any) {
      console.error('Error submitting form:', error);
      if (error.response?.data) {
        setFormErrors(error.response.data);
      }
      showNotification('Failed to save box', 'error');
    } finally {
      setSubmitting(false);
    }
  };

  const handleViewDetails = (box: BoxType) => {
    navigate(`/locations/boxes/${box.id}`);
  };

  const handleEdit = (box: BoxType) => {
    setEditingBox(box);
    setFormData({
      shelf: box.shelf,
      row: box.row,
      column: box.column,
      description: box.description || '',
    });
    setShowForm(true);
  };

  const handleDelete = (box: BoxType) => {
    setBoxToDelete(box);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!boxToDelete) return;
    
    try {
      setDeleting(true);
      await locationService.deleteBox(boxToDelete.id);
      showNotification('Box deleted successfully', 'success');
      loadBoxes();
      handleCloseDeleteDialog();
    } catch (error) {
      console.error('Error deleting box:', error);
      showNotification('Failed to delete box', 'error');
    } finally {
      setDeleting(false);
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setBoxToDelete(null);
    setDeleting(false);
  };

  const resetForm = () => {
    setFormData({
      shelf: 0,
      row: 1,
      column: 1,
      description: '',
    });
    setFormErrors({});
    setEditingBox(null);
    setShowForm(false);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Fade in timeout={800}>
        <Box sx={{ mb: 4 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs sx={{ mb: 2 }}>
            <Link
              component="button"
              variant="body1"
              onClick={() => navigate('/locations')}
              sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
            >
              <Home fontSize="small" />
              Physical Location Center
            </Link>
            <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <Archive fontSize="small" />
              Box Positions (Auto-Created)
            </Typography>
          </Breadcrumbs>

          {/* Info Alert */}
          <Alert severity="info" sx={{ mb: 3 }}>
            <Typography variant="body2">
              <strong>Auto-Created Positions:</strong> Box positions are automatically created when you create or modify shelves with row×column dimensions.
              Each shelf with 5 rows and 10 columns will automatically generate 50 box positions (R01C01 to R05C10).
              <br /><br />
              <strong>Note:</strong> You cannot manually add or delete box positions. They are managed automatically by the system based on shelf configurations.
            </Typography>
          </Alert>

          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'between', mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <IconButton onClick={() => navigate('/locations')} sx={{ mr: 1 }}>
                <ArrowBack />
              </IconButton>
              <Avatar sx={{ bgcolor: 'success.main' }}>
                <Archive />
              </Avatar>
              <Box>
                <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
                  Box Positions (Auto-Created)
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  View automatically created shelf positions - Created when shelves are configured with row×column dimensions
                </Typography>
              </Box>
            </Box>
            {/* Add button removed - boxes are created automatically */}
          </Box>
        </Box>
      </Fade>

      {/* Form Section */}
      {showForm && (
        <Fade in timeout={600}>
          <Card sx={{ mb: 4 }}>
            <CardContent sx={{ p: 4 }}>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                {editingBox ? 'Edit Box Position' : 'Add New Box Position'}
              </Typography>
              <Divider sx={{ mb: 3 }} />
              
              <form onSubmit={handleSubmit}>
                <Stack spacing={3}>
                  <FormControl fullWidth required error={!!formErrors.shelf}>
                    <InputLabel>Shelf</InputLabel>
                    <Select
                      value={formData.shelf}
                      onChange={(e) => setFormData({ ...formData, shelf: Number(e.target.value) })}
                      label="Shelf"
                      startAdornment={
                        <InputAdornment position="start">
                          <Shelves color="action" />
                        </InputAdornment>
                      }
                    >
                      {shelves.map((shelf) => (
                        <MenuItem key={shelf.id} value={shelf.id}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Chip label={shelf.code} size="small" />
                            {shelf.name}
                            {shelf.building_name && (
                              <Typography variant="caption" color="text.secondary">
                                ({shelf.building_name})
                              </Typography>
                            )}
                          </Box>
                        </MenuItem>
                      ))}
                    </Select>
                    {formErrors.shelf && (
                      <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 2 }}>
                        {formErrors.shelf}
                      </Typography>
                    )}
                  </FormControl>
                  
                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                    <TextField
                      label="Row"
                      type="number"
                      value={formData.row}
                      onChange={(e) => setFormData({ ...formData, row: parseInt(e.target.value) || 1 })}
                      error={!!formErrors.row}
                      helperText={formErrors.row || 'Row number (1-based)'}
                      required
                      inputProps={{ min: 1, max: 100 }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <GridView color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                    
                    <TextField
                      label="Column"
                      type="number"
                      value={formData.column}
                      onChange={(e) => setFormData({ ...formData, column: parseInt(e.target.value) || 1 })}
                      error={!!formErrors.column}
                      helperText={formErrors.column || 'Column number (1-based)'}
                      required
                      inputProps={{ min: 1, max: 100 }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <GridView color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>

                  <TextField
                    label="Description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    error={!!formErrors.description}
                    helperText={formErrors.description || 'Optional description for this position'}
                    multiline
                    rows={3}
                    fullWidth
                  />

                  <Box sx={{ display: 'flex', gap: 2, pt: 2 }}>
                    <Button
                      type="submit"
                      variant="contained"
                      disabled={submitting}
                      startIcon={submitting ? <CircularProgress size={20} /> : <Add />}
                      size="large"
                    >
                      {submitting ? 'Saving...' : editingBox ? 'Update Position' : 'Create Position'}
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={resetForm}
                      size="large"
                    >
                      Cancel
                    </Button>
                  </Box>
                </Stack>
              </form>
            </CardContent>
          </Card>
        </Fade>
      )}

      {/* Boxes Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
            Box Positions List
          </Typography>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : boxes.length === 0 ? (
            <Alert severity="info">
              No box positions found. Box positions are automatically created when you configure shelves with row×column dimensions.
            </Alert>
          ) : (
            <>
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Position</TableCell>
                      <TableCell>Shelf</TableCell>
                      <TableCell>Coordinates</TableCell>
                      <TableCell>Capacity</TableCell>
                      <TableCell>Created</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {boxes.map((box) => (
                      <TableRow key={box.id} hover>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <Avatar sx={{ bgcolor: 'success.main', width: 32, height: 32 }}>
                              <Archive fontSize="small" />
                            </Avatar>
                            <Box>
                              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                Position {box.id}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {box.description || 'No description'}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Shelves fontSize="small" color="action" />
                            {box.shelf_name || `Shelf ${box.shelf}`}
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <Chip
                              label={`Row ${box.row}`}
                              size="small"
                              color="primary"
                              variant="outlined"
                            />
                            <Chip
                              label={`Col ${box.column}`}
                              size="small"
                              color="secondary"
                              variant="outlined"
                            />
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {box.kent_count || 0} kents
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {new Date(box.created_at).toLocaleDateString()}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                            <IconButton
                              size="small"
                              onClick={() => handleViewDetails(box)}
                              color="info"
                              title="View Details"
                            >
                              <Visibility fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleEdit(box)}
                              color="primary"
                              title="Edit"
                            >
                              <Edit fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleDelete(box)}
                              color="error"
                              title="Delete"
                            >
                              <Delete fontSize="small" />
                            </IconButton>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              <TablePagination
                component="div"
                count={totalCount}
                page={page}
                onPageChange={handleChangePage}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                rowsPerPageOptions={[5, 10, 25, 50]}
              />
            </>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        title="Delete Box Position"
        itemName={`Position ${boxToDelete?.id} (Row ${boxToDelete?.row}, Col ${boxToDelete?.column})`}
        itemType="Box Position"
        message={`Are you sure you want to delete this box position? This will also delete all kents and files within this position. This action cannot be undone.`}
        confirmText="Delete Position"
        severity="error"
        loading={deleting}
      />
    </Container>
  );
};

export default BoxesPage;
