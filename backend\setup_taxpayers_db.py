#!/usr/bin/env python
"""
Script to set up taxpayers database tables and load initial data
Run this script to create the taxpayers tables and load sample data
"""

import os
import sys
import django
from django.db import connection
from django.core.management import call_command

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'arada_dms.settings')
django.setup()

def run_sql_file(filename):
    """Run SQL commands from a file"""
    print(f"Running SQL file: {filename}")
    
    with open(filename, 'r') as file:
        sql_content = file.read()
    
    with connection.cursor() as cursor:
        # Split by semicolon and execute each statement
        statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        for statement in statements:
            if statement:
                try:
                    cursor.execute(statement)
                    print(f"✓ Executed: {statement[:50]}...")
                except Exception as e:
                    print(f"✗ Error executing: {statement[:50]}...")
                    print(f"  Error: {e}")

def load_sample_data():
    """Load sample data using Django fixtures"""
    print("Loading sample data...")
    
    try:
        call_command('loaddata', 'taxpayers/fixtures/initial_data.json', verbosity=1)
        print("✓ Sample data loaded successfully")
    except Exception as e:
        print(f"✗ Error loading sample data: {e}")

def create_sample_taxpayers():
    """Create sample taxpayers"""
    print("Creating sample taxpayers...")
    
    try:
        from taxpayers.models import (
            BusinessSector, BusinessSubSector, TaxPayerLevel,
            OrganizationBusinessType, IndividualTaxPayer, OrganizationTaxPayer
        )
        
        # Get required objects
        agriculture_sector = BusinessSector.objects.filter(code='AGR').first()
        services_sector = BusinessSector.objects.filter(code='SER').first()
        
        if agriculture_sector and services_sector:
            crop_subsector = BusinessSubSector.objects.filter(
                code='001', business_sector=agriculture_sector
            ).first()
            professional_subsector = BusinessSubSector.objects.filter(
                code='001', business_sector=services_sector
            ).first()
            
            level_c = TaxPayerLevel.objects.filter(code='C').first()
            level_b = TaxPayerLevel.objects.filter(code='B').first()
            
            plc_type = OrganizationBusinessType.objects.filter(code='PLC').first()
            
            if all([crop_subsector, professional_subsector, level_c, level_b, plc_type]):
                # Create sample individual taxpayer
                individual, created = IndividualTaxPayer.objects.get_or_create(
                    tin='1234567890',
                    defaults={
                        'first_name': 'John',
                        'middle_name': 'Doe',
                        'last_name': 'Smith',
                        'nationality': 'ET',
                        'gender': 'M',
                        'date_of_birth': '1985-01-15',
                        'tax_payer_level': level_c,
                        'business_sector': agriculture_sector,
                        'business_sub_sector': crop_subsector,
                        'business_registration_date': '2020-01-01',
                        'business_name': 'Smith Farm',
                        'phone': '+251911123456',
                        'email': '<EMAIL>',
                        'house_number': '123',
                        'street_address': 'Main Street',
                    }
                )
                
                if created:
                    print("✓ Sample individual taxpayer created")
                
                # Create sample organization taxpayer
                organization, created = OrganizationTaxPayer.objects.get_or_create(
                    tin='**********',
                    defaults={
                        'business_name': 'Tech Solutions PLC',
                        'trade_name': 'TechSol',
                        'organization_business_type': plc_type,
                        'tax_payer_level': level_b,
                        'business_sector': services_sector,
                        'business_sub_sector': professional_subsector,
                        'business_registration_date': '2018-06-01',
                        'business_license_number': 'BL-2018-001',
                        'capital_amount': 5000000.00,
                        'number_of_employees': 25,
                        'manager_first_name': 'Jane',
                        'manager_middle_name': 'Mary',
                        'manager_last_name': 'Johnson',
                        'manager_title': 'CEO',
                        'vat_registration_date': '2018-07-01',
                        'vat_number': 'ET**********',
                        'phone': '+251911987654',
                        'email': '<EMAIL>',
                        'house_number': '456',
                        'street_address': 'Business District',
                    }
                )
                
                if created:
                    print("✓ Sample organization taxpayer created")
            else:
                print("✗ Required data not found for creating sample taxpayers")
        else:
            print("✗ Business sectors not found")
            
    except Exception as e:
        print(f"✗ Error creating sample taxpayers: {e}")

def main():
    """Main function to set up the taxpayers database"""
    print("🚀 Setting up Tax Payers Database...")
    print("=" * 50)
    
    # Step 1: Create database tables
    print("\n1. Creating database tables...")
    run_sql_file('create_taxpayers_tables.sql')
    
    # Step 2: Load sample data
    print("\n2. Loading sample data...")
    load_sample_data()
    
    # Step 3: Create sample taxpayers
    print("\n3. Creating sample taxpayers...")
    create_sample_taxpayers()
    
    print("\n" + "=" * 50)
    print("🎉 Tax Payers Database setup completed!")
    print("\nYou can now:")
    print("1. Access the admin interface at http://localhost:8000/admin/")
    print("2. Use the API endpoints at http://localhost:8000/api/taxpayers/")
    print("3. Access the frontend at http://localhost:3000/taxpayers")

if __name__ == '__main__':
    main()
