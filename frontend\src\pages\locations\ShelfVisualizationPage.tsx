import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  Typo<PERSON>,
  <PERSON>,
  CardContent,
  Grid,
  <PERSON>readc<PERSON><PERSON>,
  Link,
  Chip,
  IconButton,
  Tooltip,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Paper,
  Stack,
  Divider,
  Badge,
} from '@mui/material';
import {
  Shelves,
  Archive,
  Folder,
  FolderOpen,
  Description,
  Home,
  LocationOn,
  Business,
  ZoomIn,
  ZoomOut,
  Refresh,
  Search,
  FilterList,
  ViewModule,
  ViewList,
  Receipt,
  Gavel,
  Person,
  KeyboardArrowLeft,
  KeyboardArrowRight,
  FullscreenExit,
  Fullscreen,
} from '@mui/icons-material';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useNotification } from '../../contexts/NotificationContext';
import locationService from '../../services/locationService';
import DocumentListModal from '../../components/documents/DocumentListModal';
import fileService from '../../services/fileService';
import type { <PERSON><PERSON>, Box as BoxType, Kent, Building } from '../../services/locationService';

// Local file type definition to avoid import conflicts
interface FileType {
  id: string;
  kent: number;
  kent_location: string;
  kent_code: string;
  building_name: string;
  shelf_name: string;
  organization_name: string;
  name: string;
  file_number: string;
  full_code: string;
  location_path: string;
  file_type: number;
  file_type_name: string;
  file_type_code: string;
  file_type_color: string;
  file_type_icon: string;
  description?: string;
  status: string;
  business_name?: string;
  tin_number?: string;
  vat_number?: string;
  owner_name?: string;
  contact_phone?: string;
  contact_email?: string;
  address?: string;
  tags: string[];
  created_date?: string;
  last_activity_date?: string;
  document_count: number;
  document_types: string[];
  barcode_image?: string;
  qr_code_image?: string;
  is_active: boolean;
  created_by: number;
  created_by_name: string;
  created_at: string;
  updated_at: string;
}

interface ViewState {
  level: 'building' | 'shelf' | 'shelfDetail' | 'kent' | 'file';
  buildingId?: number;
  shelfId?: number;
  boxId?: number;
  kentId?: number;
  fileId?: number;
}

// Get color for kent - use real kent color from database or fallback to default
const getKentColor = (kent: Kent): string => {
  // Use the actual color from the kent record if available
  if (kent.color && kent.color.trim()) {
    // Handle different color formats
    const color = kent.color.trim();

    // If it's already a hex color, use it
    if (color.startsWith('#')) {
      return color;
    }

    // If it's a named color, convert to hex
    const namedColors: { [key: string]: string } = {
      'red': '#EF4444',
      'blue': '#3B82F6',
      'green': '#22C55E',
      'yellow': '#EAB308',
      'orange': '#F97316',
      'purple': '#8B5CF6',
      'pink': '#EC4899',
      'cyan': '#06B6D4',
      'teal': '#14B8A6',
      'lime': '#84CC16',
      'amber': '#F59E0B',
      'emerald': '#10B981',
      'rose': '#F43F5E',
      'brown': '#8B5A2B',
      'gray': '#6B7280',
      'grey': '#6B7280',
      'black': '#1F2937',
      'white': '#FFFFFF',
      'navy': '#2E4A87',
      'darkblue': '#2E4A87',
      'lightblue': '#0EA5E9'
    };

    const lowerColor = color.toLowerCase();
    if (namedColors[lowerColor]) {
      return namedColors[lowerColor];
    }

    // Try to use the color as-is (might be a valid CSS color)
    return color;
  }

  // Fallback to a default color if no color is specified
  return '#6B7280'; // Default gray
};

// Get file type icon
const getFileTypeIcon = (fileType: string) => {
  switch (fileType.toLowerCase()) {
    case 'tax':
      return <Receipt sx={{ fontSize: 16 }} />;
    case 'legal':
      return <Gavel sx={{ fontSize: 16 }} />;
    case 'personal':
      return <Person sx={{ fontSize: 16 }} />;
    default:
      return <Description sx={{ fontSize: 16 }} />;
  }
};

const ShelfVisualizationPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { showNotification } = useNotification();

  // State management
  const [viewState, setViewState] = useState<ViewState>({ level: 'building' });
  const [selectedShelf, setSelectedShelf] = useState<Shelf | null>(null);
  const [selectedBox, setSelectedBox] = useState<BoxType | null>(null);
  const [selectedKent, setSelectedKent] = useState<Kent | null>(null);
  
  // Data state
  const [shelves, setShelves] = useState<Shelf[]>([]);
  const [boxes, setBoxes] = useState<BoxType[]>([]);
  const [kents, setKents] = useState<Kent[]>([]);
  const [buildings, setBuildings] = useState<Building[]>([]);
  const [files, setFiles] = useState<FileType[]>([]);
  const [selectedFile, setSelectedFile] = useState<FileType | null>(null);
  const [documentModalOpen, setDocumentModalOpen] = useState(false);
  const [selectedFileForDocuments, setSelectedFileForDocuments] = useState<FileType | null>(null);
  
  // UI state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedBuildingId, setSelectedBuildingId] = useState<number | null>(null);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Highlight functionality
  const [highlightFileId, setHighlightFileId] = useState<string | null>(null);
  const [highlightType, setHighlightType] = useState<string | null>(null);
  const [highlightSource, setHighlightSource] = useState<string | null>(null);

  // Load initial data
  useEffect(() => {
    loadBuildings();

    // Check for highlight parameters
    const highlight = searchParams.get('highlight');
    const type = searchParams.get('type');
    const source = searchParams.get('source');
    if (highlight && type) {
      setHighlightFileId(highlight);
      setHighlightType(type);
      setHighlightSource(source);
      // Auto-navigate to the file location if needed
      if (type === 'file') {
        autoNavigateToFile(highlight);
      }
    }
  }, []);

  const autoNavigateToFile = async (fileId: string) => {
    try {
      // Load file details to get its location
      const fileData = await locationService.getFile(fileId);
      if (fileData && fileData.kent) {
        // Navigate to the file's location
        const kentData = await locationService.getKent(fileData.kent.toString());
        if (kentData && kentData.box) {
          const boxData = await locationService.getBox(kentData.box.toString());
          if (boxData && boxData.shelf) {
            const shelfData = await locationService.getShelf(boxData.shelf.toString());
            if (shelfData && shelfData.building) {
              // Set the navigation state to show the file directly at kent level
              setSelectedBuildingId(shelfData.building);
              setSelectedShelf(shelfData);
              setSelectedBox(boxData);
              setSelectedKent(kentData);

              // Navigate directly to the file level to show the specific kent
              setViewState({
                level: 'file',
                buildingId: shelfData.building,
                shelfId: shelfData.id,
                boxId: boxData.id,
                kentId: kentData.id
              });

              // Load the necessary data in sequence
              await loadShelves(shelfData.building);
              await loadBoxes(shelfData.id);
              await loadKents(boxData.id);
              await loadFiles(kentData.id);

              // Show notification about the navigation
              showNotification(
                `Navigated to ${kentData.name} in ${shelfData.name} - ${boxData.row}x${boxData.column}`,
                'success'
              );
            }
          }
        }
      }
    } catch (error) {
      console.error('Error auto-navigating to file:', error);
      showNotification('Could not navigate to file location', 'error');
    }
  };

  useEffect(() => {
    if (selectedBuildingId && viewState.level === 'shelf') {
      loadShelves(selectedBuildingId);
    }
  }, [selectedBuildingId, viewState.level]);

  const loadBuildings = async () => {
    try {
      setLoading(true);
      const data = await locationService.getBuildings();
      setBuildings(data.results || []);
      if (data.results?.length > 0) {
        setSelectedBuildingId(data.results[0].id);
      }
    } catch (error) {
      console.error('Error loading buildings:', error);
      setError('Failed to load buildings');
      showNotification('Failed to load buildings', 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadShelves = async (buildingId: number) => {
    try {
      setLoading(true);
      const data = await locationService.getShelves({ building: buildingId });
      setShelves(data.results || []);
    } catch (error) {
      console.error('Error loading shelves:', error);
      setError('Failed to load shelves');
      showNotification('Failed to load shelves', 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadBoxes = async (shelfId: number) => {
    try {
      setLoading(true);
      const data = await locationService.getBoxes({ shelf: shelfId });

      const boxesToSet = data.results || [];
      setBoxes(boxesToSet);
    } catch (error) {
      console.error('Error loading boxes:', error);
      setError('Failed to load boxes');
      showNotification('Failed to load boxes', 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadKents = async (boxId: number) => {
    try {
      setLoading(true);
      const data = await locationService.getKents({ box: boxId });
      setKents(data.results || []);
    } catch (error) {
      console.error('Error loading kents:', error);
      setError('Failed to load kents');
      showNotification('Failed to load kents', 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadShelfKents = async (shelfId: number) => {
    try {
      setLoading(true);
      const data = await locationService.getKents({ shelf: shelfId });
      console.log('Loaded kents for shelf:', shelfId, data);

      const kentsToSet = data.results || [];
      setKents(kentsToSet);
    } catch (error) {
      console.error('Error loading shelf kents:', error);
      setError('Failed to load shelf kents');
      showNotification('Failed to load shelf kents', 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadFiles = async (kentId: number) => {
    try {
      setLoading(true);
      const data = await fileService.getKentFiles(kentId);
      setFiles(data || []);
    } catch (error) {
      console.error('Error loading files:', error);
      setError('Failed to load files');
      showNotification('Failed to load files', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Navigation handlers
  const handleBuildingClick = (buildingId: number) => {
    setSelectedBuildingId(buildingId);
    setViewState({ level: 'shelf', buildingId });
    loadShelves(buildingId);
  };

  const handleShelfClick = async (shelf: Shelf) => {
    setSelectedShelf(shelf);
    setViewState({ level: 'shelfDetail', buildingId: selectedBuildingId, shelfId: shelf.id });

    // Load boxes first, then kents (since kents depend on boxes)
    await loadBoxes(shelf.id);
    await loadShelfKents(shelf.id);
  };

  const handleBoxClick = (box: BoxType) => {
    setSelectedBox(box);
    setViewState({ level: 'kent', buildingId: selectedBuildingId, shelfId: selectedShelf?.id, boxId: box.id });
    loadKents(box.id);
  };

  const handleKentClick = (kent: Kent) => {
    setSelectedKent(kent);
    setViewState({ level: 'file', buildingId: selectedBuildingId, shelfId: selectedShelf?.id, boxId: selectedBox?.id, kentId: kent.id });
    loadFiles(kent.id);
  };

  const handleFileClick = (file: FileType) => {
    console.log('File clicked:', file);
    console.log('File ID:', file.id);
    console.log('File name:', file.name);
    console.log('Document count:', file.document_count);

    setSelectedFile(file);
    setSelectedFileForDocuments(file);
    setDocumentModalOpen(true);

    // Also show notification with file info
    showNotification(
      `Opening documents for: ${file.name} (${file.document_count} documents)`,
      'info'
    );
  };

  const handleCloseDocumentModal = () => {
    setDocumentModalOpen(false);
    setSelectedFileForDocuments(null);
  };



  // Breadcrumb navigation
  const handleBreadcrumbClick = (level: ViewState['level']) => {
    switch (level) {
      case 'building':
        setViewState({ level: 'building' });
        setSelectedBuildingId(null);
        setSelectedShelf(null);
        setSelectedBox(null);
        setSelectedKent(null);
        setSelectedFile(null);
        break;
      case 'shelf':
        if (selectedBuildingId) {
          setViewState({ level: 'shelf', buildingId: selectedBuildingId });
          setSelectedShelf(null);
          setSelectedBox(null);
          setSelectedKent(null);
          setSelectedFile(null);
        }
        break;
      case 'shelfDetail':
        if (selectedShelf) {
          setViewState({ level: 'shelfDetail', buildingId: selectedBuildingId, shelfId: selectedShelf.id });
          setSelectedBox(null);
          setSelectedKent(null);
          setSelectedFile(null);
        }
        break;
      case 'kent':
        if (selectedShelf && selectedBox) {
          setViewState({ level: 'kent', buildingId: selectedBuildingId, shelfId: selectedShelf.id, boxId: selectedBox.id });
          setSelectedKent(null);
          setSelectedFile(null);
        }
        break;
      case 'file':
        if (selectedShelf && selectedBox && selectedKent) {
          setViewState({ level: 'file', buildingId: selectedBuildingId, shelfId: selectedShelf.id, boxId: selectedBox.id, kentId: selectedKent.id });
          setSelectedFile(null);
        }
        break;
    }
  };

  // Utility functions
  const getUtilizationColor = (utilization: number) => {
    if (utilization >= 90) return '#f44336'; // Red - Full
    if (utilization >= 70) return '#ff9800'; // Orange - High
    if (utilization >= 40) return '#ffeb3b'; // Yellow - Medium
    return '#4caf50'; // Green - Low
  };

  const getStatusColor = (isActive: boolean, isFull?: boolean) => {
    if (!isActive) return '#9e9e9e'; // Grey - Inactive
    if (isFull) return '#f44336'; // Red - Full
    return '#4caf50'; // Green - Available
  };

  const getFileStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active': return '#4caf50'; // Green
      case 'archived': return '#ff9800'; // Orange
      case 'closed': return '#f44336'; // Red
      case 'transferred': return '#9c27b0'; // Purple
      default: return '#2196f3'; // Blue
    }
  };

  const getFileTypeIcon = (fileType: string) => {
    // You can expand this based on file type icons
    switch (fileType.toLowerCase()) {
      case 'business': return <Business sx={{ fontSize: 24 }} />;
      case 'tax': return <Receipt sx={{ fontSize: 24 }} />;
      case 'legal': return <Gavel sx={{ fontSize: 24 }} />;
      case 'personal': return <Person sx={{ fontSize: 24 }} />;
      default: return <Description sx={{ fontSize: 24 }} />;
    }
  };

  // Breadcrumbs component
  const renderBreadcrumbs = () => {
    const breadcrumbItems = [
      { label: 'Dashboard', onClick: () => navigate('/dashboard'), icon: <Home fontSize="small" /> },
      { label: 'Locations', onClick: () => navigate('/locations'), icon: <LocationOn fontSize="small" /> },
      { label: 'Buildings', onClick: () => handleBreadcrumbClick('building'), icon: <Business fontSize="small" /> },
    ];

    if (selectedBuildingId && viewState.level !== 'building') {
      const building = buildings.find(b => b.id === selectedBuildingId);
      breadcrumbItems.push({
        label: building?.name || 'Building',
        onClick: () => handleBreadcrumbClick('shelf'),
        icon: <Business fontSize="small" />
      });
    }

    if (selectedShelf && viewState.level !== 'shelf') {
      breadcrumbItems.push({
        label: selectedShelf.name,
        onClick: () => handleBreadcrumbClick('shelfDetail'),
        icon: <Shelves fontSize="small" />
      });
    }

    if (selectedBox) {
      breadcrumbItems.push({
        label: `Position ${selectedBox.row}-${selectedBox.column}`,
        onClick: () => handleBreadcrumbClick('kent'),
        icon: <Archive fontSize="small" />
      });
    }

    if (selectedKent) {
      breadcrumbItems.push({
        label: selectedKent.name,
        onClick: () => handleBreadcrumbClick('file'),
        icon: <Folder fontSize="small" />
      });
    }

    if (selectedFile) {
      breadcrumbItems.push({
        label: selectedFile.name,
        onClick: () => {},
        icon: <Description fontSize="small" />
      });
    }

    return (
      <Breadcrumbs sx={{ mb: 3 }}>
        {breadcrumbItems.map((item, index) => (
          <Link
            key={index}
            component="button"
            variant="body2"
            onClick={item.onClick}
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 0.5,
              textDecoration: 'none',
              color: index === breadcrumbItems.length - 1 ? 'text.primary' : 'inherit',
              cursor: 'pointer',
              '&:hover': {
                color: 'primary.main',
              }
            }}
          >
            {item.icon}
            {item.label}
          </Link>
        ))}
      </Breadcrumbs>
    );
  };

  // Controls component
  const renderControls = () => (
    <Paper sx={{ p: 2, mb: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
        {/* Building selector only shown when not in building view */}
        {viewState.level !== 'building' && (
          <FormControl size="small" sx={{ minWidth: 200 }}>
            <InputLabel>Building</InputLabel>
            <Select
              value={selectedBuildingId || (buildings.length > 0 ? buildings[0].id : '')}
              onChange={(e) => handleBuildingClick(e.target.value as number)}
              label="Building"
            >
              {buildings.map((building) => (
                <MenuItem key={building.id} value={building.id}>
                  {building.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        )}

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Tooltip title="Zoom Out">
            <IconButton
              onClick={() => setZoomLevel(Math.max(0.5, zoomLevel - 0.1))}
              disabled={zoomLevel <= 0.5}
            >
              <ZoomOut />
            </IconButton>
          </Tooltip>
          <Typography variant="body2" sx={{ minWidth: 60, textAlign: 'center' }}>
            {Math.round(zoomLevel * 100)}%
          </Typography>
          <Tooltip title="Zoom In">
            <IconButton
              onClick={() => setZoomLevel(Math.min(2, zoomLevel + 0.1))}
              disabled={zoomLevel >= 2}
            >
              <ZoomIn />
            </IconButton>
          </Tooltip>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Tooltip title="Grid View">
            <IconButton
              onClick={() => setViewMode('grid')}
              color={viewMode === 'grid' ? 'primary' : 'default'}
            >
              <ViewModule />
            </IconButton>
          </Tooltip>
          <Tooltip title="List View">
            <IconButton
              onClick={() => setViewMode('list')}
              color={viewMode === 'list' ? 'primary' : 'default'}
            >
              <ViewList />
            </IconButton>
          </Tooltip>
        </Box>

        <Button
          startIcon={<Refresh />}
          onClick={() => {
            if (selectedBuildingId) {
              loadShelves(selectedBuildingId);
            }
          }}
          variant="outlined"
          size="small"
        >
          Refresh
        </Button>
      </Box>
    </Paper>
  );

  if (loading && !shelves.length && !boxes.length && !kents.length) {
    return (
      <Container maxWidth="xl" sx={{ py: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="xl" sx={{ py: 4 }}>
        <Alert severity="error" action={
          <Button color="inherit" size="small" onClick={loadBuildings}>
            <Refresh />
            Retry
          </Button>
        }>
          {error}
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {renderBreadcrumbs()}

      {/* HERE IS THE FILE Banner */}
      {highlightFileId && highlightSource === 'file-detail' && (
        <Alert
          severity="success"
          sx={{
            mb: 3,
            py: 2,
            fontSize: '1.1rem',
            fontWeight: 'bold',
            background: 'linear-gradient(135deg, #4caf50 0%, #66bb6a 100%)',
            color: 'white',
            border: '2px solid #4caf50',
            borderRadius: 3,
            boxShadow: '0 8px 32px rgba(76, 175, 80, 0.3)',
            animation: 'bounce 2s infinite',
            '@keyframes bounce': {
              '0%, 20%, 50%, 80%, 100%': { transform: 'translateY(0)' },
              '40%': { transform: 'translateY(-10px)' },
              '60%': { transform: 'translateY(-5px)' },
            },
            '& .MuiAlert-message': {
              fontSize: '1.1rem',
              fontWeight: 'bold',
              display: 'flex',
              alignItems: 'center',
              gap: 2,
            }
          }}
        >
          🎯 <span style={{ fontSize: '1.3rem' }}>HERE IS THE FILE!</span>
          <span style={{ fontSize: '1rem', opacity: 0.9 }}>
            The highlighted file below is the one you selected from the file details page.
          </span>
        </Alert>
      )}

      {renderControls()}

      <Typography variant="h4" component="h1" sx={{ mb: 3, fontWeight: 600 }}>
        Interactive Shelf Visualization
      </Typography>

      {/* Building Level View - Building Cards */}
      {viewState.level === 'building' && (
        <Box sx={{ bgcolor: '#f8f8f8', p: 2, borderRadius: 2 }}>
          <Typography variant="h5" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
            <Business color="primary" />
            Select a Building
          </Typography>

          <Grid container spacing={3} sx={{ transform: `scale(${zoomLevel})`, transformOrigin: 'top left' }}>
            {buildings.map((building) => (
              <Grid key={building.id} size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
                <Card
                  sx={{
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    border: '2px solid transparent',
                    height: 200,
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: 6,
                      borderColor: 'primary.main',
                    }
                  }}
                  onClick={() => handleBuildingClick(building.id)}
                >
                  <CardContent sx={{ textAlign: 'center', p: 3, height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                    <Box
                      sx={{
                        width: 80,
                        height: 80,
                        borderRadius: 2,
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mx: 'auto',
                        mb: 2,
                        color: 'white',
                        boxShadow: 3
                      }}
                    >
                      <Business sx={{ fontSize: 40 }} />
                    </Box>
                    <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                      {building.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Code: {building.code}
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, flexWrap: 'wrap' }}>
                      <Chip
                        label={`${building.shelf_count || 0} shelves`}
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                      <Chip
                        label={`${building.kent_count || 0} kents`}
                        size="small"
                        color="secondary"
                        variant="outlined"
                      />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      )}

      {/* Shelf Level View - Shelf Cards */}
      {viewState.level === 'shelf' && selectedBuildingId && (
        <Box sx={{ bgcolor: '#f8f8f8', p: 2, borderRadius: 2 }}>
          <Typography variant="h5" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
            <Shelves color="primary" />
            Shelves in {buildings.find(b => b.id === selectedBuildingId)?.name}
          </Typography>

          <Grid container spacing={3} sx={{ transform: `scale(${zoomLevel})`, transformOrigin: 'top left' }}>
            {shelves.map((shelf) => (
              <Grid key={shelf.id} size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
                <Card
                  sx={{
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    border: '2px solid transparent',
                    height: 220,
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: 6,
                      borderColor: 'primary.main',
                    }
                  }}
                  onClick={() => handleShelfClick(shelf)}
                >
                  <CardContent sx={{ textAlign: 'center', p: 3, height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                    <Box
                      sx={{
                        width: 80,
                        height: 80,
                        borderRadius: 2,
                        bgcolor: shelf.utilization && shelf.utilization > 80 ? 'error.main' : shelf.utilization && shelf.utilization > 60 ? 'warning.main' : 'success.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mx: 'auto',
                        mb: 2,
                        color: 'white',
                        boxShadow: 3
                      }}
                    >
                      <Shelves sx={{ fontSize: 40 }} />
                    </Box>
                    <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                      {shelf.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      Code: {shelf.code}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {shelf.rows} × {shelf.columns} grid
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, flexWrap: 'wrap' }}>
                      <Chip
                        label={`${shelf.utilization?.toFixed(1) || 0}% full`}
                        size="small"
                        color={shelf.utilization && shelf.utilization > 80 ? 'error' : shelf.utilization && shelf.utilization > 60 ? 'warning' : 'success'}
                      />
                      <Chip
                        label={`${shelf.kent_count || 0} kents`}
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      )}

      {/* Shelf Detail View - Real Physical Shelf Design */}
      {viewState.level === 'shelfDetail' && selectedShelf && (
        <Box sx={{ bgcolor: '#f8f8f8', p: 2 }}>
          <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
            <Shelves color="primary" />
            {selectedShelf.name} - Physical Shelf View
            <Chip label={`${kents.length} kents`} color="primary" size="small" sx={{ ml: 'auto' }} />
          </Typography>

          <Box sx={{ transform: `scale(${zoomLevel})`, transformOrigin: 'top left' }}>
            {/* Single Shelf - Real Office Style */}
            <Box>
              {/* Shelf Header */}
              <Typography variant="h6" sx={{ mb: 1, color: '#333', fontWeight: 600 }}>
                {selectedShelf.name} ({selectedShelf.code}) - {selectedShelf.utilization?.toFixed(1) || 0}% Full
              </Typography>


              {/* Real Wooden Office Shelf Structure */}
              <Box sx={{
                position: 'relative',
                bgcolor: '#C8A882', // Darker wood frame
                border: '4px solid #B8956F',
                borderRadius: '6px',
                p: 1,
                boxShadow: '0 6px 20px rgba(0,0,0,0.25)',
                background: 'linear-gradient(145deg, #D4B896 0%, #C8A882 50%, #B8956F 100%)',
                // Wood grain texture
                backgroundImage: `
                  repeating-linear-gradient(90deg,
                    rgba(184, 149, 111, 0.1) 0px,
                    rgba(184, 149, 111, 0.1) 2px,
                    transparent 2px,
                    transparent 8px),
                  linear-gradient(0deg, rgba(160, 130, 95, 0.08) 0%, transparent 100%)
                `,
                '&:before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, transparent 50%, rgba(0,0,0,0.05) 100%)',
                  borderRadius: '2px',
                  pointerEvents: 'none'
                }
              }}>
                {/* Real Wooden Shelf Compartments */}
                <Stack spacing={1}>
                  {Array.from({ length: selectedShelf.rows }, (_, rowIndex) => (
                    <Box
                      key={rowIndex}
                      sx={{
                        position: 'relative',
                        height: 200,
                        bgcolor: '#F5E5D2', // Light interior wood
                        border: '3px solid #B8956F',
                        borderRadius: '3px',
                        background: 'linear-gradient(180deg, #F8E8D5 0%, #F5E5D2 50%, #F0E0C7 100%)',
                        boxShadow: 'inset 0 3px 8px rgba(0,0,0,0.15), inset 0 -2px 4px rgba(255,255,255,0.2)',
                        overflow: 'hidden',
                        // Deep compartment effect
                        '&:before': {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          background: 'linear-gradient(180deg, rgba(0,0,0,0.08) 0%, transparent 20%, transparent 80%, rgba(0,0,0,0.05) 100%)',
                          pointerEvents: 'none'
                        }
                      }}
                    >
                      {/* Row Label */}
                      <Typography
                        variant="caption"
                        sx={{
                          position: 'absolute',
                          top: 8,
                          left: 12,
                          fontWeight: 'bold',
                          color: '#555',
                          bgcolor: 'rgba(255,255,255,0.9)',
                          px: 1.5,
                          py: 0.5,
                          borderRadius: 1,
                          fontSize: '11px',
                          zIndex: 10,
                          boxShadow: '0 1px 3px rgba(0,0,0,0.2)'
                        }}
                      >
                        Row {rowIndex + 1}
                      </Typography>

                      {/* Column Sections - Real Shelf Divisions */}
                      <Box sx={{
                        display: 'flex',
                        height: '100%',
                        position: 'relative'
                      }}>
                        {Array.from({ length: selectedShelf.columns }, (_, colIndex) => {
                          const box = boxes.find(b => b.row === rowIndex + 1 && b.column === colIndex + 1);
                          const boxKents = kents.filter(k => k.box === box?.id);

                          return (
                            <Box
                              key={colIndex}
                              sx={{
                                flex: 1,
                                height: '100%',
                                position: 'relative',
                                borderRight: colIndex < selectedShelf.columns - 1 ? '3px solid #B8956F' : 'none',
                                display: 'flex',
                                alignItems: 'flex-end',
                                justifyContent: 'flex-start',
                                px: 2,
                                py: 3,
                                minWidth: 150,
                                background: 'linear-gradient(90deg, rgba(248,232,213,0.8) 0%, rgba(245,229,210,0.6) 100%)',
                                // Wooden divider effect
                                '&:before': colIndex < selectedShelf.columns - 1 ? {
                                  content: '""',
                                  position: 'absolute',
                                  top: 0,
                                  right: -1.5,
                                  bottom: 0,
                                  width: '3px',
                                  background: 'linear-gradient(180deg, #A0825F 0%, #B8956F 50%, #A0825F 100%)',
                                  boxShadow: 'inset 1px 0 2px rgba(0,0,0,0.2), 1px 0 2px rgba(255,255,255,0.3)'
                                } : {}
                              }}
                            >
                              {/* Column Label */}
                              <Typography
                                variant="caption"
                                sx={{
                                  position: 'absolute',
                                  top: 8,
                                  right: 8,
                                  fontSize: '9px',
                                  color: '#777',
                                  bgcolor: 'rgba(255,255,255,0.8)',
                                  px: 0.5,
                                  borderRadius: 0.5
                                }}
                              >
                                Col {colIndex + 1}
                              </Typography>
                              {/* File Binders - Real Office Style */}
                              <Box sx={{
                                display: 'flex',
                                alignItems: 'flex-end',
                                justifyContent: 'flex-start', // Start from left like real office shelves
                                gap: 0.5,
                                height: '85%',
                                width: '100%',
                                pl: 0.5
                              }}>
                                {boxKents.slice(0, 6).map((kent, kentIndex) => (
                                  <Tooltip
                                    key={kent.id}
                                    title={
                                      <Box>
                                        <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                                          {kent.name}
                                        </Typography>
                                        <Typography variant="caption" display="block">
                                          Code: {kent.code}
                                        </Typography>
                                        <Typography variant="caption" display="block">
                                          Files: {kent.file_count || 0}
                                        </Typography>
                                        <Typography variant="caption" display="block">
                                          Position: Row {rowIndex + 1}, Col {colIndex + 1}
                                        </Typography>
                                      </Box>
                                    }
                                    placement="top"
                                    arrow
                                  >
                                    <Box
                                      sx={{
                                        width: 38,
                                        height: `${140 + (kentIndex % 3) * 10}px`, // Realistic height variation
                                        bgcolor: getKentColor(kent),
                                        border: '2px solid rgba(0,0,0,0.3)',
                                        borderRadius: '4px 4px 0 0',
                                        position: 'relative',
                                        cursor: 'pointer',
                                        transition: 'all 0.2s ease',
                                        marginLeft: kentIndex > 0 ? '-1px' : 0, // Tight packing like real binders
                                        zIndex: kentIndex + 1,
                                        boxShadow: '3px 0 6px rgba(0,0,0,0.25), inset -1px 0 2px rgba(0,0,0,0.1)',
                                        background: `linear-gradient(90deg,
                                          ${getKentColor(kent)} 0%,
                                          ${getKentColor(kent)}f0 20%,
                                          ${getKentColor(kent)}dd 80%,
                                          ${getKentColor(kent)}cc 100%)`,
                                        '&:hover': {
                                          transform: 'translateY(-10px) scale(1.03)',
                                          zIndex: 50,
                                          boxShadow: '0 10px 20px rgba(0,0,0,0.3)'
                                        },
                                        // Realistic binder spine with ring holes
                                        '&:before': {
                                          content: '""',
                                          position: 'absolute',
                                          left: 2,
                                          top: 15,
                                          bottom: 15,
                                          width: '4px',
                                          bgcolor: 'rgba(0,0,0,0.5)',
                                          borderRadius: '2px',
                                          boxShadow: 'inset 0 0 3px rgba(0,0,0,0.4), 1px 0 2px rgba(255,255,255,0.2)'
                                        },
                                        '&:after': {
                                          content: '""',
                                          position: 'absolute',
                                          right: 2,
                                          top: 15,
                                          bottom: 15,
                                          width: '3px',
                                          bgcolor: 'rgba(255,255,255,0.7)',
                                          borderRadius: '1px',
                                          boxShadow: 'inset 1px 0 2px rgba(0,0,0,0.1)'
                                        }
                                      }}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleKentClick(kent);
                                    }}
                                  >
                                    {/* Realistic White Label Spine */}
                                    <Box
                                      sx={{
                                        position: 'absolute',
                                        top: 25,
                                        left: 6,
                                        right: 6,
                                        height: 90,
                                        bgcolor: '#FEFEFE',
                                        border: '1px solid rgba(0,0,0,0.15)',
                                        borderRadius: '3px',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        boxShadow: 'inset 0 2px 4px rgba(0,0,0,0.08), 0 1px 2px rgba(0,0,0,0.1)',
                                        background: 'linear-gradient(180deg, #FFFFFF 0%, #FEFEFE 50%, #F8F8F8 100%)'
                                      }}
                                    >
                                      <Typography
                                        variant="caption"
                                        sx={{
                                          transform: 'rotate(-90deg)',
                                          fontSize: '9px',
                                          fontWeight: 'bold',
                                          color: 'rgba(0,0,0,0.8)',
                                          whiteSpace: 'nowrap',
                                          letterSpacing: '0.3px'
                                        }}
                                      >
                                        {kent.code}
                                      </Typography>
                                    </Box>

                                    {/* Realistic Binder Ring Holes */}
                                    {[35, 55, 75, 95, 115].map((top, i) => (
                                      <Box
                                        key={i}
                                        sx={{
                                          position: 'absolute',
                                          left: 1,
                                          top: `${top}px`,
                                          width: 6,
                                          height: 6,
                                          borderRadius: '50%',
                                          bgcolor: 'rgba(0,0,0,0.8)',
                                          border: '1px solid rgba(0,0,0,0.9)',
                                          boxShadow: 'inset 0 2px 3px rgba(0,0,0,0.6), 0 1px 1px rgba(255,255,255,0.2)'
                                        }}
                                      />
                                    ))}

                                    {/* File thickness indicator */}
                                    <Box
                                      sx={{
                                        position: 'absolute',
                                        bottom: 4,
                                        left: 2,
                                        right: 2,
                                        height: 3,
                                        bgcolor: 'rgba(0,0,0,0.2)',
                                        borderRadius: '1px'
                                      }}
                                    />
                                  </Box>
                                </Tooltip>
                                ))}

                                {/* More files indicator */}
                                {boxKents.length > 6 && (
                                  <Box
                                    sx={{
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                      width: 24,
                                      height: 100,
                                      bgcolor: 'rgba(0,0,0,0.1)',
                                      border: '1px dashed rgba(0,0,0,0.3)',
                                      borderRadius: 1,
                                      fontSize: '10px',
                                      color: '#666',
                                      fontWeight: 'bold'
                                    }}
                                  >
                                    +{boxKents.length - 6}
                                  </Box>
                                )}
                              </Box>
                            </Box>
                          );
                        })}
                      </Box>
                    </Box>
                  ))}
                </Stack>
              </Box>
              </Box>
          </Box>
        </Box>
      )}

      {/* Box Level View - Detailed Office Shelf View */}
      {viewState.level === 'kent' && selectedShelf && (
        <Box sx={{ bgcolor: '#f8f8f8', p: 2 }}>
          <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
            <Archive color="primary" />
            {selectedShelf.name} - Detailed Shelf View
            <Chip label={`${boxes.length} positions`} color="primary" size="small" sx={{ ml: 'auto' }} />
          </Typography>

          <Box sx={{ transform: `scale(${zoomLevel})`, transformOrigin: 'top left' }}>
            {/* Single Shelf - Zoomed In View */}
            <Box sx={{
              position: 'relative',
              bgcolor: '#2a2a2a',
              border: '2px solid #1a1a1a',
              borderRadius: '4px',
              p: 1,
              minHeight: 300,
              boxShadow: '0 4px 8px rgba(0,0,0,0.3)'
            }}>
              <Stack spacing={2}>
                {Array.from({ length: selectedShelf.rows }, (_, rowIndex) => (
                  <Box
                    key={rowIndex}
                    sx={{
                      position: 'relative',
                      height: 200,
                      bgcolor: '#e8e8e8',
                      border: '1px solid #ccc',
                      borderRadius: '2px',
                      display: 'flex',
                      alignItems: 'flex-end',
                      px: 1,
                      py: 0.5,
                      background: 'linear-gradient(180deg, #f0f0f0 0%, #e8e8e8 100%)',
                      boxShadow: 'inset 0 1px 3px rgba(0,0,0,0.1)'
                    }}
                  >
                    {/* Row Label */}
                    <Typography
                      variant="body2"
                      sx={{
                        position: 'absolute',
                        top: 8,
                        left: 12,
                        fontWeight: 'bold',
                        color: '#666',
                        bgcolor: 'rgba(255,255,255,0.9)',
                        px: 1.5,
                        py: 0.5,
                        borderRadius: 1,
                        fontSize: '12px'
                      }}
                    >
                      Row {rowIndex + 1}
                    </Typography>

                    {/* File Folders - Larger and More Detailed */}
                    <Box sx={{ display: 'flex', gap: 1, height: '100%', alignItems: 'flex-end', width: '100%', pt: 4 }}>
                      {Array.from({ length: selectedShelf.columns }, (_, colIndex) => {
                        const box = boxes.find(b => b.row === rowIndex + 1 && b.column === colIndex + 1);
                        const boxKents = kents.filter(k => k.box === box?.id);

                        return (
                          <Box
                            key={colIndex}
                            sx={{
                              flex: 1,
                              height: '100%',
                              display: 'flex',
                              alignItems: 'flex-end',
                              justifyContent: 'flex-start',
                              position: 'relative',
                              minWidth: 120,
                              cursor: box ? 'pointer' : 'default',
                              border: '1px dashed rgba(0,0,0,0.1)',
                              borderRadius: 1,
                              mx: 0.5
                            }}
                            onClick={() => box && handleBoxClick(box)}
                          >
                            {/* Position Label */}
                            <Typography
                              variant="caption"
                              sx={{
                                position: 'absolute',
                                top: 4,
                                left: 4,
                                fontSize: '10px',
                                color: '#999',
                                bgcolor: 'rgba(255,255,255,0.8)',
                                px: 0.5,
                                borderRadius: 0.5
                              }}
                            >
                              {rowIndex + 1}-{colIndex + 1}
                            </Typography>

                            {/* Office Binders - Larger Size */}
                            {boxKents.slice(0, 6).map((kent, kentIndex) => (
                              <Box
                                key={kent.id}
                                sx={{
                                  width: 42,
                                  height: 180,
                                  bgcolor: getKentColor(kent),
                                  border: '2px solid rgba(0,0,0,0.3)',
                                  borderRadius: '4px 4px 0 0',
                                  position: 'relative',
                                  marginLeft: kentIndex > 0 ? '-2px' : 0,
                                  zIndex: kentIndex + 1,
                                  cursor: 'pointer',
                                  transition: 'all 0.2s ease',
                                  boxShadow: '4px 0 8px rgba(0,0,0,0.25), inset -1px 0 2px rgba(0,0,0,0.1)',
                                  background: `linear-gradient(90deg,
                                    ${getKentColor(kent)} 0%,
                                    ${getKentColor(kent)}f0 20%,
                                    ${getKentColor(kent)}dd 80%,
                                    ${getKentColor(kent)}cc 100%)`,
                                  '&:hover': {
                                    transform: 'translateY(-15px) scale(1.05)',
                                    zIndex: 50,
                                    boxShadow: '0 15px 30px rgba(0,0,0,0.4)'
                                  },
                                  // Enhanced binder spine effect
                                  '&:before': {
                                    content: '""',
                                    position: 'absolute',
                                    left: 3,
                                    top: 12,
                                    bottom: 12,
                                    width: '3px',
                                    bgcolor: 'rgba(0,0,0,0.3)',
                                    borderRadius: '1px'
                                  },
                                  '&:after': {
                                    content: '""',
                                    position: 'absolute',
                                    right: 3,
                                    top: 12,
                                    bottom: 12,
                                    width: '3px',
                                    bgcolor: 'rgba(255,255,255,0.4)',
                                    borderRadius: '1px'
                                  }
                                }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleKentClick(kent);
                                }}
                              >
                                {/* White Label Spine - Like real office binders */}
                                <Box
                                  sx={{
                                    position: 'absolute',
                                    top: 25,
                                    left: 6,
                                    right: 6,
                                    height: 110,
                                    bgcolor: 'white',
                                    border: '1px solid rgba(0,0,0,0.1)',
                                    borderRadius: '2px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    boxShadow: 'inset 0 1px 3px rgba(0,0,0,0.1)'
                                  }}
                                >
                                  <Typography
                                    variant="body2"
                                    sx={{
                                      transform: 'rotate(-90deg)',
                                      fontSize: '10px',
                                      fontWeight: 'bold',
                                      color: 'rgba(0,0,0,0.8)',
                                      whiteSpace: 'nowrap',
                                      letterSpacing: '0.3px',
                                      maxWidth: 100,
                                      overflow: 'hidden',
                                      textOverflow: 'ellipsis'
                                    }}
                                  >
                                    {kent.name || kent.code}
                                  </Typography>
                                </Box>

                                {/* Binder ring holes - Like real office binders */}
                                {[35, 55, 75, 95, 115, 135].map((top, i) => (
                                  <Box
                                    key={i}
                                    sx={{
                                      position: 'absolute',
                                      left: 3,
                                      top: `${top}px`,
                                      width: 4,
                                      height: 4,
                                      borderRadius: '50%',
                                      bgcolor: 'rgba(0,0,0,0.6)',
                                      border: '0.5px solid rgba(0,0,0,0.8)',
                                      boxShadow: 'inset 0 1px 1px rgba(0,0,0,0.5)'
                                    }}
                                  />
                                ))}
                              </Box>
                            ))}

                            {/* More files indicator */}
                            {boxKents.length > 6 && (
                              <Typography
                                variant="caption"
                                sx={{
                                  position: 'absolute',
                                  bottom: 8,
                                  right: 8,
                                  fontSize: '10px',
                                  bgcolor: 'rgba(0,0,0,0.7)',
                                  color: 'white',
                                  px: 1,
                                  py: 0.5,
                                  borderRadius: 1
                                }}
                              >
                                +{boxKents.length - 6} more
                              </Typography>
                            )}

                            {/* Empty position indicator */}
                            {boxKents.length === 0 && (
                              <Box
                                sx={{
                                  width: '100%',
                                  height: 40,
                                  border: '2px dashed #ccc',
                                  borderRadius: 1,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  bgcolor: 'rgba(255,255,255,0.5)',
                                  mb: 2
                                }}
                              >
                                <Typography variant="caption" color="text.disabled">
                                  Empty Position
                                </Typography>
                              </Box>
                            )}
                          </Box>
                        );
                      })}
                    </Box>
                  </Box>
                ))}
              </Stack>
            </Box>
          </Box>
        </Box>
      )}

      {/* Kent Level View - File Folder Detail */}
      {viewState.level === 'file' && selectedBox && (
        <Card sx={{ bgcolor: '#f5f5f5', border: '2px solid #ddd' }}>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
              <FolderOpen color="primary" />
              File Folders in Position {selectedBox.row}-{selectedBox.column}
              <Chip label={`${kents.length} folders`} color="primary" size="small" sx={{ ml: 'auto' }} />
            </Typography>

            <Grid container spacing={3} sx={{ transform: `scale(${zoomLevel})`, transformOrigin: 'top left' }}>
              {kents.map((kent) => (
                <Grid key={kent.id} size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
                  <Card
                    sx={{
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      border: '2px solid transparent',
                      bgcolor: getKentColor(kent),
                      position: 'relative',
                      height: 200,
                      '&:hover': {
                        transform: 'translateY(-8px) scale(1.05)',
                        boxShadow: '0 8px 24px rgba(0,0,0,0.3)',
                        borderColor: '#333',
                        zIndex: 10
                      }
                    }}
                    onClick={() => handleKentClick(kent)}
                  >
                    {/* File folder tab */}
                    <Box
                      sx={{
                        position: 'absolute',
                        top: -8,
                        left: 16,
                        width: 60,
                        height: 16,
                        bgcolor: 'inherit',
                        borderRadius: '4px 4px 0 0',
                        border: '1px solid rgba(0,0,0,0.2)',
                        borderBottom: 'none',
                        filter: 'brightness(1.2)'
                      }}
                    />

                    <CardContent sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column' }}>
                      {/* Folder icon and title */}
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                        <FolderOpen sx={{ fontSize: 24, color: 'rgba(0,0,0,0.7)' }} />
                        <Typography variant="subtitle1" sx={{ fontWeight: 600, color: 'rgba(0,0,0,0.8)' }}>
                          {kent.name}
                        </Typography>
                      </Box>

                      {/* Kent code */}
                      <Typography variant="body2" sx={{ color: 'rgba(0,0,0,0.6)', mb: 1 }}>
                        Code: {kent.code}
                      </Typography>

                      {/* File count */}
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                        <Description sx={{ fontSize: 16, color: 'rgba(0,0,0,0.6)' }} />
                        <Typography variant="body2" sx={{ color: 'rgba(0,0,0,0.6)' }}>
                          {kent.file_count || 0} files
                        </Typography>
                      </Box>

                      {/* Status indicator */}
                      <Box sx={{ mt: 'auto' }}>
                        <Chip
                          label={kent.is_active ? 'Active' : 'Inactive'}
                          size="small"
                          color={kent.is_active ? 'success' : 'default'}
                          sx={{
                            bgcolor: kent.is_active ? 'rgba(76, 175, 80, 0.8)' : 'rgba(158, 158, 158, 0.8)',
                            color: 'white'
                          }}
                        />
                        {kent.is_full && (
                          <Chip
                            label="Full"
                            size="small"
                            color="warning"
                            sx={{ ml: 1, bgcolor: 'rgba(255, 152, 0, 0.8)', color: 'white' }}
                          />
                        )}
                      </Box>

                      {/* File preview dots */}
                      {kent.file_count > 0 && (
                        <Box sx={{
                          position: 'absolute',
                          bottom: 8,
                          right: 8,
                          display: 'flex',
                          gap: 0.5
                        }}>
                          {Array.from({ length: Math.min(kent.file_count, 5) }, (_, i) => (
                            <Box
                              key={i}
                              sx={{
                                width: 6,
                                height: 6,
                                borderRadius: '50%',
                                bgcolor: 'rgba(0,0,0,0.4)'
                              }}
                            />
                          ))}
                          {kent.file_count > 5 && (
                            <Typography variant="caption" sx={{ color: 'rgba(0,0,0,0.6)', ml: 0.5 }}>
                              +{kent.file_count - 5}
                            </Typography>
                          )}
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Document Level View */}
      {viewState.level === 'file' && selectedKent && (
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
              <Description color="primary" />
              Files in {selectedKent.name}
              <Chip label={`${files.length} files`} color="primary" size="small" sx={{ ml: 'auto' }} />
            </Typography>

            {files.length > 0 && (
              <Box sx={{ mb: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
                  File Statistics
                </Typography>
                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                  <Chip
                    label={`Total Documents: ${files.reduce((sum, file) => sum + file.document_count, 0)}`}
                    size="small"
                    color="info"
                  />
                  <Chip
                    label={`Active Files: ${files.filter(f => f.status === 'active').length}`}
                    size="small"
                    color="success"
                  />
                  <Chip
                    label={`File Types: ${new Set(files.map(f => f.file_type_name)).size}`}
                    size="small"
                    color="secondary"
                  />
                </Box>
              </Box>
            )}

            {files.length === 0 ? (
              <Alert severity="info" sx={{ textAlign: 'center' }}>
                <Typography variant="h6" sx={{ mb: 1 }}>No Files Found</Typography>
                <Typography variant="body2">
                  This kent doesn't contain any files yet. Files can be added through the file management system.
                </Typography>
              </Alert>
            ) : (
              <Grid container spacing={2} sx={{ transform: `scale(${zoomLevel})`, transformOrigin: 'top left' }}>
                {files.map((file) => (
                  <Grid key={file.id} size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
                    <Card
                      sx={{
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        border: highlightFileId === file.id ? '4px solid #ff4444' : '2px solid transparent',
                        bgcolor: file.is_active ? getFileStatusColor(file.status) : 'grey.400',
                        boxShadow: highlightFileId === file.id ? '0 8px 32px rgba(255, 68, 68, 0.4)' : 'none',
                        transform: highlightFileId === file.id ? 'scale(1.05)' : 'none',
                        '&:hover': {
                          transform: highlightFileId === file.id ? 'scale(1.08) translateY(-4px)' : 'translateY(-4px)',
                          boxShadow: highlightFileId === file.id ? '0 12px 40px rgba(255, 68, 68, 0.5)' : 4,
                          borderColor: highlightFileId === file.id ? '#ff4444' : 'secondary.main',
                        }
                      }}
                      onClick={() => handleFileClick(file)}
                    >
                      <CardContent sx={{ textAlign: 'center', p: 2 }}>
                        <Box
                          sx={{
                            width: 50,
                            height: 50,
                            borderRadius: 1,
                            bgcolor: file.file_type_color || 'rgba(255,255,255,0.2)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            mx: 'auto',
                            mb: 2,
                            color: 'white',
                          }}
                        >
                          {getFileTypeIcon(file.file_type_name)}
                        </Box>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1, color: 'white' }}>
                          {file.name}
                        </Typography>
                        <Typography variant="caption" sx={{ color: 'rgba(255,255,255,0.8)', display: 'block', mb: 1 }}>
                          {file.file_number}
                        </Typography>
                        <Typography variant="caption" sx={{ color: 'rgba(255,255,255,0.8)', display: 'block', mb: 1 }}>
                          {file.file_type_name}
                        </Typography>
                        {highlightFileId === file.id && (
                          <Alert
                            severity="error"
                            sx={{
                              mb: 1,
                              py: 0.5,
                              fontSize: '0.7rem',
                              '& .MuiAlert-message': { fontSize: '0.7rem' },
                              animation: 'pulse 2s infinite',
                              '@keyframes pulse': {
                                '0%': { opacity: 1 },
                                '50%': { opacity: 0.7 },
                                '100%': { opacity: 1 },
                              }
                            }}
                          >
                            {highlightSource === 'file-detail' ? '👆 HERE IS THE FILE!' : '📍 Taxpayer File Location'}
                          </Alert>
                        )}
                        <Box sx={{ display: 'flex', justifyContent: 'center', gap: 0.5, mt: 1 }}>
                          <Chip
                            label={`${file.document_count} docs`}
                            size="small"
                            sx={{
                              height: 16,
                              fontSize: '0.5rem',
                              bgcolor: file.document_count > 0 ? 'rgba(76, 175, 80, 0.8)' : 'rgba(255,255,255,0.2)',
                              color: 'white',
                              fontWeight: file.document_count > 0 ? 600 : 400
                            }}
                          />
                          <Chip
                            label={file.status}
                            size="small"
                            sx={{
                              height: 16,
                              fontSize: '0.5rem',
                              bgcolor: 'rgba(255,255,255,0.2)',
                              color: 'white'
                            }}
                          />
                        </Box>
                        {file.document_count > 0 && (
                          <Typography
                            variant="caption"
                            sx={{
                              color: 'rgba(255,255,255,0.9)',
                              fontSize: '0.6rem',
                              mt: 0.5,
                              display: 'block',
                              textAlign: 'center',
                              fontStyle: 'italic'
                            }}
                          >
                            Click to view documents
                          </Typography>
                        )}
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}
          </CardContent>
        </Card>
      )}

      {/* Document List Modal */}
      {selectedFileForDocuments && (
        <DocumentListModal
          open={documentModalOpen}
          onClose={handleCloseDocumentModal}
          fileId={selectedFileForDocuments.id}
          fileName={selectedFileForDocuments.name}
        />
      )}
    </Container>
  );
};

export default ShelfVisualizationPage;
