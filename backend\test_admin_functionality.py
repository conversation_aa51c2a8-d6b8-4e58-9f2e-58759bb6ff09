#!/usr/bin/env python
"""
Test script to verify Django admin functionality
Tests admin interface for all registered models with sample data
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'arada_dms.settings')
django.setup()

from django.contrib import admin
from django.contrib.auth import get_user_model
from accounts.models import User, UserSession
from organizations.models import Organization, Department
from locations.models import Building, Shelf, Kent
from documents.models import DocumentType, Document, DocumentVersion, DocumentTag
from requests.models import DocumentRequest, RequestApproval, AuditLog, Notification

User = get_user_model()

def test_admin_functionality():
    """Test admin functionality with existing data"""
    print("🧪 Testing Django Admin Functionality...")
    
    # Test admin site registration
    print(f"\n📊 Admin Site Summary:")
    print(f"  Total registered models: {len(admin.site._registry)}")
    
    # Test each admin class
    admin_tests = [
        ('User', User, 'UserAdmin'),
        ('UserSession', UserSession, 'UserSessionAdmin'),
        ('Organization', Organization, 'OrganizationAdmin'),
        ('Department', Department, 'DepartmentAdmin'),
        ('Building', Building, 'BuildingAdmin'),
        ('Shelf', Shelf, 'ShelfAdmin'),
        ('Kent', Kent, 'KentAdmin'),
        ('DocumentType', DocumentType, 'DocumentTypeAdmin'),
        ('Document', Document, 'DocumentAdmin'),
        ('DocumentVersion', DocumentVersion, 'DocumentVersionAdmin'),
        ('DocumentTag', DocumentTag, 'DocumentTagAdmin'),
        ('DocumentRequest', DocumentRequest, 'DocumentRequestAdmin'),
        ('RequestApproval', RequestApproval, 'RequestApprovalAdmin'),
        ('AuditLog', AuditLog, 'AuditLogAdmin'),
        ('Notification', Notification, 'NotificationAdmin'),
    ]
    
    print(f"\n🔍 Testing Admin Classes:")
    
    for model_name, model_class, expected_admin in admin_tests:
        try:
            if model_class in admin.site._registry:
                admin_class = admin.site._registry[model_class]
                actual_admin = admin_class.__class__.__name__
                
                # Test basic admin functionality
                queryset = model_class.objects.all()
                count = queryset.count()
                
                print(f"  ✅ {model_name}: {actual_admin} ({count} records)")
                
                # Test admin methods if they exist
                if hasattr(admin_class, 'list_display'):
                    for field in admin_class.list_display:
                        if hasattr(admin_class, field) and callable(getattr(admin_class, field)):
                            # Test custom method with first object if available
                            if count > 0:
                                obj = queryset.first()
                                try:
                                    method = getattr(admin_class, field)
                                    result = method(obj)
                                    print(f"    📋 Method {field}: Working")
                                except Exception as e:
                                    print(f"    ⚠️  Method {field}: {str(e)[:50]}...")
                
            else:
                print(f"  ❌ {model_name}: Not registered")
                
        except Exception as e:
            print(f"  ❌ {model_name}: Error - {e}")
    
    # Test specific admin features
    print(f"\n🎯 Testing Specific Admin Features:")
    
    # Test Organization admin with logo preview
    try:
        org_admin = admin.site._registry[Organization]
        if Organization.objects.exists():
            org = Organization.objects.first()
            logo_preview = org_admin.logo_preview(org)
            print(f"  ✅ Organization logo preview: Working")
        else:
            print(f"  ⚠️  Organization logo preview: No data to test")
    except Exception as e:
        print(f"  ❌ Organization logo preview: {e}")
    
    # Test Kent admin with document count
    try:
        kent_admin = admin.site._registry[Kent]
        if Kent.objects.exists():
            kent = Kent.objects.first()
            doc_count = kent_admin.document_count(kent)
            print(f"  ✅ Kent document count: Working")
        else:
            print(f"  ⚠️  Kent document count: No data to test")
    except Exception as e:
        print(f"  ❌ Kent document count: {e}")
    
    # Test Document admin with expiry status
    try:
        doc_admin = admin.site._registry[Document]
        if Document.objects.exists():
            doc = Document.objects.first()
            expiry_status = doc_admin.is_expired(doc)
            print(f"  ✅ Document expiry status: Working")
        else:
            print(f"  ⚠️  Document expiry status: No data to test")
    except Exception as e:
        print(f"  ❌ Document expiry status: {e}")
    
    # Test DocumentRequest admin with overdue status
    try:
        req_admin = admin.site._registry[DocumentRequest]
        if DocumentRequest.objects.exists():
            req = DocumentRequest.objects.first()
            overdue_status = req_admin.is_overdue(req)
            print(f"  ✅ Request overdue status: Working")
        else:
            print(f"  ⚠️  Request overdue status: No data to test")
    except Exception as e:
        print(f"  ❌ Request overdue status: {e}")
    
    # Test DocumentTag admin with color preview
    try:
        tag_admin = admin.site._registry[DocumentTag]
        if DocumentTag.objects.exists():
            tag = DocumentTag.objects.first()
            color_preview = tag_admin.color_preview(tag)
            print(f"  ✅ Tag color preview: Working")
        else:
            print(f"  ⚠️  Tag color preview: No data to test")
    except Exception as e:
        print(f"  ❌ Tag color preview: {e}")

def test_admin_permissions():
    """Test admin permissions and access control"""
    print(f"\n🔒 Testing Admin Permissions:")
    
    # Test superuser access
    superusers = User.objects.filter(is_superuser=True)
    print(f"  Superusers: {superusers.count()}")
    
    # Test staff users
    staff_users = User.objects.filter(is_staff=True)
    print(f"  Staff users: {staff_users.count()}")
    
    # Test user roles
    for role in User.Role.choices:
        role_count = User.objects.filter(role=role[0]).count()
        print(f"  {role[1]} users: {role_count}")

def test_data_integrity():
    """Test data integrity and relationships"""
    print(f"\n🔗 Testing Data Integrity:")
    
    # Test organization relationships
    orgs = Organization.objects.count()
    users_with_org = User.objects.filter(organization__isnull=False).count()
    print(f"  Organizations: {orgs}, Users with org: {users_with_org}")
    
    # Test location hierarchy
    buildings = Building.objects.count()
    shelves = Shelf.objects.count()
    kents = Kent.objects.count()
    print(f"  Location hierarchy: {buildings} buildings, {shelves} shelves, {kents} kents")
    
    # Test document relationships
    doc_types = DocumentType.objects.count()
    documents = Document.objects.count()
    print(f"  Documents: {doc_types} types, {documents} documents")
    
    # Test request workflow
    requests = DocumentRequest.objects.count()
    approvals = RequestApproval.objects.count()
    print(f"  Requests: {requests} requests, {approvals} approvals")
    
    # Test audit logging
    audit_logs = AuditLog.objects.count()
    notifications = Notification.objects.count()
    print(f"  Audit: {audit_logs} logs, {notifications} notifications")

if __name__ == '__main__':
    test_admin_functionality()
    test_admin_permissions()
    test_data_integrity()
    print("\n🎉 Admin functionality testing completed!")
