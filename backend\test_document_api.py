#!/usr/bin/env python3

import os
import sys
import django
import requests
import json

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from documents.models import Document, DocumentType
from locations.models import File, Kent, Shelf, Building
from organizations.models import Organization
from accounts.models import User

def test_document_api():
    """Test the Document API with the new number_of_pages field"""
    
    print("🧪 Testing Document API with number_of_pages field...")
    
    # Test 1: Check if we can create a document with number_of_pages
    print("\n1. Testing Document creation with number_of_pages...")
    
    # Get or create test data
    try:
        org = Organization.objects.first()
        if not org:
            print("❌ No organization found. Please create one first.")
            return
            
        doc_type = DocumentType.objects.first()
        if not doc_type:
            print("❌ No document type found. Please create one first.")
            return
            
        file_obj = File.objects.first()
        if not file_obj:
            print("❌ No file found. Please create one first.")
            return
            
        print(f"✅ Using organization: {org.name}")
        print(f"✅ Using document type: {doc_type.name}")
        print(f"✅ Using file: {file_obj.name}")
        
        # Create a test document
        document = Document.objects.create(
            title="Test Document with Pages",
            description="Testing the number_of_pages field",
            document_type=doc_type,
            mode=Document.DocumentMode.PHYSICAL,
            document_file=file_obj,
            number_of_pages=25,
            reference_number="TEST-001"
        )
        
        print(f"✅ Created document: {document.title}")
        print(f"✅ Number of pages: {document.number_of_pages}")
        print(f"✅ Document file: {document.document_file.name}")
        print(f"✅ Auto-populated kent: {document.kent}")
        
        # Test 2: Check if the API returns the new field
        print("\n2. Testing API response...")
        
        try:
            response = requests.get(f'http://127.0.0.1:8000/api/documents/{document.id}/')
            if response.status_code == 200:
                data = response.json()
                print(f"✅ API response successful")
                print(f"✅ Number of pages in API: {data.get('number_of_pages')}")
                print(f"✅ Document file in API: {data.get('document_file')}")
                print(f"✅ File name in API: {data.get('file_name')}")
                print(f"✅ File number in API: {data.get('file_number')}")
            else:
                print(f"❌ API request failed: {response.status_code}")
                print(f"Response: {response.text}")
        except requests.exceptions.ConnectionError:
            print("❌ Could not connect to API. Make sure the server is running.")
        
        # Test 3: Test Files API
        print("\n3. Testing Files API...")
        
        try:
            response = requests.get('http://127.0.0.1:8000/api/locations/files/')
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Files API response successful")
                print(f"✅ Found {data.get('count', 0)} files")
                if data.get('results'):
                    first_file = data['results'][0]
                    print(f"✅ First file: {first_file.get('name')} ({first_file.get('file_number')})")
                    print(f"✅ File location: {first_file.get('location_path')}")
            else:
                print(f"❌ Files API request failed: {response.status_code}")
        except requests.exceptions.ConnectionError:
            print("❌ Could not connect to Files API.")
        
        # Clean up
        document.delete()
        print(f"\n✅ Test document deleted")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_document_api()
