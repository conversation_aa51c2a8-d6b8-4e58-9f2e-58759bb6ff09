import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  TextField,
  InputAdornment,
  Avatar,
  Divider,
  Stack,
  Fade,
  Alert,
  CircularProgress,
  IconButton,
  Breadcrumbs,
  Link,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TablePagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
} from '@mui/material';
import {
  Description,
  ArrowBack,
  Home,
  Add,
  Edit,
  Delete,
  Visibility,
  Business,
  Schedule,
  Security,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useNotification } from '../../contexts/NotificationContext';
import documentService from '../../services/documentService';
import organizationService from '../../services/organizationService';
import { normalizeCode, createCodeBlurHandler } from '../../utils/codeUtils';
import ConfirmationDialog from '../../components/common/ConfirmationDialog';
import type { DocumentType, DocumentTypeCreate, Organization } from '../../services/types';

const DocumentTypesPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { showNotification } = useNotification();

  const [documentTypes, setDocumentTypes] = useState<DocumentType[]>([]);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingDocumentType, setEditingDocumentType] = useState<DocumentType | null>(null);
  const [formData, setFormData] = useState<DocumentTypeCreate>({
    organization: 0,
    name: '',
    code: '',
    description: '',
    retention_days: 365,
    confidentiality_level: 'public',
    requires_expiry_date: false,
    requires_approval: false,
    allowed_file_extensions: 'pdf,doc,docx,jpg,png',
    max_file_size_mb: 10,
  });
  const [formErrors, setFormErrors] = useState<any>({});
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [documentTypeToDelete, setDocumentTypeToDelete] = useState<DocumentType | null>(null);
  const [deleting, setDeleting] = useState(false);

  const confidentialityLevels = [
    { value: 'public', label: 'Public' },
    { value: 'internal', label: 'Internal' },
    { value: 'confidential', label: 'Confidential' },
    { value: 'restricted', label: 'Restricted' },
  ];

  useEffect(() => {
    loadDocumentTypes();
    loadOrganizations();
  }, [page, rowsPerPage]);

  // Handle navigation state from detail page
  useEffect(() => {
    const state = location.state as any;
    if (state?.editDocumentType && state?.showForm) {
      handleEdit(state.editDocumentType);
      // Clear the state to prevent re-triggering
      navigate(location.pathname, { replace: true });
    }
  }, [location.state]);

  const loadDocumentTypes = async () => {
    try {
      setLoading(true);
      const response = await documentService.getDocumentTypes({
        page: page + 1,
        page_size: rowsPerPage,
      });
      setDocumentTypes(response.results);
      setTotalCount(response.count);
    } catch (error) {
      console.error('Error loading document types:', error);
      showNotification('Failed to load document types', 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadOrganizations = async () => {
    try {
      const response = await organizationService.getOrganizations({ page_size: 100 });
      setOrganizations(response.results);
    } catch (error) {
      console.error('Error loading organizations:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setSubmitting(true);
      setFormErrors({});

      if (editingDocumentType) {
        await documentService.updateDocumentType(editingDocumentType.id, formData);
        showNotification('Document type updated successfully', 'success');
      } else {
        await documentService.createDocumentType(formData);
        showNotification('Document type created successfully', 'success');
      }

      resetForm();
      loadDocumentTypes();
    } catch (error: any) {
      console.error('Error submitting form:', error);
      if (error.response?.data) {
        setFormErrors(error.response.data);
      }
      showNotification('Failed to save document type', 'error');
    } finally {
      setSubmitting(false);
    }
  };

  const handleViewDetails = (documentType: DocumentType) => {
    navigate(`/document-center/document-types/${documentType.id}`);
  };

  const handleEdit = (documentType: DocumentType) => {
    setEditingDocumentType(documentType);
    setFormData({
      organization: documentType.organization,
      name: documentType.name,
      code: documentType.code,
      description: documentType.description || '',
      retention_days: documentType.retention_days || 365,
      confidentiality_level: documentType.confidentiality_level || 'public',
      requires_expiry_date: documentType.requires_expiry_date || false,
      requires_approval: documentType.requires_approval || false,
      allowed_file_extensions: documentType.allowed_file_extensions || 'pdf,doc,docx,jpg,png',
      max_file_size_mb: documentType.max_file_size_mb || 10,
    });
    setShowForm(true);
  };

  const handleDelete = (documentType: DocumentType) => {
    setDocumentTypeToDelete(documentType);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!documentTypeToDelete) return;
    
    try {
      setDeleting(true);
      await documentService.deleteDocumentType(documentTypeToDelete.id);
      showNotification('Document type deleted successfully', 'success');
      loadDocumentTypes();
      handleCloseDeleteDialog();
    } catch (error) {
      console.error('Error deleting document type:', error);
      showNotification('Failed to delete document type', 'error');
    } finally {
      setDeleting(false);
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDocumentTypeToDelete(null);
    setDeleting(false);
  };

  const resetForm = () => {
    setFormData({
      organization: 0,
      name: '',
      code: '',
      description: '',
      retention_days: 365,
      confidentiality_level: 'public',
      requires_expiry_date: false,
      requires_approval: false,
      allowed_file_extensions: 'pdf,doc,docx,jpg,png',
      max_file_size_mb: 10,
    });
    setFormErrors({});
    setEditingDocumentType(null);
    setShowForm(false);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Fade in timeout={800}>
        <Box sx={{ mb: 4 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs sx={{ mb: 2 }}>
            <Link
              component="button"
              variant="body1"
              onClick={() => navigate('/document-center')}
              sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
            >
              <Home fontSize="small" />
              Document Management Center
            </Link>
            <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <Description fontSize="small" />
              Document Types
            </Typography>
          </Breadcrumbs>

          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'between', mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <IconButton onClick={() => navigate('/document-center')} sx={{ mr: 1 }}>
                <ArrowBack />
              </IconButton>
              <Avatar sx={{ bgcolor: 'primary.main', width: 56, height: 56 }}>
                <Description />
              </Avatar>
              <Box>
                <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
                  Document Types Management
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Configure document categories, retention policies, and validation rules
                </Typography>
              </Box>
            </Box>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setShowForm(true)}
              size="large"
              sx={{ ml: 'auto' }}
            >
              Add Document Type
            </Button>
          </Box>
        </Box>
      </Fade>

      {/* Form Section */}
      {showForm && (
        <Fade in timeout={600}>
          <Card sx={{ mb: 4 }}>
            <CardContent sx={{ p: 4 }}>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                {editingDocumentType ? 'Edit Document Type' : 'Add New Document Type'}
              </Typography>
              <Divider sx={{ mb: 3 }} />
              
              <form onSubmit={handleSubmit}>
                <Stack spacing={3}>
                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                    <TextField
                      label="Document Type Name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      error={!!formErrors.name}
                      helperText={formErrors.name || 'Enter the document type name'}
                      required
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Description color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                    
                    <TextField
                      label="Document Type Code"
                      value={formData.code}
                      onChange={(e) => setFormData({ ...formData, code: e.target.value })}
                      onBlur={createCodeBlurHandler(
                        (field, value) => setFormData(prev => ({ ...prev, [field]: value })),
                        'code'
                      )}
                      error={!!formErrors.code}
                      helperText={formErrors.code || 'Code will be automatically converted to uppercase (e.g., TAX, LIC, REP)'}
                      required
                      inputProps={{ maxLength: 10 }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Business color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>

                  <FormControl fullWidth required error={!!formErrors.organization}>
                    <InputLabel>Organization</InputLabel>
                    <Select
                      value={formData.organization}
                      onChange={(e) => setFormData({ ...formData, organization: Number(e.target.value) })}
                      label="Organization"
                    >
                      {organizations.map((org) => (
                        <MenuItem key={org.id} value={org.id}>
                          {org.name}
                        </MenuItem>
                      ))}
                    </Select>
                    {formErrors.organization && (
                      <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 2 }}>
                        {formErrors.organization}
                      </Typography>
                    )}
                  </FormControl>

                  <TextField
                    label="Description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    error={!!formErrors.description}
                    helperText={formErrors.description || 'Optional description'}
                    multiline
                    rows={3}
                    fullWidth
                  />

                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: 2 }}>
                    <TextField
                      label="Retention Days"
                      type="number"
                      value={formData.retention_days}
                      onChange={(e) => setFormData({ ...formData, retention_days: parseInt(e.target.value) || 0 })}
                      error={!!formErrors.retention_days}
                      helperText={formErrors.retention_days || 'Days to retain documents'}
                      required
                      inputProps={{ min: 1, max: 36500 }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Schedule color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />

                    <FormControl fullWidth error={!!formErrors.confidentiality_level}>
                      <InputLabel>Confidentiality Level</InputLabel>
                      <Select
                        value={formData.confidentiality_level}
                        onChange={(e) => setFormData({ ...formData, confidentiality_level: e.target.value })}
                        label="Confidentiality Level"
                        startAdornment={
                          <InputAdornment position="start">
                            <Security color="action" />
                          </InputAdornment>
                        }
                      >
                        {confidentialityLevels.map((level) => (
                          <MenuItem key={level.value} value={level.value}>
                            {level.label}
                          </MenuItem>
                        ))}
                      </Select>
                      {formErrors.confidentiality_level && (
                        <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 2 }}>
                          {formErrors.confidentiality_level}
                        </Typography>
                      )}
                    </FormControl>

                    <TextField
                      label="Max File Size (MB)"
                      type="number"
                      value={formData.max_file_size_mb}
                      onChange={(e) => setFormData({ ...formData, max_file_size_mb: parseInt(e.target.value) || 0 })}
                      error={!!formErrors.max_file_size_mb}
                      helperText={formErrors.max_file_size_mb || 'Maximum file size in MB'}
                      required
                      inputProps={{ min: 1, max: 100 }}
                    />
                  </Box>

                  <TextField
                    label="Allowed File Extensions"
                    value={formData.allowed_file_extensions}
                    onChange={(e) => setFormData({ ...formData, allowed_file_extensions: e.target.value })}
                    error={!!formErrors.allowed_file_extensions}
                    helperText={formErrors.allowed_file_extensions || 'Comma-separated list (e.g., pdf,doc,docx,jpg,png)'}
                    fullWidth
                  />

                  <Box>
                    <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                      Document Requirements
                    </Typography>
                    <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.requires_expiry_date}
                            onChange={(e) => setFormData({ ...formData, requires_expiry_date: e.target.checked })}
                          />
                        }
                        label="Requires Expiry Date"
                      />
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.requires_approval}
                            onChange={(e) => setFormData({ ...formData, requires_approval: e.target.checked })}
                          />
                        }
                        label="Requires Approval"
                      />
                    </Box>
                  </Box>

                  <Box sx={{ display: 'flex', gap: 2, pt: 2 }}>
                    <Button
                      type="submit"
                      variant="contained"
                      disabled={submitting}
                      startIcon={submitting ? <CircularProgress size={20} /> : <Add />}
                      size="large"
                    >
                      {submitting ? 'Saving...' : editingDocumentType ? 'Update Document Type' : 'Create Document Type'}
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={resetForm}
                      size="large"
                    >
                      Cancel
                    </Button>
                  </Box>
                </Stack>
              </form>
            </CardContent>
          </Card>
        </Fade>
      )}

      {/* Document Types Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
            Document Types List
          </Typography>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : documentTypes.length === 0 ? (
            <Alert severity="info">
              No document types found. Click "Add Document Type" to create your first document type.
            </Alert>
          ) : (
            <>
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Document Type</TableCell>
                      <TableCell>Organization</TableCell>
                      <TableCell>Retention</TableCell>
                      <TableCell>Security</TableCell>
                      <TableCell>Requirements</TableCell>
                      <TableCell>Documents</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {documentTypes.map((docType) => (
                      <TableRow key={docType.id} hover>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <Avatar sx={{ bgcolor: 'primary.main', width: 32, height: 32 }}>
                              <Description fontSize="small" />
                            </Avatar>
                            <Box>
                              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                {docType.name}
                              </Typography>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Chip label={docType.code} size="small" color="primary" />
                                <Typography variant="caption" color="text.secondary">
                                  {docType.description || 'No description'}
                                </Typography>
                              </Box>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {docType.organization_name || 'N/A'}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Box>
                            <Typography variant="body2">
                              {docType.retention_days} days
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {docType.retention_days_display || 'Standard retention'}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={docType.confidentiality_level || 'public'}
                            size="small"
                            color={
                              docType.confidentiality_level === 'restricted' ? 'error' :
                              docType.confidentiality_level === 'confidential' ? 'warning' :
                              docType.confidentiality_level === 'internal' ? 'info' : 'success'
                            }
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            {docType.requires_expiry_date && (
                              <Chip label="Expiry" size="small" variant="outlined" />
                            )}
                            {docType.requires_approval && (
                              <Chip label="Approval" size="small" variant="outlined" />
                            )}
                            {!docType.requires_expiry_date && !docType.requires_approval && (
                              <Typography variant="caption" color="text.secondary">
                                No requirements
                              </Typography>
                            )}
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {docType.document_count || 0} documents
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                            <IconButton
                              size="small"
                              onClick={() => handleViewDetails(docType)}
                              color="info"
                              title="View Details"
                            >
                              <Visibility fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleEdit(docType)}
                              color="primary"
                              title="Edit"
                            >
                              <Edit fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleDelete(docType)}
                              color="error"
                              title="Delete"
                            >
                              <Delete fontSize="small" />
                            </IconButton>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              <TablePagination
                component="div"
                count={totalCount}
                page={page}
                onPageChange={handleChangePage}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                rowsPerPageOptions={[5, 10, 25, 50]}
              />
            </>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        title="Delete Document Type"
        itemName={documentTypeToDelete?.name}
        itemType="Document Type"
        message={`Are you sure you want to delete "${documentTypeToDelete?.name}"? This will affect all documents using this type. This action cannot be undone.`}
        confirmText="Delete Document Type"
        severity="error"
        loading={deleting}
      />
    </Container>
  );
};

export default DocumentTypesPage;
