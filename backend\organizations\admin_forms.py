from django import forms
from django.contrib import admin
from .models import Organization, Department


class OrganizationAdminForm(forms.ModelForm):
    """Custom admin form for Organization with enhanced widgets"""
    
    class Meta:
        model = Organization
        fields = '__all__'
        widgets = {
            'description': forms.Textarea(attrs={
                'rows': 4, 
                'cols': 80,
                'placeholder': 'Enter detailed description of the organization...'
            }),
            'motto': forms.Textarea(attrs={
                'rows': 2, 
                'cols': 80,
                'placeholder': 'Enter organization motto or mission statement...'
            }),
            'tagline': forms.TextInput(attrs={
                'size': 80,
                'placeholder': 'Enter brief tagline or slogan...'
            }),
            'website': forms.URLInput(attrs={
                'placeholder': 'https://example.com'
            }),
            'email': forms.EmailInput(attrs={
                'placeholder': '<EMAIL>'
            }),
            'phone': forms.TextInput(attrs={
                'placeholder': '+251911234567'
            }),
            'fax': forms.TextInput(attrs={
                'placeholder': '+251911234567'
            }),
            'address_line1': forms.TextInput(attrs={
                'placeholder': 'Street address, building number...'
            }),
            'address_line2': forms.TextInput(attrs={
                'placeholder': 'Apartment, suite, floor, etc. (optional)'
            }),
            'postal_code': forms.TextInput(attrs={
                'placeholder': '1000'
            }),
            'registration_number': forms.TextInput(attrs={
                'placeholder': 'Official registration number'
            }),
            'tax_id': forms.TextInput(attrs={
                'placeholder': 'Tax identification number'
            }),
            'license_number': forms.TextInput(attrs={
                'placeholder': 'Business license number'
            }),
            'primary_color': forms.TextInput(attrs={
                'type': 'color',
                'style': 'width: 60px; height: 40px; border: none; border-radius: 4px;'
            }),
            'secondary_color': forms.TextInput(attrs={
                'type': 'color',
                'style': 'width: 60px; height: 40px; border: none; border-radius: 4px;'
            }),
            'accent_color': forms.TextInput(attrs={
                'type': 'color',
                'style': 'width: 60px; height: 40px; border: none; border-radius: 4px;'
            }),
            'social_media': forms.Textarea(attrs={
                'rows': 6,
                'cols': 80,
                'placeholder': '{\n  "facebook": "https://facebook.com/yourorg",\n  "twitter": "https://twitter.com/yourorg",\n  "linkedin": "https://linkedin.com/company/yourorg",\n  "instagram": "https://instagram.com/yourorg",\n  "youtube": "https://youtube.com/c/yourorg"\n}',
                'style': 'font-family: monospace;'
            }),
            'office_hours_start': forms.TimeInput(attrs={
                'type': 'time',
                'style': 'font-size: 16px; text-align: center;'
            }),
            'office_hours_end': forms.TimeInput(attrs={
                'type': 'time',
                'style': 'font-size: 16px; text-align: center;'
            }),
            'established_date': forms.DateInput(attrs={
                'type': 'date'
            }),
            'document_retention_days': forms.NumberInput(attrs={
                'min': 1,
                'max': 36500,  # 100 years
                'style': 'text-align: right;'
            }),
            'max_file_size_mb': forms.NumberInput(attrs={
                'min': 1,
                'max': 1024,  # 1GB
                'style': 'text-align: right;'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Add help text and styling to specific fields
        self.fields['name'].help_text = "Official full name of the organization"
        self.fields['short_name'].help_text = "Abbreviated name (will be converted to uppercase)"
        self.fields['is_default'].help_text = "Only one organization can be set as default"
        self.fields['document_retention_days'].help_text = "Number of days to retain documents (default: 2555 = 7 years)"
        self.fields['max_file_size_mb'].help_text = "Maximum file upload size in megabytes"

        # Location hierarchy help text
        if 'country' in self.fields:
            self.fields['country'].help_text = "Select country from location hierarchy"
        if 'state_province' in self.fields:
            self.fields['state_province'].help_text = "Select region/state/province from location hierarchy"
        if 'city' in self.fields:
            self.fields['city'].help_text = "Select city from location hierarchy"
        if 'subcity' in self.fields:
            self.fields['subcity'].help_text = "Select subcity/woreda from location hierarchy"
        if 'kebele' in self.fields:
            self.fields['kebele'].help_text = "Select kebele from location hierarchy"
        
        # Make certain fields required in the admin
        self.fields['name'].required = True
        self.fields['short_name'].required = True

    def clean_short_name(self):
        """Convert short name to uppercase"""
        short_name = self.cleaned_data.get('short_name')
        if short_name:
            return short_name.upper()
        return short_name

    def clean_website(self):
        """Ensure website URL has proper protocol"""
        website = self.cleaned_data.get('website')
        if website and not website.startswith(('http://', 'https://')):
            return f'https://{website}'
        return website

    def clean(self):
        """Custom validation for the entire form"""
        cleaned_data = super().clean()
        
        # Validate office hours
        start_time = cleaned_data.get('office_hours_start')
        end_time = cleaned_data.get('office_hours_end')
        
        if start_time and end_time and start_time >= end_time:
            raise forms.ValidationError(
                "Office closing time must be after opening time."
            )
        
        # Validate retention days
        retention_days = cleaned_data.get('document_retention_days')
        if retention_days and retention_days < 1:
            raise forms.ValidationError(
                "Document retention days must be at least 1."
            )
        
        # Validate file size
        max_file_size = cleaned_data.get('max_file_size_mb')
        if max_file_size and max_file_size < 1:
            raise forms.ValidationError(
                "Maximum file size must be at least 1 MB."
            )
        
        return cleaned_data


class DepartmentAdminForm(forms.ModelForm):
    """Custom admin form for Department"""
    
    class Meta:
        model = Department
        fields = '__all__'
        widgets = {
            'description': forms.Textarea(attrs={
                'rows': 3,
                'cols': 80,
                'placeholder': 'Enter department description...'
            }),
            'code': forms.TextInput(attrs={
                'placeholder': 'DEPT',
                'style': 'text-transform: uppercase;'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Add help text
        self.fields['code'].help_text = "Short department code (e.g., HR, FIN, IT)"
        self.fields['head'].help_text = "Select the department head/manager"
        
        # Filter head choices to users from the same organization if editing
        if self.instance and self.instance.pk and self.instance.organization:
            try:
                from accounts.models import User
                self.fields['head'].queryset = User.objects.filter(
                    organization=self.instance.organization,
                    is_active=True
                )
            except ImportError:
                pass  # accounts app might not be available

    def clean_code(self):
        """Convert code to uppercase"""
        code = self.cleaned_data.get('code')
        if code:
            return code.upper()
        return code
