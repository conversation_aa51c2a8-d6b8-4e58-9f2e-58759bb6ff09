import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Chip,
} from '@mui/material';
import {
  Description,
  Edit,
  Delete,
  Home,
  Business,
  Info,
  Security,
  Settings,
  Schedule,
  FilePresent,
} from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import documentTypeService, { type DocumentType } from '../../services/documentTypeService';
import DetailPageTemplate from '../../components/common/DetailPageTemplate';
import ConfirmationDialog from '../../components/common/ConfirmationDialog';

const DocumentTypeDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showNotification } = useNotification();
  
  const [documentType, setDocumentType] = useState<DocumentType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    if (id) {
      loadDocumentType();
    }
  }, [id]);

  const loadDocumentType = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await documentTypeService.getDocumentType(Number(id));
      setDocumentType(data);
    } catch (error) {
      console.error('Error loading document type:', error);
      setError('Failed to load document type');
      showNotification('Failed to load document type', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    // Navigate back to document types page with edit state
    navigate('/document-center/document-types', { 
      state: { 
        editDocumentType: documentType,
        showForm: true 
      } 
    });
  };

  const handleDelete = () => {
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!documentType) return;
    
    try {
      setDeleting(true);
      await documentTypeService.deleteDocumentType(documentType.id);
      showNotification('Document type deleted successfully', 'success');
      navigate('/document-center/document-types');
    } catch (error) {
      console.error('Error deleting document type:', error);
      showNotification('Failed to delete document type', 'error');
    } finally {
      setDeleting(false);
      setDeleteDialogOpen(false);
    }
  };

  const handleBack = () => {
    navigate('/document-center/document-types');
  };

  if (!documentType && !loading && !error) {
    setError('Document type not found');
  }

  const breadcrumbs = [
    { label: 'Dashboard', path: '/dashboard', icon: <Home fontSize="small" /> },
    { label: 'Document Center', path: '/document-center', icon: <Description fontSize="small" /> },
    { label: 'Document Types', path: '/document-center/document-types', icon: <Description fontSize="small" /> },
    { label: documentType?.name || 'Document Type', path: undefined, icon: <FilePresent fontSize="small" /> },
  ];

  const actions = [
    {
      label: 'Edit',
      icon: <Edit />,
      onClick: handleEdit,
      color: 'primary' as const,
    },
    {
      label: 'Delete',
      icon: <Delete />,
      onClick: handleDelete,
      color: 'error' as const,
    },
  ];

  const chips = documentType ? [
    {
      label: documentType.is_active ? 'Active' : 'Inactive',
      color: documentType.is_active ? 'success' as const : 'error' as const,
    },
    {
      label: documentType.confidentiality_level.charAt(0).toUpperCase() + documentType.confidentiality_level.slice(1),
      color: 'info' as const,
    },
    {
      label: documentType.organization_name,
      color: 'primary' as const,
    },
  ] : [];

  const sections = documentType ? [
    {
      title: 'Basic Information',
      icon: <Info />,
      content: (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'primary.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'primary.200'
          }}>
            <Description sx={{ color: 'primary.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="primary.main" sx={{ fontWeight: 600 }}>
                Document Type Name
              </Typography>
              <Typography variant="body2" color="primary.dark">
                {documentType.name}
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'info.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'info.200'
          }}>
            <FilePresent sx={{ color: 'info.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="info.main" sx={{ fontWeight: 600 }}>
                Code
              </Typography>
              <Typography variant="body2" color="info.dark">
                {documentType.code}
              </Typography>
            </Box>
          </Box>
          
          {documentType.description && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" color="text.secondary" sx={{ fontWeight: 600, mb: 1 }}>
                Description
              </Typography>
              <Typography variant="body2" sx={{ 
                p: 2, 
                bgcolor: 'grey.50', 
                borderRadius: 1,
                border: '1px solid',
                borderColor: 'grey.200'
              }}>
                {documentType.description}
              </Typography>
            </Box>
          )}
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'secondary.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'secondary.200'
          }}>
            <Business sx={{ color: 'secondary.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="secondary.main" sx={{ fontWeight: 600 }}>
                Organization
              </Typography>
              <Typography variant="body2" color="secondary.dark">
                {documentType.organization_name}
              </Typography>
            </Box>
          </Box>
        </Box>
      ),
    },
    {
      title: 'Security & Classification',
      icon: <Security />,
      content: (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'warning.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'warning.200'
          }}>
            <Security sx={{ color: 'warning.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="warning.main" sx={{ fontWeight: 600 }}>
                Confidentiality Level
              </Typography>
              <Typography variant="body2" color="warning.dark">
                {documentType.confidentiality_level.charAt(0).toUpperCase() + documentType.confidentiality_level.slice(1)}
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'error.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'error.200'
          }}>
            <Settings sx={{ color: 'error.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="error.main" sx={{ fontWeight: 600 }}>
                Requires Approval
              </Typography>
              <Typography variant="body2" color="error.dark">
                {documentType.requires_approval ? 'Yes' : 'No'}
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'success.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'success.200'
          }}>
            <Schedule sx={{ color: 'success.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="success.main" sx={{ fontWeight: 600 }}>
                Requires Expiry Date
              </Typography>
              <Typography variant="body2" color="success.dark">
                {documentType.requires_expiry_date ? 'Yes' : 'No'}
              </Typography>
            </Box>
          </Box>
        </Box>
      ),
    },
    {
      title: 'File Settings & Statistics',
      icon: <Settings />,
      content: (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'primary.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'primary.200'
          }}>
            <FilePresent sx={{ color: 'primary.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="primary.main" sx={{ fontWeight: 600 }}>
                Allowed File Extensions
              </Typography>
              <Typography variant="body2" color="primary.dark">
                {Array.isArray(documentType.allowed_file_extensions) 
                  ? documentType.allowed_file_extensions.join(', ') 
                  : documentType.allowed_file_extensions || 'All types allowed'}
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'info.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'info.200'
          }}>
            <Settings sx={{ color: 'info.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="info.main" sx={{ fontWeight: 600 }}>
                Max File Size
              </Typography>
              <Typography variant="body2" color="info.dark">
                {documentType.max_file_size_mb ? `${documentType.max_file_size_mb} MB` : 'No limit'}
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'success.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'success.200'
          }}>
            <Schedule sx={{ color: 'success.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="success.main" sx={{ fontWeight: 600 }}>
                Retention Period
              </Typography>
              <Typography variant="body2" color="success.dark">
                {documentType.retention_days_display} days
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'warning.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'warning.200'
          }}>
            <Description sx={{ color: 'warning.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="warning.main" sx={{ fontWeight: 600 }}>
                Documents
              </Typography>
              <Typography variant="body2" color="warning.dark">
                {documentType.document_count} documents
              </Typography>
            </Box>
          </Box>
        </Box>
      ),
    },
  ] : [];

  return (
    <>
      <DetailPageTemplate
        loading={loading}
        error={error}
        breadcrumbs={breadcrumbs}
        title={documentType?.name || 'Document Type'}
        subtitle={documentType?.code ? `Code: ${documentType.code}` : undefined}
        avatar={{
          fallbackIcon: <Description sx={{ fontSize: 40 }} />,
          alt: documentType?.name || 'Document Type',
        }}
        chips={chips}
        actions={actions}
        sections={sections}
        onBack={handleBack}
      />

      <ConfirmationDialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleConfirmDelete}
        title="Delete Document Type"
        itemName={documentType?.name}
        itemType="Document Type"
        message={`Are you sure you want to delete "${documentType?.name}"? This will affect ${documentType?.document_count || 0} documents.`}
        confirmText="Delete Document Type"
        severity="error"
        loading={deleting}
      />
    </>
  );
};

export default DocumentTypeDetailPage;
