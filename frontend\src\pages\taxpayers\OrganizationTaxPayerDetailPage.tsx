import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Avatar,
  Chip,
  Divider,
  Button,
  IconButton,
  Alert,
  CircularProgress,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  ArrowBack,
  Edit,
  Delete,
  Business,
  Person,
  Phone,
  Email,
  LocationOn,
  CalendarToday,
  Badge,
  Category,
  AccountBalance,
  Description,
  Receipt,
  Groups,
  AttachMoney,
  Folder,
  Close as CloseIcon,
} from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import taxpayerService from '../../services/taxpayerService';
import TaxpayerPaymentHistory from '../../components/taxpayers/TaxpayerPaymentHistory';
import BusinessClosureDialog from '../../components/taxpayers/BusinessClosureDialog';

interface OrganizationTaxPayer {
  id: string;
  tin: string;
  business_name: string;
  trade_name: string;
  display_name: string;
  organization_business_type: string;
  organization_business_type_name: string;
  tax_payer_level: string;
  tax_payer_level_name: string;
  business_sector: string;
  business_sector_name: string;
  business_sub_sector: string;
  business_sub_sector_name: string;
  business_registration_date: string;
  business_license_number: string;
  capital_amount: number;
  number_of_employees: number;
  manager_first_name: string;
  manager_middle_name: string;
  manager_last_name: string;
  manager_title: string;
  manager_full_name: string;
  vat_registration_date: string;
  vat_number: string;
  phone: string;
  phone_secondary: string;
  email: string;
  address_display: string;
  country_name: string;
  region_name: string;
  zone_name: string;
  city_name: string;
  subcity_name: string;
  kebele_name: string;
  house_number: string;
  street_address: string;
  postal_code: string;
  is_active: boolean;
  registration_date: string;
  last_updated: string;
  is_business_closed: boolean;
  business_closure_date?: string;
  business_closure_reason?: string;
  tax_file?: number;
  tax_file_details?: {
    id: string;
    name: string;
    file_number: string;
    full_code: string;
    location_path: string;
    kent_name: string;
    kent_code: string;
    building_name: string;
    shelf_name: string;
  };
}

const OrganizationTaxPayerDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showNotification } = useNotification();
  
  const [taxpayer, setTaxpayer] = useState<OrganizationTaxPayer | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [closureDialogOpen, setClosureDialogOpen] = useState(false);

  useEffect(() => {
    if (id) {
      loadTaxpayer();
    }
  }, [id]);

  const loadTaxpayer = async () => {
    try {
      setLoading(true);
      const data = await taxpayerService.getOrganizationTaxPayer(id!);
      setTaxpayer(data);
    } catch (error: any) {
      console.error('Failed to load taxpayer:', error);
      setError('Failed to load taxpayer details');
      showNotification('Failed to load taxpayer details', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    navigate(`/taxpayers/organizations/${id}/edit`);
  };

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this taxpayer?')) {
      try {
        await taxpayerService.deleteOrganizationTaxPayer(id!);
        showNotification('Taxpayer deleted successfully', 'success');
        navigate('/taxpayers');
      } catch (error: any) {
        console.error('Failed to delete taxpayer:', error);
        showNotification('Failed to delete taxpayer', 'error');
      }
    }
  };



  const handleCloseBusiness = () => {
    setClosureDialogOpen(true);
  };

  const handleClosureSuccess = () => {
    loadTaxpayer(); // Reload taxpayer data to reflect closure status
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount: number) => {
    if (!amount) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'ETB',
      minimumFractionDigits: 2
    }).format(amount);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error || !taxpayer) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error || 'Taxpayer not found'}</Alert>
        <Button 
          startIcon={<ArrowBack />} 
          onClick={() => navigate('/taxpayers')}
          sx={{ mt: 2 }}
        >
          Back to Taxpayers
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <IconButton onClick={() => navigate('/taxpayers')} sx={{ mr: 2 }}>
          <ArrowBack />
        </IconButton>
        <Typography variant="h4" sx={{ flexGrow: 1 }}>
          Organization Taxpayer Details
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<Edit />}
            onClick={handleEdit}
          >
            Edit
          </Button>
          <Button
            variant="outlined"
            color={taxpayer?.is_business_closed ? "success" : "warning"}
            startIcon={taxpayer?.is_business_closed ? <Business /> : <CloseIcon />}
            onClick={handleCloseBusiness}
          >
            {taxpayer?.is_business_closed ? 'Reopen Business' : 'Close Business'}
          </Button>
          <Button
            variant="outlined"
            color="error"
            startIcon={<Delete />}
            onClick={handleDelete}
          >
            Delete
          </Button>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Organization Profile Card */}
        <Grid size={{ xs: 12, md: 4 }}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar
                sx={{ 
                  width: 120, 
                  height: 120, 
                  mx: 'auto', 
                  mb: 2,
                  fontSize: '3rem',
                  bgcolor: 'primary.main'
                }}
              >
                <Business sx={{ fontSize: '4rem' }} />
              </Avatar>
              <Typography variant="h5" gutterBottom>
                {taxpayer.display_name}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                TIN: {taxpayer.tin}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {taxpayer.organization_business_type_name}
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1, mt: 1 }}>
                <Chip
                  label={taxpayer.is_active ? 'Active' : 'Inactive'}
                  color={taxpayer.is_active ? 'success' : 'error'}
                />
                {taxpayer.is_business_closed && (
                  <Chip
                    label="Business Closed"
                    color="error"
                    variant="outlined"
                    size="small"
                  />
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Organization Information */}
        <Grid size={{ xs: 12, md: 8 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Business color="primary" />
                Organization Information
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <Grid container spacing={2}>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <Typography variant="subtitle2" color="text.secondary">Business Name</Typography>
                  <Typography variant="body1">{taxpayer.business_name}</Typography>
                </Grid>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <Typography variant="subtitle2" color="text.secondary">Trade Name</Typography>
                  <Typography variant="body1">{taxpayer.trade_name || 'N/A'}</Typography>
                </Grid>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <Typography variant="subtitle2" color="text.secondary">Business Type</Typography>
                  <Chip label={taxpayer.organization_business_type_name} color="primary" variant="outlined" />
                </Grid>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <Typography variant="subtitle2" color="text.secondary">Registration Date</Typography>
                  <Typography variant="body1">{formatDate(taxpayer.business_registration_date)}</Typography>
                </Grid>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <Typography variant="subtitle2" color="text.secondary">License Number</Typography>
                  <Typography variant="body1">{taxpayer.business_license_number || 'N/A'}</Typography>
                </Grid>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <Typography variant="subtitle2" color="text.secondary">Tax Payer Level</Typography>
                  <Chip label={taxpayer.tax_payer_level_name} color="secondary" variant="outlined" />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Business Details */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Category color="primary" />
                Business Classification
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <Grid container spacing={2}>
                <Grid size={{ xs: 12 }}>
                  <Typography variant="subtitle2" color="text.secondary">Business Sector</Typography>
                  <Chip label={taxpayer.business_sector_name} color="info" variant="outlined" />
                </Grid>
                <Grid size={{ xs: 12 }}>
                  <Typography variant="subtitle2" color="text.secondary">Business Sub-Sector</Typography>
                  <Chip label={taxpayer.business_sub_sector_name} color="warning" variant="outlined" />
                </Grid>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <Typography variant="subtitle2" color="text.secondary">Capital Amount</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <AttachMoney color="success" />
                    <Typography variant="body1">{formatCurrency(taxpayer.capital_amount)}</Typography>
                  </Box>
                </Grid>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <Typography variant="subtitle2" color="text.secondary">Number of Employees</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Groups color="action" />
                    <Typography variant="body1">{taxpayer.number_of_employees || 'N/A'}</Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Manager Information */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Person color="primary" />
                Manager Information
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <Grid container spacing={2}>
                <Grid size={{ xs: 12 }}>
                  <Typography variant="subtitle2" color="text.secondary">Full Name</Typography>
                  <Typography variant="body1">{taxpayer.manager_full_name}</Typography>
                </Grid>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <Typography variant="subtitle2" color="text.secondary">First Name</Typography>
                  <Typography variant="body1">{taxpayer.manager_first_name}</Typography>
                </Grid>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <Typography variant="subtitle2" color="text.secondary">Middle Name</Typography>
                  <Typography variant="body1">{taxpayer.manager_middle_name || 'N/A'}</Typography>
                </Grid>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <Typography variant="subtitle2" color="text.secondary">Last Name</Typography>
                  <Typography variant="body1">{taxpayer.manager_last_name}</Typography>
                </Grid>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <Typography variant="subtitle2" color="text.secondary">Title/Position</Typography>
                  <Typography variant="body1">{taxpayer.manager_title}</Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* VAT Information */}
        {(taxpayer.vat_number || taxpayer.vat_registration_date) && (
          <Grid size={{ xs: 12, md: 6 }}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Receipt color="primary" />
                  VAT Information
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Grid container spacing={2}>
                  <Grid size={{ xs: 12 }}>
                    <Typography variant="subtitle2" color="text.secondary">VAT Number</Typography>
                    <Typography variant="body1">{taxpayer.vat_number || 'N/A'}</Typography>
                  </Grid>
                  <Grid size={{ xs: 12 }}>
                    <Typography variant="subtitle2" color="text.secondary">VAT Registration Date</Typography>
                    <Typography variant="body1">{formatDate(taxpayer.vat_registration_date)}</Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        )}

        {/* Contact Information */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Phone color="primary" />
                Contact Information
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <Phone color="action" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Primary Phone"
                    secondary={taxpayer.phone}
                  />
                </ListItem>
                {taxpayer.phone_secondary && (
                  <ListItem>
                    <ListItemIcon>
                      <Phone color="action" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Secondary Phone"
                      secondary={taxpayer.phone_secondary}
                    />
                  </ListItem>
                )}
                {taxpayer.email && (
                  <ListItem>
                    <ListItemIcon>
                      <Email color="action" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Email"
                      secondary={taxpayer.email}
                    />
                  </ListItem>
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Address Information */}
        <Grid size={{ xs: 12 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <LocationOn color="primary" />
                Address Information
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <Grid container spacing={2}>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Typography variant="subtitle2" color="text.secondary">Country</Typography>
                  <Typography variant="body1">{taxpayer.country_name || 'N/A'}</Typography>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Typography variant="subtitle2" color="text.secondary">Region</Typography>
                  <Typography variant="body1">{taxpayer.region_name || 'N/A'}</Typography>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Typography variant="subtitle2" color="text.secondary">Zone</Typography>
                  <Typography variant="body1">{taxpayer.zone_name || 'N/A'}</Typography>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Typography variant="subtitle2" color="text.secondary">City</Typography>
                  <Typography variant="body1">{taxpayer.city_name || 'N/A'}</Typography>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Typography variant="subtitle2" color="text.secondary">SubCity/Woreda</Typography>
                  <Typography variant="body1">{taxpayer.subcity_name || 'N/A'}</Typography>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Typography variant="subtitle2" color="text.secondary">Kebele</Typography>
                  <Typography variant="body1">{taxpayer.kebele_name || 'N/A'}</Typography>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Typography variant="subtitle2" color="text.secondary">House Number</Typography>
                  <Typography variant="body1">{taxpayer.house_number || 'N/A'}</Typography>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Typography variant="subtitle2" color="text.secondary">Postal Code</Typography>
                  <Typography variant="body1">{taxpayer.postal_code || 'N/A'}</Typography>
                </Grid>
                {taxpayer.street_address && (
                  <Grid size={{ xs: 12 }}>
                    <Typography variant="subtitle2" color="text.secondary">Street Address</Typography>
                    <Typography variant="body1">{taxpayer.street_address}</Typography>
                  </Grid>
                )}
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* System Information */}
        <Grid size={{ xs: 12 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Description color="primary" />
                System Information
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <Grid container spacing={2}>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <Typography variant="subtitle2" color="text.secondary">Registration Date</Typography>
                  <Typography variant="body1">{formatDate(taxpayer.registration_date)}</Typography>
                </Grid>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <Typography variant="subtitle2" color="text.secondary">Last Updated</Typography>
                  <Typography variant="body1">{formatDate(taxpayer.last_updated)}</Typography>
                </Grid>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <Typography variant="subtitle2" color="text.secondary">Status</Typography>
                  <Chip
                    label={taxpayer.is_active ? 'Active' : 'Inactive'}
                    color={taxpayer.is_active ? 'success' : 'error'}
                    size="small"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Business Closure Information */}
        {taxpayer.is_business_closed && (
          <Grid size={{ xs: 12 }}>
            <Card>
              <CardContent>
                <Alert severity="error" sx={{ mb: 2 }}>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <CloseIcon color="error" />
                    Business Closed
                  </Typography>
                  <Divider sx={{ mb: 2 }} />

                  <Grid container spacing={2}>
                    <Grid size={{ xs: 12, sm: 6 }}>
                      <Typography variant="subtitle2" color="text.secondary">Closure Date</Typography>
                      <Typography variant="body1">
                        {taxpayer.business_closure_date ? formatDate(taxpayer.business_closure_date) : 'N/A'}
                      </Typography>
                    </Grid>
                    <Grid size={{ xs: 12 }}>
                      <Typography variant="subtitle2" color="text.secondary">Closure Reason</Typography>
                      <Typography variant="body1">
                        {taxpayer.business_closure_reason || 'No reason provided'}
                      </Typography>
                    </Grid>
                  </Grid>
                </Alert>
              </CardContent>
            </Card>
          </Grid>
        )}


        {/* Payment History Section */}
        <Grid size={{ xs: 12 }}>
          <TaxpayerPaymentHistory
            taxpayerId={taxpayer.id}
            taxpayerType="organization"
            taxpayerName={taxpayer.business_name}
            taxpayerTin={taxpayer.tin}
          />
        </Grid>
      </Grid>

      {/* Business Closure Dialog */}
      {taxpayer && (
        <BusinessClosureDialog
          open={closureDialogOpen}
          onClose={() => setClosureDialogOpen(false)}
          taxpayerId={taxpayer.id}
          taxpayerType="organization"
          taxpayerName={taxpayer.display_name}
          onSuccess={handleClosureSuccess}
        />
      )}
    </Box>
  );
};

export default OrganizationTaxPayerDetailPage;
