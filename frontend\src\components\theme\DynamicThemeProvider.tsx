import React, { ReactNode, useMemo } from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { useBranding } from '../../contexts/BrandingContext';

interface DynamicThemeProviderProps {
  children: ReactNode;
}

export const DynamicThemeProvider: React.FC<DynamicThemeProviderProps> = ({ children }) => {
  const { getThemeColors } = useBranding();
  const colors = getThemeColors(); // Get colors outside useMemo to use as dependency



  const theme = useMemo(() => {

    // Helper function to lighten a color
    const lightenColor = (color: string) => {
      // Simple lightening by adding alpha or adjusting brightness
      if (color.startsWith('#')) {
        // Convert hex to RGB and lighten
        const r = parseInt(color.slice(1, 3), 16);
        const g = parseInt(color.slice(3, 5), 16);
        const b = parseInt(color.slice(5, 7), 16);
        return `rgb(${Math.min(255, r + 40)}, ${Math.min(255, g + 40)}, ${Math.min(255, b + 40)})`;
      }
      return color;
    };

    // Helper function to darken a color
    const darkenColor = (color: string) => {
      if (color.startsWith('#')) {
        const r = parseInt(color.slice(1, 3), 16);
        const g = parseInt(color.slice(3, 5), 16);
        const b = parseInt(color.slice(5, 7), 16);
        return `rgb(${Math.max(0, r - 40)}, ${Math.max(0, g - 40)}, ${Math.max(0, b - 40)})`;
      }
      return color;
    };

    return createTheme({
      palette: {
        primary: {
          main: colors.primary,
          light: lightenColor(colors.primary),
          dark: darkenColor(colors.primary),
        },
        secondary: {
          main: colors.secondary,
          light: lightenColor(colors.secondary),
          dark: darkenColor(colors.secondary),
        },
        info: {
          main: colors.accent,
          light: lightenColor(colors.accent),
          dark: darkenColor(colors.accent),
        },
        background: {
          default: '#f5f5f5',
          paper: '#ffffff',
        },
      },
      typography: {
        fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
        h4: {
          fontWeight: 600,
        },
        h5: {
          fontWeight: 600,
        },
        h6: {
          fontWeight: 600,
        },
      },
      components: {
        MuiAppBar: {
          styleOverrides: {
            root: {
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              backgroundColor: colors.primary,
            },
          },
        },
        MuiButton: {
          styleOverrides: {
            root: {
              textTransform: 'none',
              borderRadius: 8,
            },
          },
        },
        MuiCard: {
          styleOverrides: {
            root: {
              borderRadius: 12,
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            },
          },
        },
        MuiPaper: {
          styleOverrides: {
            root: {
              borderRadius: 8,
            },
          },
        },
        MuiTextField: {
          styleOverrides: {
            root: {
              '& .MuiOutlinedInput-root': {
                borderRadius: 8,
              },
            },
          },
        },
      },
    });
  }, [colors.primary, colors.secondary, colors.accent]);

  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};
