import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  Typography,
  IconButton,
  Button,
  Chip,
  Grid,
  Divider,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  Skeleton,
  Avatar,

  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemButton,
} from '@mui/material';
import {
  ArrowBack,
  MoreVert,
  Edit,
  Cancel,
  CheckCircle,
  Block,
  Assignment,
  AssignmentTurnedIn,
  AssignmentReturn,
  Schedule,
  Warning,
  Person,
  CalendarToday,
  Description,
  PriorityHigh,
  AccessTime,
} from '@mui/icons-material';
import {
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
} from '@mui/lab';
import { useNotification } from '../../contexts/NotificationContext';
import { useAuth } from '../../contexts/AuthContext';
import requestService from '../../services/requestService';
import type { DocumentRequest, RequestApproval } from '../../services/requestService';

const RequestDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showSuccess, showError } = useNotification();
  const { user } = useAuth();

  const [request, setRequest] = useState<DocumentRequest | null>(null);
  const [approvals, setApprovals] = useState<RequestApproval[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [actionDialogOpen, setActionDialogOpen] = useState(false);
  const [actionType, setActionType] = useState<'approve' | 'reject' | 'checkout' | 'return' | 'cancel' | null>(null);
  const [actionComments, setActionComments] = useState('');
  const [processing, setProcessing] = useState(false);

  useEffect(() => {
    loadRequest();
  }, [id]);

  const loadRequest = async () => {
    if (!id) return;

    try {
      setLoading(true);
      setError(null);
      const [requestData, approvalsData] = await Promise.all([
        requestService.getRequest(id),
        requestService.getRequestApprovals(id).catch(() => []), // Approvals might not exist
      ]);
      setRequest(requestData);
      setApprovals(approvalsData);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to load request');
    } finally {
      setLoading(false);
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleEdit = () => {
    navigate(`/requests/${id}/edit`);
    handleMenuClose();
  };

  const handleActionClick = (action: 'approve' | 'reject' | 'checkout' | 'return' | 'cancel') => {
    setActionType(action);
    setActionComments('');
    setActionDialogOpen(true);
    handleMenuClose();
  };

  const handleActionSubmit = async () => {
    if (!request || !actionType) return;

    try {
      setProcessing(true);
      let updatedRequest: DocumentRequest;

      switch (actionType) {
        case 'approve':
          updatedRequest = await requestService.approveRequest(request.id, actionComments);
          showSuccess('Request approved successfully');
          break;
        case 'reject':
          if (!actionComments.trim()) {
            showError('Comments are required when rejecting a request');
            return;
          }
          updatedRequest = await requestService.rejectRequest(request.id, actionComments);
          showSuccess('Request rejected');
          break;
        case 'checkout':
          updatedRequest = await requestService.checkoutRequest(request.id, actionComments);
          showSuccess('Documents checked out successfully');
          break;
        case 'return':
          updatedRequest = await requestService.returnRequest(request.id, actionComments);
          showSuccess('Documents returned successfully');
          break;
        case 'cancel':
          updatedRequest = await requestService.cancelRequest(request.id, actionComments);
          showSuccess('Request cancelled');
          break;
        default:
          return;
      }

      setRequest(updatedRequest);
      setActionDialogOpen(false);
      loadRequest(); // Reload to get updated approvals
    } catch (err: any) {
      showError(err.response?.data?.detail || `Failed to ${actionType} request`);
    } finally {
      setProcessing(false);
    }
  };

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Skeleton variant="rectangular" width="100%" height={200} />
        <Box sx={{ mt: 2 }}>
          <Skeleton variant="text" width="60%" height={40} />
          <Skeleton variant="text" width="40%" height={30} />
        </Box>
      </Box>
    );
  }

  if (error || !request) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error || 'Request not found'}</Alert>
      </Box>
    );
  }

  const nextActions = user ? requestService.getNextActions(request, user.id) : [];
  const daysUntilDue = requestService.getDaysUntilDue(request.due_date);
  const isDueSoon = requestService.isDueSoon(request.due_date);

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <IconButton onClick={() => navigate('/requests')}>
            <ArrowBack />
          </IconButton>
          <Avatar sx={{ bgcolor: 'primary.main', width: 48, height: 48 }}>
            <Assignment />
          </Avatar>
          <Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="h4" component="h1">
                {request.request_number}
              </Typography>
              <Chip
                label={requestService.getStatusDisplay(request.status)}
                color={requestService.getStatusColor(request.status)}
                size="small"
              />
              <Chip
                label={requestService.getPriorityDisplay(request.priority)}
                color={requestService.getPriorityColor(request.priority)}
                size="small"
                icon={<PriorityHigh />}
              />
              {request.is_overdue && (
                <Chip
                  icon={<Warning />}
                  label={`Overdue by ${Math.abs(daysUntilDue)} days`}
                  color="error"
                  size="small"
                />
              )}
              {isDueSoon && !request.is_overdue && (
                <Chip
                  icon={<AccessTime />}
                  label={`Due in ${daysUntilDue} days`}
                  color="warning"
                  size="small"
                />
              )}
            </Box>
            <Typography variant="subtitle1" color="text.secondary">
              Requested by {request.requested_by_name}
            </Typography>
          </Box>
        </Box>

        <Box sx={{ display: 'flex', gap: 1 }}>
          {nextActions.includes('edit') && (
            <Button
              variant="outlined"
              startIcon={<Edit />}
              onClick={handleEdit}
            >
              Edit
            </Button>
          )}
          {nextActions.length > 0 && (
            <IconButton onClick={handleMenuOpen}>
              <MoreVert />
            </IconButton>
          )}
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Main Information */}
        <Grid item xs={12} md={8}>
          <Card sx={{ mb: 2 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Assignment color="primary" />
                Request Information
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="caption" color="text.secondary">
                      Purpose
                    </Typography>
                    <Typography variant="body1">
                      {request.purpose}
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <Person color="action" />
                    <Box>
                      <Typography variant="caption" color="text.secondary">
                        Requested By
                      </Typography>
                      <Typography variant="body2">
                        {request.requested_by_name}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <CalendarToday color="action" />
                    <Box>
                      <Typography variant="caption" color="text.secondary">
                        Required Date
                      </Typography>
                      <Typography variant="body2">
                        {requestService.formatDate(request.required_date)}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <Schedule color="action" />
                    <Box>
                      <Typography variant="caption" color="text.secondary">
                        Due Date
                      </Typography>
                      <Typography variant="body2" color={request.is_overdue ? 'error' : 'inherit'}>
                        {requestService.formatDate(request.due_date)}
                        {request.is_overdue && ' (Overdue)'}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                {request.approved_by_name && (
                  <Grid item xs={12} sm={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                      <CheckCircle color="action" />
                      <Box>
                        <Typography variant="caption" color="text.secondary">
                          Approved By
                        </Typography>
                        <Typography variant="body2">
                          {request.approved_by_name}
                        </Typography>
                        {request.approved_date && (
                          <Typography variant="caption" color="text.secondary">
                            on {requestService.formatDate(request.approved_date)}
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  </Grid>
                )}

                {request.checked_out_by_name && (
                  <Grid item xs={12} sm={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                      <AssignmentTurnedIn color="action" />
                      <Box>
                        <Typography variant="caption" color="text.secondary">
                          Checked Out By
                        </Typography>
                        <Typography variant="body2">
                          {request.checked_out_by_name}
                        </Typography>
                        {request.checked_out_date && (
                          <Typography variant="caption" color="text.secondary">
                            on {requestService.formatDate(request.checked_out_date)}
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  </Grid>
                )}

                {request.rejection_reason && (
                  <Grid item xs={12}>
                    <Alert severity="error">
                      <Typography variant="subtitle2">Rejection Reason:</Typography>
                      <Typography variant="body2">{request.rejection_reason}</Typography>
                    </Alert>
                  </Grid>
                )}

                {request.notes && (
                  <Grid item xs={12}>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="caption" color="text.secondary">
                        Notes
                      </Typography>
                      <Typography variant="body2">
                        {request.notes}
                      </Typography>
                    </Box>
                  </Grid>
                )}
              </Grid>
            </CardContent>
          </Card>

          {/* Requested Documents */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Description color="primary" />
                Requested Documents ({request.documents_detail.length})
              </Typography>

              <List>
                {request.documents_detail.map((document, index) => (
                  <ListItem key={document.id} divider={index < request.documents_detail.length - 1}>
                    <ListItemButton onClick={() => navigate(`/documents/${document.id}`)}>
                      <ListItemAvatar>
                        <Avatar sx={{ bgcolor: 'primary.main' }}>
                          <Description />
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={document.title}
                        secondary={
                          <Box>
                            <Typography variant="caption" color="text.secondary">
                              Type: {document.document_type_name} • Mode: {document.mode} • Status: {document.status}
                            </Typography>
                            <br />
                            <Typography variant="caption" color="text.secondary">
                              Location: {document.location_display}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItemButton>
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Sidebar */}
        <Grid item xs={12} md={4}>
          <Card sx={{ mb: 2 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <AccessTime color="primary" />
                Timeline & Status
              </Typography>

              <Timeline>
                <TimelineItem>
                  <TimelineSeparator>
                    <TimelineDot color="primary">
                      <Assignment />
                    </TimelineDot>
                    <TimelineConnector />
                  </TimelineSeparator>
                  <TimelineContent>
                    <Typography variant="subtitle2">Request Created</Typography>
                    <Typography variant="caption" color="text.secondary">
                      {requestService.formatDateTime(request.requested_date)}
                    </Typography>
                  </TimelineContent>
                </TimelineItem>

                {request.approved_date && (
                  <TimelineItem>
                    <TimelineSeparator>
                      <TimelineDot color="success">
                        <CheckCircle />
                      </TimelineDot>
                      {request.checked_out_date && <TimelineConnector />}
                    </TimelineSeparator>
                    <TimelineContent>
                      <Typography variant="subtitle2">Approved</Typography>
                      <Typography variant="caption" color="text.secondary">
                        {requestService.formatDateTime(request.approved_date)}
                      </Typography>
                      <Typography variant="caption" display="block" color="text.secondary">
                        by {request.approved_by_name}
                      </Typography>
                    </TimelineContent>
                  </TimelineItem>
                )}

                {request.checked_out_date && (
                  <TimelineItem>
                    <TimelineSeparator>
                      <TimelineDot color="info">
                        <AssignmentTurnedIn />
                      </TimelineDot>
                      {request.returned_date && <TimelineConnector />}
                    </TimelineSeparator>
                    <TimelineContent>
                      <Typography variant="subtitle2">Checked Out</Typography>
                      <Typography variant="caption" color="text.secondary">
                        {requestService.formatDateTime(request.checked_out_date)}
                      </Typography>
                      <Typography variant="caption" display="block" color="text.secondary">
                        by {request.checked_out_by_name}
                      </Typography>
                    </TimelineContent>
                  </TimelineItem>
                )}

                {request.returned_date && (
                  <TimelineItem>
                    <TimelineSeparator>
                      <TimelineDot color="success">
                        <AssignmentReturn />
                      </TimelineDot>
                    </TimelineSeparator>
                    <TimelineContent>
                      <Typography variant="subtitle2">Returned</Typography>
                      <Typography variant="caption" color="text.secondary">
                        {requestService.formatDateTime(request.returned_date)}
                      </Typography>
                      {request.returned_to_name && (
                        <Typography variant="caption" display="block" color="text.secondary">
                          to {request.returned_to_name}
                        </Typography>
                      )}
                    </TimelineContent>
                  </TimelineItem>
                )}

                {request.status === 'rejected' && (
                  <TimelineItem>
                    <TimelineSeparator>
                      <TimelineDot color="error">
                        <Block />
                      </TimelineDot>
                    </TimelineSeparator>
                    <TimelineContent>
                      <Typography variant="subtitle2">Rejected</Typography>
                      <Typography variant="caption" color="text.secondary">
                        {request.approved_date && requestService.formatDateTime(request.approved_date)}
                      </Typography>
                    </TimelineContent>
                  </TimelineItem>
                )}
              </Timeline>
            </CardContent>
          </Card>

          {/* Request Metadata */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Request Details
              </Typography>

              <Box sx={{ mb: 2 }}>
                <Typography variant="caption" color="text.secondary">
                  Request Number
                </Typography>
                <Typography variant="body2" fontFamily="monospace">
                  {request.request_number}
                </Typography>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="caption" color="text.secondary">
                  Priority
                </Typography>
                <Box sx={{ mt: 0.5 }}>
                  <Chip
                    label={requestService.getPriorityDisplay(request.priority)}
                    color={requestService.getPriorityColor(request.priority)}
                    size="small"
                  />
                </Box>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="caption" color="text.secondary">
                  Status
                </Typography>
                <Box sx={{ mt: 0.5 }}>
                  <Chip
                    label={requestService.getStatusDisplay(request.status)}
                    color={requestService.getStatusColor(request.status)}
                    size="small"
                  />
                </Box>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Box sx={{ mb: 2 }}>
                <Typography variant="caption" color="text.secondary">
                  Created
                </Typography>
                <Typography variant="body2">
                  {requestService.formatDateTime(request.created_at)}
                </Typography>
              </Box>

              <Box>
                <Typography variant="caption" color="text.secondary">
                  Last Updated
                </Typography>
                <Typography variant="body2">
                  {requestService.formatDateTime(request.updated_at)}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        {nextActions.includes('approve') && (
          <MenuItem onClick={() => handleActionClick('approve')}>
            <CheckCircle fontSize="small" sx={{ mr: 1 }} color="success" />
            Approve Request
          </MenuItem>
        )}
        {nextActions.includes('reject') && (
          <MenuItem onClick={() => handleActionClick('reject')}>
            <Block fontSize="small" sx={{ mr: 1 }} color="error" />
            Reject Request
          </MenuItem>
        )}
        {nextActions.includes('checkout') && (
          <MenuItem onClick={() => handleActionClick('checkout')}>
            <AssignmentTurnedIn fontSize="small" sx={{ mr: 1 }} color="info" />
            Checkout Documents
          </MenuItem>
        )}
        {nextActions.includes('return') && (
          <MenuItem onClick={() => handleActionClick('return')}>
            <AssignmentReturn fontSize="small" sx={{ mr: 1 }} color="success" />
            Return Documents
          </MenuItem>
        )}
        {nextActions.includes('edit') && (
          <MenuItem onClick={handleEdit}>
            <Edit fontSize="small" sx={{ mr: 1 }} />
            Edit Request
          </MenuItem>
        )}
        {nextActions.includes('cancel') && (
          <MenuItem onClick={() => handleActionClick('cancel')} sx={{ color: 'error.main' }}>
            <Cancel fontSize="small" sx={{ mr: 1 }} />
            Cancel Request
          </MenuItem>
        )}
      </Menu>

      {/* Action Dialog */}
      <Dialog open={actionDialogOpen} onClose={() => setActionDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {actionType === 'approve' && 'Approve Request'}
          {actionType === 'reject' && 'Reject Request'}
          {actionType === 'checkout' && 'Checkout Documents'}
          {actionType === 'return' && 'Return Documents'}
          {actionType === 'cancel' && 'Cancel Request'}
        </DialogTitle>
        <DialogContent>
          <Typography sx={{ mb: 2 }}>
            {actionType === 'approve' && 'Are you sure you want to approve this request?'}
            {actionType === 'reject' && 'Please provide a reason for rejecting this request:'}
            {actionType === 'checkout' && 'Confirm checkout of documents for this request:'}
            {actionType === 'return' && 'Confirm return of documents and provide condition notes:'}
            {actionType === 'cancel' && 'Are you sure you want to cancel this request?'}
          </Typography>

          <TextField
            fullWidth
            multiline
            rows={3}
            label={
              actionType === 'reject' ? 'Rejection Reason (Required)' :
              actionType === 'return' ? 'Condition Notes' :
              'Comments (Optional)'
            }
            value={actionComments}
            onChange={(e) => setActionComments(e.target.value)}
            required={actionType === 'reject'}
            placeholder={
              actionType === 'reject' ? 'Please explain why this request is being rejected...' :
              actionType === 'return' ? 'Document condition, any damages, etc...' :
              'Add any additional comments...'
            }
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setActionDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleActionSubmit}
            variant="contained"
            disabled={processing || (actionType === 'reject' && !actionComments.trim())}
            color={actionType === 'reject' || actionType === 'cancel' ? 'error' : 'primary'}
          >
            {processing ? 'Processing...' :
             actionType === 'approve' ? 'Approve' :
             actionType === 'reject' ? 'Reject' :
             actionType === 'checkout' ? 'Checkout' :
             actionType === 'return' ? 'Return' :
             'Cancel Request'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default RequestDetailPage;
