import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  TextField,
  InputAdornment,
  Card,
  CardContent,
  CardActions,
  Grid,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Fade,
} from '@mui/material';
import {
  Search,
  Add,
  Edit,
  Delete,
  Category,
  Refresh,
  Save,
  Cancel,
} from '@mui/icons-material';
import { useNotification } from '../../../contexts/NotificationContext';
import taxpayerService from '../../../services/taxpayerService';
import { normalizeCode, createCodeBlurHandler } from '../../../utils/codeUtils';

interface BusinessSector {
  id: string;
  code: string;
  name: string;
  description: string;
  is_active: boolean;
  sub_sectors_count: number;
  created_at: string;
  updated_at: string;
}

interface BusinessSubSector {
  id: string;
  business_sector: string;
  business_sector_name: string;
  business_sector_code: string;
  code: string;
  name: string;
  description: string;
  full_code: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface BusinessSubSectorCreate {
  business_sector: string;
  code: string;
  name: string;
  description?: string;
}

interface BusinessSubSectorsTabProps {
  onDataChange?: () => void;
}

const BusinessSubSectorsTab: React.FC<BusinessSubSectorsTabProps> = ({ onDataChange }) => {
  const { showNotification } = useNotification();
  const [subSectors, setSubSectors] = useState<BusinessSubSector[]>([]);
  const [sectors, setSectors] = useState<BusinessSector[]>([]);
  const [loading, setLoading] = useState(true);
  const [formOpen, setFormOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [editingSubSector, setEditingSubSector] = useState<BusinessSubSector | null>(null);
  const [subSectorToDelete, setSubSectorToDelete] = useState<BusinessSubSector | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSector, setSelectedSector] = useState('');
  const [formData, setFormData] = useState<BusinessSubSectorCreate>({
    business_sector: '',
    code: '',
    name: '',
    description: '',
  });
  const [formErrors, setFormErrors] = useState<any>({});
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    loadSubSectors();
    loadSectors();
  }, []);

  useEffect(() => {
    loadSubSectors();
  }, [searchTerm, selectedSector]);

  const loadSubSectors = async () => {
    try {
      setLoading(true);
      const params: any = {};
      if (searchTerm) params.search = searchTerm;
      if (selectedSector) params.business_sector = selectedSector;
      
      const data = await taxpayerService.getBusinessSubSectors(params);
      setSubSectors(data.results || data);
    } catch (error) {
      console.error('Failed to load business sub-sectors:', error);
      showNotification('Failed to load business sub-sectors', 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadSectors = async () => {
    try {
      const data = await taxpayerService.getBusinessSectors();
      setSectors(data.results || data);
    } catch (error) {
      console.error('Failed to load business sectors:', error);
    }
  };

  const handleCreate = () => {
    setEditingSubSector(null);
    setFormData({
      business_sector: '',
      code: '',
      name: '',
      description: '',
    });
    setFormErrors({});
    setFormOpen(true);
  };

  const handleEdit = (subSector: BusinessSubSector) => {
    setEditingSubSector(subSector);
    setFormData({
      business_sector: subSector.business_sector,
      code: subSector.code,
      name: subSector.name,
      description: subSector.description || '',
    });
    setFormErrors({});
    setFormOpen(true);
  };

  const handleDelete = (subSector: BusinessSubSector) => {
    setSubSectorToDelete(subSector);
    setDeleteDialogOpen(true);
  };

  const handleSubmit = async () => {
    try {
      setSubmitting(true);
      setFormErrors({});

      if (editingSubSector) {
        await taxpayerService.updateBusinessSubSector(editingSubSector.id, formData);
        showNotification('Business sub-sector updated successfully', 'success');
      } else {
        await taxpayerService.createBusinessSubSector(formData);
        showNotification('Business sub-sector created successfully', 'success');
      }

      setFormOpen(false);
      loadSubSectors();
      onDataChange?.();
    } catch (error: any) {
      console.error('Error submitting form:', error);
      if (error.response?.data) {
        setFormErrors(error.response.data);
      }
      showNotification('Failed to save business sub-sector', 'error');
    } finally {
      setSubmitting(false);
    }
  };

  const confirmDelete = async () => {
    if (!subSectorToDelete) return;

    try {
      await taxpayerService.deleteBusinessSubSector(subSectorToDelete.id);
      showNotification('Business sub-sector deleted successfully', 'success');
      setDeleteDialogOpen(false);
      setSubSectorToDelete(null);
      loadSubSectors();
      onDataChange?.();
    } catch (error) {
      console.error('Failed to delete business sub-sector:', error);
      showNotification('Failed to delete business sub-sector', 'error');
    }
  };

  const filteredSubSectors = subSectors.filter(subSector =>
    subSector.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    subSector.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    subSector.business_sector_name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Box sx={{ p: 3 }}>
      <Fade in timeout={600}>
        <Box>
          {/* Header */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
            <Typography variant="h5" component="h2" sx={{ fontWeight: 600 }}>
              Business Sub-Sectors ({filteredSubSectors.length})
            </Typography>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="outlined"
                startIcon={<Refresh />}
                onClick={loadSubSectors}
                disabled={loading}
                sx={{ borderRadius: 2 }}
              >
                Refresh
              </Button>
              <Button
                variant="contained"
                startIcon={<Add />}
                onClick={handleCreate}
                sx={{ 
                  borderRadius: 2,
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                  }
                }}
              >
                Add Sub-Sector
              </Button>
            </Box>
          </Box>

          {/* Filters */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Grid container spacing={2} alignItems="center">
                <Grid size={{ xs: 12, md: 6 }}>
                  <TextField
                    fullWidth
                    placeholder="Search by name, code, or sector..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Search />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                <Grid size={{ xs: 12, md: 4 }}>
                  <FormControl fullWidth>
                    <InputLabel>Business Sector</InputLabel>
                    <Select
                      value={selectedSector}
                      onChange={(e) => setSelectedSector(e.target.value)}
                      label="Business Sector"
                    >
                      <MenuItem value="">All Sectors</MenuItem>
                      {sectors.map((sector) => (
                        <MenuItem key={sector.id} value={sector.id}>
                          {sector.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid size={{ xs: 12, md: 2 }}>
                  <Button
                    fullWidth
                    variant="outlined"
                    onClick={() => {
                      setSearchTerm('');
                      setSelectedSector('');
                    }}
                  >
                    Clear
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Business Sub-Sectors Grid */}
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : filteredSubSectors.length === 0 ? (
            <Paper sx={{ p: 4, textAlign: 'center' }}>
              <Category sx={{ fontSize: 64, color: 'text.disabled', mb: 2 }} />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                No Business Sub-Sectors Found
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                {searchTerm || selectedSector
                  ? 'Try adjusting your search criteria'
                  : 'Create your first business sub-sector to get started'
                }
              </Typography>
              {!searchTerm && !selectedSector && (
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  onClick={handleCreate}
                  sx={{
                    borderRadius: 2,
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  }}
                >
                  Add First Sub-Sector
                </Button>
              )}
            </Paper>
          ) : (
            <Grid container spacing={3}>
              {filteredSubSectors.map((subSector) => (
                <Grid key={subSector.id} size={{ xs: 12, sm: 6, md: 4 }}>
                  <Card
                    sx={{
                      height: '100%',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: 4
                      }
                    }}
                  >
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <Category sx={{ color: 'primary.main', mr: 1 }} />
                        <Typography variant="h6" sx={{ fontWeight: 600 }}>
                          {subSector.name}
                        </Typography>
                      </Box>

                      <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                        <Chip
                          label={subSector.full_code}
                          color="primary"
                          size="small"
                          sx={{ fontWeight: 'bold' }}
                        />
                        <Chip
                          label={subSector.business_sector_name}
                          color="secondary"
                          size="small"
                          variant="outlined"
                        />
                      </Box>

                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        {subSector.description || 'No description provided'}
                      </Typography>

                      <Typography variant="caption" color="text.secondary">
                        Sector: {subSector.business_sector_name} ({subSector.business_sector_code})
                      </Typography>
                    </CardContent>

                    <CardActions sx={{ justifyContent: 'flex-end', pt: 0 }}>
                      <Tooltip title="Edit Sub-Sector">
                        <IconButton
                          size="small"
                          onClick={() => handleEdit(subSector)}
                          sx={{ color: 'primary.main' }}
                        >
                          <Edit />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete Sub-Sector">
                        <IconButton
                          size="small"
                          onClick={() => handleDelete(subSector)}
                          sx={{ color: 'error.main' }}
                        >
                          <Delete />
                        </IconButton>
                      </Tooltip>
                    </CardActions>
                  </Card>
                </Grid>
              ))}
            </Grid>
          )}

          {/* Form Dialog */}
          <Dialog
            open={formOpen}
            onClose={() => setFormOpen(false)}
            maxWidth="sm"
            fullWidth
          >
            <DialogTitle>
              {editingSubSector ? 'Edit Business Sub-Sector' : 'Create Business Sub-Sector'}
            </DialogTitle>
            <DialogContent>
              <Box sx={{ pt: 2 }}>
                <Grid container spacing={2}>
                  <Grid size={{ xs: 12 }}>
                    <FormControl fullWidth error={!!formErrors.business_sector}>
                      <InputLabel>Business Sector</InputLabel>
                      <Select
                        value={formData.business_sector}
                        onChange={(e) => setFormData({ ...formData, business_sector: e.target.value })}
                        label="Business Sector"
                      >
                        {sectors.map((sector) => (
                          <MenuItem key={sector.id} value={sector.id}>
                            {sector.name} ({sector.code})
                          </MenuItem>
                        ))}
                      </Select>
                      {formErrors.business_sector && (
                        <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 1.5 }}>
                          {formErrors.business_sector[0]}
                        </Typography>
                      )}
                    </FormControl>
                  </Grid>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <TextField
                      fullWidth
                      label="Sub-Sector Code"
                      value={formData.code}
                      onChange={(e) => setFormData({ ...formData, code: e.target.value })}
                      onBlur={createCodeBlurHandler(
                        (field, value) => setFormData(prev => ({ ...prev, [field]: value })),
                        'code'
                      )}
                      error={!!formErrors.code}
                      helperText={formErrors.code?.[0] || 'Code will be automatically converted to uppercase'}
                      placeholder="e.g., 001, 002, 003"
                    />
                  </Grid>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <TextField
                      fullWidth
                      label="Sub-Sector Name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      error={!!formErrors.name}
                      helperText={formErrors.name?.[0]}
                      placeholder="e.g., Crop Production"
                    />
                  </Grid>
                  <Grid size={{ xs: 12 }}>
                    <TextField
                      fullWidth
                      label="Description"
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      error={!!formErrors.description}
                      helperText={formErrors.description?.[0]}
                      multiline
                      rows={2}
                      placeholder="Describe this business sub-sector..."
                    />
                  </Grid>
                </Grid>
              </Box>
            </DialogContent>
            <DialogActions>
              <Button
                onClick={() => setFormOpen(false)}
                startIcon={<Cancel />}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmit}
                variant="contained"
                disabled={submitting}
                startIcon={submitting ? <CircularProgress size={20} /> : <Save />}
                sx={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                  }
                }}
              >
                {submitting ? 'Saving...' : 'Save'}
              </Button>
            </DialogActions>
          </Dialog>

          {/* Delete Confirmation Dialog */}
          <Dialog
            open={deleteDialogOpen}
            onClose={() => setDeleteDialogOpen(false)}
          >
            <DialogTitle>Confirm Delete</DialogTitle>
            <DialogContent>
              <Typography>
                Are you sure you want to delete the business sub-sector "{subSectorToDelete?.name}"?
                This action cannot be undone.
              </Typography>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setDeleteDialogOpen(false)}>
                Cancel
              </Button>
              <Button
                onClick={confirmDelete}
                color="error"
                variant="contained"
              >
                Delete
              </Button>
            </DialogActions>
          </Dialog>
        </Box>
      </Fade>
    </Box>
  );
};

export default BusinessSubSectorsTab;
