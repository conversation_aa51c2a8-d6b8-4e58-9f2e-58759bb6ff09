"""
Payment Processing Service

Handles payment status updates, penalty calculations, and interest calculations
for revenue collections with automatic status management.
"""

from decimal import Decimal
from django.utils import timezone
from django.db import transaction
from .models import RegionalRevenueCollection, CityServiceRevenueCollection, TaxpayerPaymentSummary


class PaymentProcessor:
    """
    Service class for processing payments and updating collection status
    """
    
    @staticmethod
    def process_payment(collection, payment_amount, payment_date=None):
        """
        Process a payment for a collection and update status
        
        Args:
            collection: RegionalRevenueCollection or CityServiceRevenueCollection instance
            payment_amount: Decimal amount being paid
            payment_date: Date of payment (defaults to today)
        
        Returns:
            dict: Payment processing result with status and details
        """
        if payment_date is None:
            payment_date = timezone.now().date()
        
        with transaction.atomic():
            # Update payment amount
            collection.paid_amount += Decimal(str(payment_amount))
            collection.payment_date = payment_date
            
            # Update payment status (this will also calculate penalties/interest)
            collection.update_payment_status()
            collection.save()
            
            # Update taxpayer payment summary
            PaymentProcessor._update_taxpayer_summary(collection)
            
            return {
                'success': True,
                'payment_status': collection.payment_status,
                'total_paid': collection.paid_amount,
                'outstanding_amount': collection.outstanding_amount,
                'is_fully_paid': collection.is_fully_paid,
                'penalty_amount': collection.penalty_amount,
                'interest_amount': collection.interest_amount,
                'total_amount_due': collection.total_amount_due,
            }
    
    @staticmethod
    def calculate_penalties_and_interest(collection):
        """
        Calculate and update penalties and interest for a collection
        
        Args:
            collection: Collection instance to update
        
        Returns:
            dict: Calculation results
        """
        old_penalty = collection.penalty_amount
        old_interest = collection.interest_amount
        
        # Calculate penalties and interest
        collection.update_payment_status()
        collection.save()
        
        return {
            'penalty_amount': collection.penalty_amount,
            'interest_amount': collection.interest_amount,
            'penalty_added': collection.penalty_amount - old_penalty,
            'interest_added': collection.interest_amount - old_interest,
            'total_amount_due': collection.total_amount_due,
        }
    
    @staticmethod
    def bulk_update_overdue_collections():
        """
        Bulk update all overdue collections with penalties and interest
        
        Returns:
            dict: Summary of updates
        """
        updated_count = 0
        total_penalty_added = Decimal('0')
        total_interest_added = Decimal('0')
        
        # Process regional collections
        regional_collections = RegionalRevenueCollection.objects.filter(
            payment_status__in=['PENDING', 'PARTIAL', 'OVERDUE'],
            due_date__lt=timezone.now().date()
        )
        
        for collection in regional_collections:
            old_penalty = collection.penalty_amount
            old_interest = collection.interest_amount
            
            collection.update_payment_status()
            collection.save()
            
            total_penalty_added += (collection.penalty_amount - old_penalty)
            total_interest_added += (collection.interest_amount - old_interest)
            updated_count += 1
        
        # Process city service collections
        city_collections = CityServiceRevenueCollection.objects.filter(
            payment_status__in=['PENDING', 'PARTIAL', 'OVERDUE'],
            due_date__lt=timezone.now().date()
        )
        
        for collection in city_collections:
            old_penalty = collection.penalty_amount
            old_interest = collection.interest_amount
            
            collection.update_payment_status()
            collection.save()
            
            total_penalty_added += (collection.penalty_amount - old_penalty)
            total_interest_added += (collection.interest_amount - old_interest)
            updated_count += 1
        
        return {
            'updated_count': updated_count,
            'total_penalty_added': total_penalty_added,
            'total_interest_added': total_interest_added,
        }
    
    @staticmethod
    def _update_taxpayer_summary(collection):
        """
        Update or create taxpayer payment summary after payment processing
        
        Args:
            collection: Collection instance that was updated
        """
        try:
            # Get or create taxpayer summary for this period
            if collection.individual_taxpayer:
                summary, created = TaxpayerPaymentSummary.objects.get_or_create(
                    individual_taxpayer=collection.individual_taxpayer,
                    period=collection.period,
                    defaults={'created_by': collection.recorded_by}
                )
            else:
                summary, created = TaxpayerPaymentSummary.objects.get_or_create(
                    organization_taxpayer=collection.organization_taxpayer,
                    period=collection.period,
                    defaults={'created_by': collection.recorded_by}
                )
            
            # Update summary with latest data
            summary.update_summary()
            summary.save()
            
        except Exception as e:
            # Log error but don't fail the payment processing
            print(f"Error updating taxpayer summary: {e}")
    
    @staticmethod
    def get_payment_breakdown(collection):
        """
        Get detailed payment breakdown for a collection
        
        Args:
            collection: Collection instance
        
        Returns:
            dict: Detailed payment breakdown
        """
        return {
            'original_amount': collection.amount,
            'penalty_amount': collection.penalty_amount,
            'interest_amount': collection.interest_amount,
            'total_amount_due': collection.total_amount_due,
            'paid_amount': collection.paid_amount,
            'outstanding_amount': collection.outstanding_amount,
            'payment_status': collection.payment_status,
            'is_overdue': collection.is_overdue,
            'days_overdue': collection.days_overdue,
            'penalty_rate': collection.penalty_rate,
            'interest_rate': collection.interest_rate,
            'due_date': collection.due_date,
            'payment_date': collection.payment_date,
        }
    
    @staticmethod
    def simulate_payment(collection, payment_amount):
        """
        Simulate a payment without actually processing it
        
        Args:
            collection: Collection instance
            payment_amount: Amount to simulate paying
        
        Returns:
            dict: Simulation results
        """
        # Calculate what the status would be after payment
        simulated_paid_amount = collection.paid_amount + Decimal(str(payment_amount))
        simulated_outstanding = collection.total_amount_due - simulated_paid_amount
        
        if simulated_paid_amount >= collection.total_amount_due:
            simulated_status = 'PAID'
        elif simulated_paid_amount > 0:
            simulated_status = 'PARTIAL'
        else:
            simulated_status = collection.payment_status
        
        return {
            'current_status': collection.payment_status,
            'simulated_status': simulated_status,
            'current_paid': collection.paid_amount,
            'simulated_paid': simulated_paid_amount,
            'current_outstanding': collection.outstanding_amount,
            'simulated_outstanding': max(simulated_outstanding, Decimal('0')),
            'payment_amount': Decimal(str(payment_amount)),
            'total_amount_due': collection.total_amount_due,
        }
