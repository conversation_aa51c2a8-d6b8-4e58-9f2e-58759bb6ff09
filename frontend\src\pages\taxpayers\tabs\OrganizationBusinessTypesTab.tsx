import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON>ert,
  Button,
  Paper,
  Card,
  CardContent,
  Grid,
  Chip,
  Fade,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip,
  CircularProgress,
  CardActions,
  FormControlLabel,
  Switch
} from '@mui/material';
import {
  AccountBalance,
  Add,
  Edit,
  Delete,
  Refresh,
  Save,
  Cancel,
  Business,
  CheckCircle,
  Close
} from '@mui/icons-material';
import { useNotification } from '../../../contexts/NotificationContext';
import taxpayerService from '../../../services/taxpayerService';
import { normalizeCode, createCodeBlurHandler } from '../../../utils/codeUtils';

interface OrganizationBusinessType {
  id: string;
  code: string;
  name: string;
  description: string;
  requires_vat_registration: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface OrganizationBusinessTypeCreate {
  code: string;
  name: string;
  description?: string;
  requires_vat_registration: boolean;
}

interface OrganizationBusinessTypesTabProps {
  onDataChange?: () => void;
}

const OrganizationBusinessTypesTab: React.FC<OrganizationBusinessTypesTabProps> = ({ onDataChange }) => {
  const { showNotification } = useNotification();
  const [types, setTypes] = useState<OrganizationBusinessType[]>([]);
  const [loading, setLoading] = useState(true);
  const [formOpen, setFormOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [editingType, setEditingType] = useState<OrganizationBusinessType | null>(null);
  const [typeToDelete, setTypeToDelete] = useState<OrganizationBusinessType | null>(null);
  const [formData, setFormData] = useState<OrganizationBusinessTypeCreate>({
    code: '',
    name: '',
    description: '',
    requires_vat_registration: false,
  });
  const [formErrors, setFormErrors] = useState<any>({});
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    loadTypes();
  }, []);

  const loadTypes = async () => {
    try {
      setLoading(true);
      const data = await taxpayerService.getOrganizationBusinessTypes();
      setTypes(data.results || data);
    } catch (error) {
      console.error('Failed to load organization business types:', error);
      showNotification('Failed to load organization business types', 'error');
    } finally {
      setLoading(false);
    }
  };
  const handleCreate = () => {
    setEditingType(null);
    setFormData({
      code: '',
      name: '',
      description: '',
      requires_vat_registration: false,
    });
    setFormErrors({});
    setFormOpen(true);
  };

  const handleEdit = (type: OrganizationBusinessType) => {
    setEditingType(type);
    setFormData({
      code: type.code,
      name: type.name,
      description: type.description || '',
      requires_vat_registration: type.requires_vat_registration,
    });
    setFormErrors({});
    setFormOpen(true);
  };

  const handleDelete = (type: OrganizationBusinessType) => {
    setTypeToDelete(type);
    setDeleteDialogOpen(true);
  };

  const handleSubmit = async () => {
    try {
      setSubmitting(true);
      setFormErrors({});

      if (editingType) {
        await taxpayerService.updateOrganizationBusinessType(editingType.id, formData);
        showNotification('Organization business type updated successfully', 'success');
      } else {
        await taxpayerService.createOrganizationBusinessType(formData);
        showNotification('Organization business type created successfully', 'success');
      }

      setFormOpen(false);
      loadTypes();
      onDataChange?.();
    } catch (error: any) {
      console.error('Error submitting form:', error);
      if (error.response?.data) {
        setFormErrors(error.response.data);
      }
      showNotification('Failed to save organization business type', 'error');
    } finally {
      setSubmitting(false);
    }
  };

  const confirmDelete = async () => {
    if (!typeToDelete) return;

    try {
      await taxpayerService.deleteOrganizationBusinessType(typeToDelete.id);
      showNotification('Organization business type deleted successfully', 'success');
      setDeleteDialogOpen(false);
      setTypeToDelete(null);
      loadTypes();
      onDataChange?.();
    } catch (error) {
      console.error('Failed to delete organization business type:', error);
      showNotification('Failed to delete organization business type', 'error');
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Fade in timeout={600}>
        <Box>
          {/* Header */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
            <Typography variant="h5" component="h2" sx={{ fontWeight: 600 }}>
              Organization Business Types ({types.length})
            </Typography>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="outlined"
                startIcon={<Refresh />}
                onClick={loadTypes}
                disabled={loading}
                sx={{ borderRadius: 2 }}
              >
                Refresh
              </Button>
              <Button
                variant="contained"
                startIcon={<Add />}
                onClick={handleCreate}
                sx={{
                  borderRadius: 2,
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                  }
                }}
              >
                Add Type
              </Button>
            </Box>
          </Box>

          {/* Organization Business Types Grid */}
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : types.length === 0 ? (
            <Paper sx={{ p: 4, textAlign: 'center' }}>
              <Business sx={{ fontSize: 64, color: 'text.disabled', mb: 2 }} />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                No Organization Business Types Found
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Create your first organization business type to get started
              </Typography>
              <Button
                variant="contained"
                startIcon={<Add />}
                onClick={handleCreate}
                sx={{
                  borderRadius: 2,
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                }}
              >
                Add First Type
              </Button>
            </Paper>
          ) : (
            <Grid container spacing={3}>
              {types.map((type) => (
                <Grid key={type.id} size={{ xs: 12, sm: 6, md: 4 }}>
                  <Card
                    sx={{
                      height: '100%',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: 4
                      }
                    }}
                  >
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <Business sx={{ color: 'primary.main', mr: 1 }} />
                        <Typography variant="h6" sx={{ fontWeight: 600 }}>
                          {type.name}
                        </Typography>
                      </Box>

                      <Chip
                        label={type.code}
                        color="primary"
                        sx={{ mb: 2, fontWeight: 'bold' }}
                      />

                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        {type.description}
                      </Typography>

                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        {type.requires_vat_registration ? (
                          <CheckCircle sx={{ color: 'success.main', mr: 1, fontSize: 20 }} />
                        ) : (
                          <Close sx={{ color: 'error.main', mr: 1, fontSize: 20 }} />
                        )}
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {type.requires_vat_registration ? 'VAT Registration Required' : 'VAT Registration Optional'}
                        </Typography>
                      </Box>
                    </CardContent>

                    <CardActions sx={{ justifyContent: 'flex-end', pt: 0 }}>
                      <Tooltip title="Edit Type">
                        <IconButton
                          size="small"
                          onClick={() => handleEdit(type)}
                          sx={{ color: 'primary.main' }}
                        >
                          <Edit />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete Type">
                        <IconButton
                          size="small"
                          onClick={() => handleDelete(type)}
                          sx={{ color: 'error.main' }}
                        >
                          <Delete />
                        </IconButton>
                      </Tooltip>
                    </CardActions>
                  </Card>
                </Grid>
              ))}
            </Grid>
          )}
          {/* Form Dialog */}
          <Dialog
            open={formOpen}
            onClose={() => setFormOpen(false)}
            maxWidth="sm"
            fullWidth
          >
            <DialogTitle>
              {editingType ? 'Edit Organization Business Type' : 'Create Organization Business Type'}
            </DialogTitle>
            <DialogContent>
              <Box sx={{ pt: 2 }}>
                <Grid container spacing={2}>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <TextField
                      fullWidth
                      label="Business Type Code"
                      value={formData.code}
                      onChange={(e) => setFormData({ ...formData, code: e.target.value })}
                      onBlur={createCodeBlurHandler(
                        (field, value) => setFormData(prev => ({ ...prev, [field]: value })),
                        'code'
                      )}
                      error={!!formErrors.code}
                      helperText={formErrors.code?.[0] || 'Code will be automatically converted to uppercase'}
                      placeholder="e.g., PLC, SC, COOP"
                    />
                  </Grid>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <TextField
                      fullWidth
                      label="Business Type Name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      error={!!formErrors.name}
                      helperText={formErrors.name?.[0]}
                      placeholder="e.g., Private Limited Company"
                    />
                  </Grid>
                  <Grid size={{ xs: 12 }}>
                    <TextField
                      fullWidth
                      label="Description"
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      error={!!formErrors.description}
                      helperText={formErrors.description?.[0]}
                      multiline
                      rows={2}
                      placeholder="Describe this organization business type..."
                    />
                  </Grid>
                  <Grid size={{ xs: 12 }}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={formData.requires_vat_registration}
                          onChange={(e) => setFormData({
                            ...formData,
                            requires_vat_registration: e.target.checked
                          })}
                        />
                      }
                      label="Requires VAT Registration"
                    />
                  </Grid>
                </Grid>
              </Box>
            </DialogContent>
            <DialogActions>
              <Button
                onClick={() => setFormOpen(false)}
                startIcon={<Cancel />}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmit}
                variant="contained"
                disabled={submitting}
                startIcon={submitting ? <CircularProgress size={20} /> : <Save />}
                sx={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                  }
                }}
              >
                {submitting ? 'Saving...' : 'Save'}
              </Button>
            </DialogActions>
          </Dialog>

          {/* Delete Confirmation Dialog */}
          <Dialog
            open={deleteDialogOpen}
            onClose={() => setDeleteDialogOpen(false)}
          >
            <DialogTitle>Confirm Delete</DialogTitle>
            <DialogContent>
              <Typography>
                Are you sure you want to delete the organization business type "{typeToDelete?.name}"?
                This action cannot be undone.
              </Typography>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setDeleteDialogOpen(false)}>
                Cancel
              </Button>
              <Button
                onClick={confirmDelete}
                color="error"
                variant="contained"
              >
                Delete
              </Button>
            </DialogActions>
          </Dialog>
        </Box>
      </Fade>
    </Box>
  );
};

export default OrganizationBusinessTypesTab;
