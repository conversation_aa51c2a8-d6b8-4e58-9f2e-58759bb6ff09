// Re-export all types from service files for easier importing

// Document exports
export type {
  Document,
  DocumentCreate,
  DocumentUpdate,
  DocumentListResponse,
  DocumentStats
} from './documentService';

// Request exports
export type {
  DocumentRequest,
  DocumentSummary,
  RequestCreate,
  RequestUpdate,
  RequestAction,
  RequestApproval,
  RequestListResponse,
  RequestStats
} from './requestService';

// Document Type exports
export type {
  DocumentType,
  DocumentTypeCreate,
  DocumentTypeUpdate,
  DocumentTypeListResponse
} from './documentTypeService';

// User exports
export type {
  User,
  UserCreate,
  UserUpdate,
  UserPasswordUpdate,
  UserListResponse,
  UserProfile
} from './userService';

// Role exports
export type {
  Role,
  RoleCreate,
  RoleUpdate,
  RoleListResponse,
  Permission,
  PermissionCategory
} from './roleService';

// Organization exports
export type {
  Organization,
  OrganizationCreate,
  OrganizationUpdate,
  OrganizationListResponse,
  Department,
  DepartmentCreate,
  DepartmentUpdate
} from './organizationService';

// Location exports
export type {
  Building,
  BuildingCreate,
  BuildingUpdate,
  Shelf,
  ShelfCreate,
  ShelfUpdate,
  Box,
  BoxCreate,
  BoxUpdate,
  Kent,
  KentCreate,
  KentUpdate,
  FileType,
  FileTypeCreate,
  FileTypeUpdate,
  LocationHierarchy,
  ShelfWithKents,
  LocationSummary,
  LocationListResponse
} from './locationService';

// File exports
export type {
  File,
  FileCreate,
  FileUpdate,
  FileWithDocuments,
  FileListResponse,
  FileStats
} from './fileService';
