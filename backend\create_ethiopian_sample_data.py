#!/usr/bin/env python
"""
<PERSON>ript to create comprehensive Ethiopian sample data in Amharic
"""
import os
import sys
import django
import random
from datetime import datetime, timedelta

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'document_management.settings')
django.setup()

from taxpayers.models import (
    TaxPayerLevel, BusinessSector, BusinessSubSector, OrganizationBusinessType,
    IndividualTaxPayer, OrganizationTaxPayer
)
from geographical_locations.models import Country, Region, Zone, City, SubCity, Kebele

def create_geographical_data():
    """Create Ethiopian geographical hierarchy"""
    print("🇪🇹 Creating Ethiopian geographical data...")
    
    # Create Ethiopia
    ethiopia, created = Country.objects.get_or_create(
        code='ET',
        defaults={'name': 'ኢትዮጵያ', 'is_active': True}
    )
    if created:
        print("✅ Created country: ኢትዮጵያ")
    
    # Create Addis Ababa Region
    addis_region, created = Region.objects.get_or_create(
        country=ethiopia,
        code='AA',
        defaults={'name': 'አዲስ አበባ', 'is_active': True}
    )
    if created:
        print("✅ Created region: አዲስ አበባ")
    
    # Create Addis Ababa City
    addis_city, created = City.objects.get_or_create(
        region=addis_region,
        code='AA01',
        defaults={'name': 'አዲስ አበባ', 'population': 3500000, 'is_active': True}
    )
    if created:
        print("✅ Created city: አዲስ አበባ")
    
    # Create Kirkos SubCity
    kirkos_subcity, created = SubCity.objects.get_or_create(
        city=addis_city,
        code='KRK',
        defaults={
            'name': 'ቂርቆስ',
            'type': 'subcity',
            'population': 250000,
            'is_active': True
        }
    )
    if created:
        print("✅ Created subcity: ቂርቆስ")
    
    # Create multiple kebeles in Kirkos
    kebeles_data = [
        {'code': 'KRK01', 'name': 'ቂርቆስ ቀበሌ 01', 'number': 1},
        {'code': 'KRK02', 'name': 'ቂርቆስ ቀበሌ 02', 'number': 2},
        {'code': 'KRK03', 'name': 'ቂርቆስ ቀበሌ 03', 'number': 3},
        {'code': 'KRK04', 'name': 'ቂርቆስ ቀበሌ 04', 'number': 4},
        {'code': 'KRK05', 'name': 'ቂርቆስ ቀበሌ 05', 'number': 5},
        {'code': 'KRK06', 'name': 'ቂርቆስ ቀበሌ 06', 'number': 6},
        {'code': 'KRK07', 'name': 'ቂርቆስ ቀበሌ 07', 'number': 7},
        {'code': 'KRK08', 'name': 'ቂርቆስ ቀበሌ 08', 'number': 8},
        {'code': 'KRK09', 'name': 'ቂርቆስ ቀበሌ 09', 'number': 9},
        {'code': 'KRK10', 'name': 'ቂርቆስ ቀበሌ 10', 'number': 10},
    ]
    
    kebeles = []
    for kebele_data in kebeles_data:
        kebele, created = Kebele.objects.get_or_create(
            subcity=kirkos_subcity,
            code=kebele_data['code'],
            defaults={
                'name': kebele_data['name'],
                'number': kebele_data['number'],
                'population': random.randint(15000, 35000),
                'is_active': True
            }
        )
        if created:
            print(f"✅ Created kebele: {kebele.name}")
        kebeles.append(kebele)
    
    return ethiopia, addis_region, addis_city, kirkos_subcity, kebeles

def create_business_data():
    """Create Ethiopian business classification data"""
    print("🏢 Creating Ethiopian business classification data...")
    
    # Tax Payer Levels
    tax_levels_data = [
        {'code': 'A', 'name': 'ምድብ ሀ', 'description': 'ትላልቅ ግብር ከፋዮች'},
        {'code': 'B', 'name': 'ምድብ ለ', 'description': 'መካከለኛ ግብር ከፋዮች'},
        {'code': 'C', 'name': 'ምድብ ሐ', 'description': 'ትናንሽ ግብር ከፋዮች'},
    ]
    
    tax_levels = []
    for level_data in tax_levels_data:
        level, created = TaxPayerLevel.objects.get_or_create(
            code=level_data['code'],
            defaults={
                'name': level_data['name'],
                'description': level_data['description']
            }
        )
        if created:
            print(f"✅ Created tax level: {level.name}")
        tax_levels.append(level)
    
    # Business Sectors
    sectors_data = [
        {'code': 'AGR', 'name': 'ግብርና', 'description': 'የግብርና እንቅስቃሴዎች'},
        {'code': 'TRD', 'name': 'ንግድ', 'description': 'የንግድ እንቅስቃሴዎች'},
        {'code': 'SRV', 'name': 'አገልግሎት', 'description': 'የአገልግሎት ዘርፎች'},
        {'code': 'MAN', 'name': 'ማኑፋክቸሪንግ', 'description': 'የማምረቻ ኢንዱስትሪዎች'},
        {'code': 'CON', 'name': 'ኮንስትራክሽን', 'description': 'የግንባታ ስራዎች'},
        {'code': 'TRA', 'name': 'ትራንስፖርት', 'description': 'የመጓጓዣ አገልግሎቶች'},
        {'code': 'EDU', 'name': 'ትምህርት', 'description': 'የትምህርት አገልግሎቶች'},
        {'code': 'HLT', 'name': 'ጤና', 'description': 'የጤና አገልግሎቶች'},
    ]
    
    sectors = []
    for sector_data in sectors_data:
        sector, created = BusinessSector.objects.get_or_create(
            code=sector_data['code'],
            defaults={
                'name': sector_data['name'],
                'description': sector_data['description']
            }
        )
        if created:
            print(f"✅ Created business sector: {sector.name}")
        sectors.append(sector)
    
    # Business Sub-Sectors
    sub_sectors_data = [
        # Agriculture
        {'sector_code': 'AGR', 'code': '001', 'name': 'የእህል ምርት', 'description': 'ስንዴ፣ ገብስ፣ ጤፍ ምርት'},
        {'sector_code': 'AGR', 'code': '002', 'name': 'የእንስሳት እርባታ', 'description': 'ከብት፣ በግ፣ ፍየል እርባታ'},
        {'sector_code': 'AGR', 'code': '003', 'name': 'የቡና ምርት', 'description': 'የቡና ዘር ምርት እና ማቀነባበር'},
        
        # Trade
        {'sector_code': 'TRD', 'code': '001', 'name': 'የችርቻሮ ንግድ', 'description': 'የችርቻሮ የንግድ እንቅስቃሴዎች'},
        {'sector_code': 'TRD', 'code': '002', 'name': 'የጅምላ ንግድ', 'description': 'የጅምላ የንግድ እንቅስቃሴዎች'},
        {'sector_code': 'TRD', 'code': '003', 'name': 'የምግብ ንግድ', 'description': 'የምግብ እና መጠጥ ንግድ'},
        
        # Services
        {'sector_code': 'SRV', 'code': '001', 'name': 'የባንክ አገልግሎት', 'description': 'የባንክ እና የፋይናንስ አገልግሎቶች'},
        {'sector_code': 'SRV', 'code': '002', 'name': 'የሆቴል አገልግሎት', 'description': 'የሆቴል እና የምግብ ቤት አገልግሎቶች'},
        {'sector_code': 'SRV', 'code': '003', 'name': 'የቴክኖሎጂ አገልግሎት', 'description': 'የኮምፒውተር እና የቴክኖሎጂ አገልግሎቶች'},
        
        # Manufacturing
        {'sector_code': 'MAN', 'code': '001', 'name': 'የጨርቃ ጨርቅ ምርት', 'description': 'የጨርቃ ጨርቅ እና የልብስ ምርት'},
        {'sector_code': 'MAN', 'code': '002', 'name': 'የምግብ ማቀነባበር', 'description': 'የምግብ እና መጠጥ ማቀነባበር'},
        {'sector_code': 'MAN', 'code': '003', 'name': 'የሲሚንቶ ምርት', 'description': 'የሲሚንቶ እና የግንባታ ቁሳቁስ ምርት'},
    ]
    
    sub_sectors = []
    for sub_sector_data in sub_sectors_data:
        try:
            sector = BusinessSector.objects.get(code=sub_sector_data['sector_code'])
            sub_sector, created = BusinessSubSector.objects.get_or_create(
                business_sector=sector,
                code=sub_sector_data['code'],
                defaults={
                    'name': sub_sector_data['name'],
                    'description': sub_sector_data['description']
                }
            )
            if created:
                print(f"✅ Created business sub-sector: {sub_sector.name}")
            sub_sectors.append(sub_sector)
        except BusinessSector.DoesNotExist:
            continue
    
    # Organization Business Types
    org_types_data = [
        {'code': 'PLC', 'name': 'የግል ኃላፊነቱ የተወሰነ ኩባንያ', 'requires_vat': True},
        {'code': 'SC', 'name': 'የአክሲዮን ኩባንያ', 'requires_vat': True},
        {'code': 'SOLE', 'name': 'የግል ንግድ', 'requires_vat': False},
        {'code': 'COOP', 'name': 'ህብረት ስራ ማህበር', 'requires_vat': False},
        {'code': 'NGO', 'name': 'መንግስታዊ ያልሆነ ድርጅት', 'requires_vat': False},
        {'code': 'GOV', 'name': 'የመንግስት ድርጅት', 'requires_vat': False},
    ]
    
    org_types = []
    for org_type_data in org_types_data:
        org_type, created = OrganizationBusinessType.objects.get_or_create(
            code=org_type_data['code'],
            defaults={
                'name': org_type_data['name'],
                'requires_vat_registration': org_type_data['requires_vat']
            }
        )
        if created:
            print(f"✅ Created organization type: {org_type.name}")
        org_types.append(org_type)
    
    return tax_levels, sectors, sub_sectors, org_types

def create_sample_taxpayers(tax_levels, sectors, sub_sectors, org_types, kebeles, ethiopia, addis_region, addis_city, kirkos_subcity):
    """Create sample Ethiopian taxpayers"""
    print("👥 Creating sample Ethiopian taxpayers...")
    
    # Ethiopian names
    ethiopian_first_names_male = [
        'አበበ', 'ተስፋዬ', 'ግርማ', 'ሃይሌ', 'ዳንኤል', 'ሳሙኤል', 'ዮሴፍ', 'ሚካኤል', 'ገብረ', 'ተክለ',
        'አስፋው', 'ብርሃኑ', 'ደረጀ', 'ፍቅሩ', 'ጌታቸው', 'ሃብታሙ', 'ኪዳኔ', 'ለማ', 'መንግስቱ', 'ነጋ'
    ]
    
    ethiopian_first_names_female = [
        'አልማዝ', 'በዛይት', 'ፋጢማ', 'ሃና', 'ክብረት', 'ሊዲያ', 'ማርታ', 'ናዚ', 'ሳራ', 'ታደለች',
        'አስካለች', 'በለጠች', 'ደስታ', 'እመቤት', 'ፍሬወይኒ', 'ገነት', 'ሄኖክ', 'ኪዳነ', 'ሌላ', 'መሪያም'
    ]
    
    ethiopian_last_names = [
        'አበበ', 'ተስፋዬ', 'ወልደ', 'ገብረ', 'ተክለ', 'መንግስቱ', 'ሃይሌ', 'ዳንኤል', 'ሳሙኤል', 'ዮሴፍ',
        'ሚካኤል', 'አስፋው', 'ብርሃኑ', 'ደረጀ', 'ፍቅሩ', 'ጌታቸው', 'ሃብታሙ', 'ኪዳኔ', 'ለማ', 'ነጋ'
    ]
    
    ethiopian_company_names = [
        'ኢትዮ ንግድ ኃላፊነቱ የተወሰነ ኩባንያ',
        'አዲስ አበባ የግንባታ ኩባንያ',
        'ሰላም የምግብ ማቀነባበር ኩባንያ',
        'ብርሃን የቴክኖሎጂ አገልግሎት',
        'ኢትዮጵያ የቡና ኤክስፖርት ኩባንያ',
        'አበበ እና ወንድሞች ንግድ ቤት',
        'ሃይሌ የግብርና ምርት ኩባንያ',
        'ተስፋ የትራንስፖርት አገልግሎት',
        'ዳንኤል የሆቴል እና ቱሪዝም ኩባንያ',
        'ሳሙኤል የጨርቃ ጨርቅ ምርት ኩባንያ'
    ]
    
    # Create Individual Taxpayers
    individual_count = 0
    for i in range(150):  # Create 150 individual taxpayers
        try:
            tin = f"{random.randint(**********, **********)}"
            
            if IndividualTaxPayer.objects.filter(tin=tin).exists():
                continue
            
            gender = random.choice(['M', 'F'])
            first_names = ethiopian_first_names_male if gender == 'M' else ethiopian_first_names_female
            
            individual = IndividualTaxPayer.objects.create(
                tin=tin,
                first_name=random.choice(first_names),
                middle_name=random.choice(ethiopian_first_names_male + ethiopian_first_names_female) if random.choice([True, False]) else '',
                last_name=random.choice(ethiopian_last_names),
                nationality='ET',
                gender=gender,
                date_of_birth=datetime.now().date() - timedelta(days=random.randint(18*365, 70*365)),
                tax_payer_level=random.choice(tax_levels),
                business_sector=random.choice(sectors),
                business_sub_sector=random.choice(sub_sectors),
                business_registration_date=datetime.now().date() - timedelta(days=random.randint(30, 1825)),
                business_name=f"{random.choice(ethiopian_first_names_male)} የንግድ ቤት" if random.choice([True, False]) else '',
                business_license_number=f"BL{random.randint(100000, 999999)}" if random.choice([True, False]) else '',
                phone=f"+251{random.choice(['9', '7'])}{random.randint(10000000, 99999999)}",
                phone_secondary=f"+251{random.choice(['9', '7'])}{random.randint(10000000, 99999999)}" if random.choice([True, False]) else '',
                email=f"user{i}@example.com" if random.choice([True, False]) else '',
                country=ethiopia,
                region=addis_region,
                city=addis_city,
                subcity=kirkos_subcity,
                kebele=random.choice(kebeles),
                house_number=str(random.randint(1, 9999)),
                street_address=f"ቤት ቁጥር {random.randint(1, 999)}" if random.choice([True, False]) else '',
                postal_code=str(random.randint(1000, 9999)) if random.choice([True, False]) else '',
                is_active=random.choice([True, True, True, False])  # 75% active
            )
            individual_count += 1
            
            if individual_count % 30 == 0:
                print(f"✅ Created {individual_count} individual taxpayers...")
                
        except Exception as e:
            print(f"❌ Error creating individual taxpayer: {e}")
            continue
    
    # Create Organization Taxpayers
    organization_count = 0
    for i in range(50):  # Create 50 organization taxpayers
        try:
            tin = f"{random.randint(**********, **********)}"
            
            if OrganizationTaxPayer.objects.filter(tin=tin).exists():
                continue
            
            org_type = random.choice(org_types)
            requires_vat = org_type.requires_vat_registration
            
            organization = OrganizationTaxPayer.objects.create(
                tin=tin,
                business_name=random.choice(ethiopian_company_names),
                trade_name=f"{random.choice(ethiopian_first_names_male)} ንግድ" if random.choice([True, False]) else '',
                organization_business_type=org_type,
                tax_payer_level=random.choice(tax_levels),
                business_sector=random.choice(sectors),
                business_sub_sector=random.choice(sub_sectors),
                business_registration_date=datetime.now().date() - timedelta(days=random.randint(365, 3650)),
                business_license_number=f"BL{random.randint(100000, 999999)}" if random.choice([True, False]) else '',
                capital_amount=random.randint(100000, 50000000) if random.choice([True, False]) else None,
                number_of_employees=random.randint(5, 1000) if random.choice([True, False]) else None,
                manager_first_name=random.choice(ethiopian_first_names_male),
                manager_middle_name=random.choice(ethiopian_first_names_male + ethiopian_first_names_female) if random.choice([True, False]) else '',
                manager_last_name=random.choice(ethiopian_last_names),
                manager_title=random.choice(['ዋና ሥራ አስኪያጅ', 'ዋና ሥራ አስፈፃሚ', 'ዳይሬክተር', 'ባለቤት', 'ፕሬዚዳንት']),
                vat_registration_date=datetime.now().date() - timedelta(days=random.randint(30, 1825)) if requires_vat else None,
                vat_number=f"ET{random.randint(**********, **********)}" if requires_vat else None,
                phone=f"+251{random.choice(['9', '7'])}{random.randint(10000000, 99999999)}",
                phone_secondary=f"+251{random.choice(['9', '7'])}{random.randint(10000000, 99999999)}" if random.choice([True, False]) else '',
                email=f"company{i}@example.com" if random.choice([True, False]) else '',
                country=ethiopia,
                region=addis_region,
                city=addis_city,
                subcity=kirkos_subcity,
                kebele=random.choice(kebeles),
                house_number=str(random.randint(1, 9999)),
                street_address=f"ቤት ቁጥር {random.randint(1, 999)}" if random.choice([True, False]) else '',
                postal_code=str(random.randint(1000, 9999)) if random.choice([True, False]) else '',
                is_active=random.choice([True, True, True, False])  # 75% active
            )
            organization_count += 1
            
            if organization_count % 10 == 0:
                print(f"✅ Created {organization_count} organization taxpayers...")
                
        except Exception as e:
            print(f"❌ Error creating organization taxpayer: {e}")
            continue
    
    print(f"\n🎉 Ethiopian sample data creation completed!")
    print(f"📊 Summary:")
    print(f"   - Individual Taxpayers: {IndividualTaxPayer.objects.count()}")
    print(f"   - Organization Taxpayers: {OrganizationTaxPayer.objects.count()}")
    print(f"   - Total Taxpayers: {IndividualTaxPayer.objects.count() + OrganizationTaxPayer.objects.count()}")
    print(f"   - Kebeles: {len(kebeles)}")

def main():
    """Main function to create all Ethiopian sample data"""
    print("🇪🇹 Starting Ethiopian sample data creation...")
    
    try:
        # Create geographical data
        ethiopia, addis_region, addis_city, kirkos_subcity, kebeles = create_geographical_data()
        
        # Create business classification data
        tax_levels, sectors, sub_sectors, org_types = create_business_data()
        
        # Create sample taxpayers
        create_sample_taxpayers(tax_levels, sectors, sub_sectors, org_types, kebeles, ethiopia, addis_region, addis_city, kirkos_subcity)
        
        print("\n🎉 All Ethiopian sample data created successfully!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
