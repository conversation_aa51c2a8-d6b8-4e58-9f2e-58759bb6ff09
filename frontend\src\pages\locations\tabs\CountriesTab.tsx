import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControlLabel,
  Switch,
  InputAdornment,
  Typography,
  Tooltip,
  Menu,
  MenuItem,
  Alert,
  Card,
  CardContent,
  CardHeader,
  Avatar,
  Skeleton,
  Fade,
  Zoom,
  Divider,
  Stack,
  Badge,
} from '@mui/material';
import {
  Add,
  Search,
  Edit,
  Delete,
  MoreVert,
  Public,
  Phone,
  AttachMoney,
  Refresh,
  FilterList,
  Visibility,
  CheckCircle,
  Cancel,
  Language,
  Flag,
} from '@mui/icons-material';
import { useNotification } from '../../../contexts/NotificationContext';
import locationHierarchyService from '../../../services/locationHierarchyService';
import type { Country, CountryCreate } from '../../../services/locationHierarchyService';

interface CountriesTabProps {
  onStatsChange?: () => void;
}

const CountriesTab: React.FC<CountriesTabProps> = ({ onStatsChange }) => {
  const { showNotification } = useNotification();

  // State management
  const [countries, setCountries] = useState<Country[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCountry, setSelectedCountry] = useState<Country | null>(null);
  const [menuAnchor, setMenuAnchor] = useState<HTMLElement | null>(null);

  // Dialog states
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);

  // Form states
  const [formData, setFormData] = useState<CountryCreate>({
    name: '',
    code: '',
    phone_code: '',
    currency: '',
    is_active: true,
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [submitting, setSubmitting] = useState(false);

  // Load countries
  const loadCountries = async (search = '') => {
    try {
      setLoading(true);
      const response = await locationHierarchyService.getCountries({
        search: search.trim() || undefined,
        page_size: 100,
      });
      setCountries(response.results);
    } catch (error) {
      console.error('Error loading countries:', error);
      showNotification('Failed to load countries', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    loadCountries();
  }, []);

  // Search handler
  const handleSearch = () => {
    loadCountries(searchTerm);
  };

  // Handle search on Enter key
  const handleSearchKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      handleSearch();
    }
  };

  // Menu handlers
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, country: Country) => {
    setMenuAnchor(event.currentTarget);
    setSelectedCountry(country);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
    setSelectedCountry(null);
  };

  // Form validation
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = 'Country name is required';
    } else if (formData.name.length < 2) {
      errors.name = 'Country name must be at least 2 characters';
    } else if (formData.name.length > 100) {
      errors.name = 'Country name must be less than 100 characters';
    }

    if (!formData.code.trim()) {
      errors.code = 'Country code is required';
    } else if (!/^[A-Z]{2,3}$/.test(formData.code)) {
      errors.code = 'Country code must be 2-3 uppercase letters (e.g., ET, US, GB)';
    }

    if (formData.phone_code && !/^\+\d{1,4}$/.test(formData.phone_code)) {
      errors.phone_code = 'Phone code must start with + followed by 1-4 digits (e.g., +251)';
    }

    if (formData.currency && !/^[A-Z]{3}$/.test(formData.currency)) {
      errors.currency = 'Currency must be 3 uppercase letters (e.g., ETB, USD)';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // CRUD operations
  const handleCreate = async () => {
    if (!validateForm()) return;

    try {
      setSubmitting(true);
      await locationHierarchyService.createCountry(formData);
      showNotification('Country created successfully', 'success');
      setCreateDialogOpen(false);
      resetForm();
      loadCountries(searchTerm);
      onStatsChange?.();
    } catch (error) {
      console.error('Error creating country:', error);
      showNotification('Failed to create country', 'error');
    } finally {
      setSubmitting(false);
    }
  };

  const handleEdit = async () => {
    if (!selectedCountry || !validateForm()) return;

    try {
      setSubmitting(true);
      await locationHierarchyService.updateCountry(selectedCountry.id, formData);
      showNotification('Country updated successfully', 'success');
      setEditDialogOpen(false);
      resetForm();
      loadCountries(searchTerm);
      onStatsChange?.();
      handleMenuClose();
    } catch (error) {
      console.error('Error updating country:', error);
      showNotification('Failed to update country', 'error');
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async () => {
    if (!selectedCountry) return;

    try {
      setSubmitting(true);
      await locationHierarchyService.deleteCountry(selectedCountry.id);
      showNotification('Country deleted successfully', 'success');
      setDeleteDialogOpen(false);
      loadCountries(searchTerm);
      onStatsChange?.();
      handleMenuClose();
    } catch (error) {
      console.error('Error deleting country:', error);
      showNotification('Failed to delete country', 'error');
    } finally {
      setSubmitting(false);
    }
  };

  // Dialog handlers
  const resetForm = () => {
    setFormData({
      name: '',
      code: '',
      phone_code: '',
      currency: '',
      is_active: true,
    });
    setFormErrors({});
  };

  const openCreateDialog = () => {
    resetForm();
    setCreateDialogOpen(true);
  };

  const openEditDialog = (country: Country) => {
    setFormData({
      name: country.name,
      code: country.code,
      phone_code: country.phone_code || '',
      currency: country.currency || '',
      is_active: country.is_active,
    });
    setFormErrors({});
    setEditDialogOpen(true);
    handleMenuClose();
  };

  const openViewDialog = (country: Country) => {
    setSelectedCountry(country);
    setViewDialogOpen(true);
    handleMenuClose();
  };

  const openDeleteDialog = () => {
    setDeleteDialogOpen(true);
    handleMenuClose();
  };

  // Render loading skeleton
  const renderSkeleton = () => (
    <TableContainer component={Paper} elevation={2}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Country</TableCell>
            <TableCell>Code</TableCell>
            <TableCell>Phone Code</TableCell>
            <TableCell>Currency</TableCell>
            <TableCell>Status</TableCell>
            <TableCell>Created</TableCell>
            <TableCell align="right">Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {[...Array(5)].map((_, index) => (
            <TableRow key={index}>
              <TableCell>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Skeleton variant="circular" width={40} height={40} />
                  <Skeleton variant="text" width={120} />
                </Box>
              </TableCell>
              <TableCell><Skeleton variant="text" width={60} /></TableCell>
              <TableCell><Skeleton variant="text" width={80} /></TableCell>
              <TableCell><Skeleton variant="text" width={60} /></TableCell>
              <TableCell><Skeleton variant="rectangular" width={60} height={24} /></TableCell>
              <TableCell><Skeleton variant="text" width={100} /></TableCell>
              <TableCell align="right"><Skeleton variant="circular" width={32} height={32} /></TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  return (
    <Box>
      {/* Professional Header */}
      <Card sx={{ mb: 3, background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}>
                <Public />
              </Avatar>
              <Box>
                <Typography variant="h5" sx={{ color: 'white', fontWeight: 600 }}>
                  Countries Management
                </Typography>
                <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                  Manage countries in the administrative hierarchy
                </Typography>
              </Box>
            </Box>
            <Zoom in timeout={500}>
              <Button
                variant="contained"
                size="large"
                startIcon={<Add />}
                onClick={openCreateDialog}
                sx={{
                  bgcolor: 'rgba(255,255,255,0.2)',
                  color: 'white',
                  backdropFilter: 'blur(10px)',
                  '&:hover': {
                    bgcolor: 'rgba(255,255,255,0.3)',
                  },
                }}
              >
                Add Country
              </Button>
            </Zoom>
          </Box>
        </CardContent>
      </Card>

      {/* Search and Filters */}
      <Card sx={{ mb: 3 }} elevation={2}>
        <CardContent>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
            <TextField
              placeholder="Search countries by name or code..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyPress={handleSearchKeyPress}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search color="primary" />
                  </InputAdornment>
                ),
              }}
              sx={{ flexGrow: 1, minWidth: 300 }}
              variant="outlined"
            />
            <Button
              variant="contained"
              onClick={handleSearch}
              startIcon={<Search />}
              sx={{ minWidth: 120 }}
            >
              Search
            </Button>
            <Button
              variant="outlined"
              onClick={() => {
                setSearchTerm('');
                loadCountries('');
              }}
              startIcon={<Refresh />}
              sx={{ minWidth: 120 }}
            >
              Reset
            </Button>
            <Button
              variant="outlined"
              startIcon={<FilterList />}
              sx={{ minWidth: 120 }}
            >
              Filter
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Results Summary */}
      {!loading && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            <Badge badgeContent={countries.length} color="primary" sx={{ mr: 1 }}>
              <Language />
            </Badge>
            Showing {countries.length} countries
          </Typography>
        </Box>
      )}

      {/* Professional Countries Table */}
      {loading ? (
        renderSkeleton()
      ) : countries.length === 0 ? (
        <Card elevation={2}>
          <CardContent sx={{ textAlign: 'center', py: 8 }}>
            <Fade in timeout={800}>
              <Box>
                <Avatar sx={{ bgcolor: 'primary.50', color: 'primary.main', width: 80, height: 80, mx: 'auto', mb: 3 }}>
                  <Public sx={{ fontSize: 40 }} />
                </Avatar>
                <Typography variant="h6" gutterBottom>
                  No countries found
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  {searchTerm ? 'Try adjusting your search criteria' : 'Get started by creating your first country'}
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  onClick={openCreateDialog}
                  size="large"
                >
                  Add First Country
                </Button>
              </Box>
            </Fade>
          </CardContent>
        </Card>
      ) : (
        <TableContainer component={Paper} elevation={2}>
          <Table>
            <TableHead>
              <TableRow sx={{ bgcolor: 'grey.50' }}>
                <TableCell sx={{ fontWeight: 600 }}>Country</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>Code</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>Phone Code</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>Currency</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>Status</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>Created</TableCell>
                <TableCell align="right" sx={{ fontWeight: 600 }}>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {countries.map((country, index) => (
                <Fade in timeout={300 + index * 100} key={country.id}>
                  <TableRow
                    hover
                    sx={{
                      '&:hover': {
                        bgcolor: 'primary.50',
                        transform: 'scale(1.001)',
                        transition: 'all 0.2s ease-in-out'
                      }
                    }}
                  >
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar sx={{ bgcolor: 'primary.main', width: 40, height: 40 }}>
                          <Flag />
                        </Avatar>
                        <Box>
                          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                            {country.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            ID: {country.id}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={country.code}
                        size="small"
                        color="primary"
                        variant="outlined"
                        sx={{ fontWeight: 600 }}
                      />
                    </TableCell>
                    <TableCell>
                      {country.phone_code ? (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Phone fontSize="small" color="action" />
                          <Typography variant="body2">{country.phone_code}</Typography>
                        </Box>
                      ) : (
                        <Typography variant="body2" color="text.secondary">—</Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      {country.currency ? (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <AttachMoney fontSize="small" color="action" />
                          <Typography variant="body2">{country.currency}</Typography>
                        </Box>
                      ) : (
                        <Typography variant="body2" color="text.secondary">—</Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={country.is_active ? 'Active' : 'Inactive'}
                        color={country.is_active ? 'success' : 'error'}
                        size="small"
                        icon={country.is_active ? <CheckCircle /> : <Cancel />}
                        variant={country.is_active ? 'filled' : 'outlined'}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {new Date(country.created_at).toLocaleDateString()}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {new Date(country.created_at).toLocaleTimeString()}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      <Tooltip title="More actions">
                        <IconButton
                          onClick={(e) => handleMenuOpen(e, country)}
                          size="small"
                          sx={{
                            '&:hover': {
                              bgcolor: 'primary.50',
                              color: 'primary.main'
                            }
                          }}
                        >
                          <MoreVert />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                </Fade>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Professional Action Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
        PaperProps={{
          elevation: 8,
          sx: {
            mt: 1,
            minWidth: 180,
            '& .MuiMenuItem-root': {
              px: 2,
              py: 1,
              borderRadius: 1,
              mx: 1,
              my: 0.5,
            },
          },
        }}
      >
        <MenuItem onClick={() => selectedCountry && openViewDialog(selectedCountry)}>
          <Visibility sx={{ mr: 2, fontSize: 20 }} />
          <Typography variant="body2">View Details</Typography>
        </MenuItem>
        <MenuItem onClick={() => selectedCountry && openEditDialog(selectedCountry)}>
          <Edit sx={{ mr: 2, fontSize: 20 }} />
          <Typography variant="body2">Edit Country</Typography>
        </MenuItem>
        <Divider sx={{ my: 1 }} />
        <MenuItem
          onClick={openDeleteDialog}
          sx={{
            color: 'error.main',
            '&:hover': { bgcolor: 'error.50' }
          }}
        >
          <Delete sx={{ mr: 2, fontSize: 20 }} />
          <Typography variant="body2">Delete Country</Typography>
        </MenuItem>
      </Menu>

      {/* Professional Create Dialog */}
      <Dialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          elevation: 24,
          sx: { borderRadius: 3 }
        }}
      >
        <DialogTitle sx={{ pb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar sx={{ bgcolor: 'primary.main' }}>
              <Add />
            </Avatar>
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                Create New Country
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Add a new country to the administrative hierarchy
              </Typography>
            </Box>
          </Box>
        </DialogTitle>
        <Divider />
        <DialogContent sx={{ pt: 3 }}>
          <Stack spacing={3}>
            <TextField
              label="Country Name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              error={!!formErrors.name}
              helperText={formErrors.name || 'Enter the full country name'}
              fullWidth
              required
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Public color="action" />
                  </InputAdornment>
                ),
              }}
            />

            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
              <TextField
                label="Country Code"
                value={formData.code}
                onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
                error={!!formErrors.code}
                helperText={formErrors.code || 'e.g., ET, US, GB'}
                required
                inputProps={{ maxLength: 3 }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Flag color="action" />
                    </InputAdornment>
                  ),
                }}
              />

              <TextField
                label="Phone Code"
                value={formData.phone_code}
                onChange={(e) => setFormData({ ...formData, phone_code: e.target.value })}
                error={!!formErrors.phone_code}
                helperText={formErrors.phone_code || 'e.g., +251, +1, +44'}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Phone color="action" />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>

            <TextField
              label="Currency Code"
              value={formData.currency}
              onChange={(e) => setFormData({ ...formData, currency: e.target.value.toUpperCase() })}
              error={!!formErrors.currency}
              helperText={formErrors.currency || 'e.g., ETB, USD, GBP'}
              inputProps={{ maxLength: 3 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <AttachMoney color="action" />
                  </InputAdornment>
                ),
              }}
            />

            <FormControlLabel
              control={
                <Switch
                  checked={formData.is_active}
                  onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                  color="primary"
                />
              }
              label={
                <Box>
                  <Typography variant="body2">Active Status</Typography>
                  <Typography variant="caption" color="text.secondary">
                    {formData.is_active ? 'Country is active and visible' : 'Country is inactive and hidden'}
                  </Typography>
                </Box>
              }
            />
          </Stack>
        </DialogContent>
        <Divider />
        <DialogActions sx={{ p: 3, gap: 1 }}>
          <Button
            onClick={() => setCreateDialogOpen(false)}
            variant="outlined"
            size="large"
          >
            Cancel
          </Button>
          <Button
            onClick={handleCreate}
            variant="contained"
            size="large"
            disabled={submitting}
            startIcon={submitting ? <CircularProgress size={20} /> : <Add />}
          >
            {submitting ? 'Creating...' : 'Create Country'}
          </Button>
        </DialogActions>
      </Dialog>
      {/* Professional Edit Dialog */}
      <Dialog
        open={editDialogOpen}
        onClose={() => setEditDialogOpen(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          elevation: 24,
          sx: { borderRadius: 3 }
        }}
      >
        <DialogTitle sx={{ pb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar sx={{ bgcolor: 'warning.main' }}>
              <Edit />
            </Avatar>
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                Edit Country
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Update country information
              </Typography>
            </Box>
          </Box>
        </DialogTitle>
        <Divider />
        <DialogContent sx={{ pt: 3 }}>
          <Stack spacing={3}>
            <TextField
              label="Country Name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              error={!!formErrors.name}
              helperText={formErrors.name || 'Enter the full country name'}
              fullWidth
              required
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Public color="action" />
                  </InputAdornment>
                ),
              }}
            />

            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
              <TextField
                label="Country Code"
                value={formData.code}
                onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
                error={!!formErrors.code}
                helperText={formErrors.code || 'e.g., ET, US, GB'}
                required
                inputProps={{ maxLength: 3 }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Flag color="action" />
                    </InputAdornment>
                  ),
                }}
              />

              <TextField
                label="Phone Code"
                value={formData.phone_code}
                onChange={(e) => setFormData({ ...formData, phone_code: e.target.value })}
                error={!!formErrors.phone_code}
                helperText={formErrors.phone_code || 'e.g., +251, +1, +44'}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Phone color="action" />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>

            <TextField
              label="Currency Code"
              value={formData.currency}
              onChange={(e) => setFormData({ ...formData, currency: e.target.value.toUpperCase() })}
              error={!!formErrors.currency}
              helperText={formErrors.currency || 'e.g., ETB, USD, GBP'}
              inputProps={{ maxLength: 3 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <AttachMoney color="action" />
                  </InputAdornment>
                ),
              }}
            />

            <FormControlLabel
              control={
                <Switch
                  checked={formData.is_active}
                  onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                  color="primary"
                />
              }
              label={
                <Box>
                  <Typography variant="body2">Active Status</Typography>
                  <Typography variant="caption" color="text.secondary">
                    {formData.is_active ? 'Country is active and visible' : 'Country is inactive and hidden'}
                  </Typography>
                </Box>
              }
            />
          </Stack>
        </DialogContent>
        <Divider />
        <DialogActions sx={{ p: 3, gap: 1 }}>
          <Button
            onClick={() => setEditDialogOpen(false)}
            variant="outlined"
            size="large"
          >
            Cancel
          </Button>
          <Button
            onClick={handleEdit}
            variant="contained"
            size="large"
            disabled={submitting}
            startIcon={submitting ? <CircularProgress size={20} /> : <Edit />}
          >
            {submitting ? 'Updating...' : 'Update Country'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Professional Delete Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        maxWidth="sm"
        PaperProps={{
          elevation: 24,
          sx: { borderRadius: 3 }
        }}
      >
        <DialogTitle sx={{ pb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar sx={{ bgcolor: 'error.main' }}>
              <Delete />
            </Avatar>
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                Delete Country
              </Typography>
              <Typography variant="body2" color="text.secondary">
                This action cannot be undone
              </Typography>
            </Box>
          </Box>
        </DialogTitle>
        <Divider />
        <DialogContent sx={{ pt: 3 }}>
          <Alert severity="warning" sx={{ mb: 3 }}>
            <Typography variant="body2" sx={{ fontWeight: 600 }}>
              Warning: This will permanently delete the country and all related data
            </Typography>
          </Alert>
          <Typography variant="body1">
            Are you sure you want to delete <strong>"{selectedCountry?.name}"</strong>?
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            This will also affect all regions, zones, cities, subcities, and kebeles within this country.
          </Typography>
        </DialogContent>
        <Divider />
        <DialogActions sx={{ p: 3, gap: 1 }}>
          <Button
            onClick={() => setDeleteDialogOpen(false)}
            variant="outlined"
            size="large"
          >
            Cancel
          </Button>
          <Button
            onClick={handleDelete}
            variant="contained"
            color="error"
            size="large"
            disabled={submitting}
            startIcon={submitting ? <CircularProgress size={20} /> : <Delete />}
          >
            {submitting ? 'Deleting...' : 'Delete Country'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CountriesTab;
