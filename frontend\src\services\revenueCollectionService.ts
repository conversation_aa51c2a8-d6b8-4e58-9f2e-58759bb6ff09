/**
 * Revenue Collection Service
 * 
 * This service provides API methods for managing revenue collection data
 * including categories, sources, periods, collections, and summaries.
 */

import api from './api';

// Base URL for revenue collection endpoints
const BASE_URL = '/revenue-collection/api';

// Types for Revenue Collection
export interface RegionalCategory {
  id: string;
  name: string;
  code: string;
  description: string;
  is_active: boolean;
  revenue_sources_count: number;
  created_by: string;
  created_by_name: string;
  created_at: string;
  updated_at: string;
}

export interface CityServiceCategory {
  id: string;
  name: string;
  code: string;
  description: string;
  is_active: boolean;
  revenue_sources_count: number;
  created_by: string;
  created_by_name: string;
  created_at: string;
  updated_at: string;
}

export interface RegionalRevenueSource {
  id: string;
  name: string;
  code: string;
  description: string;
  is_active: boolean;
  category: string;
  category_name: string;
  category_code: string;
  collections_count: number;
  created_by: string;
  created_by_name: string;
  created_at: string;
  updated_at: string;
}

export interface CityServiceRevenueSource {
  id: string;
  name: string;
  code: string;
  description: string;
  is_active: boolean;
  category: string;
  category_name: string;
  category_code: string;
  collections_count: number;
  created_by: string;
  created_by_name: string;
  created_at: string;
  updated_at: string;
}

export interface RevenuePeriod {
  id: string;
  name: string;
  code: string;
  start_date: string;
  end_date: string;
  is_closed: boolean;
  is_current: boolean;
  collections_count: {
    regional: number;
    city_service: number;
    total: number;
  };
  total_revenue: {
    regional: number;
    city_service: number;
    grand_total: number;
  };
  created_by: string;
  created_by_name: string;
  created_at: string;
  updated_at: string;
}

export interface RegionalRevenueCollection {
  id: string;
  individual_taxpayer?: string;
  organization_taxpayer?: string;
  taxpayer_name: string;
  taxpayer_tin: string;
  taxpayer_type: 'individual' | 'organization';
  revenue_source: string;
  revenue_source_name: string;
  revenue_source_code: string;
  category_name: string;
  period: string;
  period_name: string;
  amount: number;
  paid_amount: number;
  outstanding_amount: number;
  collection_date: string;
  payment_date?: string;
  due_date?: string;
  payment_status: 'PENDING' | 'PAID' | 'OVERDUE' | 'PARTIAL' | 'CANCELLED';
  is_fully_paid: boolean;
  is_overdue: boolean;
  days_overdue: number;
  receipt_number: string;
  notes: string;
  recorded_by: string;
  recorded_by_name: string;
  recorded_date: string;
  last_modified_by?: string;
  last_modified_by_name?: string;
  last_modified: string;
}

export interface CityServiceRevenueCollection {
  id: string;
  individual_taxpayer?: string;
  organization_taxpayer?: string;
  taxpayer_name: string;
  taxpayer_tin: string;
  taxpayer_type: 'individual' | 'organization';
  revenue_source: string;
  revenue_source_name: string;
  revenue_source_code: string;
  category_name: string;
  period: string;
  period_name: string;
  amount: number;
  paid_amount: number;
  outstanding_amount: number;
  collection_date: string;
  payment_date?: string;
  due_date?: string;
  payment_status: 'PENDING' | 'PAID' | 'OVERDUE' | 'PARTIAL' | 'CANCELLED';
  is_fully_paid: boolean;
  is_overdue: boolean;
  days_overdue: number;
  receipt_number: string;
  notes: string;
  recorded_by: string;
  recorded_by_name: string;
  recorded_date: string;
  last_modified_by?: string;
  last_modified_by_name?: string;
  last_modified: string;
}

export interface RevenueSummary {
  id: string;
  period: string;
  period_name: string;
  country?: string;
  region?: string;
  zone?: string;
  city?: string;
  subcity?: string;
  kebele?: string;
  location_display: string;
  location_hierarchy: {
    country?: { id: string; name: string };
    region?: { id: string; name: string };
    zone?: { id: string; name: string };
    city?: { id: string; name: string };
    subcity?: { id: string; name: string };
    kebele?: { id: string; name: string };
  };
  regional_total: number;
  city_service_total: number;
  grand_total: number;
  summary_date: string;
  last_updated: string;
  created_by: string;
  created_by_name: string;
}

export interface TaxpayerPaymentSummary {
  id: string;
  individual_taxpayer?: string;
  organization_taxpayer?: string;
  taxpayer_name: string;
  taxpayer_tin: string;
  taxpayer_type: 'individual' | 'organization';
  period: string;
  period_name: string;
  total_assessed: number;
  total_paid: number;
  total_outstanding: number;
  total_overdue: number;
  total_collections: number;
  paid_collections: number;
  overdue_collections: number;
  last_payment_date?: string;
  next_due_date?: string;
  payment_completion_rate: number;
  created_at: string;
  updated_at: string;
  created_by: string;
}

// Create types for form data
export interface RegionalCategoryCreate {
  name: string;
  code: string;
  description?: string;
  is_active?: boolean;
}

export interface CityServiceCategoryCreate {
  name: string;
  code: string;
  description?: string;
  is_active?: boolean;
}

export interface RegionalRevenueSourceCreate {
  category: string;
  name: string;
  code: string;
  description?: string;
  is_active?: boolean;
}

export interface CityServiceRevenueSourceCreate {
  category: string;
  name: string;
  code: string;
  description?: string;
  is_active?: boolean;
}

export interface RevenuePeriodCreate {
  name: string;
  code: string;
  start_date: string;
  end_date: string;
  is_closed?: boolean;
}

export interface RegionalRevenueCollectionCreate {
  individual_taxpayer?: string;
  organization_taxpayer?: string;
  revenue_source: string;
  period: string;
  amount: number;
  paid_amount?: number;
  collection_date: string;
  payment_date?: string;
  due_date?: string;
  payment_status?: 'PENDING' | 'PAID' | 'OVERDUE' | 'PARTIAL' | 'CANCELLED';
  receipt_number?: string;
  notes?: string;
}

export interface CityServiceRevenueCollectionCreate {
  individual_taxpayer?: string;
  organization_taxpayer?: string;
  revenue_source: string;
  period: string;
  amount: number;
  paid_amount?: number;
  collection_date: string;
  payment_date?: string;
  due_date?: string;
  payment_status?: 'PENDING' | 'PAID' | 'OVERDUE' | 'PARTIAL' | 'CANCELLED';
  receipt_number?: string;
  notes?: string;
}

// API Response types
export interface ApiResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

class RevenueCollectionService {
  // Regional Categories
  async getRegionalCategories(params?: any): Promise<ApiResponse<RegionalCategory>> {
    const response = await api.get(`${BASE_URL}/regional-categories/`, { params });
    return response.data;
  }

  async getRegionalCategory(id: string): Promise<RegionalCategory> {
    const response = await api.get(`${BASE_URL}/regional-categories/${id}/`);
    return response.data;
  }

  async createRegionalCategory(data: RegionalCategoryCreate): Promise<RegionalCategory> {
    const response = await api.post(`${BASE_URL}/regional-categories/`, data);
    return response.data;
  }

  async updateRegionalCategory(id: string, data: Partial<RegionalCategoryCreate>): Promise<RegionalCategory> {
    const response = await api.patch(`${BASE_URL}/regional-categories/${id}/`, data);
    return response.data;
  }

  async deleteRegionalCategory(id: string): Promise<void> {
    await api.delete(`${BASE_URL}/regional-categories/${id}/`);
  }

  async getRegionalCategoryRevenueSources(id: string): Promise<RegionalRevenueSource[]> {
    const response = await api.get(`${BASE_URL}/regional-categories/${id}/revenue_sources/`);
    return response.data;
  }

  // City Service Categories
  async getCityServiceCategories(params?: any): Promise<ApiResponse<CityServiceCategory>> {
    const response = await api.get(`${BASE_URL}/city-service-categories/`, { params });
    return response.data;
  }

  async getCityServiceCategory(id: string): Promise<CityServiceCategory> {
    const response = await api.get(`${BASE_URL}/city-service-categories/${id}/`);
    return response.data;
  }

  async createCityServiceCategory(data: CityServiceCategoryCreate): Promise<CityServiceCategory> {
    const response = await api.post(`${BASE_URL}/city-service-categories/`, data);
    return response.data;
  }

  async updateCityServiceCategory(id: string, data: Partial<CityServiceCategoryCreate>): Promise<CityServiceCategory> {
    const response = await api.patch(`${BASE_URL}/city-service-categories/${id}/`, data);
    return response.data;
  }

  async deleteCityServiceCategory(id: string): Promise<void> {
    await api.delete(`${BASE_URL}/city-service-categories/${id}/`);
  }

  async getCityServiceCategoryRevenueSources(id: string): Promise<CityServiceRevenueSource[]> {
    const response = await api.get(`${BASE_URL}/city-service-categories/${id}/revenue_sources/`);
    return response.data;
  }

  // Regional Revenue Sources
  async getRegionalRevenueSources(params?: any): Promise<ApiResponse<RegionalRevenueSource>> {
    const response = await api.get(`${BASE_URL}/regional-revenue-sources/`, { params });
    return response.data;
  }

  async getRegionalRevenueSource(id: string): Promise<RegionalRevenueSource> {
    const response = await api.get(`${BASE_URL}/regional-revenue-sources/${id}/`);
    return response.data;
  }

  async createRegionalRevenueSource(data: RegionalRevenueSourceCreate): Promise<RegionalRevenueSource> {
    const response = await api.post(`${BASE_URL}/regional-revenue-sources/`, data);
    return response.data;
  }

  async updateRegionalRevenueSource(id: string, data: Partial<RegionalRevenueSourceCreate>): Promise<RegionalRevenueSource> {
    const response = await api.patch(`${BASE_URL}/regional-revenue-sources/${id}/`, data);
    return response.data;
  }

  async deleteRegionalRevenueSource(id: string): Promise<void> {
    await api.delete(`${BASE_URL}/regional-revenue-sources/${id}/`);
  }

  // City Service Revenue Sources
  async getCityServiceRevenueSources(params?: any): Promise<ApiResponse<CityServiceRevenueSource>> {
    const response = await api.get(`${BASE_URL}/city-service-revenue-sources/`, { params });
    return response.data;
  }

  async getCityServiceRevenueSource(id: string): Promise<CityServiceRevenueSource> {
    const response = await api.get(`${BASE_URL}/city-service-revenue-sources/${id}/`);
    return response.data;
  }

  async createCityServiceRevenueSource(data: CityServiceRevenueSourceCreate): Promise<CityServiceRevenueSource> {
    const response = await api.post(`${BASE_URL}/city-service-revenue-sources/`, data);
    return response.data;
  }

  async updateCityServiceRevenueSource(id: string, data: Partial<CityServiceRevenueSourceCreate>): Promise<CityServiceRevenueSource> {
    const response = await api.patch(`${BASE_URL}/city-service-revenue-sources/${id}/`, data);
    return response.data;
  }

  async deleteCityServiceRevenueSource(id: string): Promise<void> {
    await api.delete(`${BASE_URL}/city-service-revenue-sources/${id}/`);
  }

  // Revenue Periods
  async getRevenuePeriods(params?: any): Promise<ApiResponse<RevenuePeriod>> {
    const response = await api.get(`${BASE_URL}/periods/`, { params });
    return response.data;
  }

  async getRevenuePeriod(id: string): Promise<RevenuePeriod> {
    const response = await api.get(`${BASE_URL}/periods/${id}/`);
    return response.data;
  }

  async createRevenuePeriod(data: RevenuePeriodCreate): Promise<RevenuePeriod> {
    const response = await api.post(`${BASE_URL}/periods/`, data);
    return response.data;
  }

  async updateRevenuePeriod(id: string, data: Partial<RevenuePeriodCreate>): Promise<RevenuePeriod> {
    const response = await api.patch(`${BASE_URL}/periods/${id}/`, data);
    return response.data;
  }

  async deleteRevenuePeriod(id: string): Promise<void> {
    await api.delete(`${BASE_URL}/periods/${id}/`);
  }

  async getCurrentPeriod(): Promise<RevenuePeriod> {
    const response = await api.get(`${BASE_URL}/periods/current/`);
    return response.data;
  }

  async getPeriodCollectionsSummary(id: string): Promise<any> {
    const response = await api.get(`${BASE_URL}/periods/${id}/collections_summary/`);
    return response.data;
  }

  // Regional Revenue Collections
  async getRegionalRevenueCollections(params?: any): Promise<ApiResponse<RegionalRevenueCollection>> {
    const response = await api.get(`${BASE_URL}/regional-collections/`, { params });
    return response.data;
  }

  async getRegionalRevenueCollection(id: string): Promise<RegionalRevenueCollection> {
    const response = await api.get(`${BASE_URL}/regional-collections/${id}/`);
    return response.data;
  }

  async createRegionalRevenueCollection(data: RegionalRevenueCollectionCreate): Promise<RegionalRevenueCollection> {
    const response = await api.post(`${BASE_URL}/regional-collections/`, data);
    return response.data;
  }

  async updateRegionalRevenueCollection(id: string, data: Partial<RegionalRevenueCollectionCreate>): Promise<RegionalRevenueCollection> {
    const response = await api.patch(`${BASE_URL}/regional-collections/${id}/`, data);
    return response.data;
  }

  async deleteRegionalRevenueCollection(id: string): Promise<void> {
    await api.delete(`${BASE_URL}/regional-collections/${id}/`);
  }

  // City Service Revenue Collections
  async getCityServiceRevenueCollections(params?: any): Promise<ApiResponse<CityServiceRevenueCollection>> {
    const response = await api.get(`${BASE_URL}/city-service-collections/`, { params });
    return response.data;
  }

  async getCityServiceRevenueCollection(id: string): Promise<CityServiceRevenueCollection> {
    const response = await api.get(`${BASE_URL}/city-service-collections/${id}/`);
    return response.data;
  }

  async createCityServiceRevenueCollection(data: CityServiceRevenueCollectionCreate): Promise<CityServiceRevenueCollection> {
    const response = await api.post(`${BASE_URL}/city-service-collections/`, data);
    return response.data;
  }

  async updateCityServiceRevenueCollection(id: string, data: Partial<CityServiceRevenueCollectionCreate>): Promise<CityServiceRevenueCollection> {
    const response = await api.patch(`${BASE_URL}/city-service-collections/${id}/`, data);
    return response.data;
  }

  async deleteCityServiceRevenueCollection(id: string): Promise<void> {
    await api.delete(`${BASE_URL}/city-service-collections/${id}/`);
  }

  // Revenue Summaries
  async getRevenueSummaries(params?: any): Promise<ApiResponse<RevenueSummary>> {
    const response = await api.get(`${BASE_URL}/summaries/`, { params });
    return response.data;
  }

  async getRevenueSummary(id: string): Promise<RevenueSummary> {
    const response = await api.get(`${BASE_URL}/summaries/${id}/`);
    return response.data;
  }

  async generateRevenueSummaries(data: {
    period_id: string;
    location_level: 'region' | 'city' | 'subcity' | 'kebele' | 'all';
    location_id?: string;
  }): Promise<any> {
    const response = await api.post(`${BASE_URL}/summaries/generate_summaries/`, data);
    return response.data;
  }

  async updateSummaryTotals(id: string): Promise<any> {
    const response = await api.post(`${BASE_URL}/summaries/${id}/update_totals/`);
    return response.data;
  }

  async getRevenueAnalytics(params?: any): Promise<any> {
    const response = await api.get(`${BASE_URL}/summaries/analytics/`, { params });
    return response.data;
  }

  // Taxpayer Payment Summaries
  async getTaxpayerPaymentSummaries(params?: any): Promise<ApiResponse<TaxpayerPaymentSummary>> {
    const response = await api.get(`${BASE_URL}/taxpayer-summaries/`, { params });
    return response.data;
  }

  async getTaxpayerPaymentSummary(id: string): Promise<TaxpayerPaymentSummary> {
    const response = await api.get(`${BASE_URL}/taxpayer-summaries/${id}/`);
    return response.data;
  }

  async getTaxpayerPaymentHistory(taxpayerId: string, taxpayerType: 'individual' | 'organization'): Promise<{
    summaries: TaxpayerPaymentSummary[];
    collections: (RegionalRevenueCollection | CityServiceRevenueCollection)[];
    totals: {
      total_assessed: number;
      total_paid: number;
      total_outstanding: number;
      total_overdue: number;
    };
  }> {
    const params = taxpayerType === 'individual'
      ? { individual_taxpayer: taxpayerId }
      : { organization_taxpayer: taxpayerId };

    const [summariesResponse, regionalResponse, cityServiceResponse] = await Promise.all([
      this.getTaxpayerPaymentSummaries(params),
      this.getRegionalRevenueCollections(params),
      this.getCityServiceRevenueCollections(params),
    ]);

    const collections = [
      ...regionalResponse.results.map(c => ({ ...c, type: 'regional' as const })),
      ...cityServiceResponse.results.map(c => ({ ...c, type: 'city_service' as const })),
    ];

    const totals = {
      total_assessed: collections.reduce((sum, c) => sum + (Number(c.amount) || 0), 0),
      total_paid: collections.reduce((sum, c) => sum + (Number(c.paid_amount) || 0), 0),
      total_outstanding: collections.reduce((sum, c) => sum + (Number(c.outstanding_amount) || 0), 0),
      total_overdue: collections.filter(c => c.is_overdue).reduce((sum, c) => sum + (Number(c.outstanding_amount) || 0), 0),
    };

    return {
      summaries: summariesResponse.results,
      collections,
      totals,
    };
  }

  async updateTaxpayerPaymentSummary(id: string): Promise<TaxpayerPaymentSummary> {
    const response = await api.post(`${BASE_URL}/taxpayer-summaries/${id}/update_summary/`);
    return response.data;
  }

  // Payment Processing Methods
  async processPayment(
    collectionId: string,
    collectionType: 'regional' | 'city-service',
    paymentAmount: number,
    paymentDate?: string
  ): Promise<any> {
    const endpoint = collectionType === 'regional'
      ? `${BASE_URL}/regional-collections/${collectionId}/process_payment/`
      : `${BASE_URL}/city-service-collections/${collectionId}/process_payment/`;

    const response = await api.post(endpoint, {
      payment_amount: paymentAmount,
      payment_date: paymentDate
    });
    return response.data;
  }

  async calculatePenalties(
    collectionId: string,
    collectionType: 'regional' | 'city-service'
  ): Promise<any> {
    const endpoint = collectionType === 'regional'
      ? `${BASE_URL}/regional-collections/${collectionId}/calculate_penalties/`
      : `${BASE_URL}/city-service-collections/${collectionId}/calculate_penalties/`;

    const response = await api.post(endpoint);
    return response.data;
  }

  async getPaymentBreakdown(
    collectionId: string,
    collectionType: 'regional' | 'city-service'
  ): Promise<any> {
    const endpoint = collectionType === 'regional'
      ? `${BASE_URL}/regional-collections/${collectionId}/payment_breakdown/`
      : `${BASE_URL}/city-service-collections/${collectionId}/payment_breakdown/`;

    const response = await api.get(endpoint);
    return response.data;
  }

  async simulatePayment(
    collectionId: string,
    collectionType: 'regional' | 'city-service',
    paymentAmount: number
  ): Promise<any> {
    const endpoint = collectionType === 'regional'
      ? `${BASE_URL}/regional-collections/${collectionId}/simulate_payment/`
      : `${BASE_URL}/city-service-collections/${collectionId}/simulate_payment/`;

    const response = await api.post(endpoint, {
      payment_amount: paymentAmount
    });
    return response.data;
  }

  async bulkUpdateOverdueCollections(): Promise<any> {
    const response = await api.post(`${BASE_URL}/summaries/bulk_update_overdue/`);
    return response.data;
  }
}

export default new RevenueCollectionService();
