import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  TextField,
  InputAdornment,
  Card,
  CardContent,
  CardActions,
  Grid,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Search,
  Add,
  Edit,
  Delete,
  Category,
  Refresh,
} from '@mui/icons-material';
import { useNotification } from '../../../contexts/NotificationContext';
// Import the service
import taxpayerService from '../../../services/taxpayerService';
import { normalizeCode, createCodeBlurHandler } from '../../../utils/codeUtils';

// Define interfaces locally to avoid import issues
interface BusinessSector {
  id: string;
  code: string;
  name: string;
  description: string;
  is_active: boolean;
  sub_sectors_count: number;
  created_at: string;
  updated_at: string;
}

interface BusinessSectorCreate {
  code: string;
  name: string;
  description?: string;
}

interface BusinessSectorsTabProps {
  onDataChange?: () => void;
}

const BusinessSectorsTab: React.FC<BusinessSectorsTabProps> = ({ onDataChange }) => {
  const { showNotification } = useNotification();
  
  // State
  const [sectors, setSectors] = useState<BusinessSector[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  
  // Form state
  const [formOpen, setFormOpen] = useState(false);
  const [editingSector, setEditingSector] = useState<BusinessSector | null>(null);
  const [formData, setFormData] = useState<BusinessSectorCreate>({
    code: '',
    name: '',
    description: '',
  });
  const [formLoading, setFormLoading] = useState(false);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  
  // Delete state
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [sectorToDelete, setSectorToDelete] = useState<BusinessSector | null>(null);

  useEffect(() => {
    loadSectors();
  }, [searchTerm]);

  const loadSectors = async () => {
    try {
      setLoading(true);
      const params = searchTerm ? { search: searchTerm } : {};
      const response = await taxpayerService.getBusinessSectors(params);
      setSectors(response.results);
    } catch (error) {
      console.error('Failed to load business sectors:', error);
      showNotification('Failed to load business sectors', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = () => {
    setEditingSector(null);
    setFormData({ code: '', name: '', description: '' });
    setFormErrors({});
    setFormOpen(true);
  };

  const handleEdit = (sector: BusinessSector) => {
    setEditingSector(sector);
    setFormData({
      code: sector.code,
      name: sector.name,
      description: sector.description,
    });
    setFormErrors({});
    setFormOpen(true);
  };

  const handleDelete = (sector: BusinessSector) => {
    setSectorToDelete(sector);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!sectorToDelete) return;

    try {
      await taxpayerService.deleteBusinessSector(sectorToDelete.id);
      showNotification('Business sector deleted successfully', 'success');
      loadSectors();
      if (onDataChange) onDataChange();
    } catch (error) {
      console.error('Failed to delete sector:', error);
      showNotification('Failed to delete sector', 'error');
    } finally {
      setDeleteDialogOpen(false);
      setSectorToDelete(null);
    }
  };

  const handleFormSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!formData.code || !formData.name) {
      setFormErrors({
        code: !formData.code ? 'Code is required' : '',
        name: !formData.name ? 'Name is required' : '',
      });
      return;
    }

    try {
      setFormLoading(true);
      
      if (editingSector) {
        await taxpayerService.updateBusinessSector(editingSector.id, formData);
        showNotification('Business sector updated successfully', 'success');
      } else {
        await taxpayerService.createBusinessSector(formData);
        showNotification('Business sector created successfully', 'success');
      }
      
      setFormOpen(false);
      loadSectors();
      if (onDataChange) onDataChange();
    } catch (error: any) {
      console.error('Failed to save sector:', error);
      
      if (error.response?.data) {
        setFormErrors(error.response.data);
      } else {
        showNotification('Failed to save sector', 'error');
      }
    } finally {
      setFormLoading(false);
    }
  };

  const SectorCard = ({ sector }: { sector: BusinessSector }) => (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <CardContent sx={{ flexGrow: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Category sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h6" component="div">
            {sector.code}
          </Typography>
        </Box>
        
        <Typography variant="h6" gutterBottom>
          {sector.name}
        </Typography>
        
        {sector.description && (
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {sector.description}
          </Typography>
        )}
        
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          <Chip 
            label={`${sector.sub_sectors_count} Sub-Sectors`} 
            size="small" 
            color="primary" 
            variant="outlined"
          />
          <Chip 
            label={sector.is_active ? 'Active' : 'Inactive'} 
            size="small" 
            color={sector.is_active ? 'success' : 'default'}
          />
        </Box>
      </CardContent>

      <CardActions>
        <Button
          size="small"
          startIcon={<Edit />}
          onClick={() => handleEdit(sector)}
        >
          Edit
        </Button>
        <Button
          size="small"
          color="error"
          startIcon={<Delete />}
          onClick={() => handleDelete(sector)}
        >
          Delete
        </Button>
      </CardActions>
    </Card>
  );

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" component="h2" sx={{ fontWeight: 600 }}>
          Business Sectors ({sectors.length})
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={loadSectors}
            disabled={loading}
            sx={{ borderRadius: 2 }}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={handleCreate}
            sx={{
              borderRadius: 2,
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
              }
            }}
          >
            Add Sector
          </Button>
        </Box>
      </Box>

      {/* Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <TextField
            fullWidth
            placeholder="Search business sectors..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
          />
        </CardContent>
      </Card>

      {/* Content */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
          <CircularProgress />
        </Box>
      ) : sectors.length === 0 ? (
        <Alert severity="info">
          No business sectors found. {searchTerm && 'Try adjusting your search or '}
          <Button onClick={handleCreate}>add the first one</Button>.
        </Alert>
      ) : (
        <Grid container spacing={3}>
          {sectors.map((sector) => (
            <Grid key={sector.id} size={{ xs: 12, sm: 6, md: 4 }}>
              <SectorCard sector={sector} />
            </Grid>
          ))}
        </Grid>
      )}

      {/* Form Dialog */}
      <Dialog
        open={formOpen}
        onClose={() => setFormOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {editingSector ? 'Edit Business Sector' : 'Add Business Sector'}
        </DialogTitle>
        <DialogContent>
          <Box component="form" onSubmit={handleFormSubmit} sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid size={{ xs: 12, md: 6 }}>
                <TextField
                  fullWidth
                  label="Code *"
                  value={formData.code}
                  onChange={(e) => setFormData({ ...formData, code: e.target.value })}
                  onBlur={createCodeBlurHandler(
                    (field, value) => setFormData(prev => ({ ...prev, [field]: value })),
                    'code'
                  )}
                  error={!!formErrors.code}
                  helperText={formErrors.code || 'Code will be automatically converted to uppercase'}
                  inputProps={{ maxLength: 10 }}
                />
              </Grid>
              <Grid size={{ xs: 12, md: 6 }}>
                <TextField
                  fullWidth
                  label="Name *"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  error={!!formErrors.name}
                  helperText={formErrors.name}
                />
              </Grid>
              <Grid size={{ xs: 12 }}>
                <TextField
                  fullWidth
                  label="Description"
                  multiline
                  rows={3}
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setFormOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleFormSubmit}
            variant="contained"
            disabled={formLoading}
          >
            {formLoading ? 'Saving...' : editingSector ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the business sector "{sectorToDelete?.name}"?
            This action cannot be undone and may affect related sub-sectors and taxpayers.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default BusinessSectorsTab;
