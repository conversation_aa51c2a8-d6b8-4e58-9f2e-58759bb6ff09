import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Link,
  Divider,
  Tooltip,
  IconButton,
  Popover,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
} from '@mui/material';
import {
  Copyright,
  Code,
  Person,
  School,
} from '@mui/icons-material';
import { useBrandingInfo } from '../../contexts/BrandingContext';

const Footer: React.FC = () => {
  const brandingInfo = useBrandingInfo();
  const currentYear = new Date().getFullYear();
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);

  const handleDeveloperClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  const developers = [
    { name: 'Tewodr<PERSON> Abebaw', role: 'Lead Developer' },
    { name: '<PERSON><PERSON><PERSON>', role: 'Backend Developer' },
    { name: '<PERSON><PERSON><PERSON>', role: 'Frontend Developer' },
  ];

  return (
    <Box
      component="footer"
      sx={{
        bgcolor: 'background.paper',
        borderTop: '1px solid',
        borderColor: 'divider',
        py: 2,
        mt: 'auto',
      }}
    >
      <Container maxWidth="lg">
        {/* Copyright and Developers */}
        <Box sx={{ textAlign: 'center' }}>
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 1,
              mb: 1
            }}
          >
            <Copyright sx={{ fontSize: 16 }} />
            {currentYear} {brandingInfo.name}. All rights reserved.
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
            <Code sx={{ fontSize: 16, color: 'text.secondary' }} />
            <Typography variant="body2" color="text.secondary">
              Developed by
            </Typography>

            <Tooltip title="Click to see development team">
              <IconButton
                size="small"
                onClick={handleDeveloperClick}
                sx={{
                  color: 'primary.main',
                  '&:hover': {
                    bgcolor: 'primary.50',
                    transform: 'scale(1.1)',
                  },
                  transition: 'all 0.2s ease-in-out',
                }}
              >
                <Person />
              </IconButton>
            </Tooltip>

            <Typography
              variant="body2"
              color="primary.main"
              sx={{
                fontWeight: 'medium',
                cursor: 'pointer',
                '&:hover': {
                  textDecoration: 'underline',
                },
              }}
              onClick={handleDeveloperClick}
            >
              University of Gondar Software Team
            </Typography>
          </Box>
        </Box>

        {/* Developer Team Popover */}
        <Popover
          open={open}
          anchorEl={anchorEl}
          onClose={handleClose}
          anchorOrigin={{
            vertical: 'top',
            horizontal: 'center',
          }}
          transformOrigin={{
            vertical: 'bottom',
            horizontal: 'center',
          }}
          PaperProps={{
            sx: {
              mt: -1,
              boxShadow: 3,
              borderRadius: 2,
              border: '1px solid',
              borderColor: 'divider',
            }
          }}
        >
          <Box sx={{ p: 2, minWidth: 280 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom sx={{ textAlign: 'center', color: 'primary.main' }}>
              Development Team
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2, justifyContent: 'center' }}>
              <School color="primary" />
              <Typography variant="body2" color="text.secondary">
                University of Gondar
              </Typography>
            </Box>
            <List dense>
              {developers.map((dev, index) => (
                <ListItem key={index} sx={{ py: 0.5 }}>
                  <ListItemIcon sx={{ minWidth: 36 }}>
                    <Person color="primary" />
                  </ListItemIcon>
                  <ListItemText
                    primary={dev.name}
                    secondary={dev.role}
                    primaryTypographyProps={{ fontWeight: 'medium', fontSize: '0.9rem' }}
                    secondaryTypographyProps={{ fontSize: '0.8rem' }}
                  />
                </ListItem>
              ))}
            </List>
          </Box>
        </Popover>
      </Container>
    </Box>
  );
};

export default Footer;
