# Generated by Django 5.2.3 on 2025-07-23 04:26

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("locations", "0002_initial"),
        (
            "taxpayers",
            "0003_individualtaxpayer_city_individualtaxpayer_country_and_more",
        ),
    ]

    operations = [
        migrations.AddField(
            model_name="file",
            name="linked_individual_taxpayer",
            field=models.ForeignKey(
                blank=True,
                help_text="Individual taxpayer linked to this file",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="linked_files",
                to="taxpayers.individualtaxpayer",
            ),
        ),
        migrations.AddField(
            model_name="file",
            name="linked_organization_taxpayer",
            field=models.ForeignKey(
                blank=True,
                help_text="Organization taxpayer linked to this file",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="linked_files",
                to="taxpayers.organizationtaxpayer",
            ),
        ),
    ]
