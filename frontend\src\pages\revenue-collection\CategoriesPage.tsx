/**
 * Revenue Categories Management Page
 * 
 * Manages both Regional and City Service revenue categories
 * with tabbed interface and CRUD operations.
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControlLabel,
  Switch,
  CircularProgress,
  Alert,
  Tooltip,
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Visibility,
  AccountBalance,
  Business,
} from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import revenueCollectionService from '../../services/revenueCollectionService';
import type {
  RegionalCategory,
  CityServiceCategory,
  RegionalCategoryCreate,
  CityServiceCategoryCreate,
} from '../../services/revenueCollectionService';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`category-tabpanel-${index}`}
      aria-labelledby={`category-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const CategoriesPage: React.FC = () => {
  const { showSuccess, showError } = useNotification();
  
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(true);
  const [regionalCategories, setRegionalCategories] = useState<RegionalCategory[]>([]);
  const [cityServiceCategories, setCityServiceCategories] = useState<CityServiceCategory[]>([]);
  
  // Dialog states
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogMode, setDialogMode] = useState<'create' | 'edit'>('create');
  const [dialogType, setDialogType] = useState<'regional' | 'city_service'>('regional');
  const [editingItem, setEditingItem] = useState<RegionalCategory | CityServiceCategory | null>(null);
  
  // Form states
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    description: '',
    is_active: true,
  });

  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      setLoading(true);
      const [regionalResponse, cityServiceResponse] = await Promise.all([
        revenueCollectionService.getRegionalCategories(),
        revenueCollectionService.getCityServiceCategories(),
      ]);
      
      setRegionalCategories(regionalResponse.results);
      setCityServiceCategories(cityServiceResponse.results);
    } catch (error) {
      console.error('Error loading categories:', error);
      showError('Failed to load categories');
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleOpenDialog = (
    mode: 'create' | 'edit',
    type: 'regional' | 'city_service',
    item?: RegionalCategory | CityServiceCategory
  ) => {
    setDialogMode(mode);
    setDialogType(type);
    setEditingItem(item || null);
    
    if (mode === 'edit' && item) {
      setFormData({
        name: item.name,
        code: item.code,
        description: item.description,
        is_active: item.is_active,
      });
    } else {
      setFormData({
        name: '',
        code: '',
        description: '',
        is_active: true,
      });
    }
    
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingItem(null);
    setFormData({
      name: '',
      code: '',
      description: '',
      is_active: true,
    });
  };

  const handleSubmit = async () => {
    try {
      if (dialogMode === 'create') {
        if (dialogType === 'regional') {
          await revenueCollectionService.createRegionalCategory(formData as RegionalCategoryCreate);
          showSuccess('Regional category created successfully');
        } else {
          await revenueCollectionService.createCityServiceCategory(formData as CityServiceCategoryCreate);
          showSuccess('City service category created successfully');
        }
      } else if (editingItem) {
        if (dialogType === 'regional') {
          await revenueCollectionService.updateRegionalCategory(editingItem.id, formData);
          showSuccess('Regional category updated successfully');
        } else {
          await revenueCollectionService.updateCityServiceCategory(editingItem.id, formData);
          showSuccess('City service category updated successfully');
        }
      }
      
      handleCloseDialog();
      loadCategories();
    } catch (error) {
      console.error('Error saving category:', error);
      showError('Failed to save category');
    }
  };

  const handleDelete = async (type: 'regional' | 'city_service', id: string) => {
    if (!window.confirm('Are you sure you want to delete this category?')) {
      return;
    }

    try {
      if (type === 'regional') {
        await revenueCollectionService.deleteRegionalCategory(id);
        showSuccess('Regional category deleted successfully');
      } else {
        await revenueCollectionService.deleteCityServiceCategory(id);
        showSuccess('City service category deleted successfully');
      }
      
      loadCategories();
    } catch (error) {
      console.error('Error deleting category:', error);
      showError('Failed to delete category');
    }
  };

  const renderCategoryTable = (
    categories: (RegionalCategory | CityServiceCategory)[],
    type: 'regional' | 'city_service'
  ) => (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Code</TableCell>
            <TableCell>Name</TableCell>
            <TableCell>Description</TableCell>
            <TableCell>Revenue Sources</TableCell>
            <TableCell>Status</TableCell>
            <TableCell>Created</TableCell>
            <TableCell>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {categories.map((category) => (
            <TableRow key={category.id}>
              <TableCell>
                <Typography variant="body2" fontWeight="medium">
                  {category.code}
                </Typography>
              </TableCell>
              <TableCell>
                <Typography variant="body2">
                  {category.name}
                </Typography>
              </TableCell>
              <TableCell>
                <Typography variant="body2" color="text.secondary">
                  {category.description || 'No description'}
                </Typography>
              </TableCell>
              <TableCell>
                <Chip
                  label={`${category.revenue_sources_count} sources`}
                  size="small"
                  color="primary"
                  variant="outlined"
                />
              </TableCell>
              <TableCell>
                <Chip
                  label={category.is_active ? 'Active' : 'Inactive'}
                  color={category.is_active ? 'success' : 'default'}
                  size="small"
                />
              </TableCell>
              <TableCell>
                <Typography variant="body2" color="text.secondary">
                  {new Date(category.created_at).toLocaleDateString()}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  by {category.created_by_name}
                </Typography>
              </TableCell>
              <TableCell>
                <Tooltip title="Edit">
                  <IconButton
                    size="small"
                    onClick={() => handleOpenDialog('edit', type, category)}
                  >
                    <Edit />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Delete">
                  <IconButton
                    size="small"
                    color="error"
                    onClick={() => handleDelete(type, category.id)}
                  >
                    <Delete />
                  </IconButton>
                </Tooltip>
              </TableCell>
            </TableRow>
          ))}
          {categories.length === 0 && (
            <TableRow>
              <TableCell colSpan={7} align="center">
                <Typography variant="body2" color="text.secondary">
                  No categories found
                </Typography>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </TableContainer>
  );

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Revenue Categories
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Manage regional and city service revenue categories
        </Typography>
      </Box>

      {/* Tabs */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab
              label="Regional Categories"
              icon={<AccountBalance />}
              iconPosition="start"
            />
            <Tab
              label="City Service Categories"
              icon={<Business />}
              iconPosition="start"
            />
          </Tabs>
        </Box>

        {/* Regional Categories Tab */}
        <TabPanel value={tabValue} index={0}>
          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">
              Regional Revenue Categories ({regionalCategories.length})
            </Typography>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => handleOpenDialog('create', 'regional')}
            >
              Add Regional Category
            </Button>
          </Box>
          {renderCategoryTable(regionalCategories, 'regional')}
        </TabPanel>

        {/* City Service Categories Tab */}
        <TabPanel value={tabValue} index={1}>
          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">
              City Service Revenue Categories ({cityServiceCategories.length})
            </Typography>
            <Button
              variant="contained"
              color="secondary"
              startIcon={<Add />}
              onClick={() => handleOpenDialog('create', 'city_service')}
            >
              Add City Service Category
            </Button>
          </Box>
          {renderCategoryTable(cityServiceCategories, 'city_service')}
        </TabPanel>
      </Card>

      {/* Create/Edit Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {dialogMode === 'create' ? 'Create' : 'Edit'} {' '}
          {dialogType === 'regional' ? 'Regional' : 'City Service'} Category
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <TextField
              fullWidth
              label="Category Name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              margin="normal"
              required
            />
            <TextField
              fullWidth
              label="Category Code"
              value={formData.code}
              onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
              margin="normal"
              required
              helperText="Unique code for this category (e.g., INCOME, BUSINESS)"
            />
            <TextField
              fullWidth
              label="Description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              margin="normal"
              multiline
              rows={3}
            />
            <FormControlLabel
              control={
                <Switch
                  checked={formData.is_active}
                  onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                />
              }
              label="Active"
              sx={{ mt: 2 }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={!formData.name || !formData.code}
          >
            {dialogMode === 'create' ? 'Create' : 'Update'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CategoriesPage;
