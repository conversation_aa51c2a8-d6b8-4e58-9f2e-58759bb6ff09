# Arada Revenue Office Document Management System

A comprehensive Django-React document management system designed specifically for the Gondar Arada Revenue Office, supporting both physical and digital document management with advanced tracking, barcode/QR code integration, and workflow management.

## 🏢 System Overview

This system provides:
- **Organization Management**: Multi-tenant support with organization profiles
- **Document Classification**: Configurable document types with metadata
- **Physical & Digital Modes**: Support for both physical and digital documents
- **Location Hierarchy**: Building → Shelf → Kent (Box) structure
- **Request/Return Workflow**: Complete document checkout and return process
- **Barcode/QR Integration**: Auto-generated codes for tracking
- **Role-Based Access**: Admin, Manager, Clerk, Auditor, Expert roles
- **Audit Logging**: Comprehensive activity tracking

## 🛠 Technology Stack

### Backend
- **Django 5.2.4** - Web framework
- **Django REST Framework** - API development
- **PostgreSQL** - Primary database
- **JWT Authentication** - Secure token-based auth
- **Python Barcode & QRCode** - Code generation
- **ReportLab** - PDF generation

### Frontend
- **React 19** with TypeScript
- **Vite** - Build tool and dev server
- **Material-UI (MUI)** - Component library
- **React Router** - Navigation
- **TanStack Query** - Data fetching
- **React Hook Form** - Form management

## 📁 Project Structure

```
Document Management/
├── backend/                 # Django backend
│   ├── arada_dms/          # Main project settings
│   ├── accounts/           # User management & authentication
│   ├── organizations/      # Organization profiles
│   ├── documents/          # Document management
│   ├── locations/          # Location hierarchy
│   ├── requests/           # Request/return workflow
│   ├── media/              # File uploads
│   ├── logs/               # Application logs
│   └── requirements.txt    # Python dependencies
├── frontend/               # React frontend
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom hooks
│   │   ├── services/       # API services
│   │   └── types/          # TypeScript definitions
│   └── package.json        # Node dependencies
└── README.md
```

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- Node.js 18+
- PostgreSQL 12+

### Backend Setup
1. Navigate to backend directory:
   ```bash
   cd backend
   ```

2. Create and activate virtual environment:
   ```bash
   python -m venv venv
   source venv/Scripts/activate  # Windows
   # or
   source venv/bin/activate      # Linux/Mac
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Configure environment variables in `.env`:
   ```env
   DB_NAME=arada-subcity
   DB_USER=postgres
   DB_PASSWORD=postgres
   DB_HOST=localhost
   DB_PORT=5432
   ```

5. Run migrations:
   ```bash
   python manage.py migrate
   ```

6. Create superuser:
   ```bash
   python manage.py createsuperuser
   ```

7. Start development server:
   ```bash
   python manage.py runserver
   ```

### Frontend Setup
1. Navigate to frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start development server:
   ```bash
   npm run dev
   ```

## 🔧 Configuration

### Database Setup
Create PostgreSQL database:
```sql
CREATE DATABASE "arada-subcity";
CREATE USER postgres WITH PASSWORD 'postgres';
GRANT ALL PRIVILEGES ON DATABASE "arada-subcity" TO postgres;
```

### Environment Variables
Copy `.env.example` to `.env` and configure:
- Database credentials
- JWT settings
- File upload limits
- Email configuration

## 📋 Features

### Core Functionality
- [x] Project setup and architecture
- [ ] Database models and schema
- [ ] Authentication system
- [ ] Organization management
- [ ] Document type management
- [ ] Location hierarchy
- [ ] Document CRUD operations
- [ ] Request/return workflow
- [ ] Search and filtering
- [ ] Audit logging
- [ ] Barcode/QR integration
- [ ] Notification system

### User Roles
- **Admin**: Full system access, user management
- **Manager**: Department oversight, approval workflows
- **Clerk**: Document entry, basic operations
- **Auditor**: Read-only access, audit reports
- **Expert**: Specialized document handling

## 🏗 Development Guidelines

### Code Standards
- Follow PEP 8 for Python code
- Use TypeScript for all React components
- Implement comprehensive error handling
- Write unit tests for critical functionality

### API Design
- RESTful endpoints with consistent naming
- Proper HTTP status codes
- Comprehensive error responses
- API versioning support

## 📊 Next Steps

1. **Database Models**: Design and implement core models
2. **Authentication**: JWT-based user authentication
3. **API Development**: RESTful endpoints for all features
4. **Frontend Components**: React UI components
5. **Integration**: Connect frontend with backend APIs
6. **Testing**: Unit and integration tests
7. **Documentation**: API documentation and user guides

## 🤝 Contributing

1. Follow the established code standards
2. Write tests for new features
3. Update documentation as needed
4. Submit pull requests for review

## 📄 License

This project is developed for the Gondar Arada Revenue Office.
