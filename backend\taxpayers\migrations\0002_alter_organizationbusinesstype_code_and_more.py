# Generated by Django 5.2.3 on 2025-07-14 21:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("taxpayers", "0001_initial"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="organizationbusinesstype",
            name="code",
            field=models.Char<PERSON>ield(
                help_text="Business type code (e.g., PLC, SC, COOP)",
                max_length=10,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="organizationbusinesstype",
            name="name",
            field=models.CharField(
                help_text="Business type name (e.g., Private Limited Company)",
                max_length=100,
            ),
        ),
        migrations.AlterField(
            model_name="taxpayerlevel",
            name="code",
            field=models.Char<PERSON>ield(
                help_text="Level code (e.g., A, B, C, D)", max_length=5, unique=True
            ),
        ),
    ]
