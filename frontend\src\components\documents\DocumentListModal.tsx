import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  IconButton,
  Typography,
  Box,
  Chip,
  CircularProgress,
  Alert,
  Divider,
  Tooltip,
  Paper,
} from '@mui/material';
import {
  Description,
  Visibility,
  Download,
  Print,
  Close,
  CalendarToday,
  Category,
  Info,
  FilePresent,
} from '@mui/icons-material';
import documentService from '../../services/documentService';
import type { Document } from '../../services/types';
import { useNotification } from '../../contexts/NotificationContext';

interface DocumentListModalProps {
  open: boolean;
  onClose: () => void;
  fileId: string;
  fileName: string;
}

const DocumentListModal: React.FC<DocumentListModalProps> = ({
  open,
  onClose,
  fileId,
  fileName
}) => {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [previewDocument, setPreviewDocument] = useState<Document | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [previewLoading, setPreviewLoading] = useState(false);
  const [previewType, setPreviewType] = useState<string>('');
  const [canPreview, setCanPreview] = useState(false);
  const { showNotification } = useNotification();

  useEffect(() => {
    if (open && fileId) {
      loadDocuments();
    }
  }, [open, fileId]);

  const loadDocuments = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('Loading documents for file ID:', fileId);
      console.log('File name:', fileName);

      const response = await documentService.getDocumentsByFile(fileId, {
        ordering: '-created_at',
        page_size: 100
      });

      console.log('Documents response:', response);
      console.log('Documents count:', response.count);
      console.log('Documents results:', response.results);

      setDocuments(response.results || []);

      if (response.results && response.results.length === 0) {
        console.log('No documents found for this file');
      }

    } catch (error: any) {
      console.error('Error loading documents:', error);
      console.error('Error details:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      });

      // Check if it's a 404 error (file not found)
      if (error.response?.status === 404) {
        setError('File not found. The file may have been deleted or moved.');
      } else if (error.response?.status === 401) {
        setError('Authentication required. Please log in again.');
      } else {
        setError(`Failed to load documents: ${error.message || 'Unknown error'}`);
      }

      showNotification('Failed to load documents', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handlePreview = async (document: Document) => {
    try {
      if (!document.file) {
        showNotification('No file attached to this document', 'warning');
        return;
      }

      setPreviewLoading(true);
      setPreviewDocument(document);

      const previewData = await documentService.getDocumentPreviewUrl(document.id);
      setPreviewUrl(previewData.url);
      setPreviewType(previewData.type);
      setCanPreview(previewData.canPreview);
      setShowPreview(true);

      if (!previewData.canPreview) {
        showNotification('This file type cannot be previewed inline. Use download to view the file.', 'info');
      }

    } catch (error: any) {
      console.error('Error previewing document:', error);
      showNotification('Failed to preview document', 'error');
    } finally {
      setPreviewLoading(false);
    }
  };

  const handleClosePreview = () => {
    setShowPreview(false);
    setPreviewDocument(null);
    if (previewUrl) {
      window.URL.revokeObjectURL(previewUrl);
      setPreviewUrl(null);
    }
  };

  const handleModalClose = () => {
    handleClosePreview(); // Clean up preview when modal closes
    onClose();
  };

  const handleDownload = async (document: Document) => {
    try {
      if (!document.file) {
        showNotification('No file attached to this document', 'warning');
        return;
      }

      const blob = await documentService.downloadDocument(document.id);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = document.file_name || `${document.title}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      showNotification('Document downloaded successfully', 'success');
    } catch (error: any) {
      console.error('Error downloading document:', error);
      showNotification('Failed to download document', 'error');
    }
  };

  const handlePrint = async (document: Document) => {
    try {
      if (!document.file) {
        showNotification('No file attached to this document', 'warning');
        return;
      }

      await documentService.printDocument(document.id);
      showNotification('Document sent to printer', 'success');
    } catch (error: any) {
      console.error('Error printing document:', error);
      showNotification('Failed to print document', 'error');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'checked_out': return 'warning';
      case 'archived': return 'info';
      case 'destroyed': return 'error';
      case 'lost': return 'error';
      default: return 'default';
    }
  };

  const getModeColor = (mode: string) => {
    switch (mode) {
      case 'physical': return 'primary';
      case 'digital': return 'secondary';
      case 'hybrid': return 'info';
      default: return 'default';
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <Dialog
      open={open}
      onClose={handleModalClose}
      maxWidth={showPreview ? "xl" : "md"}
      fullWidth
      PaperProps={{
        sx: {
          height: '90vh',
          maxHeight: '90vh'
        }
      }}
    >
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1, pb: 1 }}>
        <FilePresent color="primary" />
        <Box sx={{ flexGrow: 1 }}>
          <Typography variant="h6">Documents in File</Typography>
          <Typography variant="subtitle2" color="text.secondary">
            {fileName}
          </Typography>
        </Box>
        <IconButton onClick={handleModalClose} size="small">
          <Close />
        </IconButton>
      </DialogTitle>

      <Divider />

      <DialogContent sx={{ p: 0, display: 'flex', height: '100%' }}>
        {/* Document List Panel */}
        <Box sx={{
          width: showPreview ? '40%' : '100%',
          borderRight: showPreview ? 1 : 0,
          borderColor: 'divider',
          display: 'flex',
          flexDirection: 'column'
        }}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
              <CircularProgress />
            </Box>
          ) : error ? (
            <Alert severity="error" sx={{ m: 2 }}>
              {error}
            </Alert>
          ) : documents.length === 0 ? (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Description sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                No Documents Found
              </Typography>
              <Typography variant="body2" color="text.secondary">
                This file doesn't contain any documents yet.
              </Typography>
            </Box>
          ) : (
          <List sx={{ p: 0, flex: 1, overflow: 'auto' }}>
            {documents.map((document, index) => (
              <React.Fragment key={document.id}>
                <ListItem
                  sx={{
                    py: 2,
                    px: 3,
                    bgcolor: previewDocument?.id === document.id ? 'action.selected' : 'transparent',
                    '&:hover': {
                      bgcolor: previewDocument?.id === document.id ? 'action.selected' : 'action.hover'
                    }
                  }}
                >
                  <ListItemIcon>
                    <Description color="primary" />
                  </ListItemIcon>
                  
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                          {document.title}
                        </Typography>
                        <Chip
                          label={document.status}
                          size="small"
                          color={getStatusColor(document.status) as any}
                        />
                        <Chip
                          label={document.mode}
                          size="small"
                          color={getModeColor(document.mode) as any}
                          variant="outlined"
                        />
                      </Box>
                    }
                    secondary={
                      <Box sx={{ mt: 1 }}>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          {document.description || 'No description'}
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', mt: 1 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            <Category sx={{ fontSize: 16 }} />
                            <Typography variant="caption">
                              {document.document_type_name}
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            <CalendarToday sx={{ fontSize: 16 }} />
                            <Typography variant="caption">
                              {formatDate(document.document_date)}
                            </Typography>
                          </Box>
                          {document.number_of_pages && (
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                              <Info sx={{ fontSize: 16 }} />
                              <Typography variant="caption">
                                {document.number_of_pages} pages
                              </Typography>
                            </Box>
                          )}
                        </Box>
                      </Box>
                    }
                  />
                  
                  <ListItemSecondaryAction>
                    <Box sx={{ display: 'flex', gap: 0.5 }}>
                      <Tooltip title="Preview Document">
                        <IconButton
                          size="small"
                          onClick={() => handlePreview(document)}
                          disabled={!document.file || previewLoading}
                        >
                          {previewLoading && previewDocument?.id === document.id ? (
                            <CircularProgress size={20} />
                          ) : (
                            <Visibility />
                          )}
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Download Document">
                        <IconButton
                          size="small"
                          onClick={() => handleDownload(document)}
                          disabled={!document.file}
                        >
                          <Download />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Print Document">
                        <IconButton
                          size="small"
                          onClick={() => handlePrint(document)}
                          disabled={!document.file}
                        >
                          <Print />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </ListItemSecondaryAction>
                </ListItem>
                {index < documents.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        )}
        </Box>

        {/* Document Preview Panel */}
        {showPreview && (
          <Box sx={{
            width: '60%',
            display: 'flex',
            flexDirection: 'column',
            height: '100%'
          }}>
            {/* Preview Header */}
            <Box sx={{
              p: 2,
              borderBottom: 1,
              borderColor: 'divider',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}>
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Document Preview
                </Typography>
                {previewDocument && (
                  <Typography variant="body2" color="text.secondary">
                    {previewDocument.title}
                  </Typography>
                )}
              </Box>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Tooltip title="Download Document">
                  <IconButton
                    size="small"
                    onClick={() => previewDocument && handleDownload(previewDocument)}
                    disabled={!previewDocument?.file}
                  >
                    <Download />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Print Document">
                  <IconButton
                    size="small"
                    onClick={() => previewDocument && handlePrint(previewDocument)}
                    disabled={!previewDocument?.file}
                  >
                    <Print />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Close Preview">
                  <IconButton
                    size="small"
                    onClick={handleClosePreview}
                  >
                    <Close />
                  </IconButton>
                </Tooltip>
              </Box>
            </Box>

            {/* Preview Content */}
            <Box sx={{
              flex: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              bgcolor: 'grey.50',
              position: 'relative'
            }}>
              {previewLoading ? (
                <Box sx={{ textAlign: 'center' }}>
                  <CircularProgress size={40} />
                  <Typography variant="body2" sx={{ mt: 2 }}>
                    Loading preview...
                  </Typography>
                </Box>
              ) : previewUrl && canPreview ? (
                <>
                  {previewType.startsWith('image/') ? (
                    <img
                      src={previewUrl}
                      alt={previewDocument?.title}
                      style={{
                        maxWidth: '100%',
                        maxHeight: '100%',
                        objectFit: 'contain',
                        borderRadius: '4px'
                      }}
                    />
                  ) : previewType === 'application/pdf' ? (
                    <iframe
                      src={previewUrl}
                      style={{
                        width: '100%',
                        height: '100%',
                        border: 'none',
                        borderRadius: '4px'
                      }}
                      title={`Preview of ${previewDocument?.title}`}
                    />
                  ) : previewType === 'application/msword' ||
                       previewType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
                       previewType === 'application/vnd.ms-excel' ||
                       previewType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                       previewType === 'application/vnd.ms-powerpoint' ||
                       previewType === 'application/vnd.openxmlformats-officedocument.presentationml.presentation' ? (
                    <Box sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      height: '100%',
                      p: 4,
                      textAlign: 'center'
                    }}>
                      <Description sx={{ fontSize: 64, color: 'primary.main', mb: 2 }} />
                      <Typography variant="h6" gutterBottom>
                        {previewType.includes('word') ? '📄 Word Document' :
                         previewType.includes('excel') || previewType.includes('sheet') ? '📊 Excel Spreadsheet' :
                         previewType.includes('powerpoint') || previewType.includes('presentation') ? '📽️ PowerPoint Presentation' :
                         '📋 Office Document'}
                      </Typography>
                      <Typography variant="body1" color="text.secondary" gutterBottom sx={{ fontWeight: 500 }}>
                        {previewDocument?.title}
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 2, mb: 2, justifyContent: 'center' }}>
                        {previewDocument?.document_type_name && (
                          <Chip
                            label={previewDocument.document_type_name}
                            size="small"
                            color="primary"
                            variant="outlined"
                          />
                        )}
                        {previewDocument?.number_of_pages && (
                          <Chip
                            label={`${previewDocument.number_of_pages} pages`}
                            size="small"
                            color="info"
                            variant="outlined"
                          />
                        )}
                        {previewDocument?.document_date && (
                          <Chip
                            label={new Date(previewDocument.document_date).toLocaleDateString()}
                            size="small"
                            color="default"
                            variant="outlined"
                          />
                        )}
                      </Box>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                        This Office document can be downloaded and opened in Microsoft Office, Google Docs, or compatible applications for full editing and viewing capabilities.
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', justifyContent: 'center' }}>
                        <Button
                          variant="contained"
                          startIcon={<Download />}
                          onClick={() => previewDocument && handleDownload(previewDocument)}
                        >
                          Download & Open
                        </Button>
                        <Button
                          variant="outlined"
                          startIcon={<Visibility />}
                          onClick={() => {
                            if (previewDocument && previewUrl) {
                              window.open(previewUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
                            }
                          }}
                        >
                          Open in New Tab
                        </Button>
                        <Button
                          variant="outlined"
                          startIcon={<Print />}
                          onClick={() => previewDocument && handlePrint(previewDocument)}
                        >
                          Print
                        </Button>
                      </Box>
                      {(previewDocument?.description || previewDocument?.reference_number) && (
                        <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1, maxWidth: 500 }}>
                          {previewDocument.description && (
                            <>
                              <Typography variant="caption" color="text.secondary" display="block" gutterBottom>
                                Description:
                              </Typography>
                              <Typography variant="body2" sx={{ mb: 2 }}>
                                {previewDocument.description}
                              </Typography>
                            </>
                          )}
                          {previewDocument.reference_number && (
                            <>
                              <Typography variant="caption" color="text.secondary" display="block" gutterBottom>
                                Reference Number:
                              </Typography>
                              <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                                {previewDocument.reference_number}
                              </Typography>
                            </>
                          )}
                        </Box>
                      )}
                    </Box>
                  ) : previewType.startsWith('text/') ? (
                    <iframe
                      src={previewUrl}
                      style={{
                        width: '100%',
                        height: '100%',
                        border: 'none',
                        borderRadius: '4px',
                        backgroundColor: 'white'
                      }}
                      title={`Preview of ${previewDocument?.title}`}
                    />
                  ) : (
                    <iframe
                      src={previewUrl}
                      style={{
                        width: '100%',
                        height: '100%',
                        border: 'none',
                        borderRadius: '4px'
                      }}
                      title={`Preview of ${previewDocument?.title}`}
                    />
                  )}
                </>
              ) : previewUrl && !canPreview ? (
                <Box sx={{ textAlign: 'center', color: 'text.secondary', p: 4 }}>
                  <Description sx={{ fontSize: 48, mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    Preview Not Available
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    This file type ({previewType}) cannot be previewed inline.
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 3 }}>
                    Use the download button to view the file in your default application.
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<Download />}
                    onClick={() => previewDocument && handleDownload(previewDocument)}
                  >
                    Download File
                  </Button>
                </Box>
              ) : (
                <Box sx={{ textAlign: 'center', color: 'text.secondary' }}>
                  <Description sx={{ fontSize: 48, mb: 2 }} />
                  <Typography variant="body1">
                    Unable to preview this document
                  </Typography>
                  <Typography variant="body2">
                    The document format may not be supported for preview
                  </Typography>
                </Box>
              )}
            </Box>
          </Box>
        )}
      </DialogContent>

      <Divider />

      <DialogActions sx={{ px: 3, py: 2 }}>
        <Typography variant="body2" color="text.secondary" sx={{ flexGrow: 1 }}>
          {documents.length} document{documents.length !== 1 ? 's' : ''} found
        </Typography>
        <Button onClick={handleModalClose} variant="outlined">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DocumentListModal;
