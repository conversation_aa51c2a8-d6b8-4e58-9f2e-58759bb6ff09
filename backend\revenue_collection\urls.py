"""
Revenue Collection URL Configuration

This module defines URL patterns for the revenue collection API endpoints
using Django REST Framework routers for automatic URL generation.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    RegionalCategoryViewSet,
    CityServiceCategoryViewSet,
    RegionalRevenueSourceViewSet,
    CityServiceRevenueSourceViewSet,
    RevenuePeriodViewSet,
    RegionalRevenueCollectionViewSet,
    CityServiceRevenueCollectionViewSet,
    RevenueSummaryViewSet,
    TaxpayerPaymentSummaryViewSet
)

# Create router and register viewsets
router = DefaultRouter()

# Category endpoints
router.register(r'regional-categories', RegionalCategoryViewSet, basename='regionalcategory')
router.register(r'city-service-categories', CityServiceCategoryViewSet, basename='cityservicecategory')

# Revenue source endpoints
router.register(r'regional-revenue-sources', RegionalRevenueSourceViewSet, basename='regionalrevenuesource')
router.register(r'city-service-revenue-sources', CityServiceRevenueSourceViewSet, basename='cityservicerevenuesource')

# Period endpoints
router.register(r'periods', RevenuePeriodViewSet, basename='revenueperiod')

# Collection endpoints
router.register(r'regional-collections', RegionalRevenueCollectionViewSet, basename='regionalrevenuecollection')
router.register(r'city-service-collections', CityServiceRevenueCollectionViewSet, basename='cityservicerevenuecollection')

# Summary endpoints
router.register(r'summaries', RevenueSummaryViewSet, basename='revenuesummary')

# Taxpayer payment summary endpoints
router.register(r'taxpayer-summaries', TaxpayerPaymentSummaryViewSet, basename='taxpayerpaymentsummary')

app_name = 'revenue_collection'

urlpatterns = [
    path('api/', include(router.urls)),
]

"""
Available API Endpoints:

Categories:
- GET/POST /api/regional-categories/
- GET/PUT/PATCH/DELETE /api/regional-categories/{id}/
- GET /api/regional-categories/{id}/revenue_sources/

- GET/POST /api/city-service-categories/
- GET/PUT/PATCH/DELETE /api/city-service-categories/{id}/
- GET /api/city-service-categories/{id}/revenue_sources/

Revenue Sources:
- GET/POST /api/regional-revenue-sources/
- GET/PUT/PATCH/DELETE /api/regional-revenue-sources/{id}/
- GET /api/regional-revenue-sources/{id}/collections/

- GET/POST /api/city-service-revenue-sources/
- GET/PUT/PATCH/DELETE /api/city-service-revenue-sources/{id}/
- GET /api/city-service-revenue-sources/{id}/collections/

Periods:
- GET/POST /api/periods/
- GET/PUT/PATCH/DELETE /api/periods/{id}/
- GET /api/periods/current/
- GET /api/periods/{id}/collections_summary/

Collections:
- GET/POST /api/regional-collections/
- GET/PUT/PATCH/DELETE /api/regional-collections/{id}/

- GET/POST /api/city-service-collections/
- GET/PUT/PATCH/DELETE /api/city-service-collections/{id}/

Summaries:
- GET/POST /api/summaries/
- GET/PUT/PATCH/DELETE /api/summaries/{id}/
- POST /api/summaries/generate_summaries/
- POST /api/summaries/{id}/update_totals/
- GET /api/summaries/analytics/

Query Parameters:
- All endpoints support standard DRF filtering, searching, and ordering
- Collections support: ?period=<id>&revenue_source=<id>&category=<id>&taxpayer_type=individual|organization
- Summaries support: ?period=<id>&region=<id>&city=<id>&subcity=<id>&kebele=<id>
- Analytics support: ?periods=<id1>&periods=<id2>&region=<id>&city=<id>
"""
