from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from .models import User, UserSession


class UserSerializer(serializers.ModelSerializer):
    """User serializer for API responses"""
    
    full_name = serializers.ReadOnlyField()
    organization_name = serializers.CharField(source='organization.name', read_only=True)
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name', 'full_name',
            'role', 'employee_id', 'phone_number', 'department', 'profile_picture',
            'organization', 'organization_name', 'is_active', 'date_joined', 'last_login'
        ]
        read_only_fields = ['id', 'date_joined', 'last_login']


class UserCreateSerializer(serializers.ModelSerializer):
    """User creation serializer with password validation"""
    
    password = serializers.Char<PERSON>ield(write_only=True, validators=[validate_password])
    password_confirm = serializers.<PERSON><PERSON><PERSON><PERSON>(write_only=True)
    
    class Meta:
        model = User
        fields = [
            'username', 'email', 'password', 'password_confirm', 'first_name', 'last_name',
            'role', 'employee_id', 'phone_number', 'department', 'organization'
        ]
    
    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Passwords don't match")
        return attrs
    
    def create(self, validated_data):
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()
        return user


class UserUpdateSerializer(serializers.ModelSerializer):
    """User update serializer"""
    
    class Meta:
        model = User
        fields = [
            'email', 'first_name', 'last_name', 'phone_number', 
            'department', 'profile_picture'
        ]


class PasswordChangeSerializer(serializers.Serializer):
    """Password change serializer"""
    
    old_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True, validators=[validate_password])
    new_password_confirm = serializers.CharField(required=True)
    
    def validate(self, attrs):
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError("New passwords don't match")
        return attrs
    
    def validate_old_password(self, value):
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError("Old password is incorrect")
        return value


class LoginSerializer(serializers.Serializer):
    """Login serializer"""
    
    username = serializers.CharField()
    password = serializers.CharField()
    
    def validate(self, attrs):
        username = attrs.get('username')
        password = attrs.get('password')
        
        if username and password:
            user = authenticate(username=username, password=password)
            if not user:
                raise serializers.ValidationError('Invalid credentials')
            if not user.is_active:
                raise serializers.ValidationError('User account is disabled')
            attrs['user'] = user
        else:
            raise serializers.ValidationError('Must include username and password')
        
        return attrs


class UserSessionSerializer(serializers.ModelSerializer):
    """User session serializer"""
    
    user_username = serializers.CharField(source='user.username', read_only=True)
    duration = serializers.SerializerMethodField()
    
    class Meta:
        model = UserSession
        fields = [
            'id', 'user', 'user_username', 'session_key', 'ip_address', 
            'user_agent', 'login_time', 'logout_time', 'is_active', 'duration'
        ]
        read_only_fields = ['id', 'session_key', 'login_time', 'logout_time']
    
    def get_duration(self, obj):
        """Calculate session duration"""
        if obj.logout_time:
            duration = obj.logout_time - obj.login_time
            return str(duration).split('.')[0]  # Remove microseconds
        elif obj.is_active:
            from django.utils import timezone
            duration = timezone.now() - obj.login_time
            return f"{str(duration).split('.')[0]} (Active)"
        return "Unknown"


class UserProfileSerializer(serializers.ModelSerializer):
    """User profile serializer for current user"""
    
    full_name = serializers.ReadOnlyField()
    organization_name = serializers.CharField(source='organization.name', read_only=True)
    can_approve_requests = serializers.ReadOnlyField()
    can_manage_documents = serializers.ReadOnlyField()
    is_read_only = serializers.ReadOnlyField()
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name', 'full_name',
            'role', 'employee_id', 'phone_number', 'department', 'profile_picture',
            'organization', 'organization_name', 'can_approve_requests', 
            'can_manage_documents', 'is_read_only', 'date_joined', 'last_login'
        ]
        read_only_fields = [
            'id', 'username', 'role', 'employee_id', 'organization', 
            'date_joined', 'last_login'
        ]
