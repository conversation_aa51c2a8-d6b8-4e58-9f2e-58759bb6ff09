from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db.models import Count, Q
from django.utils import timezone
from datetime import datetime, timedelta
from .models import IndividualTaxPayer, OrganizationTaxPayer, TaxPayerLevel, BusinessSector, BusinessSubSector, OrganizationBusinessType
from locations.location_hierarchy_models import Kebele

class TaxPayerAnalyticsView(APIView):
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Get comprehensive taxpayer analytics"""
        try:
            # Overview Statistics
            total_individuals = IndividualTaxPayer.objects.count()
            total_organizations = OrganizationTaxPayer.objects.count()
            total_taxpayers = total_individuals + total_organizations
            active_taxpayers = (
                IndividualTaxPayer.objects.filter(is_active=True).count() +
                OrganizationTaxPayer.objects.filter(is_active=True).count()
            )
            inactive_taxpayers = total_taxpayers - active_taxpayers
            
            # Gender Distribution (Individuals only)
            gender_stats = IndividualTaxPayer.objects.values('gender').annotate(count=Count('id'))
            gender_distribution = []
            for stat in gender_stats:
                gender_name = {'M': 'Male', 'F': 'Female', 'O': 'Other'}.get(stat['gender'], 'Other')
                percentage = (stat['count'] / total_individuals * 100) if total_individuals > 0 else 0
                gender_distribution.append({
                    'gender': gender_name,
                    'count': stat['count'],
                    'percentage': round(percentage, 1)
                })
            
            # Tax Level Distribution
            tax_level_stats = []
            for level in TaxPayerLevel.objects.all():
                individuals = IndividualTaxPayer.objects.filter(tax_payer_level=level).count()
                organizations = OrganizationTaxPayer.objects.filter(tax_payer_level=level).count()
                total = individuals + organizations
                tax_level_stats.append({
                    'level': level.name,
                    'individuals': individuals,
                    'organizations': organizations,
                    'total': total
                })
            
            # Sector Distribution
            sector_stats = []
            for sector in BusinessSector.objects.all():
                individuals = IndividualTaxPayer.objects.filter(business_sector=sector).count()
                organizations = OrganizationTaxPayer.objects.filter(business_sector=sector).count()
                total = individuals + organizations
                if total > 0:  # Only include sectors with data
                    sector_stats.append({
                        'sector': sector.name,
                        'individuals': individuals,
                        'organizations': organizations,
                        'total': total
                    })
            
            # Registration Trends (Last 12 months)
            registration_trends = []
            for i in range(12):
                month_start = timezone.now().replace(day=1) - timedelta(days=30*i)
                month_end = month_start + timedelta(days=30)
                
                individuals = IndividualTaxPayer.objects.filter(
                    registration_date__gte=month_start,
                    registration_date__lt=month_end
                ).count()
                
                organizations = OrganizationTaxPayer.objects.filter(
                    registration_date__gte=month_start,
                    registration_date__lt=month_end
                ).count()
                
                registration_trends.append({
                    'month': month_start.strftime('%b'),
                    'individuals': individuals,
                    'organizations': organizations,
                    'total': individuals + organizations
                })
            
            registration_trends.reverse()  # Show oldest to newest
            
            # Kebele Distribution
            kebele_stats = []
            for kebele in Kebele.objects.all():
                individuals = IndividualTaxPayer.objects.filter(kebele=kebele).count()
                organizations = OrganizationTaxPayer.objects.filter(kebele=kebele).count()
                total = individuals + organizations
                if total > 0:  # Only include kebeles with data
                    percentage = (total / total_taxpayers * 100) if total_taxpayers > 0 else 0
                    kebele_stats.append({
                        'kebele': kebele.name,
                        'count': total,
                        'percentage': round(percentage, 1)
                    })
            
            # Sort by count descending
            kebele_stats.sort(key=lambda x: x['count'], reverse=True)
            
            # Organization Types
            org_type_stats = []
            for org_type in OrganizationBusinessType.objects.all():
                count = OrganizationTaxPayer.objects.filter(organization_business_type=org_type).count()
                if count > 0:  # Only include types with data
                    percentage = (count / total_organizations * 100) if total_organizations > 0 else 0
                    org_type_stats.append({
                        'type': org_type.name,
                        'count': count,
                        'percentage': round(percentage, 1)
                    })
            
            # VAT Statistics
            total_vat_registered = OrganizationTaxPayer.objects.filter(
                vat_number__isnull=False,
                vat_number__gt=''
            ).count()
            vat_percentage = (total_vat_registered / total_organizations * 100) if total_organizations > 0 else 0
            non_vat_count = total_organizations - total_vat_registered
            
            analytics_data = {
                'overview': {
                    'total_individuals': total_individuals,
                    'total_organizations': total_organizations,
                    'total_taxpayers': total_taxpayers,
                    'active_taxpayers': active_taxpayers,
                    'inactive_taxpayers': inactive_taxpayers,
                },
                'gender_distribution': gender_distribution,
                'tax_level_distribution': tax_level_stats,
                'sector_distribution': sector_stats,
                'registration_trends': registration_trends,
                'kebele_distribution': kebele_stats,
                'organization_types': org_type_stats,
                'vat_statistics': {
                    'total_vat_registered': total_vat_registered,
                    'vat_percentage': round(vat_percentage, 1),
                    'non_vat_count': non_vat_count,
                }
            }
            
            return Response(analytics_data)
            
        except Exception as e:
            return Response(
                {'error': f'Failed to generate analytics: {str(e)}'},
                status=500
            )
