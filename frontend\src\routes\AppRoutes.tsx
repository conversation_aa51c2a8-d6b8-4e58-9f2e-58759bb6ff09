import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import Layout from '../components/Layout/Layout';
import LoginPage from '../pages/auth/LoginPage';
import DashboardPage from '../pages/dashboard/DashboardPage';
import DocumentsPage from '../pages/documents/DocumentsPage';
import DocumentDetailPage from '../pages/documents/DocumentDetailPageSimple';
import CreateDocumentPage, { DocumentFormPage } from '../pages/documents/CreateDocumentPage';

import RequestsPage from '../pages/requests/RequestsPage';
import RequestDetailPage from '../pages/requests/RequestDetailPage';
import CreateRequestPage, { RequestFormPage } from '../pages/requests/CreateRequestPage';
import LocationsPage from '../pages/locations/LocationsPage';
import LocationHierarchyPage from '../pages/locations/LocationHierarchyPage';
import CountriesPage from '../pages/locations/CountriesPage';
import RegionsPage from '../pages/locations/RegionsPage';
import ZonesPage from '../pages/locations/ZonesPage';
import CitiesPage from '../pages/locations/CitiesPage';
import SubCitiesPage from '../pages/locations/SubCitiesPage';
import KebelesPage from '../pages/locations/KebelesPage';
import SpecialLocationsPage from '../pages/locations/SpecialLocationsPage';
import BuildingsPage from '../pages/locations/BuildingsPage';
import ShelvesPage from '../pages/locations/ShelvesPage';
import BoxesPage from '../pages/locations/BoxesPage';
import KentsPage from '../pages/locations/KentsPage';
import LocationFilesPage from '../pages/locations/FilesPage';
import LocationForm from '../pages/locations/LocationForm';
import DocumentCenterPage from '../pages/documents/DocumentCenterPage';
import FileTypesPage from '../pages/documents/FileTypesPage';
import FileTypeDetailPage from '../pages/documents/FileTypeDetailPage';
import DocumentCenterTypesPage from '../pages/documents/DocumentTypesPage';
import DocumentTypeDetailPage from '../pages/documents/DocumentTypeDetailPage';
import BusinessFileDetailPage from '../pages/documents/BusinessFileDetailPage';
import BusinessFilesPage from '../pages/documents/BusinessFilesPage';
import DocumentCenterDocumentsPage from '../pages/documents/DocumentsPage';
import BuildingDetailPage from '../pages/locations/BuildingDetailPage';
import ShelfDetailPage from '../pages/locations/ShelfDetailPage';
import BoxDetailPage from '../pages/locations/BoxDetailPage';
import KentDetailPage from '../pages/locations/KentDetailPage';
import ShelfVisualizationPage from '../pages/locations/ShelfVisualizationPage';

import LocationAnalytics from '../pages/locations/LocationAnalytics';

import UsersPage from '../pages/users/UsersPage';
import UserDetailPage from '../pages/users/UserDetailPage';
import UserFormPage from '../pages/users/UserFormPage';

import ProfilePage from '../pages/profile/ProfilePage';
import LoadingSpinner from '../components/common/LoadingSpinner';
import LocationHierarchyDemo from '../pages/LocationHierarchyDemo';
import OrganizationsPage from '../pages/organizations/OrganizationsPage';
import OrganizationDetailPage from '../pages/organizations/OrganizationDetailPage';
import TaxPayersPage from '../pages/taxpayers/TaxPayersPage';
import IndividualTaxPayerDetailPage from '../pages/taxpayers/IndividualTaxPayerDetailPage';
import OrganizationTaxPayerDetailPage from '../pages/taxpayers/OrganizationTaxPayerDetailPage';
import TaxPayerAnalyticsDashboard from '../pages/taxpayers/TaxPayerAnalyticsDashboard';
import LevelAssessmentPage from '../pages/taxpayers/LevelAssessmentPage';

// Revenue Collection Pages
import {
  RevenueCollectionDashboard,
  CategoriesPage,
  PeriodsPage,
  SourcesPage,
  CollectionsPage,
  CollectionForm,
  CollectionDetailPage,
  AnalyticsPage,
  SummariesPage,
} from '../pages/revenue-collection';

// Protected Route Component
interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: string[];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children, requiredRole }) => {
  const { isAuthenticated, user, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (requiredRole && user && !requiredRole.includes(user.role)) {
    return <Navigate to="/dashboard" replace />;
  }

  return <>{children}</>;
};

// Public Route Component (redirect to dashboard if authenticated)
interface PublicRouteProps {
  children: React.ReactNode;
}

const PublicRoute: React.FC<PublicRouteProps> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }

  return <>{children}</>;
};

const AppRoutes: React.FC = () => {
  return (
    <Routes>
      {/* Public Routes */}
      <Route
        path="/login"
        element={
          <PublicRoute>
            <LoginPage />
          </PublicRoute>
        }
      />

      {/* Protected Routes */}
      <Route
        path="/"
        element={
          <ProtectedRoute>
            <Layout />
          </ProtectedRoute>
        }
      >
        {/* Dashboard */}
        <Route index element={<Navigate to="/dashboard" replace />} />
        <Route path="dashboard" element={<DashboardPage />} />

        {/* Documents */}
        <Route path="documents" element={<DocumentsPage />} />
        <Route
          path="documents/create"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager', 'employee']}>
              <CreateDocumentPage />
            </ProtectedRoute>
          }
        />
        <Route path="documents/:id" element={<DocumentDetailPage />} />
        <Route
          path="documents/:id/edit"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager', 'employee']}>
              <DocumentFormPage mode="edit" />
            </ProtectedRoute>
          }
        />



        {/* Requests */}
        <Route path="requests" element={<RequestsPage />} />
        <Route
          path="requests/create"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager', 'employee']}>
              <CreateRequestPage />
            </ProtectedRoute>
          }
        />
        <Route path="requests/:id" element={<RequestDetailPage />} />
        <Route
          path="requests/:id/edit"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager', 'employee']}>
              <RequestFormPage mode="edit" />
            </ProtectedRoute>
          }
        />

        {/* Locations */}
        <Route path="locations" element={<LocationsPage />} />
        <Route
          path="locations/hierarchy"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <LocationHierarchyPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="locations/countries"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <CountriesPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="locations/regions"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <RegionsPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="locations/zones"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <ZonesPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="locations/cities"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <CitiesPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="locations/subcities"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <SubCitiesPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="locations/kebeles"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <KebelesPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="locations/special-locations"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <SpecialLocationsPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="locations/buildings"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <BuildingsPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="locations/shelves"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <ShelvesPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="locations/boxes"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <BoxesPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="locations/kents"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <KentsPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="locations/files"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <LocationFilesPage />
            </ProtectedRoute>
          }
        />

        {/* Document Management Center Routes */}
        <Route
          path="document-center"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager', 'user']}>
              <DocumentCenterPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="document-center/business-files/:id"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager', 'employee']}>
              <BusinessFileDetailPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="document-center/file-types"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <FileTypesPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="document-center/file-types/:id"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <FileTypeDetailPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="document-center/document-types"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <DocumentCenterTypesPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="document-center/document-types/:id"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <DocumentTypeDetailPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="document-center/files"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager', 'user']}>
              <BusinessFilesPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="document-center/documents"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager', 'user']}>
              <DocumentCenterDocumentsPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="document-center/documents/:id"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager', 'user']}>
              <DocumentDetailPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="locations/demo"
          element={<LocationHierarchyDemo />}
        />
        <Route
          path="locations/analytics"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <LocationAnalytics />
            </ProtectedRoute>
          }
        />
        <Route
          path="locations/visualization"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager', 'employee']}>
              <ShelfVisualizationPage />
            </ProtectedRoute>
          }
        />

        <Route
          path="locations/create"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <LocationForm mode="create" />
            </ProtectedRoute>
          }
        />
        <Route
          path="locations/buildings/create"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <LocationForm mode="create" type="building" />
            </ProtectedRoute>
          }
        />
        <Route
          path="locations/shelves/create"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <LocationForm mode="create" type="shelf" />
            </ProtectedRoute>
          }
        />
        <Route
          path="locations/boxes/create"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <LocationForm mode="create" type="box" />
            </ProtectedRoute>
          }
        />
        <Route
          path="locations/kents/create"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <LocationForm mode="create" type="kent" />
            </ProtectedRoute>
          }
        />
        <Route
          path="locations/buildings/:id"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager', 'employee']}>
              <BuildingDetailPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="locations/buildings/:id/edit"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <LocationForm mode="edit" type="building" />
            </ProtectedRoute>
          }
        />
        <Route
          path="locations/shelves/:id"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager', 'employee']}>
              <ShelfDetailPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="locations/shelves/:id/edit"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <LocationForm mode="edit" type="shelf" />
            </ProtectedRoute>
          }
        />
        <Route
          path="locations/boxes/:id"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager', 'employee']}>
              <BoxDetailPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="locations/boxes/:id/edit"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <LocationForm mode="edit" type="box" />
            </ProtectedRoute>
          }
        />
        <Route
          path="locations/kents/:id"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager', 'employee']}>
              <KentDetailPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="locations/kents/:id/edit"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <LocationForm mode="edit" type="kent" />
            </ProtectedRoute>
          }
        />



        {/* Tax Payers - Admin and Manager only */}
        <Route
          path="taxpayers"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <TaxPayersPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="taxpayers/individuals/:id"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <IndividualTaxPayerDetailPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="taxpayers/organizations/:id"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <OrganizationTaxPayerDetailPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="taxpayers/analytics"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <TaxPayerAnalyticsDashboard />
            </ProtectedRoute>
          }
        />
        <Route
          path="taxpayers/level-assessment"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <LevelAssessmentPage />
            </ProtectedRoute>
          }
        />

        {/* Revenue Collection - Admin and Manager only */}
        <Route
          path="revenue-collection"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <RevenueCollectionDashboard />
            </ProtectedRoute>
          }
        />
        <Route
          path="revenue-collection/categories"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <CategoriesPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="revenue-collection/periods"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <PeriodsPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="revenue-collection/sources"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <SourcesPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="revenue-collection/collections"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <CollectionsPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="revenue-collection/collections/regional/create"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <CollectionForm mode="create" type="regional" />
            </ProtectedRoute>
          }
        />
        <Route
          path="revenue-collection/collections/city-service/create"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <CollectionForm mode="create" type="city-service" />
            </ProtectedRoute>
          }
        />
        <Route
          path="revenue-collection/collections/regional/:id/edit"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <CollectionForm mode="edit" type="regional" />
            </ProtectedRoute>
          }
        />
        <Route
          path="revenue-collection/collections/city-service/:id/edit"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <CollectionForm mode="edit" type="city-service" />
            </ProtectedRoute>
          }
        />
        <Route
          path="revenue-collection/collections/regional/:id"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <CollectionDetailPage type="regional" />
            </ProtectedRoute>
          }
        />
        <Route
          path="revenue-collection/collections/city-service/:id"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <CollectionDetailPage type="city-service" />
            </ProtectedRoute>
          }
        />
        <Route
          path="revenue-collection/analytics"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <AnalyticsPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="revenue-collection/summaries"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <SummariesPage />
            </ProtectedRoute>
          }
        />



        {/* Organizations - Admin only */}
        <Route
          path="organizations"
          element={
            <ProtectedRoute requiredRole={['admin']}>
              <OrganizationsPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="organizations/:id"
          element={
            <ProtectedRoute requiredRole={['admin']}>
              <OrganizationDetailPage />
            </ProtectedRoute>
          }
        />

        {/* Users - Admin and Manager only */}
        <Route
          path="users"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <UsersPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="users/create"
          element={
            <ProtectedRoute requiredRole={['admin']}>
              <UserFormPage mode="create" />
            </ProtectedRoute>
          }
        />
        <Route
          path="users/:id"
          element={
            <ProtectedRoute requiredRole={['admin', 'manager']}>
              <UserDetailPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="users/:id/edit"
          element={
            <ProtectedRoute requiredRole={['admin']}>
              <UserFormPage mode="edit" />
            </ProtectedRoute>
          }
        />



        {/* Profile */}
        <Route path="profile" element={<ProfilePage />} />
      </Route>

      {/* Test Routes (No Authentication Required) */}

      {/* Catch all route */}
      <Route path="*" element={<Navigate to="/dashboard" replace />} />
    </Routes>
  );
};

export default AppRoutes;
