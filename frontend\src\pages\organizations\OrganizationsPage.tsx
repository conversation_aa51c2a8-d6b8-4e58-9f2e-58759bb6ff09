import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  TextField,
  InputAdornment,
  Avatar,
  Divider,
  Stack,
  Fade,
  Alert,
  CircularProgress,
  IconButton,
  Breadcrumbs,
  Link,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TablePagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Business,
  ArrowBack,
  Home,
  Add,
  Edit,
  Delete,
  Star,
  Email,
  Phone,
  Language,
  LocationOn,
  CloudUpload,
  Image,
  Visibility,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useNotification } from '../../contexts/NotificationContext';
import { useBranding } from '../../contexts/BrandingContext';
import organizationService from '../../services/organizationService';
import ConfirmationDialog from '../../components/common/ConfirmationDialog';
import type { Organization, OrganizationCreate } from '../../services/organizationService';

const OrganizationsPage: React.FC = () => {
  const navigate = useNavigate();
  const { showNotification } = useNotification();
  const { refreshBranding } = useBranding();

  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingOrganization, setEditingOrganization] = useState<Organization | null>(null);
  const [formData, setFormData] = useState<OrganizationCreate>({
    name: '',
    short_name: '',
    logo: null,
    motto: '',
    tagline: '',
    description: '',
    website: '',
    email: '',
    phone: '',
    fax: '',
    address_line1: '',
    address_line2: '',
    postal_code: '',
    country: null,
    state_province: null,
    city: null,
    subcity: null,
    kebele: null,
    office_hours_start: '08:00',
    office_hours_end: '17:00',
    established_date: '',
    registration_number: '',
    tax_id: '',
    license_number: '',
    primary_color: '#1976d2',
    secondary_color: '#dc004e',
    accent_color: '#ff9800',
    social_media: {
      facebook: '',
      twitter: '',
      linkedin: '',
      youtube: '',
    },
    document_retention_days: 2555,
    max_file_size_mb: 50,
    individual_penalty_rate: 5.0,
    individual_interest_rate: 2.0,
    organization_penalty_rate: 10.0,
    organization_interest_rate: 3.0,
  });
  const [formErrors, setFormErrors] = useState<any>({});
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [organizationToDelete, setOrganizationToDelete] = useState<Organization | null>(null);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    loadOrganizations();
  }, [page, rowsPerPage]);

  const loadOrganizations = async () => {
    try {
      setLoading(true);
      const response = await organizationService.getOrganizations({
        page: page + 1,
        page_size: rowsPerPage,
      });
      setOrganizations(response.results);
      setTotalCount(response.count);
    } catch (error) {
      console.error('Error loading organizations:', error);
      showNotification('Failed to load organizations', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setSubmitting(true);
      setFormErrors({});

      // Create FormData for file upload
      const submitData = new FormData();

      // Debug: Log the formData state
      console.log('FormData state before submission:', formData);
      console.log('Tax collection settings in formData:', {
        individual_penalty_rate: formData.individual_penalty_rate,
        individual_interest_rate: formData.individual_interest_rate,
        organization_penalty_rate: formData.organization_penalty_rate,
        organization_interest_rate: formData.organization_interest_rate,
      });

      // Add all form fields to FormData
      Object.entries(formData).forEach(([key, value]) => {
        if (key === 'logo' && value instanceof File) {
          submitData.append('logo', value);
        } else if (key === 'social_media' && value) {
          submitData.append('social_media', JSON.stringify(value));
        } else if (value !== null && value !== undefined) {
          // Handle different value types properly
          if (typeof value === 'number' || (typeof value === 'string' && value !== '')) {
            submitData.append(key, String(value));
          }
        }
      });

      // Debug: Log the FormData contents
      console.log('FormData contents:');
      const formDataEntries = [];
      for (let [key, value] of submitData.entries()) {
        console.log(`${key}: ${value}`);
        formDataEntries.push({ key, value });
      }

      // Check specifically for tax collection fields
      const taxFields = formDataEntries.filter(entry =>
        entry.key.includes('penalty_rate') || entry.key.includes('interest_rate')
      );
      console.log('Tax collection fields in FormData:', taxFields);

      if (editingOrganization) {
        // Check if we have a file upload
        const hasFileUpload = formData.logo instanceof File;
        console.log('hasFileUpload:', hasFileUpload);
        console.log('formData.logo:', formData.logo);
        console.log('formData.logo type:', typeof formData.logo);

        if (hasFileUpload) {
          // Use FormData for file uploads
          console.log('Using FormData for file upload');
          const response = await organizationService.updateOrganizationWithFormData(editingOrganization.id, submitData);
          console.log('FormData API Response:', response);

          // Update the editing organization with the response data
          setEditingOrganization(response);

          // Update the form data with the response to reflect any backend changes
          setFormData(prev => ({
            ...prev,
            individual_penalty_rate: Number(response.individual_penalty_rate),
            individual_interest_rate: Number(response.individual_interest_rate),
            organization_penalty_rate: Number(response.organization_penalty_rate),
            organization_interest_rate: Number(response.organization_interest_rate),
          }));

          // Update the organizations list to reflect the changes
          setOrganizations(prev => prev.map(org =>
            org.id === response.id ? { ...org, ...response } : org
          ));
        } else {
          // Use JSON for regular updates (better for number fields)
          console.log('Using JSON for regular update');

          // Create a clean JSON object with only the fields we need
          const jsonData = {
            name: formData.name,
            short_name: formData.short_name,
            motto: formData.motto,
            tagline: formData.tagline,
            description: formData.description,
            website: formData.website,
            email: formData.email,
            phone: formData.phone,
            address_line1: formData.address_line1,
            country: formData.country,
            state_province: formData.state_province,
            city: formData.city,
            subcity: formData.subcity,
            kebele: formData.kebele,
            office_hours_start: formData.office_hours_start,
            office_hours_end: formData.office_hours_end,
            primary_color: formData.primary_color,
            secondary_color: formData.secondary_color,
            accent_color: formData.accent_color,
            social_media: formData.social_media,
            document_retention_days: formData.document_retention_days,
            max_file_size_mb: formData.max_file_size_mb,
            individual_penalty_rate: formData.individual_penalty_rate,
            individual_interest_rate: formData.individual_interest_rate,
            organization_penalty_rate: formData.organization_penalty_rate,
            organization_interest_rate: formData.organization_interest_rate,
          };

          console.log('JSON data to be sent:', jsonData);
          console.log('Tax collection fields being sent:', {
            individual_penalty_rate: jsonData.individual_penalty_rate,
            individual_interest_rate: jsonData.individual_interest_rate,
            organization_penalty_rate: jsonData.organization_penalty_rate,
            organization_interest_rate: jsonData.organization_interest_rate,
          });

          const response = await organizationService.updateOrganization(editingOrganization.id, jsonData);
          console.log('API Response:', response);
          console.log('Tax collection fields in response:', {
            individual_penalty_rate: response.individual_penalty_rate,
            individual_interest_rate: response.individual_interest_rate,
            organization_penalty_rate: response.organization_penalty_rate,
            organization_interest_rate: response.organization_interest_rate,
          });

          // Update the editing organization with the response data
          setEditingOrganization(response);

          // Update the form data with the response to reflect any backend changes
          setFormData(prev => ({
            ...prev,
            individual_penalty_rate: Number(response.individual_penalty_rate),
            individual_interest_rate: Number(response.individual_interest_rate),
            organization_penalty_rate: Number(response.organization_penalty_rate),
            organization_interest_rate: Number(response.organization_interest_rate),
          }));

          // Update the organizations list to reflect the changes
          setOrganizations(prev => prev.map(org =>
            org.id === response.id ? { ...org, ...response } : org
          ));
        }
        showNotification('Organization updated successfully', 'success');
      } else {
        await organizationService.createOrganizationWithFormData(submitData);
        showNotification('Organization created successfully', 'success');
      }

      resetForm();
      loadOrganizations();
    } catch (error: any) {
      console.error('Error submitting form:', error);
      console.error('Error details:', error.response?.data);
      console.error('Error status:', error.response?.status);
      console.error('Error headers:', error.response?.headers);
      if (error.response?.data) {
        setFormErrors(error.response.data);
      }
      showNotification(`Failed to save organization: ${error.response?.data?.detail || error.message}`, 'error');
    } finally {
      setSubmitting(false);
    }
  };

  const handleEdit = async (organization: Organization) => {
    console.log('Editing organization from list:', organization);
    console.log('Tax collection fields in list organization:', {
      individual_penalty_rate: organization.individual_penalty_rate,
      individual_interest_rate: organization.individual_interest_rate,
      organization_penalty_rate: organization.organization_penalty_rate,
      organization_interest_rate: organization.organization_interest_rate,
    });

    try {
      // Fetch complete organization details to ensure we have all fields
      console.log('Fetching complete organization details...');
      const completeOrganization = await organizationService.getOrganization(organization.id);
      console.log('Complete organization:', completeOrganization);
      console.log('Tax collection fields in complete organization:', {
        individual_penalty_rate: completeOrganization.individual_penalty_rate,
        individual_interest_rate: completeOrganization.individual_interest_rate,
        organization_penalty_rate: completeOrganization.organization_penalty_rate,
        organization_interest_rate: completeOrganization.organization_interest_rate,
      });

      setEditingOrganization(completeOrganization);
      setFormData({
        name: completeOrganization.name,
        short_name: completeOrganization.short_name,
        logo: null, // Logo will be handled separately for file uploads
        motto: completeOrganization.motto || '',
        tagline: completeOrganization.tagline || '',
        description: completeOrganization.description || '',
        website: completeOrganization.website || '',
        email: completeOrganization.email || '',
        phone: completeOrganization.phone || '',
        fax: completeOrganization.fax || '',
        address_line1: completeOrganization.address_line1 || '',
        address_line2: completeOrganization.address_line2 || '',
        postal_code: completeOrganization.postal_code || '',
        country: completeOrganization.country || null,
        state_province: completeOrganization.state_province || null,
        city: completeOrganization.city || null,
        subcity: completeOrganization.subcity || null,
        kebele: completeOrganization.kebele || null,
        office_hours_start: completeOrganization.office_hours_start || '08:00',
        office_hours_end: completeOrganization.office_hours_end || '17:00',
        established_date: completeOrganization.established_date || '',
        registration_number: completeOrganization.registration_number || '',
        tax_id: completeOrganization.tax_id || '',
        license_number: completeOrganization.license_number || '',
        primary_color: completeOrganization.primary_color || '#1976d2',
        secondary_color: completeOrganization.secondary_color || '#dc004e',
        accent_color: completeOrganization.accent_color || '#ff9800',
        social_media: completeOrganization.social_media || {
          facebook: '',
          twitter: '',
          linkedin: '',
          youtube: '',
        },
        document_retention_days: completeOrganization.document_retention_days,
        max_file_size_mb: completeOrganization.max_file_size_mb,
        individual_penalty_rate: Number(completeOrganization.individual_penalty_rate) || 5.0,
        individual_interest_rate: Number(completeOrganization.individual_interest_rate) || 2.0,
        organization_penalty_rate: Number(completeOrganization.organization_penalty_rate) || 10.0,
        organization_interest_rate: Number(completeOrganization.organization_interest_rate) || 3.0,
      });
      setShowForm(true);
    } catch (error) {
      console.error('Error fetching organization details:', error);
      showNotification('Failed to load organization details', 'error');
    }
  };

  const handleDelete = (organization: Organization) => {
    setOrganizationToDelete(organization);
    setDeleteDialogOpen(true);
  };

  const handleSetAsDefault = async (organization: Organization) => {
    try {
      await organizationService.setAsDefault(organization.id);
      showNotification('Organization set as default successfully', 'success');
      loadOrganizations();
      // Refresh branding to update site logo, title, and colors
      await refreshBranding();
    } catch (error) {
      console.error('Error setting as default:', error);
      showNotification('Failed to set as default', 'error');
    }
  };

  const handleConfirmDelete = async () => {
    if (!organizationToDelete) return;
    
    try {
      setDeleting(true);
      await organizationService.deleteOrganization(organizationToDelete.id);
      showNotification('Organization deleted successfully', 'success');
      loadOrganizations();
      handleCloseDeleteDialog();
    } catch (error) {
      console.error('Error deleting organization:', error);
      showNotification('Failed to delete organization', 'error');
    } finally {
      setDeleting(false);
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setOrganizationToDelete(null);
    setDeleting(false);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      short_name: '',
      logo: null,
      motto: '',
      tagline: '',
      description: '',
      website: '',
      email: '',
      phone: '',
      fax: '',
      address_line1: '',
      address_line2: '',
      postal_code: '',
      country: null,
      state_province: null,
      city: null,
      subcity: null,
      kebele: null,
      office_hours_start: '08:00',
      office_hours_end: '17:00',
      established_date: '',
      registration_number: '',
      tax_id: '',
      license_number: '',
      primary_color: '#1976d2',
      secondary_color: '#dc004e',
      accent_color: '#ff9800',
      social_media: {
        facebook: '',
        twitter: '',
        linkedin: '',
        youtube: '',
      },
      document_retention_days: 2555,
      max_file_size_mb: 50,
      individual_penalty_rate: 5.0,
      individual_interest_rate: 2.0,
      organization_penalty_rate: 10.0,
      organization_interest_rate: 3.0,
    });
    setFormErrors({});
    setEditingOrganization(null);
    setShowForm(false);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Fade in timeout={800}>
        <Box sx={{ mb: 4 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs sx={{ mb: 2 }}>
            <Link
              component="button"
              variant="body1"
              onClick={() => navigate('/')}
              sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
            >
              <Home fontSize="small" />
              Dashboard
            </Link>
            <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <Business fontSize="small" />
              Organizations
            </Typography>
          </Breadcrumbs>

          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'between', mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <IconButton onClick={() => navigate('/')} sx={{ mr: 1 }}>
                <ArrowBack />
              </IconButton>
              <Avatar sx={{ bgcolor: 'success.main', width: 56, height: 56 }}>
                <Business />
              </Avatar>
              <Box>
                <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
                  Organizations Management
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Manage organizations and their settings
                </Typography>
              </Box>
            </Box>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setShowForm(true)}
              size="large"
              sx={{ ml: 'auto' }}
            >
              Add Organization
            </Button>
          </Box>
        </Box>
      </Fade>

      {/* Form Section */}
      {showForm && (
        <Fade in timeout={600}>
          <Card sx={{ mb: 4 }}>
            <CardContent sx={{ p: 4 }}>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                {editingOrganization ? 'Edit Organization' : 'Add New Organization'}
              </Typography>
              <Divider sx={{ mb: 3 }} />
              
              <form onSubmit={handleSubmit}>
                <Stack spacing={3}>
                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                    <TextField
                      label="Organization Name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      error={!!formErrors.name}
                      helperText={formErrors.name || 'Full organization name'}
                      required
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Business color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                    
                    <TextField
                      label="Short Name"
                      value={formData.short_name}
                      onChange={(e) => setFormData({ ...formData, short_name: e.target.value })}
                      error={!!formErrors.short_name}
                      helperText={formErrors.short_name || 'Abbreviated name'}
                      required
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Business color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>

                  {/* Logo Upload */}
                  <Box>
                    <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
                      Organization Logo
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Button
                        variant="outlined"
                        component="label"
                        startIcon={<CloudUpload />}
                        sx={{ minWidth: 200 }}
                      >
                        Upload Logo
                        <input
                          type="file"
                          hidden
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              setFormData({ ...formData, logo: file });
                            }
                          }}
                        />
                      </Button>
                      {formData.logo && (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Image color="primary" />
                          <Typography variant="body2">
                            {formData.logo instanceof File ? formData.logo.name : 'Current logo'}
                          </Typography>
                        </Box>
                      )}
                      {editingOrganization?.logo && !formData.logo && (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <img
                            src={editingOrganization.logo}
                            alt="Current logo"
                            style={{ width: 40, height: 40, objectFit: 'contain' }}
                          />
                          <Typography variant="body2" color="text.secondary">
                            Current logo
                          </Typography>
                        </Box>
                      )}
                    </Box>
                    {formErrors.logo && (
                      <Typography variant="caption" color="error" sx={{ mt: 0.5, display: 'block' }}>
                        {formErrors.logo}
                      </Typography>
                    )}
                  </Box>

                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                    <TextField
                      label="Email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                      error={!!formErrors.email}
                      helperText={formErrors.email || 'Organization email address'}
                      required
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Email color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />

                    <TextField
                      label="Phone"
                      value={formData.phone}
                      onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                      error={!!formErrors.phone}
                      helperText={formErrors.phone || 'Contact phone number'}
                      required
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Phone color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>

                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: 2 }}>
                    <TextField
                      label="Fax"
                      value={formData.fax}
                      onChange={(e) => setFormData({ ...formData, fax: e.target.value })}
                      error={!!formErrors.fax}
                      helperText={formErrors.fax || 'Fax number'}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Phone color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />

                    <TextField
                      label="Website"
                      value={formData.website}
                      onChange={(e) => setFormData({ ...formData, website: e.target.value })}
                      error={!!formErrors.website}
                      helperText={formErrors.website || 'Organization website URL'}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Language color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />

                    <TextField
                      label="Tagline"
                      value={formData.tagline}
                      onChange={(e) => setFormData({ ...formData, tagline: e.target.value })}
                      error={!!formErrors.tagline}
                      helperText={formErrors.tagline || 'Brief tagline or slogan'}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Business color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>

                  <TextField
                    label="Motto"
                    value={formData.motto}
                    onChange={(e) => setFormData({ ...formData, motto: e.target.value })}
                    error={!!formErrors.motto}
                    helperText={formErrors.motto || 'Organization motto or mission statement'}
                    fullWidth
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Business color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />

                  <TextField
                    label="Description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    error={!!formErrors.description}
                    helperText={formErrors.description || 'Optional description'}
                    multiline
                    rows={3}
                    fullWidth
                  />

                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: 2 }}>
                    <TextField
                      label="Address Line 1"
                      value={formData.address_line1}
                      onChange={(e) => setFormData({ ...formData, address_line1: e.target.value })}
                      error={!!formErrors.address_line1}
                      helperText={formErrors.address_line1 || 'Street address'}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <LocationOn color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />

                    <TextField
                      label="Address Line 2"
                      value={formData.address_line2}
                      onChange={(e) => setFormData({ ...formData, address_line2: e.target.value })}
                      error={!!formErrors.address_line2}
                      helperText={formErrors.address_line2 || 'Apartment, suite, etc.'}
                    />

                    <TextField
                      label="Postal Code"
                      value={formData.postal_code}
                      onChange={(e) => setFormData({ ...formData, postal_code: e.target.value })}
                      error={!!formErrors.postal_code}
                      helperText={formErrors.postal_code || 'ZIP/Postal code'}
                    />
                  </Box>

                  {/* Additional Information */}
                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr 1fr', gap: 2 }}>
                    <TextField
                      label="Established Date"
                      type="date"
                      value={formData.established_date}
                      onChange={(e) => setFormData({ ...formData, established_date: e.target.value })}
                      error={!!formErrors.established_date}
                      helperText={formErrors.established_date || 'Date established'}
                      InputLabelProps={{ shrink: true }}
                    />

                    <TextField
                      label="Registration Number"
                      value={formData.registration_number}
                      onChange={(e) => setFormData({ ...formData, registration_number: e.target.value })}
                      error={!!formErrors.registration_number}
                      helperText={formErrors.registration_number || 'Official registration number'}
                    />

                    <TextField
                      label="Tax ID"
                      value={formData.tax_id}
                      onChange={(e) => setFormData({ ...formData, tax_id: e.target.value })}
                      error={!!formErrors.tax_id}
                      helperText={formErrors.tax_id || 'Tax identification number'}
                    />

                    <TextField
                      label="License Number"
                      value={formData.license_number}
                      onChange={(e) => setFormData({ ...formData, license_number: e.target.value })}
                      error={!!formErrors.license_number}
                      helperText={formErrors.license_number || 'Business license number'}
                    />
                  </Box>

                  {/* Social Media */}
                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr 1fr', gap: 2 }}>
                    <TextField
                      label="Facebook"
                      value={formData.social_media?.facebook || ''}
                      onChange={(e) => setFormData({
                        ...formData,
                        social_media: { ...formData.social_media, facebook: e.target.value }
                      })}
                      error={!!formErrors.social_media?.facebook}
                      helperText="Facebook page URL"
                    />

                    <TextField
                      label="Twitter"
                      value={formData.social_media?.twitter || ''}
                      onChange={(e) => setFormData({
                        ...formData,
                        social_media: { ...formData.social_media, twitter: e.target.value }
                      })}
                      error={!!formErrors.social_media?.twitter}
                      helperText="Twitter profile URL"
                    />

                    <TextField
                      label="LinkedIn"
                      value={formData.social_media?.linkedin || ''}
                      onChange={(e) => setFormData({
                        ...formData,
                        social_media: { ...formData.social_media, linkedin: e.target.value }
                      })}
                      error={!!formErrors.social_media?.linkedin}
                      helperText="LinkedIn page URL"
                    />

                    <TextField
                      label="YouTube"
                      value={formData.social_media?.youtube || ''}
                      onChange={(e) => setFormData({
                        ...formData,
                        social_media: { ...formData.social_media, youtube: e.target.value }
                      })}
                      error={!!formErrors.social_media?.youtube}
                      helperText="YouTube channel URL"
                    />
                  </Box>

                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr 1fr', gap: 2 }}>
                    <TextField
                      label="Document Retention (Days)"
                      type="number"
                      value={formData.document_retention_days}
                      onChange={(e) => setFormData({ ...formData, document_retention_days: Number(e.target.value) })}
                      error={!!formErrors.document_retention_days}
                      helperText={formErrors.document_retention_days || 'Default: 2555 (7 years)'}
                      inputProps={{ min: 30, max: 10000 }}
                    />

                    <TextField
                      label="Max File Size (MB)"
                      type="number"
                      value={formData.max_file_size_mb}
                      onChange={(e) => setFormData({ ...formData, max_file_size_mb: Number(e.target.value) })}
                      error={!!formErrors.max_file_size_mb}
                      helperText={formErrors.max_file_size_mb || 'Default: 50 MB'}
                      inputProps={{ min: 1, max: 1000 }}
                    />

                    <TextField
                      label="Office Hours Start"
                      type="time"
                      value={formData.office_hours_start}
                      onChange={(e) => setFormData({ ...formData, office_hours_start: e.target.value })}
                      error={!!formErrors.office_hours_start}
                      helperText={formErrors.office_hours_start || 'Opening time'}
                      InputLabelProps={{ shrink: true }}
                    />

                    <TextField
                      label="Office Hours End"
                      type="time"
                      value={formData.office_hours_end}
                      onChange={(e) => setFormData({ ...formData, office_hours_end: e.target.value })}
                      error={!!formErrors.office_hours_end}
                      helperText={formErrors.office_hours_end || 'Closing time'}
                      InputLabelProps={{ shrink: true }}
                    />
                  </Box>

                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: 2 }}>
                    <TextField
                      label="Primary Color"
                      type="color"
                      value={formData.primary_color}
                      onChange={(e) => setFormData({ ...formData, primary_color: e.target.value })}
                      error={!!formErrors.primary_color}
                      helperText={formErrors.primary_color || 'Brand primary color'}
                      InputLabelProps={{ shrink: true }}
                    />

                    <TextField
                      label="Secondary Color"
                      type="color"
                      value={formData.secondary_color}
                      onChange={(e) => setFormData({ ...formData, secondary_color: e.target.value })}
                      error={!!formErrors.secondary_color}
                      helperText={formErrors.secondary_color || 'Brand secondary color'}
                      InputLabelProps={{ shrink: true }}
                    />

                    <TextField
                      label="Accent Color"
                      type="color"
                      value={formData.accent_color}
                      onChange={(e) => setFormData({ ...formData, accent_color: e.target.value })}
                      error={!!formErrors.accent_color}
                      helperText={formErrors.accent_color || 'Brand accent color'}
                      InputLabelProps={{ shrink: true }}
                    />
                  </Box>

                  {/* Tax Collection Settings */}
                  {editingOrganization && (
                    <Box>
                      <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>
                        Tax Collection Settings
                      </Typography>
                      <Alert severity="info" sx={{ mb: 2 }}>
                        Configure penalty and interest rates for tax collection calculations.
                      </Alert>

                      <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 3 }}>
                        {/* Individual Taxpayer Settings */}
                        <Paper sx={{ p: 2, bgcolor: 'primary.50', border: '1px solid', borderColor: 'primary.200' }}>
                          <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>
                            Individual Taxpayers
                          </Typography>
                          <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                            <TextField
                              label="Penalty Rate (%)"
                              type="number"
                              value={formData.individual_penalty_rate || 5.0}
                              onChange={(e) => setFormData({ ...formData, individual_penalty_rate: Number(e.target.value) })}
                              error={!!formErrors.individual_penalty_rate}
                              helperText={formErrors.individual_penalty_rate || 'Default: 5%'}
                              inputProps={{ min: 0, max: 100, step: 0.1 }}
                              size="small"
                            />
                            <TextField
                              label="Interest Rate (% monthly)"
                              type="number"
                              value={formData.individual_interest_rate || 2.0}
                              onChange={(e) => setFormData({ ...formData, individual_interest_rate: Number(e.target.value) })}
                              error={!!formErrors.individual_interest_rate}
                              helperText={formErrors.individual_interest_rate || 'Default: 2% per month'}
                              inputProps={{ min: 0, max: 100, step: 0.1 }}
                              size="small"
                            />
                          </Box>
                        </Paper>

                        {/* Organization Taxpayer Settings */}
                        <Paper sx={{ p: 2, bgcolor: 'secondary.50', border: '1px solid', borderColor: 'secondary.200' }}>
                          <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: 'secondary.main' }}>
                            Organization Taxpayers
                          </Typography>
                          <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                            <TextField
                              label="Penalty Rate (%)"
                              type="number"
                              value={formData.organization_penalty_rate || 10.0}
                              onChange={(e) => setFormData({ ...formData, organization_penalty_rate: Number(e.target.value) })}
                              error={!!formErrors.organization_penalty_rate}
                              helperText={formErrors.organization_penalty_rate || 'Default: 10%'}
                              inputProps={{ min: 0, max: 100, step: 0.1 }}
                              size="small"
                            />
                            <TextField
                              label="Interest Rate (% monthly)"
                              type="number"
                              value={formData.organization_interest_rate || 3.0}
                              onChange={(e) => setFormData({ ...formData, organization_interest_rate: Number(e.target.value) })}
                              error={!!formErrors.organization_interest_rate}
                              helperText={formErrors.organization_interest_rate || 'Default: 3% per month'}
                              inputProps={{ min: 0, max: 100, step: 0.1 }}
                              size="small"
                            />
                          </Box>
                        </Paper>
                      </Box>
                    </Box>
                  )}

                  <Box sx={{ display: 'flex', gap: 2, pt: 2 }}>
                    <Button
                      type="submit"
                      variant="contained"
                      disabled={submitting}
                      startIcon={submitting ? <CircularProgress size={20} /> : <Add />}
                      size="large"
                    >
                      {submitting ? 'Saving...' : editingOrganization ? 'Update Organization' : 'Create Organization'}
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={resetForm}
                      size="large"
                    >
                      Cancel
                    </Button>
                  </Box>
                </Stack>
              </form>
            </CardContent>
          </Card>
        </Fade>
      )}

      {/* Organizations Table - Hide when editing */}
      {!showForm && (
        <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
            Organizations List
          </Typography>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : organizations.length === 0 ? (
            <Alert severity="info">
              No organizations found. Click "Add Organization" to create your first organization.
            </Alert>
          ) : (
            <>
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Organization</TableCell>
                      <TableCell>Contact</TableCell>
                      <TableCell>Settings</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Created</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {organizations.map((organization) => (
                      <TableRow key={organization.id} hover>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            {organization.logo ? (
                              <Avatar sx={{ width: 32, height: 32 }}>
                                <img
                                  src={organization.logo}
                                  alt={organization.name}
                                  style={{ width: '100%', height: '100%', objectFit: 'contain' }}
                                />
                              </Avatar>
                            ) : (
                              <Avatar sx={{ bgcolor: 'success.main', width: 32, height: 32 }}>
                                <Business fontSize="small" />
                              </Avatar>
                            )}
                            <Box>
                              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                {organization.name}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {organization.short_name} • {organization.tagline || organization.motto || 'No tagline'}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box>
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {organization.email}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {organization.phone}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                            <Chip
                              label={`${organization.document_retention_days}d retention`}
                              size="small"
                              variant="outlined"
                            />
                            <Chip
                              label={`${organization.max_file_size_mb}MB max`}
                              size="small"
                              variant="outlined"
                            />
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Chip
                              label={organization.is_default ? 'Default' : 'Active'}
                              size="small"
                              color={organization.is_default ? 'primary' : 'default'}
                              icon={organization.is_default ? <Star /> : undefined}
                            />
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {new Date(organization.created_at).toLocaleDateString()}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                            <IconButton
                              size="small"
                              onClick={() => navigate(`/organizations/${organization.id}`)}
                              color="info"
                              title="View Details"
                            >
                              <Visibility fontSize="small" />
                            </IconButton>
                            {!organization.is_default && (
                              <IconButton
                                size="small"
                                onClick={() => handleSetAsDefault(organization)}
                                color="warning"
                                title="Set as Default"
                              >
                                <Star fontSize="small" />
                              </IconButton>
                            )}
                            <IconButton
                              size="small"
                              onClick={() => handleEdit(organization)}
                              color="primary"
                              title="Edit Organization"
                            >
                              <Edit fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleDelete(organization)}
                              color="error"
                              disabled={organization.is_default}
                              title="Delete Organization"
                            >
                              <Delete fontSize="small" />
                            </IconButton>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              <TablePagination
                component="div"
                count={totalCount}
                page={page}
                onPageChange={handleChangePage}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                rowsPerPageOptions={[5, 10, 25, 50]}
              />
            </>
          )}
        </CardContent>
      </Card>
      )}

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        title="Delete Organization"
        itemName={organizationToDelete?.name}
        itemType="Organization"
        message={`Are you sure you want to delete "${organizationToDelete?.name}"? This action cannot be undone.`}
        confirmText="Delete Organization"
        severity="error"
        loading={deleting}
      />
    </Container>
  );
};

export default OrganizationsPage;
