import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  <PERSON><PERSON><PERSON>,
  Card,
  CardContent,
  Button,
  TextField,
  FormControlLabel,
  Switch,
  InputAdornment,
  Avatar,
  Divider,
  Stack,
  Fade,
  Alert,
  CircularProgress,
  IconButton,
  Breadcrumbs,
  Link,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TablePagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  LocationOn,
  ArrowBack,
  Home,
  Add,
  Edit,
  Delete,
  CheckCircle,
  Cancel,
  Map,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useNotification } from '../../contexts/NotificationContext';
import locationHierarchyService from '../../services/locationHierarchyService';
import ConfirmationDialog from '../../components/common/ConfirmationDialog';

interface Zone {
  id: number;
  name: string;
  code: string;
  region: number;
  region_name?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface Region {
  id: number;
  name: string;
  code: string;
  country_name?: string;
}

const ZonesPage: React.FC = () => {
  const navigate = useNavigate();
  const { showNotification } = useNotification();

  const [zones, setZones] = useState<Zone[]>([]);
  const [regions, setRegions] = useState<Region[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingZone, setEditingZone] = useState<Zone | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    region: '',
    is_active: true,
  });
  const [formErrors, setFormErrors] = useState<any>({});
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [zoneToDelete, setZoneToDelete] = useState<Zone | null>(null);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    loadZones();
    loadRegions();
  }, [page, rowsPerPage]);

  const loadZones = async () => {
    try {
      setLoading(true);
      const response = await locationHierarchyService.getZones({
        page: page + 1,
        page_size: rowsPerPage,
      });
      setZones(response.results);
      setTotalCount(response.count);
    } catch (error) {
      console.error('Error loading zones:', error);
      showNotification('Failed to load zones', 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadRegions = async () => {
    try {
      const response = await locationHierarchyService.getRegions({ page_size: 100 });
      setRegions(response.results);
    } catch (error) {
      console.error('Error loading regions:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setSubmitting(true);
      setFormErrors({});

      if (editingZone) {
        await locationHierarchyService.updateZone(editingZone.id, formData);
        showNotification('Zone updated successfully', 'success');
      } else {
        await locationHierarchyService.createZone(formData);
        showNotification('Zone created successfully', 'success');
      }

      resetForm();
      loadZones();
    } catch (error: any) {
      console.error('Error submitting form:', error);
      if (error.response?.data) {
        setFormErrors(error.response.data);
      }
      showNotification('Failed to save zone', 'error');
    } finally {
      setSubmitting(false);
    }
  };

  const handleEdit = (zone: Zone) => {
    setEditingZone(zone);
    setFormData({
      name: zone.name,
      code: zone.code,
      region: zone.region.toString(),
      is_active: zone.is_active,
    });
    setShowForm(true);
  };

  const handleDelete = (zone: Zone) => {
    setZoneToDelete(zone);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!zoneToDelete) return;

    try {
      setDeleting(true);
      await locationHierarchyService.deleteZone(zoneToDelete.id);
      showNotification('Zone deleted successfully', 'success');
      loadZones();
      handleCloseDeleteDialog();
    } catch (error) {
      console.error('Error deleting zone:', error);
      showNotification('Failed to delete zone', 'error');
    } finally {
      setDeleting(false);
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setZoneToDelete(null);
    setDeleting(false);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      code: '',
      region: '',
      is_active: true,
    });
    setFormErrors({});
    setEditingZone(null);
    setShowForm(false);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Fade in timeout={800}>
        <Box sx={{ mb: 4 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs sx={{ mb: 2 }}>
            <Link
              component="button"
              variant="body1"
              onClick={() => navigate('/locations/hierarchy')}
              sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
            >
              <Home fontSize="small" />
              Location Hierarchy
            </Link>
            <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <LocationOn fontSize="small" />
              Zones
            </Typography>
          </Breadcrumbs>

          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'between', mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <IconButton onClick={() => navigate('/locations/hierarchy')} sx={{ mr: 1 }}>
                <ArrowBack />
              </IconButton>
              <Avatar sx={{ bgcolor: 'success.main' }}>
                <LocationOn />
              </Avatar>
              <Box>
                <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
                  Zones Management
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Manage zones within regions
                </Typography>
              </Box>
            </Box>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setShowForm(true)}
              size="large"
              sx={{ ml: 'auto' }}
            >
              Add Zone
            </Button>
          </Box>
        </Box>
      </Fade>

      {/* Form Section */}
      {showForm && (
        <Fade in timeout={600}>
          <Card sx={{ mb: 4 }}>
            <CardContent sx={{ p: 4 }}>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                {editingZone ? 'Edit Zone' : 'Add New Zone'}
              </Typography>
              <Divider sx={{ mb: 3 }} />

              <form onSubmit={handleSubmit}>
                <Stack spacing={3}>
                  <TextField
                    label="Zone Name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    error={!!formErrors.name}
                    helperText={formErrors.name || 'Enter the full zone name'}
                    fullWidth
                    required
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <LocationOn color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />

                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                    <TextField
                      label="Zone Code"
                      value={formData.code}
                      onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
                      error={!!formErrors.code}
                      helperText={formErrors.code || 'e.g., CZ, NZ, SZ'}
                      required
                      inputProps={{ maxLength: 10 }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <LocationOn color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />

                    <FormControl fullWidth required error={!!formErrors.region}>
                      <InputLabel>Region</InputLabel>
                      <Select
                        value={formData.region}
                        onChange={(e) => setFormData({ ...formData, region: e.target.value })}
                        label="Region"
                        startAdornment={
                          <InputAdornment position="start">
                            <Map color="action" />
                          </InputAdornment>
                        }
                      >
                        {regions.map((region) => (
                          <MenuItem key={region.id} value={region.id.toString()}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Chip label={region.code} size="small" />
                              {region.name}
                              {region.country_name && (
                                <Typography variant="caption" color="text.secondary">
                                  ({region.country_name})
                                </Typography>
                              )}
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                      {formErrors.region && (
                        <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 2 }}>
                          {formErrors.region}
                        </Typography>
                      )}
                    </FormControl>
                  </Box>

                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.is_active}
                        onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                        color="primary"
                      />
                    }
                    label="Active Status"
                  />

                  <Box sx={{ display: 'flex', gap: 2, pt: 2 }}>
                    <Button
                      type="submit"
                      variant="contained"
                      disabled={submitting}
                      startIcon={submitting ? <CircularProgress size={20} /> : <Add />}
                      size="large"
                    >
                      {submitting ? 'Saving...' : editingZone ? 'Update Zone' : 'Create Zone'}
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={resetForm}
                      size="large"
                    >
                      Cancel
                    </Button>
                  </Box>
                </Stack>
              </form>
            </CardContent>
          </Card>
        </Fade>
      )}

      {/* Zones Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
            Zones List
          </Typography>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : zones.length === 0 ? (
            <Alert severity="info">
              No zones found. Click "Add Zone" to create your first zone.
            </Alert>
          ) : (
            <>
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Zone</TableCell>
                      <TableCell>Code</TableCell>
                      <TableCell>Region</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Created</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {zones.map((zone) => (
                      <TableRow key={zone.id} hover>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <Avatar sx={{ bgcolor: 'success.main', width: 32, height: 32 }}>
                              <LocationOn fontSize="small" />
                            </Avatar>
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {zone.name}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip label={zone.code} size="small" color="success" />
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Map fontSize="small" color="action" />
                            {zone.region_name || `Region ${zone.region}`}
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={zone.is_active ? 'Active' : 'Inactive'}
                            color={zone.is_active ? 'success' : 'error'}
                            size="small"
                            icon={zone.is_active ? <CheckCircle /> : <Cancel />}
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {new Date(zone.created_at).toLocaleDateString()}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                            <IconButton
                              size="small"
                              onClick={() => handleEdit(zone)}
                              color="primary"
                            >
                              <Edit fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleDelete(zone)}
                              color="error"
                            >
                              <Delete fontSize="small" />
                            </IconButton>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              <TablePagination
                component="div"
                count={totalCount}
                page={page}
                onPageChange={handleChangePage}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                rowsPerPageOptions={[5, 10, 25, 50]}
              />
            </>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        title="Delete Zone"
        itemName={zoneToDelete?.name}
        itemType="Zone"
        message={`Are you sure you want to delete "${zoneToDelete?.name}"? This will also delete all cities, subcities, and kebeles within this zone. This action cannot be undone.`}
        confirmText="Delete Zone"
        severity="error"
        loading={deleting}
      />
    </Container>
  );
};

export default ZonesPage;
