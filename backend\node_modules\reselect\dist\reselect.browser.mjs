var re={inputStabilityCheck:"once",identityFunctionCheck:"once"},ie=e=>{Object.assign(re,e)};var S=Symbol("NOT_FOUND");function T(e,t=`expected a function, instead received ${typeof e}`){if(typeof e!="function")throw new TypeError(t)}function V(e,t=`expected an object, instead received ${typeof e}`){if(typeof e!="object")throw new TypeError(t)}function ce(e,t="expected all items to be functions, instead received the following types: "){if(!e.every(n=>typeof n=="function")){let n=e.map(c=>typeof c=="function"?`function ${c.name||"unnamed"}()`:typeof c).join(", ");throw new TypeError(`${t}[${n}]`)}}var O=e=>Array.isArray(e)?e:[e];function K(e){let t=Array.isArray(e[0])?e[0]:e;return ce(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}function W(e,t){let n=[],{length:c}=e;for(let s=0;s<c;s++)n.push(e[s].apply(null,t));return n}var G=0,M=null,F=class{revision=G;_value;_lastValue;_isEqual=v;constructor(t,n=v){this._value=this._lastValue=t,this._isEqual=n}get value(){return M?.add(this),this._value}set value(t){this.value!==t&&(this._value=t,this.revision=++G)}};function v(e,t){return e===t}var b=class{_cachedValue;_cachedRevision=-1;_deps=[];hits=0;fn;constructor(t){this.fn=t}clear(){this._cachedValue=void 0,this._cachedRevision=-1,this._deps=[],this.hits=0}get value(){if(this.revision>this._cachedRevision){let{fn:t}=this,n=new Set,c=M;M=n,this._cachedValue=t(),M=c,this.hits++,this._deps=Array.from(n),this._cachedRevision=this.revision}return M?.add(this),this._cachedValue}get revision(){return Math.max(...this._deps.map(t=>t.revision),0)}};function g(e){return e instanceof F||console.warn("Not a valid cell! ",e),e.value}function L(e,t){if(!(e instanceof F))throw new TypeError("setValue must be passed a tracked store created with `createStorage`.");e.value=e._lastValue=t}function $(e,t=v){return new F(e,t)}function Y(e){return T(e,"the first parameter to `createCache` must be a function"),new b(e)}var se=(e,t)=>!1;function z(){return $(null,se)}function k(e,t){L(e,t)}var A=e=>{let t=e.collectionTag;t===null&&(t=e.collectionTag=z()),g(t)},h=e=>{let t=e.collectionTag;t!==null&&k(t,null)};var xe=Symbol(),H=0,ue=Object.getPrototypeOf({}),I=class{constructor(t){this.value=t;this.value=t,this.tag.value=t}proxy=new Proxy(this,C);tag=z();tags={};children={};collectionTag=null;id=H++},C={get(e,t){function n(){let{value:s}=e,o=Reflect.get(s,t);if(typeof t=="symbol"||t in ue)return o;if(typeof o=="object"&&o!==null){let i=e.children[t];return i===void 0&&(i=e.children[t]=E(o)),i.tag&&g(i.tag),i.proxy}else{let i=e.tags[t];return i===void 0&&(i=e.tags[t]=z(),i.value=o),g(i),o}}return n()},ownKeys(e){return A(e),Reflect.ownKeys(e.value)},getOwnPropertyDescriptor(e,t){return Reflect.getOwnPropertyDescriptor(e.value,t)},has(e,t){return Reflect.has(e.value,t)}},N=class{constructor(t){this.value=t;this.value=t,this.tag.value=t}proxy=new Proxy([this],ae);tag=z();tags={};children={};collectionTag=null;id=H++},ae={get([e],t){return t==="length"&&A(e),C.get(e,t)},ownKeys([e]){return C.ownKeys(e)},getOwnPropertyDescriptor([e],t){return C.getOwnPropertyDescriptor(e,t)},has([e],t){return C.has(e,t)}};function E(e){return Array.isArray(e)?new N(e):new I(e)}function D(e,t){let{value:n,tags:c,children:s}=e;if(e.value=t,Array.isArray(n)&&Array.isArray(t)&&n.length!==t.length)h(e);else if(n!==t){let o=0,i=0,r=!1;for(let u in n)o++;for(let u in t)if(i++,!(u in n)){r=!0;break}(r||o!==i)&&h(e)}for(let o in c){let i=n[o],r=t[o];i!==r&&(h(e),k(c[o],r)),typeof r=="object"&&r!==null&&delete c[o]}for(let o in s){let i=s[o],r=t[o];i.value!==r&&(typeof r=="object"&&r!==null?D(i,r):(X(i),delete s[o]))}}function X(e){e.tag&&k(e.tag,null),h(e);for(let t in e.tags)k(e.tags[t],null);for(let t in e.children)X(e.children[t])}function le(e){let t;return{get(n){return t&&e(t.key,n)?t.value:S},put(n,c){t={key:n,value:c}},getEntries(){return t?[t]:[]},clear(){t=void 0}}}function pe(e,t){let n=[];function c(r){let a=n.findIndex(u=>t(r,u.key));if(a>-1){let u=n[a];return a>0&&(n.splice(a,1),n.unshift(u)),u.value}return S}function s(r,a){c(r)===S&&(n.unshift({key:r,value:a}),n.length>e&&n.pop())}function o(){return n}function i(){n=[]}return{get:c,put:s,getEntries:o,clear:i}}var w=(e,t)=>e===t;function j(e){return function(n,c){if(n===null||c===null||n.length!==c.length)return!1;let{length:s}=n;for(let o=0;o<s;o++)if(!e(n[o],c[o]))return!1;return!0}}function me(e,t){let n=typeof t=="object"?t:{equalityCheck:t},{equalityCheck:c=w,maxSize:s=1,resultEqualityCheck:o}=n,i=j(c),r=0,a=s<=1?le(i):pe(s,i);function u(){let l=a.get(arguments);if(l===S){if(l=e.apply(null,arguments),r++,o){let y=a.getEntries().find(p=>o(p.value,l));y&&(l=y.value,r!==0&&r--)}a.put(arguments,l)}return l}return u.clearCache=()=>{a.clear(),u.resetResultsCount()},u.resultsCount=()=>r,u.resetResultsCount=()=>{r=0},u}function de(e){let t=E([]),n=null,c=j(w),s=Y(()=>e.apply(null,t.proxy));function o(){return c(n,arguments)||(D(t,arguments),n=arguments),s.value}return o.clearCache=()=>s.clear(),o}var _=class{constructor(t){this.value=t}deref(){return this.value}},ye=typeof WeakRef<"u"?WeakRef:_,fe=0,B=1;function R(){return{s:fe,v:void 0,o:null,p:null}}function x(e,t={}){let n=R(),{resultEqualityCheck:c}=t,s,o=0;function i(){let r=n,{length:a}=arguments;for(let m=0,y=a;m<y;m++){let p=arguments[m];if(typeof p=="function"||typeof p=="object"&&p!==null){let d=r.o;d===null&&(r.o=d=new WeakMap);let f=d.get(p);f===void 0?(r=R(),d.set(p,r)):r=f}else{let d=r.p;d===null&&(r.p=d=new Map);let f=d.get(p);f===void 0?(r=R(),d.set(p,r)):r=f}}let u=r,l;if(r.s===B)l=r.v;else if(l=e.apply(null,arguments),o++,c){let m=s?.deref?.()??s;m!=null&&c(m,l)&&(l=m,o!==0&&o--),s=typeof l=="object"&&l!==null||typeof l=="function"?new ye(l):l}return u.s=B,u.v=l,l}return i.clearCache=()=>{n=R(),i.resetResultsCount()},i.resultsCount=()=>o,i.resetResultsCount=()=>{o=0},i}function J(e,...t){let n=typeof e=="function"?{memoize:e,memoizeOptions:t}:e,c=(...s)=>{let o=0,i=0,r,a={},u=s.pop();typeof u=="object"&&(a=u,u=s.pop()),T(u,`createSelector expects an output function after the inputs, but received: [${typeof u}]`);let l={...n,...a},{memoize:m,memoizeOptions:y=[],argsMemoize:p=x,argsMemoizeOptions:d=[],devModeChecks:f={}}=l,Z=O(y),ee=O(d),q=K(s),P=m(function(){return o++,u.apply(null,arguments)},...Z),Se=!0,te=p(function(){i++;let oe=W(q,arguments);return r=P.apply(null,oe),r},...ee);return Object.assign(te,{resultFunc:u,memoizedResultFunc:P,dependencies:q,dependencyRecomputations:()=>i,resetDependencyRecomputations:()=>{i=0},lastResult:()=>r,recomputations:()=>o,resetRecomputations:()=>{o=0},memoize:m,argsMemoize:p})};return Object.assign(c,{withTypes:()=>c}),c}var U=J(x);var Q=Object.assign((e,t=U)=>{V(e,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof e}`);let n=Object.keys(e),c=n.map(o=>e[o]);return t(c,(...o)=>o.reduce((i,r,a)=>(i[n[a]]=r,i),{}))},{withTypes:()=>Q});export{U as createSelector,J as createSelectorCreator,Q as createStructuredSelector,me as lruMemoize,w as referenceEqualityCheck,ie as setGlobalDevModeChecks,de as unstable_autotrackMemoize,x as weakMapMemoize};
//# sourceMappingURL=reselect.browser.mjs.map