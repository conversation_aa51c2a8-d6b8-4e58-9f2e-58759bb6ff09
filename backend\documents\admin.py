from django.contrib import admin
from django.utils.html import format_html
from django.utils import timezone
from .models import DocumentType, Document, DocumentVersion, DocumentTag


@admin.register(DocumentType)
class DocumentTypeAdmin(admin.ModelAdmin):
    """Document Type admin"""

    list_display = ['name', 'code', 'organization', 'confidentiality_level', 'requires_expiry_date', 'is_active', 'created_at']
    list_filter = ['organization', 'confidentiality_level', 'requires_expiry_date', 'requires_approval', 'is_active', 'created_at']
    search_fields = ['name', 'code', 'description']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['organization', 'name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('organization', 'name', 'code', 'description', 'is_active')
        }),
        ('Classification', {
            'fields': ('confidentiality_level', 'requires_expiry_date', 'requires_approval')
        }),
        ('File Settings', {
            'fields': ('allowed_file_extensions', 'max_file_size_mb')
        }),
        ('Retention', {
            'fields': ('retention_days',)
        }),
        ('Audit Information', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(Document)
class DocumentAdmin(admin.ModelAdmin):
    """Document admin with comprehensive management"""

    list_display = ['title', 'document_type', 'mode', 'status', 'number_of_pages', 'document_file', 'created_by', 'created_at']
    list_filter = ['document_type', 'mode', 'status', 'kent__box__shelf__building__organization', 'created_at']
    search_fields = ['title', 'description', 'reference_number', 'tags']
    readonly_fields = ['id', 'created_at', 'updated_at', 'file_size', 'barcode_preview', 'qr_code_preview', 'location_display', 'is_expired']
    ordering = ['-created_at']

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'description', 'document_type', 'reference_number')
        }),
        ('Classification', {
            'fields': ('mode', 'status', 'tags')
        }),
        ('Location & File', {
            'fields': ('document_file', 'kent', 'location_display', 'file', 'file_size', 'number_of_pages')
        }),
        ('Dates', {
            'fields': ('document_date', 'expiry_date', 'is_expired')
        }),
        ('Generated Codes', {
            'fields': ('barcode_preview', 'qr_code_preview'),
            'classes': ('collapse',)
        }),
        ('Audit Information', {
            'fields': ('id', 'created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def barcode_preview(self, obj):
        """Display barcode preview"""
        if obj.barcode_image:
            return format_html('<img src="{}" style="max-width: 200px;" />', obj.barcode_image.url)
        return "No barcode generated"
    barcode_preview.short_description = 'Barcode'

    def qr_code_preview(self, obj):
        """Display QR code preview"""
        if obj.qr_code_image:
            return format_html('<img src="{}" style="max-width: 100px;" />', obj.qr_code_image.url)
        return "No QR code generated"
    qr_code_preview.short_description = 'QR Code'

    def is_expired(self, obj):
        """Display expiry status"""
        if obj.expiry_date:
            if obj.is_expired:
                return format_html('<span style="color: red;">Expired</span>')
            else:
                days_left = (obj.expiry_date - timezone.now().date()).days
                color = 'orange' if days_left <= 30 else 'green'
                return format_html('<span style="color: {};">{} days left</span>', color, days_left)
        return "No expiry date"
    is_expired.short_description = 'Expiry Status'

    def save_model(self, request, obj, form, change):
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'document_type__organization', 'kent__box__shelf__building', 'created_by'
        )


@admin.register(DocumentVersion)
class DocumentVersionAdmin(admin.ModelAdmin):
    """Document Version admin"""

    list_display = ['document', 'version_number', 'file_size', 'created_by', 'created_at']
    list_filter = ['document__document_type', 'created_at']
    search_fields = ['document__title', 'change_summary']
    readonly_fields = ['file_size', 'created_at']
    ordering = ['document', '-version_number']

    fieldsets = (
        ('Version Information', {
            'fields': ('document', 'version_number', 'file', 'file_size')
        }),
        ('Changes', {
            'fields': ('change_summary',)
        }),
        ('Audit Information', {
            'fields': ('created_by', 'created_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(DocumentTag)
class DocumentTagAdmin(admin.ModelAdmin):
    """Document Tag admin"""

    list_display = ['name', 'organization', 'color_preview', 'is_active', 'created_at']
    list_filter = ['organization', 'is_active', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at']
    ordering = ['organization', 'name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('organization', 'name', 'description', 'is_active')
        }),
        ('Display', {
            'fields': ('color',)
        }),
        ('Audit Information', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ['created_at', 'color_preview']

    def color_preview(self, obj):
        """Display color preview"""
        return format_html(
            '<div style="width: 20px; height: 20px; background-color: {}; border: 1px solid #ccc; display: inline-block;"></div>',
            obj.color
        )
    color_preview.short_description = 'Color'
