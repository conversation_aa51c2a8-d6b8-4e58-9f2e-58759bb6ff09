import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Chip,
} from '@mui/material';
import {
  Folder,
  Edit,
  Delete,
  Home,
  Description,
  Info,
  Business,
  LocationOn,
  Category,
  Person,
  Phone,
  Email,
  ContactMail,
  Numbers,
  CalendarToday,
  Archive,
  Visibility,
  MyLocation,
} from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import fileService, { type File } from '../../services/fileService';
import DetailPageTemplate from '../../components/common/DetailPageTemplate';
import ConfirmationDialog from '../../components/common/ConfirmationDialog';

const BusinessFileDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showNotification } = useNotification();
  
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    if (id) {
      loadFile();
    }
  }, [id]);

  const loadFile = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await fileService.getFile(id!);
      setFile(data);
    } catch (error) {
      console.error('Error loading file:', error);
      setError('Failed to load file');
      showNotification('Failed to load file', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    // Navigate back to business files page with edit state
    navigate('/document-center/business-files', { 
      state: { 
        editFile: file,
        showForm: true 
      } 
    });
  };

  const handleDelete = () => {
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!file) return;
    
    try {
      setDeleting(true);
      await fileService.deleteFile(file.id);
      showNotification('Business file deleted successfully', 'success');
      navigate('/document-center/business-files');
    } catch (error) {
      console.error('Error deleting file:', error);
      showNotification('Failed to delete business file', 'error');
    } finally {
      setDeleting(false);
      setDeleteDialogOpen(false);
    }
  };

  const handleBack = () => {
    navigate('/document-center/business-files');
  };

  const handleViewInShelf = () => {
    if (file) {
      // Navigate to shelf visualization with highlighted file and special indicator
      navigate(`/locations/visualization?highlight=${file.id}&type=file&source=file-detail`);
    }
  };

  if (!file && !loading && !error) {
    setError('Business file not found');
  }

  const breadcrumbs = [
    { label: 'Dashboard', path: '/dashboard', icon: <Home fontSize="small" /> },
    { label: 'Document Center', path: '/document-center', icon: <Description fontSize="small" /> },
    { label: 'Business Files', path: '/document-center/business-files', icon: <Folder fontSize="small" /> },
    { label: file?.name || 'Business File', path: undefined, icon: <Archive fontSize="small" /> },
  ];

  const actions = [
    {
      label: 'View in Shelf',
      icon: <MyLocation />,
      onClick: handleViewInShelf,
      color: 'secondary' as const,
      variant: 'contained' as const,
    },
    {
      label: 'Edit',
      icon: <Edit />,
      onClick: handleEdit,
      color: 'primary' as const,
    },
    {
      label: 'Delete',
      icon: <Delete />,
      onClick: handleDelete,
      color: 'error' as const,
    },
  ];

  const chips = file ? [
    {
      label: file.is_active ? 'Active' : 'Inactive',
      color: file.is_active ? 'success' as const : 'error' as const,
    },
    {
      label: file.status.charAt(0).toUpperCase() + file.status.slice(1),
      color: 'info' as const,
    },
    {
      label: file.file_type_name || 'Unknown Type',
      color: 'primary' as const,
    },
  ] : [];

  const sections = file ? [
    {
      title: 'Basic Information',
      icon: <Info />,
      content: (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'primary.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'primary.200'
          }}>
            <Folder sx={{ color: 'primary.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="primary.main" sx={{ fontWeight: 600 }}>
                File Name
              </Typography>
              <Typography variant="body2" color="primary.dark">
                {file.name}
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'info.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'info.200'
          }}>
            <Numbers sx={{ color: 'info.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="info.main" sx={{ fontWeight: 600 }}>
                File Number
              </Typography>
              <Typography variant="body2" color="info.dark">
                {file.file_number}
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'secondary.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'secondary.200'
          }}>
            <Archive sx={{ color: 'secondary.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="secondary.main" sx={{ fontWeight: 600 }}>
                Full Code
              </Typography>
              <Typography variant="body2" color="secondary.dark">
                {file.full_code}
              </Typography>
            </Box>
          </Box>
          
          {file.description && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" color="text.secondary" sx={{ fontWeight: 600, mb: 1 }}>
                Description
              </Typography>
              <Typography variant="body2" sx={{ 
                p: 2, 
                bgcolor: 'grey.50', 
                borderRadius: 1,
                border: '1px solid',
                borderColor: 'grey.200'
              }}>
                {file.description}
              </Typography>
            </Box>
          )}
        </Box>
      ),
    },
    {
      title: 'Location Information',
      icon: <LocationOn />,
      content: (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'success.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'success.200'
          }}>
            <LocationOn sx={{ color: 'success.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="success.main" sx={{ fontWeight: 600 }}>
                Location Path
              </Typography>
              <Typography variant="body2" color="success.dark">
                {file.location_path}
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'warning.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'warning.200'
          }}>
            <Business sx={{ color: 'warning.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="warning.main" sx={{ fontWeight: 600 }}>
                Building
              </Typography>
              <Typography variant="body2" color="warning.dark">
                {file.building_name || 'Not specified'}
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'error.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'error.200'
          }}>
            <Archive sx={{ color: 'error.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="error.main" sx={{ fontWeight: 600 }}>
                Kent Location
              </Typography>
              <Typography variant="body2" color="error.dark">
                {file.kent_location}
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'info.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'info.200'
          }}>
            <Business sx={{ color: 'info.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="info.main" sx={{ fontWeight: 600 }}>
                Organization
              </Typography>
              <Typography variant="body2" color="info.dark">
                {file.organization_name || 'Not specified'}
              </Typography>
            </Box>
          </Box>
        </Box>
      ),
    },
    {
      title: 'File Type & Classification',
      icon: <Category />,
      content: (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'primary.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'primary.200'
          }}>
            <Category sx={{ color: 'primary.main', mr: 2 }} />
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Box>
                <Typography variant="subtitle2" color="primary.main" sx={{ fontWeight: 600 }}>
                  File Type
                </Typography>
                <Typography variant="body2" color="primary.dark">
                  {file.file_type_name} ({file.file_type_code})
                </Typography>
              </Box>
              {file.file_type_color && (
                <Box
                  sx={{
                    width: 32,
                    height: 32,
                    borderRadius: 1,
                    bgcolor: file.file_type_color,
                    border: '2px solid',
                    borderColor: 'grey.300',
                  }}
                />
              )}
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'secondary.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'secondary.200'
          }}>
            <Archive sx={{ color: 'secondary.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="secondary.main" sx={{ fontWeight: 600 }}>
                Status
              </Typography>
              <Typography variant="body2" color="secondary.dark">
                {file.status.charAt(0).toUpperCase() + file.status.slice(1)}
              </Typography>
            </Box>
          </Box>
          
          {file.tags && file.tags.length > 0 && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" color="text.secondary" sx={{ fontWeight: 600, mb: 1 }}>
                Tags
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {file.tags.map((tag, index) => (
                  <Chip key={index} label={tag} size="small" color="primary" variant="outlined" />
                ))}
              </Box>
            </Box>
          )}
        </Box>
      ),
    },
    {
      title: 'Business Information',
      icon: <Business />,
      content: (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          {file.business_name && (
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              p: 2,
              bgcolor: 'success.50',
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'success.200'
            }}>
              <Business sx={{ color: 'success.main', mr: 2 }} />
              <Box>
                <Typography variant="subtitle2" color="success.main" sx={{ fontWeight: 600 }}>
                  Business Name
                </Typography>
                <Typography variant="body2" color="success.dark">
                  {file.business_name}
                </Typography>
              </Box>
            </Box>
          )}

          {file.tin_number && (
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              p: 2,
              bgcolor: 'warning.50',
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'warning.200'
            }}>
              <Numbers sx={{ color: 'warning.main', mr: 2 }} />
              <Box>
                <Typography variant="subtitle2" color="warning.main" sx={{ fontWeight: 600 }}>
                  TIN Number
                </Typography>
                <Typography variant="body2" color="warning.dark">
                  {file.tin_number}
                </Typography>
              </Box>
            </Box>
          )}

          {file.vat_number && (
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              p: 2,
              bgcolor: 'error.50',
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'error.200'
            }}>
              <Numbers sx={{ color: 'error.main', mr: 2 }} />
              <Box>
                <Typography variant="subtitle2" color="error.main" sx={{ fontWeight: 600 }}>
                  VAT Number
                </Typography>
                <Typography variant="body2" color="error.dark">
                  {file.vat_number}
                </Typography>
              </Box>
            </Box>
          )}

          {file.license_number && (
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              p: 2,
              bgcolor: 'info.50',
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'info.200'
            }}>
              <ContactMail sx={{ color: 'info.main', mr: 2 }} />
              <Box>
                <Typography variant="subtitle2" color="info.main" sx={{ fontWeight: 600 }}>
                  License Number
                </Typography>
                <Typography variant="body2" color="info.dark">
                  {file.license_number}
                </Typography>
              </Box>
            </Box>
          )}

          {file.owner_name && (
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              p: 2,
              bgcolor: 'primary.50',
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'primary.200'
            }}>
              <Person sx={{ color: 'primary.main', mr: 2 }} />
              <Box>
                <Typography variant="subtitle2" color="primary.main" sx={{ fontWeight: 600 }}>
                  Owner Name
                </Typography>
                <Typography variant="body2" color="primary.dark">
                  {file.owner_name}
                </Typography>
              </Box>
            </Box>
          )}

          {file.phone_number && (
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              p: 2,
              bgcolor: 'secondary.50',
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'secondary.200'
            }}>
              <Phone sx={{ color: 'secondary.main', mr: 2 }} />
              <Box>
                <Typography variant="subtitle2" color="secondary.main" sx={{ fontWeight: 600 }}>
                  Phone Number
                </Typography>
                <Typography variant="body2" color="secondary.dark">
                  {file.phone_number}
                </Typography>
              </Box>
            </Box>
          )}

          {file.email && (
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              p: 2,
              bgcolor: 'success.50',
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'success.200'
            }}>
              <Email sx={{ color: 'success.main', mr: 2 }} />
              <Box>
                <Typography variant="subtitle2" color="success.main" sx={{ fontWeight: 600 }}>
                  Email Address
                </Typography>
                <Typography variant="body2" color="success.dark">
                  {file.email}
                </Typography>
              </Box>
            </Box>
          )}

          {file.address && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" color="text.secondary" sx={{ fontWeight: 600, mb: 1 }}>
                Address
              </Typography>
              <Typography variant="body2" sx={{
                p: 2,
                bgcolor: 'grey.50',
                borderRadius: 1,
                border: '1px solid',
                borderColor: 'grey.200'
              }}>
                {file.address}
              </Typography>
            </Box>
          )}
        </Box>
      ),
    },
    {
      title: 'Documents & Activity',
      icon: <Description />,
      content: (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            p: 2,
            bgcolor: 'primary.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'primary.200'
          }}>
            <Description sx={{ color: 'primary.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="primary.main" sx={{ fontWeight: 600 }}>
                Documents
              </Typography>
              <Typography variant="body2" color="primary.dark">
                {file.document_count || 0} documents
              </Typography>
            </Box>
          </Box>

          {file.document_types && file.document_types.length > 0 && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" color="text.secondary" sx={{ fontWeight: 600, mb: 1 }}>
                Document Types
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {file.document_types.map((type, index) => (
                  <Chip key={index} label={type} size="small" color="info" variant="outlined" />
                ))}
              </Box>
            </Box>
          )}

          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            p: 2,
            bgcolor: 'info.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'info.200'
          }}>
            <CalendarToday sx={{ color: 'info.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="info.main" sx={{ fontWeight: 600 }}>
                Created Date
              </Typography>
              <Typography variant="body2" color="info.dark">
                {file.created_at ? new Date(file.created_at).toLocaleDateString() : 'Not specified'}
              </Typography>
            </Box>
          </Box>

          {file.last_activity_date && (
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              p: 2,
              bgcolor: 'warning.50',
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'warning.200'
            }}>
              <CalendarToday sx={{ color: 'warning.main', mr: 2 }} />
              <Box>
                <Typography variant="subtitle2" color="warning.main" sx={{ fontWeight: 600 }}>
                  Last Activity
                </Typography>
                <Typography variant="body2" color="warning.dark">
                  {new Date(file.last_activity_date).toLocaleDateString()}
                </Typography>
              </Box>
            </Box>
          )}
        </Box>
      ),
    },
  ] : [];

  return (
    <>
      <DetailPageTemplate
        loading={loading}
        error={error}
        breadcrumbs={breadcrumbs}
        title={file?.name || 'Business File'}
        subtitle={file?.file_number ? `File #${file.file_number}` : undefined}
        avatar={{
          fallbackIcon: <Folder sx={{ fontSize: 40 }} />,
          alt: file?.name || 'Business File',
        }}
        chips={chips}
        actions={actions}
        sections={sections}
        onBack={handleBack}
      />

      <ConfirmationDialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleConfirmDelete}
        title="Delete Business File"
        itemName={file?.name}
        itemType="Business File"
        message={`Are you sure you want to delete "${file?.name}"? This will affect ${file?.document_count || 0} documents.`}
        confirmText="Delete Business File"
        severity="error"
        loading={deleting}
      />
    </>
  );
};

export default BusinessFileDetailPage;
