import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  TextField,
  InputAdornment,
  Autocomplete,
  Paper,
  Typography,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemButton,
  Divider,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Collapse,
  IconButton,
} from '@mui/material';
import {
  Search,
  Business,
  Shelves,
  Inventory,
  LocationOn,
  FilterList,
  Clear,
  ExpandMore,
  ExpandLess,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useNotification } from '../../contexts/NotificationContext';
import locationService from '../../services/locationService';
import organizationService from '../../services/organizationService';
import type { Organization } from '../../services/types';

interface SearchResult {
  type: 'building' | 'shelf' | 'kent';
  id: number;
  name: string;
  code: string;
  full_code: string;
  description?: string;
  organization_name: string;
  location_path: string;
  building_name?: string;
  shelf_name?: string;
  file_count?: number;
  kent_count?: number;
  shelf_count?: number;
  document_count?: number;
  utilization?: number;
  is_full?: boolean;
}

interface LocationSearchProps {
  onResultSelect?: (result: SearchResult) => void;
  placeholder?: string;
  showFilters?: boolean;
  autoFocus?: boolean;
  variant?: 'outlined' | 'filled' | 'standard';
}

const LocationSearch: React.FC<LocationSearchProps> = ({
  onResultSelect,
  placeholder = 'Search locations...',
  showFilters = true,
  autoFocus = false,
  variant = 'outlined',
}) => {
  const navigate = useNavigate();
  const { showError } = useNotification();

  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  // Filters
  const [selectedType, setSelectedType] = useState<'all' | 'building' | 'shelf' | 'kent'>('all');
  const [selectedOrganization, setSelectedOrganization] = useState<number | ''>('');
  const [minUtilization, setMinUtilization] = useState<number | ''>('');
  const [maxUtilization, setMaxUtilization] = useState<number | ''>('');
  const [onlyFull, setOnlyFull] = useState(false);

  useEffect(() => {
    loadOrganizations();
  }, []);

  const loadOrganizations = async () => {
    try {
      const data = await organizationService.getOrganizations();
      setOrganizations(data.results);
    } catch (err) {
      showError('Failed to load organizations');
    }
  };

  const searchLocations = useCallback(async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([]);
      return;
    }

    try {
      setLoading(true);
      const searchResults = await locationService.searchLocations(
        searchQuery,
        selectedType === 'all' ? undefined : selectedType
      );
      
      // Apply additional filters
      let filteredResults = searchResults;
      
      if (selectedOrganization) {
        filteredResults = filteredResults.filter(
          (result: SearchResult) => result.organization_name === organizations.find(org => org.id === selectedOrganization)?.name
        );
      }
      
      if (minUtilization !== '') {
        filteredResults = filteredResults.filter(
          (result: SearchResult) => (result.utilization || 0) >= minUtilization
        );
      }
      
      if (maxUtilization !== '') {
        filteredResults = filteredResults.filter(
          (result: SearchResult) => (result.utilization || 0) <= maxUtilization
        );
      }
      
      if (onlyFull) {
        filteredResults = filteredResults.filter(
          (result: SearchResult) => result.is_full === true
        );
      }
      
      setResults(filteredResults);
    } catch (err) {
      showError('Search failed');
      setResults([]);
    } finally {
      setLoading(false);
    }
  }, [selectedType, selectedOrganization, minUtilization, maxUtilization, onlyFull, organizations, showError]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      searchLocations(query);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [query, searchLocations]);

  const handleResultClick = (result: SearchResult) => {
    if (onResultSelect) {
      onResultSelect(result);
    } else {
      // Default navigation behavior
      const path = result.type === 'building' 
        ? `/locations/buildings/${result.id}`
        : result.type === 'shelf'
        ? `/locations/shelves/${result.id}`
        : `/locations/kents/${result.id}`;
      navigate(path);
    }
    setQuery('');
    setResults([]);
  };

  const clearFilters = () => {
    setSelectedType('all');
    setSelectedOrganization('');
    setMinUtilization('');
    setMaxUtilization('');
    setOnlyFull(false);
  };

  const getResultIcon = (type: string) => {
    switch (type) {
      case 'building':
        return <Business />;
      case 'shelf':
        return <Shelves />;
      case 'kent':
        return <Inventory />;
      default:
        return <LocationOn />;
    }
  };

  const getResultColor = (type: string) => {
    switch (type) {
      case 'building':
        return 'primary.main';
      case 'shelf':
        return 'secondary.main';
      case 'kent':
        return 'success.main';
      default:
        return 'grey.500';
    }
  };

  const formatResultSecondaryText = (result: SearchResult) => {
    const parts = [result.full_code];
    if (result.type === 'shelf' && result.building_name) {
      parts.push(result.building_name);
    }
    if (result.type === 'kent' && result.shelf_name && result.building_name) {
      parts.push(`${result.building_name} → ${result.shelf_name}`);
    }
    return parts.join(' • ');
  };

  const getResultStats = (result: SearchResult) => {
    const stats = [];
    if (result.file_count !== undefined) stats.push(`${result.file_count} files`);
    if (result.document_count !== undefined) stats.push(`${result.document_count} docs`);
    if (result.kent_count !== undefined) stats.push(`${result.kent_count} kents`);
    if (result.shelf_count !== undefined) stats.push(`${result.shelf_count} shelves`);
    if (result.utilization !== undefined) stats.push(`${result.utilization.toFixed(1)}% used`);
    return stats;
  };

  return (
    <Box sx={{ position: 'relative' }}>
      <TextField
        fullWidth
        variant={variant}
        placeholder={placeholder}
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        autoFocus={autoFocus}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Search />
            </InputAdornment>
          ),
          endAdornment: (
            <InputAdornment position="end">
              {loading && <CircularProgress size={20} />}
              {showFilters && (
                <IconButton
                  size="small"
                  onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                  color={showAdvancedFilters ? 'primary' : 'default'}
                >
                  <FilterList />
                </IconButton>
              )}
            </InputAdornment>
          ),
        }}
      />

      {/* Advanced Filters */}
      {showFilters && (
        <Collapse in={showAdvancedFilters}>
          <Paper sx={{ mt: 1, p: 2, border: 1, borderColor: 'divider' }}>
            <Typography variant="subtitle2" gutterBottom>
              Advanced Filters
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'center' }}>
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Type</InputLabel>
                <Select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value as any)}
                  label="Type"
                >
                  <MenuItem value="all">All Types</MenuItem>
                  <MenuItem value="building">Buildings</MenuItem>
                  <MenuItem value="shelf">Shelves</MenuItem>
                  <MenuItem value="kent">Kents</MenuItem>
                </Select>
              </FormControl>

              <FormControl size="small" sx={{ minWidth: 150 }}>
                <InputLabel>Organization</InputLabel>
                <Select
                  value={selectedOrganization}
                  onChange={(e) => setSelectedOrganization(e.target.value as number)}
                  label="Organization"
                >
                  <MenuItem value="">All Organizations</MenuItem>
                  {organizations.map((org) => (
                    <MenuItem key={org.id} value={org.id}>
                      {org.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <TextField
                size="small"
                label="Min Utilization %"
                type="number"
                value={minUtilization}
                onChange={(e) => setMinUtilization(e.target.value ? parseInt(e.target.value) : '')}
                sx={{ width: 140 }}
                inputProps={{ min: 0, max: 100 }}
              />

              <TextField
                size="small"
                label="Max Utilization %"
                type="number"
                value={maxUtilization}
                onChange={(e) => setMaxUtilization(e.target.value ? parseInt(e.target.value) : '')}
                sx={{ width: 140 }}
                inputProps={{ min: 0, max: 100 }}
              />

              <Button
                size="small"
                variant={onlyFull ? 'contained' : 'outlined'}
                onClick={() => setOnlyFull(!onlyFull)}
              >
                Full Only
              </Button>

              <Button
                size="small"
                startIcon={<Clear />}
                onClick={clearFilters}
              >
                Clear
              </Button>
            </Box>
          </Paper>
        </Collapse>
      )}

      {/* Search Results */}
      {results.length > 0 && (
        <Paper
          sx={{
            position: 'absolute',
            top: '100%',
            left: 0,
            right: 0,
            zIndex: 1000,
            maxHeight: 400,
            overflow: 'auto',
            mt: 1,
            border: 1,
            borderColor: 'divider',
          }}
        >
          <List dense>
            {results.map((result, index) => (
              <React.Fragment key={`${result.type}-${result.id}`}>
                <ListItemButton onClick={() => handleResultClick(result)}>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: getResultColor(result.type), width: 32, height: 32 }}>
                      {getResultIcon(result.type)}
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="subtitle2">
                          {result.name}
                        </Typography>
                        <Chip
                          label={result.type}
                          size="small"
                          variant="outlined"
                          sx={{ textTransform: 'capitalize' }}
                        />
                        {result.is_full && (
                          <Chip label="Full" size="small" color="error" />
                        )}
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="caption" color="text.secondary">
                          {formatResultSecondaryText(result)}
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 1, mt: 0.5, flexWrap: 'wrap' }}>
                          {getResultStats(result).map((stat, statIndex) => (
                            <Chip
                              key={statIndex}
                              label={stat}
                              size="small"
                              variant="outlined"
                              sx={{ fontSize: '0.7rem', height: 20 }}
                            />
                          ))}
                        </Box>
                      </Box>
                    }
                  />
                </ListItemButton>
                {index < results.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        </Paper>
      )}

      {query && !loading && results.length === 0 && (
        <Paper
          sx={{
            position: 'absolute',
            top: '100%',
            left: 0,
            right: 0,
            zIndex: 1000,
            mt: 1,
            p: 2,
            textAlign: 'center',
            border: 1,
            borderColor: 'divider',
          }}
        >
          <Typography variant="body2" color="text.secondary">
            No locations found for "{query}"
          </Typography>
        </Paper>
      )}
    </Box>
  );
};

export default LocationSearch;
