from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from taxpayers.models import (
    BusinessSector, BusinessSubSector, TaxPayerLevel, 
    OrganizationBusinessType, IndividualTaxPayer, OrganizationTaxPayer
)
from locations.location_hierarchy_models import SubCity, <PERSON>bele
from datetime import date, datetime
import random

User = get_user_model()


class Command(BaseCommand):
    help = 'Populate taxpayer system with sample data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing data before populating',
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write('Clearing existing data...')
            IndividualTaxPayer.objects.all().delete()
            OrganizationTaxPayer.objects.all().delete()
            BusinessSubSector.objects.all().delete()
            BusinessSector.objects.all().delete()
            TaxPayerLevel.objects.all().delete()
            OrganizationBusinessType.objects.all().delete()

        # Get or create admin user
        admin_user = User.objects.filter(is_superuser=True).first()
        if not admin_user:
            admin_user = User.objects.create_superuser(
                username='admin',
                email='<EMAIL>',
                password='admin123'
            )

        # Create Tax Payer Levels
        self.stdout.write('Creating tax payer levels...')
        levels_data = [
            {'code': 'A', 'name': 'Category A', 'description': 'Large taxpayers with annual turnover > 1M ETB', 'min_turnover': 1000000},
            {'code': 'B', 'name': 'Category B', 'description': 'Medium taxpayers with annual turnover 500K-1M ETB', 'min_turnover': 500000, 'max_turnover': 1000000},
            {'code': 'C', 'name': 'Category C', 'description': 'Small taxpayers with annual turnover 100K-500K ETB', 'min_turnover': 100000, 'max_turnover': 500000},
            {'code': 'D', 'name': 'Category D', 'description': 'Micro taxpayers with annual turnover < 100K ETB', 'max_turnover': 100000},
        ]
        
        for level_data in levels_data:
            level, created = TaxPayerLevel.objects.get_or_create(
                code=level_data['code'],
                defaults={
                    'name': level_data['name'],
                    'description': level_data['description'],
                    'minimum_annual_turnover': level_data.get('min_turnover'),
                    'maximum_annual_turnover': level_data.get('max_turnover'),
                }
            )
            if created:
                self.stdout.write(f'  Created level: {level.name}')

        # Create Organization Business Types
        self.stdout.write('Creating organization business types...')
        org_types_data = [
            {'code': 'PLC', 'name': 'Private Limited Company', 'description': 'Private Limited Company', 'requires_vat': True},
            {'code': 'SC', 'name': 'Share Company', 'description': 'Share Company', 'requires_vat': True},
            {'code': 'COOP', 'name': 'Cooperative', 'description': 'Cooperative Organization', 'requires_vat': False},
            {'code': 'NGO', 'name': 'Non-Governmental Organization', 'description': 'NGO', 'requires_vat': False},
            {'code': 'SOLE', 'name': 'Sole Proprietorship', 'description': 'Sole Proprietorship', 'requires_vat': False},
        ]
        
        for org_type_data in org_types_data:
            org_type, created = OrganizationBusinessType.objects.get_or_create(
                code=org_type_data['code'],
                defaults={
                    'name': org_type_data['name'],
                    'description': org_type_data['description'],
                    'requires_vat_registration': org_type_data['requires_vat'],
                }
            )
            if created:
                self.stdout.write(f'  Created org type: {org_type.name}')

        # Create Business Sectors
        self.stdout.write('Creating business sectors...')
        sectors_data = [
            {'code': 'AGR', 'name': 'Agriculture', 'description': 'Agriculture and farming'},
            {'code': 'MAN', 'name': 'Manufacturing', 'description': 'Manufacturing and production'},
            {'code': 'TRD', 'name': 'Trade', 'description': 'Trade and commerce'},
            {'code': 'SRV', 'name': 'Services', 'description': 'Service industries'},
            {'code': 'CON', 'name': 'Construction', 'description': 'Construction and real estate'},
            {'code': 'TEC', 'name': 'Technology', 'description': 'Information technology'},
        ]
        
        sectors = {}
        for sector_data in sectors_data:
            sector, created = BusinessSector.objects.get_or_create(
                code=sector_data['code'],
                defaults={
                    'name': sector_data['name'],
                    'description': sector_data['description'],
                }
            )
            sectors[sector_data['code']] = sector
            if created:
                self.stdout.write(f'  Created sector: {sector.name}')

        # Create Business Sub-Sectors
        self.stdout.write('Creating business sub-sectors...')
        sub_sectors_data = [
            {'sector': 'AGR', 'code': '001', 'name': 'Crop Production', 'description': 'Crop farming and production'},
            {'sector': 'AGR', 'code': '002', 'name': 'Livestock', 'description': 'Livestock farming'},
            {'sector': 'MAN', 'code': '001', 'name': 'Food Processing', 'description': 'Food and beverage processing'},
            {'sector': 'MAN', 'code': '002', 'name': 'Textile', 'description': 'Textile and garment manufacturing'},
            {'sector': 'TRD', 'code': '001', 'name': 'Retail Trade', 'description': 'Retail trading'},
            {'sector': 'TRD', 'code': '002', 'name': 'Wholesale Trade', 'description': 'Wholesale trading'},
            {'sector': 'SRV', 'code': '001', 'name': 'Financial Services', 'description': 'Banking and financial services'},
            {'sector': 'SRV', 'code': '002', 'name': 'Professional Services', 'description': 'Professional and consulting services'},
            {'sector': 'CON', 'code': '001', 'name': 'Building Construction', 'description': 'Building and infrastructure construction'},
            {'sector': 'TEC', 'code': '001', 'name': 'Software Development', 'description': 'Software development and IT services'},
        ]
        
        sub_sectors = {}
        for sub_sector_data in sub_sectors_data:
            sector = sectors[sub_sector_data['sector']]
            sub_sector, created = BusinessSubSector.objects.get_or_create(
                business_sector=sector,
                code=sub_sector_data['code'],
                defaults={
                    'name': sub_sector_data['name'],
                    'description': sub_sector_data['description'],
                }
            )
            sub_sectors[f"{sub_sector_data['sector']}-{sub_sector_data['code']}"] = sub_sector
            if created:
                self.stdout.write(f'  Created sub-sector: {sub_sector.name}')

        # Create sample individual taxpayers
        self.stdout.write('Creating sample individual taxpayers...')
        individual_names = [
            ('Abebe', 'Kebede', 'M', 'Abebe General Trading'),
            ('Almaz', 'Tadesse', 'F', 'Almaz Fashion Store'),
            ('Dawit', 'Haile', 'M', 'Dawit Electronics'),
            ('Hanan', 'Mohammed', 'F', 'Hanan Restaurant'),
            ('Tekle', 'Wolde', 'M', 'Tekle Construction'),
        ]

        levels = list(TaxPayerLevel.objects.all())
        sectors_list = list(BusinessSector.objects.all())

        for i, (first_name, last_name, gender, business_name) in enumerate(individual_names):
            sector = random.choice(sectors_list)
            sub_sector = sector.sub_sectors.first()
            level = random.choice(levels)

            individual = IndividualTaxPayer.objects.create(
                tin=f'*********{i+1}',
                first_name=first_name,
                last_name=last_name,
                nationality='ET',
                gender=gender,
                date_of_birth=date(1980 + i, 1 + i, 15),
                tax_payer_level=level,
                business_sector=sector,
                business_sub_sector=sub_sector,
                business_registration_date=date(2020 + i, 1, 1),
                business_name=business_name,
                phone=f'+***********{i}',
                email=f'{first_name.lower()}@example.com',
                created_by=admin_user
            )
            self.stdout.write(f'  Created individual: {individual.get_full_name()}')

        # Create sample organization taxpayers
        self.stdout.write('Creating sample organization taxpayers...')
        organization_names = [
            ('Addis Software Solutions PLC', 'ASS PLC', 'TEC', 'PLC'),
            ('Ethiopian Trading Company', 'ETC', 'TRD', 'PLC'),
            ('Habesha Manufacturing SC', 'HMS', 'MAN', 'SC'),
            ('Green Agriculture Cooperative', 'GAC', 'AGR', 'COOP'),
            ('Modern Construction PLC', 'MCP', 'CON', 'PLC'),
        ]

        org_types = {ot.code: ot for ot in OrganizationBusinessType.objects.all()}

        for i, (business_name, trade_name, sector_code, org_type_code) in enumerate(organization_names):
            sector = sectors[sector_code]
            sub_sector = sector.sub_sectors.first()
            level = random.choice(levels)
            org_type = org_types[org_type_code]

            organization = OrganizationTaxPayer.objects.create(
                tin=f'*********{i+1}',
                business_name=business_name,
                trade_name=trade_name,
                organization_business_type=org_type,
                tax_payer_level=level,
                business_sector=sector,
                business_sub_sector=sub_sector,
                business_registration_date=date(2018 + i, 1, 1),
                manager_first_name='Manager',
                manager_last_name=f'Name{i+1}',
                manager_title='General Manager',
                vat_registration_date=date(2018 + i, 2, 1) if org_type.requires_vat_registration else None,
                vat_number=f'VAT{i+1:03d}' if org_type.requires_vat_registration else None,
                phone=f'+***********{i}',
                email=f'info@{trade_name.lower().replace(" ", "")}.com',
                capital_amount=1000000 + (i * 500000),
                number_of_employees=10 + (i * 5),
                created_by=admin_user
            )
            self.stdout.write(f'  Created organization: {organization.business_name}')

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully populated taxpayer system with sample data!'
            )
        )
