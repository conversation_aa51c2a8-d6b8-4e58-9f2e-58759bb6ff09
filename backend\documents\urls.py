from django.urls import path
from . import views

app_name = 'documents'

urlpatterns = [
    # Document Type endpoints
    path('types/', views.DocumentTypeListCreateView.as_view(), name='document_type_list_create'),
    path('types/<int:pk>/', views.DocumentTypeDetailView.as_view(), name='document_type_detail'),
    
    # Document endpoints
    path('', views.DocumentListCreateView.as_view(), name='document_list_create'),
    path('<uuid:pk>/', views.DocumentDetailView.as_view(), name='document_detail'),
    path('search/', views.DocumentSearchView.as_view(), name='document_search'),
    path('stats/', views.DocumentStatsView.as_view(), name='document_stats'),
    
    # Document Version endpoints
    path('<uuid:document_pk>/versions/', views.DocumentVersionListCreateView.as_view(), name='document_version_list_create'),
    path('versions/<int:pk>/', views.DocumentVersionDetailView.as_view(), name='document_version_detail'),
    
    # Document Tag endpoints
    path('tags/', views.DocumentTagListCreateView.as_view(), name='document_tag_list_create'),
    path('tags/<int:pk>/', views.DocumentTagDetailView.as_view(), name='document_tag_detail'),
    
    # File operations
    path('<uuid:pk>/upload-file/', views.DocumentUploadFileView.as_view(), name='document_upload_file'),
    path('<uuid:pk>/download/', views.DocumentDownloadView.as_view(), name='document_download'),
    path('<uuid:pk>/view/', views.DocumentViewView.as_view(), name='document_view'),
    path('versions/<int:pk>/download/', views.DocumentVersionDownloadView.as_view(), name='document_version_download'),

    # Code generation
    path('<uuid:pk>/generate-barcode/', views.DocumentGenerateBarcodeView.as_view(), name='document_generate_barcode'),
    path('<uuid:pk>/generate-qr/', views.DocumentGenerateQRView.as_view(), name='document_generate_qr'),
]
