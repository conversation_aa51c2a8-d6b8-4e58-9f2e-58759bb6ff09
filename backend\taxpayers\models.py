from django.db import models
from django.core.validators import RegexValidator, EmailValidator
from django.conf import settings
from django.utils import timezone
import uuid
from locations.location_hierarchy_models import Country, Region, Zone, City, SubCity, Kebele
from locations.models import File


class BusinessSector(models.Model):
    """Business sectors for tax classification"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    code = models.CharField(max_length=10, unique=True, help_text="Unique sector code (e.g., AGR, MAN, SER)")
    name = models.CharField(max_length=100, help_text="Sector name (e.g., Agriculture, Manufacturing)")
    description = models.TextField(blank=True, help_text="Detailed description of the sector")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        # Convert code to uppercase for case-insensitive storage
        if self.code:
            self.code = self.code.upper()
        super().save(*args, **kwargs)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        ordering = ['name']
        verbose_name = 'Business Sector'
        verbose_name_plural = 'Business Sectors'

    def __str__(self):
        return f"{self.code} - {self.name}"


class BusinessSubSector(models.Model):
    """Business sub-sectors for detailed tax classification"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    business_sector = models.ForeignKey(
        BusinessSector, 
        on_delete=models.CASCADE, 
        related_name='sub_sectors',
        help_text="Parent business sector"
    )
    code = models.CharField(max_length=15, help_text="Sub-sector code (e.g., AGR001, MAN002)")
    name = models.CharField(max_length=150, help_text="Sub-sector name")
    description = models.TextField(blank=True, help_text="Detailed description of the sub-sector")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        ordering = ['business_sector__name', 'name']
        unique_together = ['business_sector', 'code']
        verbose_name = 'Business Sub-Sector'
        verbose_name_plural = 'Business Sub-Sectors'

    def __str__(self):
        return f"{self.business_sector.code}-{self.code} - {self.name}"

    @property
    def full_code(self):
        return f"{self.business_sector.code}-{self.code}"

    def save(self, *args, **kwargs):
        # Convert code to uppercase for case-insensitive storage
        if self.code:
            self.code = self.code.upper()
        super().save(*args, **kwargs)


class TaxPayerLevel(models.Model):
    """Tax payer classification levels with daily income ranges for annual assessment"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, help_text="Level name (e.g., Large Taxpayer)")
    code = models.CharField(max_length=5, unique=True, help_text="Level code (e.g., A, B, C, D)")
    description = models.TextField(help_text="Criteria and description for this level")

    # Annual turnover ranges (legacy - kept for compatibility)
    minimum_annual_turnover = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Minimum annual turnover for this level"
    )
    maximum_annual_turnover = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Maximum annual turnover for this level"
    )

    # Daily income ranges for assessment
    min_daily_income = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        help_text="Minimum average daily income for this level (in local currency)"
    )
    max_daily_income = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Maximum average daily income for this level (null for unlimited)"
    )

    # Level priority for automatic upgrades (lower number = higher level)
    priority = models.IntegerField(
        default=1,
        help_text="Level priority (1=highest level like A, 3=lowest level like C)"
    )

    # Tax rate information
    tax_rate_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        help_text="Tax rate percentage for this level"
    )

    # Assessment settings
    requires_annual_assessment = models.BooleanField(
        default=True,
        help_text="Whether taxpayers at this level require annual income assessment"
    )

    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['priority', 'code']
        verbose_name = 'Tax Payer Level'
        verbose_name_plural = 'Tax Payer Levels'

    def __str__(self):
        income_range = f"${self.min_daily_income}"
        if self.max_daily_income:
            income_range += f"-${self.max_daily_income}"
        else:
            income_range += "+"
        return f"{self.code} - {self.name} ({income_range}/day)"

    def save(self, *args, **kwargs):
        # Convert code to uppercase for case-insensitive storage
        if self.code:
            self.code = self.code.upper()
        super().save(*args, **kwargs)

    def is_income_in_range(self, daily_income):
        """Check if daily income falls within this level's range"""
        if daily_income < self.min_daily_income:
            return False
        if self.max_daily_income and daily_income > self.max_daily_income:
            return False
        return True

    @classmethod
    def get_appropriate_level(cls, daily_income):
        """Get the appropriate level for a given daily income"""
        levels = cls.objects.filter(is_active=True).order_by('priority')
        for level in levels:
            if level.is_income_in_range(daily_income):
                return level
        return None

    @classmethod
    def get_higher_levels(cls, current_level):
        """Get levels higher than the current level"""
        if not current_level:
            return cls.objects.none()
        return cls.objects.filter(
            is_active=True,
            priority__lt=current_level.priority
        ).order_by('priority')


class DailyIncomeAnalysis(models.Model):
    """Annual daily income analysis for taxpayer level assessment"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Analysis period
    analysis_year = models.IntegerField(help_text="Year of analysis (e.g., 2024)")
    analysis_date = models.DateField(auto_now_add=True, help_text="Date when analysis was conducted")

    # Taxpayer reference (polymorphic - can be individual or organization)
    taxpayer_type = models.CharField(
        max_length=20,
        choices=[
            ('individual', 'Individual Taxpayer'),
            ('organization', 'Organization Taxpayer'),
        ],
        help_text="Type of taxpayer"
    )
    taxpayer_id = models.UUIDField(help_text="ID of the taxpayer (individual or organization)")

    # Income analysis data
    total_annual_income = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Total annual income reported/calculated"
    )
    average_daily_income = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Average daily income (total_annual_income / 365)"
    )
    working_days = models.IntegerField(
        default=365,
        help_text="Number of working days considered for calculation"
    )

    # Current and recommended levels
    current_level = models.ForeignKey(
        TaxPayerLevel,
        on_delete=models.PROTECT,
        related_name='current_analyses',
        help_text="Current taxpayer level"
    )
    recommended_level = models.ForeignKey(
        TaxPayerLevel,
        on_delete=models.PROTECT,
        related_name='recommended_analyses',
        null=True,
        blank=True,
        help_text="Recommended level based on income analysis"
    )

    # Analysis status
    STATUS_CHOICES = [
        ('pending', 'Pending Review'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('implemented', 'Level Change Implemented'),
    ]
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        help_text="Status of the analysis"
    )

    # Review information
    reviewed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='reviewed_analyses',
        help_text="Revenue expert who reviewed this analysis"
    )
    reviewed_at = models.DateTimeField(null=True, blank=True)
    review_notes = models.TextField(blank=True, help_text="Notes from the reviewer")

    # Analysis metadata
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_analyses',
        help_text="User who created this analysis"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-analysis_year', '-created_at']
        verbose_name = 'Daily Income Analysis'
        verbose_name_plural = 'Daily Income Analyses'
        unique_together = ['taxpayer_type', 'taxpayer_id', 'analysis_year']

    def __str__(self):
        return f"{self.analysis_year} Analysis - {self.taxpayer_type} {self.taxpayer_id} - ${self.average_daily_income}/day"

    @property
    def requires_level_upgrade(self):
        """Check if this analysis recommends a level upgrade"""
        return (
            self.recommended_level and
            self.recommended_level.priority < self.current_level.priority
        )

    @property
    def taxpayer_name(self):
        """Get the name of the taxpayer"""
        if self.taxpayer_type == 'individual':
            try:
                from .models import IndividualTaxPayer
                taxpayer = IndividualTaxPayer.objects.get(id=self.taxpayer_id)
                return taxpayer.full_name
            except:
                return f"Individual {self.taxpayer_id}"
        else:
            try:
                from .models import OrganizationTaxPayer
                taxpayer = OrganizationTaxPayer.objects.get(id=self.taxpayer_id)
                return taxpayer.business_name
            except:
                return f"Organization {self.taxpayer_id}"

    def approve_level_change(self, reviewed_by_user):
        """Approve the level change and update taxpayer"""
        if not self.recommended_level or not self.requires_level_upgrade:
            raise ValueError("No level upgrade recommended")

        # Update the taxpayer's level
        if self.taxpayer_type == 'individual':
            from .models import IndividualTaxPayer
            taxpayer = IndividualTaxPayer.objects.get(id=self.taxpayer_id)
            taxpayer.tax_payer_level = self.recommended_level
            taxpayer.save()
        else:
            from .models import OrganizationTaxPayer
            taxpayer = OrganizationTaxPayer.objects.get(id=self.taxpayer_id)
            taxpayer.tax_payer_level = self.recommended_level
            taxpayer.save()

        # Update analysis status
        self.status = 'implemented'
        self.reviewed_by = reviewed_by_user
        self.reviewed_at = timezone.now()
        self.save()


class LevelUpgradeNotification(models.Model):
    """Notifications for taxpayer level upgrades pending approval"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Related analysis
    analysis = models.OneToOneField(
        DailyIncomeAnalysis,
        on_delete=models.CASCADE,
        related_name='notification',
        help_text="Related income analysis"
    )

    # Notification details
    title = models.CharField(max_length=200, help_text="Notification title")
    message = models.TextField(help_text="Notification message")

    # Status
    is_read = models.BooleanField(default=False, help_text="Whether notification has been read")
    is_dismissed = models.BooleanField(default=False, help_text="Whether notification has been dismissed")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    read_at = models.DateTimeField(null=True, blank=True)
    dismissed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Level Upgrade Notification'
        verbose_name_plural = 'Level Upgrade Notifications'

    def __str__(self):
        return f"Level Upgrade: {self.analysis.taxpayer_name} ({self.analysis.analysis_year})"

    def mark_as_read(self):
        """Mark notification as read"""
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save()

    def dismiss(self):
        """Dismiss the notification"""
        self.is_dismissed = True
        self.dismissed_at = timezone.now()
        self.save()

    @classmethod
    def create_for_analysis(cls, analysis):
        """Create a notification for a level upgrade analysis"""
        if not analysis.requires_level_upgrade:
            return None

        title = f"Level Upgrade Recommended: {analysis.taxpayer_name}"
        message = (
            f"Based on {analysis.analysis_year} income analysis, "
            f"{analysis.taxpayer_name} qualifies for upgrade from "
            f"Level {analysis.current_level.code} to Level {analysis.recommended_level.code}. "
            f"Average daily income: ${analysis.average_daily_income}"
        )

        notification, created = cls.objects.get_or_create(
            analysis=analysis,
            defaults={
                'title': title,
                'message': message,
            }
        )

        return notification


class OrganizationBusinessType(models.Model):
    """Types of business organizations - dynamically created by administrators"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    code = models.CharField(max_length=10, unique=True, help_text="Business type code (e.g., PLC, SC, COOP)")
    name = models.CharField(max_length=100, help_text="Business type name (e.g., Private Limited Company)")
    description = models.TextField(blank=True, help_text="Description of the business type")
    requires_vat_registration = models.BooleanField(default=True, help_text="Whether this type requires VAT registration")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']
        verbose_name = 'Organization Business Type'
        verbose_name_plural = 'Organization Business Types'

    def __str__(self):
        return f"{self.code} - {self.name}"

    def save(self, *args, **kwargs):
        # Convert code to uppercase for case-insensitive storage
        if self.code:
            self.code = self.code.upper()
        super().save(*args, **kwargs)


class BaseContact(models.Model):
    """Abstract base model for contact information"""
    phone_regex = RegexValidator(
        regex=r'^\+?1?\d{9,15}$',
        message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed."
    )
    
    phone = models.CharField(validators=[phone_regex], max_length=17, help_text="Primary phone number")
    phone_secondary = models.CharField(
        validators=[phone_regex], 
        max_length=17, 
        blank=True, 
        help_text="Secondary phone number"
    )
    email = models.EmailField(blank=True, help_text="Email address")
    
    # Location information - Full hierarchy
    country = models.ForeignKey(
        Country,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Country"
    )
    region = models.ForeignKey(
        Region,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Region/State"
    )
    zone = models.ForeignKey(
        Zone,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Zone"
    )
    city = models.ForeignKey(
        City,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="City"
    )
    subcity = models.ForeignKey(
        SubCity,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="SubCity/Woreda"
    )
    kebele = models.ForeignKey(
        Kebele,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Kebele"
    )
    house_number = models.CharField(max_length=20, blank=True, help_text="House/Building number")
    street_address = models.CharField(max_length=200, blank=True, help_text="Street address")
    postal_code = models.CharField(max_length=10, blank=True, help_text="Postal code")
    
    class Meta:
        abstract = True


class BaseTaxPayer(BaseContact):
    """Abstract base model for tax payers"""
    tin_regex = RegexValidator(
        regex=r'^\d{10}$',
        message="TIN must be exactly 10 digits."
    )
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    tin = models.CharField(
        max_length=10, 
        unique=True, 
        validators=[tin_regex],
        help_text="Tax Identification Number (10 digits)"
    )
    tax_payer_level = models.ForeignKey(
        TaxPayerLevel, 
        on_delete=models.PROTECT,
        help_text="Tax payer classification level"
    )
    business_sector = models.ForeignKey(
        BusinessSector, 
        on_delete=models.PROTECT,
        help_text="Primary business sector"
    )
    business_sub_sector = models.ForeignKey(
        BusinessSubSector, 
        on_delete=models.PROTECT,
        help_text="Specific business sub-sector"
    )
    business_registration_date = models.DateField(help_text="Date of business registration")
    
    # File attachment for documents
    tax_file = models.ForeignKey(
        File,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='%(class)s_taxpayers',
        help_text="File containing tax-related documents"
    )
    
    # Status and metadata
    is_active = models.BooleanField(default=True)
    registration_date = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)

    # Business closure fields
    is_business_closed = models.BooleanField(
        default=False,
        help_text="Whether the business has been permanently closed"
    )
    business_closure_date = models.DateField(
        null=True,
        blank=True,
        help_text="Date when the business was officially closed"
    )
    business_closure_reason = models.TextField(
        null=True,
        blank=True,
        help_text="Reason for business closure (e.g., Bankruptcy, Voluntary closure, Relocation, etc.)"
    )
    closed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='%(class)s_closed_businesses',
        help_text="User who processed the business closure"
    )
    
    class Meta:
        abstract = True

    def clean(self):
        from django.core.exceptions import ValidationError
        # Validate that sub-sector belongs to the selected sector
        if self.business_sub_sector and self.business_sector:
            if self.business_sub_sector.business_sector != self.business_sector:
                raise ValidationError({
                    'business_sub_sector': 'Selected sub-sector must belong to the selected business sector.'
                })

        # Validate business closure fields
        if self.is_business_closed:
            if not self.business_closure_date:
                raise ValidationError({
                    'business_closure_date': 'Closure date is required when business is marked as closed.'
                })
            if not self.business_closure_reason:
                raise ValidationError({
                    'business_closure_reason': 'Closure reason is required when business is marked as closed.'
                })
        elif self.business_closure_date or self.business_closure_reason:
            # If closure fields are filled but business is not marked as closed
            raise ValidationError({
                'is_business_closed': 'Business must be marked as closed when closure date or reason is provided.'
            })

    def close_business(self, closure_date, closure_reason, closed_by_user=None):
        """Close the business with proper validation and logging"""
        from django.utils import timezone

        if self.is_business_closed:
            raise ValueError("Business is already closed")

        self.is_business_closed = True
        self.business_closure_date = closure_date
        self.business_closure_reason = closure_reason
        self.closed_by = closed_by_user
        self.is_active = False  # Also mark as inactive
        self.save()

        return True

    def reopen_business(self, reopened_by_user=None):
        """Reopen a closed business"""
        if not self.is_business_closed:
            raise ValueError("Business is not closed")

        self.is_business_closed = False
        self.business_closure_date = None
        self.business_closure_reason = None
        self.closed_by = None
        self.is_active = True  # Mark as active again
        self.save()

        return True


class IndividualTaxPayer(BaseTaxPayer):
    """Individual tax payer registration"""
    GENDER_CHOICES = [
        ('M', 'Male'),
        ('F', 'Female'),
        ('O', 'Other'),
    ]

    NATIONALITY_CHOICES = [
        ('ET', 'Ethiopian'),
        ('US', 'American'),
        ('UK', 'British'),
        ('CA', 'Canadian'),
        ('DE', 'German'),
        ('FR', 'French'),
        ('IT', 'Italian'),
        ('CN', 'Chinese'),
        ('IN', 'Indian'),
        ('KE', 'Kenyan'),
        ('UG', 'Ugandan'),
        ('TZ', 'Tanzanian'),
        ('OTHER', 'Other'),
    ]

    # Personal information
    first_name = models.CharField(max_length=50, help_text="First name")
    middle_name = models.CharField(max_length=50, blank=True, help_text="Middle name")
    last_name = models.CharField(max_length=50, help_text="Last name")
    nationality = models.CharField(max_length=10, choices=NATIONALITY_CHOICES, default='ET')
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES)
    date_of_birth = models.DateField(help_text="Date of birth")

    # Profile picture
    profile_picture = models.ImageField(
        upload_to='taxpayers/individuals/photos/',
        null=True,
        blank=True,
        help_text="Profile picture/photo"
    )

    # Additional business information
    business_name = models.CharField(
        max_length=200,
        blank=True,
        help_text="Business/Trade name (if applicable)"
    )
    business_license_number = models.CharField(
        max_length=50,
        blank=True,
        help_text="Business license number"
    )

    class Meta:
        ordering = ['last_name', 'first_name']
        verbose_name = 'Individual Tax Payer'
        verbose_name_plural = 'Individual Tax Payers'
        indexes = [
            models.Index(fields=['tin']),
            models.Index(fields=['last_name', 'first_name']),
            models.Index(fields=['business_registration_date']),
        ]

    def __str__(self):
        return f"{self.get_full_name()} (TIN: {self.tin})"

    def get_full_name(self):
        """Return the full name of the individual"""
        names = [self.first_name]
        if self.middle_name:
            names.append(self.middle_name)
        names.append(self.last_name)
        return ' '.join(names)

    @property
    def display_name(self):
        """Display name for UI purposes"""
        business_part = f" - {self.business_name}" if self.business_name else ""
        return f"{self.get_full_name()}{business_part}"


class OrganizationTaxPayer(BaseTaxPayer):
    """Organization tax payer registration"""
    vat_regex = RegexValidator(
        regex=r'^ET\d{10}$',
        message="VAT number must be in format: ET followed by 10 digits (e.g., ET1234567890)."
    )

    # Organization information
    business_name = models.CharField(max_length=200, help_text="Official business name")
    trade_name = models.CharField(
        max_length=200,
        blank=True,
        help_text="Trade name (if different from business name)"
    )
    organization_business_type = models.ForeignKey(
        OrganizationBusinessType,
        on_delete=models.PROTECT,
        help_text="Type of business organization"
    )

    # Manager/Owner information
    manager_first_name = models.CharField(max_length=50, help_text="Manager/Owner first name")
    manager_middle_name = models.CharField(max_length=50, blank=True, help_text="Manager/Owner middle name")
    manager_last_name = models.CharField(max_length=50, help_text="Manager/Owner last name")
    manager_title = models.CharField(
        max_length=100,
        default='Manager',
        help_text="Manager's title/position"
    )

    # VAT information
    vat_registration_date = models.DateField(
        null=True,
        blank=True,
        help_text="VAT registration date"
    )
    vat_number = models.CharField(
        max_length=12,
        validators=[vat_regex],
        null=True,
        blank=True,
        unique=True,
        help_text="VAT registration number (ET + 10 digits)"
    )

    # Additional business information
    business_license_number = models.CharField(
        max_length=50,
        blank=True,
        help_text="Business license number"
    )
    capital_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Registered capital amount"
    )
    number_of_employees = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Number of employees"
    )

    class Meta:
        ordering = ['business_name']
        verbose_name = 'Organization Tax Payer'
        verbose_name_plural = 'Organization Tax Payers'
        indexes = [
            models.Index(fields=['tin']),
            models.Index(fields=['business_name']),
            models.Index(fields=['vat_number']),
            models.Index(fields=['business_registration_date']),
        ]

    def __str__(self):
        return f"{self.business_name} (TIN: {self.tin})"

    def get_manager_full_name(self):
        """Return the full name of the manager/owner"""
        names = [self.manager_first_name]
        if self.manager_middle_name:
            names.append(self.manager_middle_name)
        names.append(self.manager_last_name)
        return ' '.join(names)

    @property
    def display_name(self):
        """Display name for UI purposes"""
        trade_part = f" ({self.trade_name})" if self.trade_name and self.trade_name != self.business_name else ""
        return f"{self.business_name}{trade_part}"

    def clean(self):
        super().clean()
        from django.core.exceptions import ValidationError

        # Validate VAT requirements
        if self.organization_business_type and self.organization_business_type.requires_vat_registration:
            if not self.vat_number:
                raise ValidationError({
                    'vat_number': f'{self.organization_business_type.name} requires VAT registration.'
                })
            if not self.vat_registration_date:
                raise ValidationError({
                    'vat_registration_date': f'{self.organization_business_type.name} requires VAT registration date.'
                })
