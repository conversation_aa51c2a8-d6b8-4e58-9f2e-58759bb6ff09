from rest_framework import serializers
from django.utils import timezone
from .models import DocumentRequest, RequestApproval, AuditLog, Notification
from documents.serializers import DocumentSummarySerializer
from locations.serializers import FileSerializer
from accounts.serializers import UserSerializer


class DocumentRequestSerializer(serializers.ModelSerializer):
    """Document request serializer"""
    
    requested_by_name = serializers.CharField(source='requested_by.full_name', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.full_name', read_only=True)
    checked_out_by_name = serializers.CharField(source='checked_out_by.full_name', read_only=True)
    returned_to_name = serializers.CharField(source='returned_to.full_name', read_only=True)

    # File and document details
    files_detail = FileSerializer(source='files', many=True, read_only=True)
    documents_detail = DocumentSummarySerializer(source='documents', many=True, read_only=True)

    # Request summary and status
    request_summary = serializers.ReadOnlyField()
    total_document_count = serializers.ReadOnlyField()
    file_count = serializers.ReadOnlyField()
    individual_document_count = serializers.ReadOnlyField()

    is_overdue = serializers.ReadOnlyField()
    days_until_due = serializers.ReadOnlyField()
    can_be_approved = serializers.ReadOnlyField()
    can_be_checked_out = serializers.ReadOnlyField()
    can_be_returned = serializers.ReadOnlyField()
    
    class Meta:
        model = DocumentRequest
        fields = [
            'id', 'request_number', 'requested_by', 'requested_by_name',
            'files', 'files_detail', 'documents', 'documents_detail',
            'request_summary', 'total_document_count', 'file_count', 'individual_document_count',
            'purpose', 'priority', 'requested_date', 'required_date', 'due_date', 'status',
            'approved_by', 'approved_by_name', 'approved_date', 'rejection_reason',
            'checked_out_date', 'checked_out_by', 'checked_out_by_name',
            'returned_date', 'returned_to', 'returned_to_name', 'notes',
            'is_overdue', 'days_until_due', 'can_be_approved',
            'can_be_checked_out', 'can_be_returned', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'request_number', 'requested_date', 'approved_date',
            'checked_out_date', 'returned_date', 'created_at', 'updated_at'
        ]


class DocumentRequestCreateSerializer(serializers.ModelSerializer):
    """Document request creation serializer"""
    
    class Meta:
        model = DocumentRequest
        fields = [
            'files', 'documents', 'purpose', 'priority', 'required_date', 'due_date'
        ]
    
    def validate(self, attrs):
        required_date = attrs.get('required_date')
        due_date = attrs.get('due_date')
        files = attrs.get('files', [])
        documents = attrs.get('documents', [])

        # Validate that at least one file or document is requested
        if not files and not documents:
            raise serializers.ValidationError(
                "At least one file or document must be requested"
            )

        # Validate dates - now handling datetime
        if required_date and due_date:
            # Convert to datetime if they're date objects
            if hasattr(required_date, 'date'):
                required_dt = required_date
            else:
                required_dt = timezone.datetime.combine(required_date, timezone.datetime.min.time())
                required_dt = timezone.make_aware(required_dt)

            if hasattr(due_date, 'date'):
                due_dt = due_date
            else:
                due_dt = timezone.datetime.combine(due_date, timezone.datetime.min.time())
                due_dt = timezone.make_aware(due_dt)

            # Due date must be at least 15 minutes after required date
            minimum_due_time = required_dt + timezone.timedelta(minutes=15)

            if due_dt < minimum_due_time:
                raise serializers.ValidationError(
                    "Due date must be at least 15 minutes after required date"
                )

        # Validate documents availability
        documents = attrs.get('documents', [])
        if not documents:
            raise serializers.ValidationError(
                "At least one document must be requested"
            )

        for document in documents:
            if not document.can_be_requested():
                raise serializers.ValidationError(
                    f"Document '{document.title}' is not available for request"
                )

        return attrs
    
    def create(self, validated_data):
        validated_data['requested_by'] = self.context['request'].user
        return super().create(validated_data)


class DocumentRequestUpdateSerializer(serializers.ModelSerializer):
    """Document request update serializer"""
    
    class Meta:
        model = DocumentRequest
        fields = ['purpose', 'priority', 'required_date', 'due_date', 'notes']


class RequestApprovalSerializer(serializers.ModelSerializer):
    """Request approval serializer"""
    
    request_number = serializers.CharField(source='request.request_number', read_only=True)
    approver_name = serializers.CharField(source='approver.full_name', read_only=True)
    
    class Meta:
        model = RequestApproval
        fields = [
            'id', 'request', 'request_number', 'approver', 'approver_name',
            'action', 'comments', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']
    
    def create(self, validated_data):
        validated_data['approver'] = self.context['request'].user
        return super().create(validated_data)


class RequestActionSerializer(serializers.Serializer):
    """Request action serializer for approve/reject/checkout/return"""
    
    action = serializers.ChoiceField(
        choices=['approve', 'reject', 'checkout', 'return']
    )
    comments = serializers.CharField(required=False, allow_blank=True)
    condition_notes = serializers.CharField(required=False, allow_blank=True)
    
    def validate(self, attrs):
        action = attrs.get('action')
        
        if action == 'reject' and not attrs.get('comments'):
            raise serializers.ValidationError(
                "Comments are required when rejecting a request"
            )
        
        return attrs


class AuditLogSerializer(serializers.ModelSerializer):
    """Audit log serializer"""
    
    user_name = serializers.CharField(source='user.full_name', read_only=True)
    content_type_name = serializers.CharField(source='content_type.name', read_only=True)
    
    class Meta:
        model = AuditLog
        fields = [
            'id', 'user', 'user_name', 'action', 'content_type', 'content_type_name',
            'object_id', 'object_repr', 'description', 'ip_address', 'user_agent',
            'changes', 'timestamp'
        ]
        read_only_fields = ['id', 'timestamp']


class NotificationSerializer(serializers.ModelSerializer):
    """Notification serializer"""
    
    recipient_name = serializers.CharField(source='recipient.full_name', read_only=True)
    content_type_name = serializers.CharField(source='content_type.name', read_only=True)
    
    class Meta:
        model = Notification
        fields = [
            'id', 'recipient', 'recipient_name', 'title', 'message',
            'notification_type', 'content_type', 'content_type_name',
            'object_id', 'is_read', 'read_at', 'email_sent', 'email_sent_at',
            'created_at'
        ]
        read_only_fields = ['id', 'read_at', 'email_sent_at', 'created_at']


class NotificationCreateSerializer(serializers.ModelSerializer):
    """Notification creation serializer"""
    
    class Meta:
        model = Notification
        fields = [
            'recipient', 'title', 'message', 'notification_type'
        ]


class RequestStatsSerializer(serializers.Serializer):
    """Request statistics serializer"""
    
    total_requests = serializers.IntegerField()
    pending_requests = serializers.IntegerField()
    approved_requests = serializers.IntegerField()
    checked_out_requests = serializers.IntegerField()
    overdue_requests = serializers.IntegerField()
    returned_requests = serializers.IntegerField()
    rejected_requests = serializers.IntegerField()
    requests_by_priority = serializers.DictField()
    recent_requests = DocumentRequestSerializer(many=True)


class DashboardStatsSerializer(serializers.Serializer):
    """Dashboard statistics serializer"""
    
    # User stats
    total_users = serializers.IntegerField()
    active_users = serializers.IntegerField()
    
    # Document stats
    total_documents = serializers.IntegerField()
    physical_documents = serializers.IntegerField()
    digital_documents = serializers.IntegerField()
    checked_out_documents = serializers.IntegerField()
    
    # Location stats
    total_buildings = serializers.IntegerField()
    total_shelves = serializers.IntegerField()
    total_kents = serializers.IntegerField()
    
    # Request stats
    pending_requests = serializers.IntegerField()
    overdue_requests = serializers.IntegerField()
    
    # Recent activity
    recent_documents = DocumentSummarySerializer(many=True)
    recent_requests = DocumentRequestSerializer(many=True)


class UserActivitySerializer(serializers.Serializer):
    """User activity serializer"""
    
    user = UserSerializer()
    total_requests = serializers.IntegerField()
    pending_requests = serializers.IntegerField()
    overdue_requests = serializers.IntegerField()
    documents_created = serializers.IntegerField()
    last_activity = serializers.DateTimeField()


class SystemHealthSerializer(serializers.Serializer):
    """System health check serializer"""
    
    database_status = serializers.CharField()
    storage_usage = serializers.DictField()
    recent_errors = serializers.ListField()
    performance_metrics = serializers.DictField()
    backup_status = serializers.DictField()
