import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Avatar,
  Chip,
  Grid,
  Card,
  CardContent,
  Breadcrumbs,
  Link,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  ArrowBack,
  Edit,
  Delete,
  Home,
} from '@mui/icons-material';

export interface BreadcrumbItem {
  label: string;
  path?: string;
  icon?: React.ReactElement;
}

export interface ActionButton {
  label: string;
  icon: React.ReactElement;
  onClick: () => void;
  color?: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success';
  variant?: 'contained' | 'outlined' | 'text';
  disabled?: boolean;
}

export interface DetailSection {
  title: string;
  icon: React.ReactElement;
  content: React.ReactNode;
  gridSize?: number; // 1-12, defaults to 6
}

export interface DetailPageTemplateProps {
  loading?: boolean;
  error?: string | null;
  breadcrumbs: BreadcrumbItem[];
  title: string;
  subtitle?: string;
  avatar?: {
    src?: string;
    fallbackIcon: React.ReactElement;
    alt: string;
  };
  chips?: Array<{
    label: string;
    color?: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success' | 'default';
    icon?: React.ReactElement;
  }>;
  actions: ActionButton[];
  sections: DetailSection[];
  onBack: () => void;
  children?: React.ReactNode;
}

const DetailPageTemplate: React.FC<DetailPageTemplateProps> = ({
  loading = false,
  error = null,
  breadcrumbs,
  title,
  subtitle,
  avatar,
  chips = [],
  actions,
  sections,
  onBack,
  children,
}) => {
  const navigate = useNavigate();
  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error">{error}</Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 3 }}>
        {breadcrumbs.map((breadcrumb, index) => (
          <Link
            key={index}
            component="button"
            variant="body2"
            onClick={() => breadcrumb.path && navigate(breadcrumb.path)}
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 0.5,
              textDecoration: 'none',
              color: index === breadcrumbs.length - 1 ? 'text.primary' : 'inherit',
              cursor: breadcrumb.path ? 'pointer' : 'default',
              '&:hover': breadcrumb.path ? {
                color: 'primary.main',
              } : {}
            }}
            disabled={!breadcrumb.path}
          >
            {breadcrumb.icon}
            {breadcrumb.label}
          </Link>
        ))}
      </Breadcrumbs>

      {/* Header */}
      <Paper sx={{ p: 3, mb: 3, background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<ArrowBack />}
              onClick={onBack}
              sx={{ 
                bgcolor: 'white',
                '&:hover': { bgcolor: 'grey.100' }
              }}
            >
              Back
            </Button>
          </Box>
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {actions.map((action, index) => (
              <Button
                key={index}
                variant={action.variant || 'outlined'}
                startIcon={action.icon}
                onClick={action.onClick}
                color={action.color || 'primary'}
                disabled={action.disabled}
                sx={{ 
                  bgcolor: action.variant === 'outlined' ? 'white' : undefined,
                  '&:hover': { 
                    bgcolor: action.variant === 'outlined' ? 'grey.100' : undefined 
                  }
                }}
              >
                {action.label}
              </Button>
            ))}
          </Box>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
          {avatar && (
            <Avatar
              src={avatar.src}
              sx={{ 
                width: 80, 
                height: 80,
                bgcolor: 'primary.main',
                border: '4px solid white',
                boxShadow: 3
              }}
              alt={avatar.alt}
            >
              {avatar.fallbackIcon}
            </Avatar>
          )}
          <Box sx={{ flex: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1, flexWrap: 'wrap' }}>
              <Typography 
                variant="h4" 
                component="h1" 
                sx={{ 
                  fontWeight: 700,
                  color: 'primary.main',
                  textShadow: '0 2px 4px rgba(0,0,0,0.1)'
                }}
              >
                {title}
              </Typography>
              {chips.map((chip, index) => (
                <Chip
                  key={index}
                  label={chip.label}
                  color={chip.color || 'default'}
                  icon={chip.icon}
                  size="small"
                  sx={{ 
                    fontWeight: 600,
                    boxShadow: 1
                  }}
                />
              ))}
            </Box>
            {subtitle && (
              <Typography 
                variant="h6" 
                color="text.secondary" 
                gutterBottom
                sx={{ fontWeight: 500 }}
              >
                {subtitle}
              </Typography>
            )}
          </Box>
        </Box>
      </Paper>

      {/* Content Sections */}
      <Grid container spacing={3}>
        {sections.map((section, index) => (
          <Grid key={index} size={{ xs: 12, md: section.gridSize || 6 }}>
            <Card sx={{ 
              height: '100%',
              transition: 'all 0.3s ease',
              '&:hover': {
                boxShadow: 4,
                transform: 'translateY(-2px)'
              }
            }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  gap: 1,
                  color: 'primary.main',
                  fontWeight: 600,
                  borderBottom: '2px solid',
                  borderColor: 'primary.main',
                  pb: 1,
                  mb: 3
                }}>
                  {section.icon}
                  {section.title}
                </Typography>
                {section.content}
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Additional Content */}
      {children}
    </Container>
  );
};

export default DetailPageTemplate;
