import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Alert,
  Skeleton,
  Divider,
  Paper,
  LinearProgress,
  Autocomplete,
} from '@mui/material';
import {
  ArrowBack,
  Save,
  CloudUpload,
  Description,
  Delete,
  AttachFile,
  CameraAlt,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { useNotification } from '../../contexts/NotificationContext';
import documentService from '../../services/documentService';
import documentTypeService from '../../services/documentTypeService';
import locationService from '../../services/locationService';
import fileService from '../../services/fileService';
import CameraCapture from '../../components/CameraCapture';
import type { DocumentCreate, DocumentUpdate, Document } from '../../services/documentService';
import type { DocumentType } from '../../services/documentTypeService';
import type { File as BusinessFile } from '../../services/fileService';

interface DocumentFormPageProps {
  mode: 'create' | 'edit';
}

const DocumentFormPage: React.FC<DocumentFormPageProps> = ({ mode }) => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showSuccess, showError } = useNotification();

  const [formData, setFormData] = useState<DocumentCreate>({
    title: '',
    description: '',
    document_type: 0,
    mode: 'digital',
    tags: [],
    number_of_pages: undefined,
    reference_number: '',
    document_date: '',
    expiry_date: '',
    document_file: undefined,
    kent: undefined,
    file: undefined,
  });

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [filePreview, setFilePreview] = useState<string>('');
  const [documentTypes, setDocumentTypes] = useState<DocumentType[]>([]);
  const [files, setFiles] = useState<BusinessFile[]>([]);
  const [selectedDocumentType, setSelectedDocumentType] = useState<DocumentType | null>(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [fileErrors, setFileErrors] = useState<string[]>([]);
  const [cameraOpen, setCameraOpen] = useState(false);

  const availableTags = [
    'Important', 'Urgent', 'Confidential', 'Archive', 'Legal', 'Financial',
    'HR', 'Contract', 'Invoice', 'Receipt', 'Certificate', 'License'
  ];

  const documentModes = [
    { value: 'digital', label: 'Digital Only' },
    { value: 'physical', label: 'Physical Only' },
    { value: 'hybrid', label: 'Digital + Physical' },
  ];

  useEffect(() => {
    const initializeForm = async () => {
      await loadInitialData();
      if (mode === 'edit' && id) {
        await loadDocument();
      }
    };
    initializeForm();
  }, [mode, id]);

  const loadInitialData = async () => {
    try {
      const [typesResponse, filesResponse] = await Promise.all([
        documentTypeService.getDocumentTypes(),
        fileService.getFiles(),
      ]);

      setDocumentTypes(typesResponse.results);
      setFiles(filesResponse.results);
    } catch (err: any) {
      showError('Failed to load form data');
    }
  };

  const loadDocument = async () => {
    if (!id) return;

    try {
      setLoading(true);
      const data = await documentService.getDocument(id);
      setFormData({
        title: data.title,
        description: data.description || '',
        document_type: data.document_type,
        mode: data.mode,
        tags: data.tags || [],
        number_of_pages: data.number_of_pages,
        reference_number: data.reference_number || '',
        document_date: data.document_date || '',
        expiry_date: data.expiry_date || '',
        document_file: data.document_file,
        kent: data.kent,
        file: undefined, // Don't populate file for edit
      });

      // Set selected document type (wait for documentTypes to be loaded)
      if (documentTypes.length > 0) {
        const docType = documentTypes.find(dt => dt.id === data.document_type);
        setSelectedDocumentType(docType || null);
      } else {
        // If documentTypes not loaded yet, fetch the specific document type
        try {
          const docType = await documentTypeService.getDocumentType(data.document_type);
          setSelectedDocumentType(docType);
        } catch (err) {
          console.warn('Failed to load document type:', err);
        }
      }
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to load document');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof DocumentCreate) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | { target: { value: unknown } }
  ) => {
    let value = event.target.value;

    // Handle number fields
    if (field === 'number_of_pages') {
      value = value ? parseInt(value as string) : undefined;
    }

    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Update selected document type when document_type changes
    if (field === 'document_type') {
      const docType = documentTypes.find(dt => dt.id === Number(value));
      setSelectedDocumentType(docType || null);
      setFileErrors([]); // Clear file errors when document type changes
    }
  };

  const handleDateChange = (field: 'document_date' | 'expiry_date') => (date: Date | null) => {
    setFormData(prev => ({
      ...prev,
      [field]: date ? date.toISOString().split('T')[0] : ''
    }));
  };

  const handleTagsChange = (event: any, newValue: string[]) => {
    setFormData(prev => ({
      ...prev,
      tags: newValue
    }));
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file if document type is selected
    if (selectedDocumentType) {
      const validation = documentService.validateFile(
        file,
        selectedDocumentType.allowed_file_extensions,
        selectedDocumentType.max_file_size_mb || 10
      );

      if (!validation.isValid) {
        setFileErrors(validation.errors);
        return;
      }
    }

    setSelectedFile(file);
    setFileErrors([]);
    setFormData(prev => ({
      ...prev,
      file: file
    }));

    // Create preview for images
    if (file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setFilePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      setFilePreview('');
    }
  };

  const removeFile = () => {
    setSelectedFile(null);
    setFilePreview('');
    setFileErrors([]);
    setFormData(prev => ({
      ...prev,
      file: undefined
    }));

    // Clear file input
    const fileInput = document.getElementById('file-upload') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  };

  const handleCameraCapture = (file: File) => {
    // Validate file if document type is selected
    if (selectedDocumentType) {
      const validation = documentService.validateFile(
        file,
        selectedDocumentType.allowed_file_extensions,
        selectedDocumentType.max_file_size_mb || 10
      );

      if (!validation.isValid) {
        setFileErrors(validation.errors);
        setCameraOpen(false);
        return;
      }
    }

    setSelectedFile(file);
    setFileErrors([]);
    setFormData(prev => ({
      ...prev,
      file: file
    }));

    // Create preview for the captured image
    const reader = new FileReader();
    reader.onload = (e) => {
      setFilePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    setCameraOpen(false);
  };

  const validateForm = () => {
    if (!formData.title.trim()) {
      showError('Title is required');
      return false;
    }
    if (!formData.document_type) {
      showError('Document type is required');
      return false;
    }
    if (formData.mode === 'physical' && !formData.document_file) {
      showError('Business file is required for physical documents');
      return false;
    }
    if (mode === 'create' && formData.mode !== 'physical' && !selectedFile) {
      showError('File is required for digital documents');
      return false;
    }
    return true;
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!validateForm()) return;

    try {
      setSaving(true);
      setError(null);
      setUploadProgress(0);

      if (mode === 'create') {
        const newDocument = await documentService.createDocument(formData);
        showSuccess('Document created successfully');
        navigate(`/documents/${newDocument.id}`);
      } else {
        const updateData: DocumentUpdate = {
          title: formData.title,
          description: formData.description,
          document_type: formData.document_type,
          mode: formData.mode,
          tags: formData.tags,
          number_of_pages: formData.number_of_pages,
          reference_number: formData.reference_number,
          document_date: formData.document_date,
          expiry_date: formData.expiry_date,
          document_file: formData.document_file,
          kent: formData.kent,
        };

        await documentService.updateDocument(id!, updateData);

        // Upload new file if provided
        if (selectedFile) {
          await documentService.uploadFile(id!, selectedFile);
        }

        showSuccess('Document updated successfully');
        navigate(`/documents/${id}`);
      }
    } catch (err: any) {
      setError(err.response?.data?.detail || `Failed to ${mode} document`);
    } finally {
      setSaving(false);
      setUploadProgress(0);
    }
  };

  const handleCancel = () => {
    if (mode === 'edit' && id) {
      navigate(`/documents/${id}`);
    } else {
      navigate('/documents');
    }
  };

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Skeleton variant="rectangular" width="100%" height={400} />
      </Box>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <IconButton onClick={handleCancel}>
              <ArrowBack />
            </IconButton>
            <Typography variant="h4" component="h1">
              {mode === 'create' ? 'Create Document' : 'Edit Document'}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button variant="outlined" onClick={handleCancel}>
              Cancel
            </Button>
            <Button
              variant="contained"
              startIcon={<Save />}
              onClick={handleSubmit}
              disabled={saving}
            >
              {saving ? 'Saving...' : 'Save'}
            </Button>
          </Box>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {saving && (
          <Box sx={{ mb: 3 }}>
            <LinearProgress variant="determinate" value={uploadProgress} />
            <Typography variant="caption" color="text.secondary">
              {mode === 'create' ? 'Creating document...' : 'Updating document...'}
            </Typography>
          </Box>
        )}

        <form onSubmit={handleSubmit}>
          <Grid container spacing={3}>
            {/* Main Form */}
            <Grid size={{ xs: 12, md: 8 }}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Description color="primary" />
                    Document Information
                  </Typography>

                  <Grid container spacing={2}>
                    <Grid size={{ xs: 12 }}>
                      <TextField
                        fullWidth
                        label="Title"
                        value={formData.title}
                        onChange={handleInputChange('title')}
                        required
                        placeholder="Enter document title"
                      />
                    </Grid>

                    <Grid size={{ xs: 12 }}>
                      <TextField
                        fullWidth
                        label="Description"
                        multiline
                        rows={3}
                        value={formData.description}
                        onChange={handleInputChange('description')}
                        placeholder="Enter document description (optional)"
                      />
                    </Grid>

                    <Grid size={{ xs: 12, sm: 6 }}>
                      <FormControl fullWidth required>
                        <InputLabel>Document Type</InputLabel>
                        <Select
                          value={formData.document_type || ''}
                          label="Document Type"
                          onChange={handleInputChange('document_type')}
                        >
                          {documentTypes.map((type) => (
                            <MenuItem key={type.id} value={type.id}>
                              {type.name}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>

                    <Grid size={{ xs: 12, sm: 6 }}>
                      <FormControl fullWidth required>
                        <InputLabel>Mode</InputLabel>
                        <Select
                          value={formData.mode}
                          label="Mode"
                          onChange={handleInputChange('mode')}
                        >
                          {documentModes.map((mode) => (
                            <MenuItem key={mode.value} value={mode.value}>
                              {mode.label}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>

                    <Grid size={{ xs: 12, sm: 6 }}>
                      <TextField
                        fullWidth
                        label="Reference Number"
                        value={formData.reference_number}
                        onChange={handleInputChange('reference_number')}
                        placeholder="Enter reference number (optional)"
                      />
                    </Grid>

                    {(formData.mode === 'physical' || formData.mode === 'hybrid') && (
                      <Grid size={{ xs: 12, sm: 6 }}>
                        <FormControl fullWidth required={formData.mode === 'physical'}>
                          <InputLabel>Business File</InputLabel>
                          <Select
                            value={formData.document_file || ''}
                            label="Business File"
                            onChange={handleInputChange('document_file')}
                          >
                            {files.map((file) => (
                              <MenuItem key={file.id} value={file.id}>
                                {file.name} ({file.file_number}) - {file.location_path}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </Grid>
                    )}

                    <Grid size={{ xs: 12, sm: 6 }}>
                      <DatePicker
                        label="Document Date"
                        value={formData.document_date ? new Date(formData.document_date) : null}
                        onChange={handleDateChange('document_date')}
                        slotProps={{
                          textField: {
                            fullWidth: true,
                            placeholder: 'Select document date'
                          }
                        }}
                      />
                    </Grid>

                    {selectedDocumentType?.requires_expiry_date && (
                      <Grid size={{ xs: 12, sm: 6 }}>
                        <DatePicker
                          label="Expiry Date"
                          value={formData.expiry_date ? new Date(formData.expiry_date) : null}
                          onChange={handleDateChange('expiry_date')}
                          slotProps={{
                            textField: {
                              fullWidth: true,
                              required: true,
                              placeholder: 'Select expiry date'
                            }
                          }}
                        />
                      </Grid>
                    )}

                    <Grid size={{ xs: 12, sm: 6 }}>
                      <TextField
                        fullWidth
                        label="Number of Pages"
                        type="number"
                        value={formData.number_of_pages || ''}
                        onChange={handleInputChange('number_of_pages')}
                        inputProps={{ min: 1 }}
                        helperText="Total number of pages (for audit purposes)"
                      />
                    </Grid>

                    <Grid size={{ xs: 12 }}>
                      <Autocomplete
                        multiple
                        freeSolo
                        options={availableTags}
                        value={formData.tags}
                        onChange={handleTagsChange}
                        renderTags={(value, getTagProps) =>
                          value.map((option, index) => (
                            <Chip
                              variant="outlined"
                              label={option}
                              {...getTagProps({ index })}
                              key={option}
                            />
                          ))
                        }
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            label="Tags"
                            placeholder="Add tags to categorize this document"
                          />
                        )}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* File Upload Section */}
            <Grid size={{ xs: 12, md: 4 }}>
              {(formData.mode === 'digital' || formData.mode === 'hybrid') && (
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <CloudUpload color="primary" />
                      File Upload
                    </Typography>

                    {!selectedFile ? (
                      <Box>
                        <input
                          accept={selectedDocumentType?.allowed_file_extensions.map(ext => `.${ext}`).join(',')}
                          style={{ display: 'none' }}
                          id="file-upload"
                          type="file"
                          onChange={handleFileChange}
                        />
                        <label htmlFor="file-upload">
                          <Paper
                            sx={{
                              p: 3,
                              textAlign: 'center',
                              border: '2px dashed',
                              borderColor: 'primary.main',
                              cursor: 'pointer',
                              mb: 2,
                              '&:hover': {
                                backgroundColor: 'action.hover'
                              }
                            }}
                          >
                            <CloudUpload sx={{ fontSize: 48, color: 'primary.main', mb: 1 }} />
                            <Typography variant="h6" gutterBottom>
                              Upload File
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Click to select a file or drag and drop
                            </Typography>
                            {selectedDocumentType && (
                              <Box sx={{ mt: 2 }}>
                                <Typography variant="caption" color="text.secondary">
                                  Allowed: {selectedDocumentType.allowed_file_extensions.join(', ')}
                                </Typography>
                                <br />
                                <Typography variant="caption" color="text.secondary">
                                  Max size: {selectedDocumentType.max_file_size_mb || 10} MB
                                </Typography>
                              </Box>
                            )}
                          </Paper>
                        </label>

                        <Divider sx={{ my: 2 }}>
                          <Typography variant="body2" color="text.secondary">
                            OR
                          </Typography>
                        </Divider>

                        <Button
                          variant="outlined"
                          startIcon={<CameraAlt />}
                          onClick={() => setCameraOpen(true)}
                          fullWidth
                          sx={{ mb: 1 }}
                        >
                          Capture with Camera
                        </Button>
                      </Box>
                    ) : (
                      <Box>
                        <Paper sx={{ p: 2, mb: 2 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <AttachFile color="primary" />
                            <Box sx={{ flexGrow: 1 }}>
                              <Typography variant="subtitle2">
                                {selectedFile?.name || 'Unknown File'}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {selectedFile?.size ? documentService.formatFileSize(selectedFile.size) : 'Unknown Size'}
                              </Typography>
                              <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                                Type: {selectedFile?.type || 'Unknown Type'}
                              </Typography>
                            </Box>
                            <IconButton onClick={removeFile} color="error" size="small">
                              <Delete />
                            </IconButton>
                          </Box>
                        </Paper>

                        {filePreview && (
                          <Paper sx={{ p: 1, textAlign: 'center', mb: 2 }}>
                            <img
                              src={filePreview}
                              alt="Preview"
                              style={{
                                maxWidth: '100%',
                                maxHeight: 200,
                                objectFit: 'contain'
                              }}
                            />
                          </Paper>
                        )}

                        <Button
                          variant="outlined"
                          startIcon={<CloudUpload />}
                          onClick={() => document.getElementById('file-upload')?.click()}
                          fullWidth
                        >
                          Change File
                        </Button>
                      </Box>
                    )}

                    {fileErrors.length > 0 && (
                      <Alert severity="error" sx={{ mt: 2 }}>
                        <ul style={{ margin: 0, paddingLeft: 20 }}>
                          {fileErrors.map((error, index) => (
                            <li key={index}>{error}</li>
                          ))}
                        </ul>
                      </Alert>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Document Type Info */}
              {selectedDocumentType && (
                <Card sx={{ mt: 2 }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Document Type Info
                    </Typography>

                    <Box sx={{ mb: 2 }}>
                      <Typography variant="caption" color="text.secondary">
                        Name
                      </Typography>
                      <Typography variant="body2">
                        {selectedDocumentType.name}
                      </Typography>
                    </Box>

                    <Box sx={{ mb: 2 }}>
                      <Typography variant="caption" color="text.secondary">
                        Code
                      </Typography>
                      <Typography variant="body2" fontFamily="monospace">
                        {selectedDocumentType.code}
                      </Typography>
                    </Box>

                    <Box sx={{ mb: 2 }}>
                      <Typography variant="caption" color="text.secondary">
                        Confidentiality
                      </Typography>
                      <Typography variant="body2">
                        {selectedDocumentType.confidentiality_level.toUpperCase()}
                      </Typography>
                    </Box>

                    {selectedDocumentType.retention_days && (
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="caption" color="text.secondary">
                          Retention Period
                        </Typography>
                        <Typography variant="body2">
                          {selectedDocumentType.retention_period_days} days
                        </Typography>
                      </Box>
                    )}

                    <Box sx={{ mb: 2 }}>
                      <Typography variant="caption" color="text.secondary">
                        Requires Approval
                      </Typography>
                      <Typography variant="body2">
                        {selectedDocumentType.requires_approval ? 'Yes' : 'No'}
                      </Typography>
                    </Box>

                    <Box>
                      <Typography variant="caption" color="text.secondary">
                        Requires Expiry Date
                      </Typography>
                      <Typography variant="body2">
                        {selectedDocumentType.requires_expiry_date ? 'Yes' : 'No'}
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              )}
            </Grid>
          </Grid>
        </form>

        {/* Camera Capture Dialog */}
        <CameraCapture
          open={cameraOpen}
          onClose={() => setCameraOpen(false)}
          onCapture={handleCameraCapture}
          title="Capture Document"
        />
      </Box>
    </LocalizationProvider>
  );
};

// Create a wrapper component that determines the mode
const CreateDocumentPage: React.FC = () => {
  return <DocumentFormPage mode="create" />;
};

export default CreateDocumentPage;
export { DocumentFormPage };
