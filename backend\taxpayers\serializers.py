from rest_framework import serializers
from .models import (
    BusinessSector, BusinessSubSector, TaxPayerLevel,
    OrganizationBusinessType, IndividualTaxPayer, OrganizationTaxPayer,
    DailyIncomeAnalysis, LevelUpgradeNotification
)


class BusinessSectorSerializer(serializers.ModelSerializer):
    """Business Sector serializer"""
    sub_sectors_count = serializers.SerializerMethodField()
    
    class Meta:
        model = BusinessSector
        fields = [
            'id', 'code', 'name', 'description', 'is_active',
            'sub_sectors_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'sub_sectors_count']
    
    def get_sub_sectors_count(self, obj):
        return obj.sub_sectors.filter(is_active=True).count()


class BusinessSubSectorSerializer(serializers.ModelSerializer):
    """Business Sub-Sector serializer"""
    business_sector_name = serializers.CharField(source='business_sector.name', read_only=True)
    business_sector_code = serializers.CharField(source='business_sector.code', read_only=True)
    full_code = serializers.CharField(read_only=True)
    
    class Meta:
        model = BusinessSubSector
        fields = [
            'id', 'business_sector', 'business_sector_name', 'business_sector_code',
            'code', 'name', 'description', 'full_code', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'full_code']


class BusinessSubSectorCreateSerializer(serializers.ModelSerializer):
    """Business Sub-Sector creation serializer"""
    
    class Meta:
        model = BusinessSubSector
        fields = ['business_sector', 'code', 'name', 'description']


class TaxPayerLevelSerializer(serializers.ModelSerializer):
    """Tax Payer Level serializer with income ranges"""
    turnover_range = serializers.SerializerMethodField()
    daily_income_range = serializers.SerializerMethodField()

    class Meta:
        model = TaxPayerLevel
        fields = [
            'id', 'name', 'code', 'description', 'priority',
            'min_daily_income', 'max_daily_income', 'daily_income_range',
            'tax_rate_percentage', 'requires_annual_assessment',
            'minimum_annual_turnover', 'maximum_annual_turnover', 'turnover_range',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'turnover_range', 'daily_income_range']

    def get_turnover_range(self, obj):
        if obj.minimum_annual_turnover and obj.maximum_annual_turnover:
            return f"{obj.minimum_annual_turnover:,.2f} - {obj.maximum_annual_turnover:,.2f}"
        elif obj.minimum_annual_turnover:
            return f"≥ {obj.minimum_annual_turnover:,.2f}"
        elif obj.maximum_annual_turnover:
            return f"≤ {obj.maximum_annual_turnover:,.2f}"
        return "Not specified"

    def get_daily_income_range(self, obj):
        """Get formatted daily income range"""
        min_income = f"${obj.min_daily_income:,.2f}"
        if obj.max_daily_income:
            max_income = f"${obj.max_daily_income:,.2f}"
            return f"{min_income} - {max_income}"
        return f"{min_income}+"


class OrganizationBusinessTypeSerializer(serializers.ModelSerializer):
    """Organization Business Type serializer"""
    
    class Meta:
        model = OrganizationBusinessType
        fields = [
            'id', 'code', 'name', 'description', 'requires_vat_registration',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class IndividualTaxPayerSerializer(serializers.ModelSerializer):
    """Individual Tax Payer serializer"""
    full_name = serializers.CharField(source='get_full_name', read_only=True)
    display_name = serializers.CharField(read_only=True)
    
    # Related field details
    tax_payer_level_details = serializers.SerializerMethodField()
    tax_payer_level_name = serializers.CharField(source='tax_payer_level.name', read_only=True)
    business_sector_name = serializers.CharField(source='business_sector.name', read_only=True)
    business_sub_sector_name = serializers.CharField(source='business_sub_sector.name', read_only=True)

    # Location hierarchy details
    country_name = serializers.CharField(source='country.name', read_only=True)
    region_name = serializers.CharField(source='region.name', read_only=True)
    zone_name = serializers.CharField(source='zone.name', read_only=True)
    city_name = serializers.CharField(source='city.name', read_only=True)
    subcity_name = serializers.CharField(source='subcity.name', read_only=True)
    kebele_name = serializers.CharField(source='kebele.name', read_only=True)

    tax_file_name = serializers.CharField(source='tax_file.name', read_only=True)
    tax_file_details = serializers.SerializerMethodField()
    
    # Location display
    location_display = serializers.SerializerMethodField()
    
    class Meta:
        model = IndividualTaxPayer
        fields = [
            'id', 'tin', 'first_name', 'middle_name', 'last_name', 'full_name',
            'display_name', 'nationality', 'gender', 'date_of_birth',
            'tax_payer_level', 'tax_payer_level_name', 'tax_payer_level_details',
            'business_sector', 'business_sector_name',
            'business_sub_sector', 'business_sub_sector_name',
            'business_registration_date', 'business_name', 'business_license_number',
            'phone', 'phone_secondary', 'email',
            'country', 'country_name', 'region', 'region_name', 'zone', 'zone_name',
            'city', 'city_name', 'subcity', 'subcity_name', 'kebele', 'kebele_name',
            'house_number', 'street_address', 'postal_code', 'location_display',
            'profile_picture', 'tax_file', 'tax_file_name', 'tax_file_details',
            'is_active', 'registration_date', 'last_updated',
            'is_business_closed', 'business_closure_date', 'business_closure_reason', 'closed_by'
        ]
        read_only_fields = [
            'id', 'full_name', 'display_name', 'location_display',
            'registration_date', 'last_updated'
        ]
    
    def get_location_display(self, obj):
        parts = []
        if obj.country:
            parts.append(obj.country.name)
        if obj.region:
            parts.append(obj.region.name)
        if obj.zone:
            parts.append(obj.zone.name)
        if obj.city:
            parts.append(obj.city.name)
        if obj.subcity:
            parts.append(obj.subcity.name)
        if obj.kebele:
            parts.append(obj.kebele.name)
        if obj.house_number:
            parts.append(f"House {obj.house_number}")
        return ', '.join(parts) if parts else 'Not specified'

    def get_tax_payer_level_details(self, obj):
        """Get detailed tax payer level information"""
        if obj.tax_payer_level:
            return {
                'id': str(obj.tax_payer_level.id),
                'code': obj.tax_payer_level.code,
                'name': obj.tax_payer_level.name,
                'daily_income_range': f"${obj.tax_payer_level.min_daily_income:,.2f}" +
                                    (f" - ${obj.tax_payer_level.max_daily_income:,.2f}" if obj.tax_payer_level.max_daily_income else "+"),
                'tax_rate_percentage': float(obj.tax_payer_level.tax_rate_percentage),
                'priority': obj.tax_payer_level.priority,
            }
        return None

    def get_tax_file_details(self, obj):
        """Get detailed tax file information"""
        if obj.tax_file:
            return {
                'id': str(obj.tax_file.id),
                'name': obj.tax_file.name,
                'file_number': obj.tax_file.file_number,
                'full_code': obj.tax_file.full_code,
                'location_path': obj.tax_file.location_path,
                'kent_name': obj.tax_file.kent.name if obj.tax_file.kent else None,
                'kent_code': obj.tax_file.kent.code if obj.tax_file.kent else None,
                'building_name': obj.tax_file.kent.box.shelf.building.name if obj.tax_file.kent and obj.tax_file.kent.box and obj.tax_file.kent.box.shelf and obj.tax_file.kent.box.shelf.building else None,
                'shelf_name': obj.tax_file.kent.box.shelf.name if obj.tax_file.kent and obj.tax_file.kent.box and obj.tax_file.kent.box.shelf else None,
            }
        return None


class IndividualTaxPayerCreateSerializer(serializers.ModelSerializer):
    """Individual Tax Payer creation serializer"""
    
    class Meta:
        model = IndividualTaxPayer
        fields = [
            'tin', 'first_name', 'middle_name', 'last_name',
            'nationality', 'gender', 'date_of_birth',
            'tax_payer_level', 'business_sector', 'business_sub_sector',
            'business_registration_date', 'business_name', 'business_license_number',
            'phone', 'phone_secondary', 'email',
            'country', 'region', 'zone', 'city', 'subcity', 'kebele',
            'house_number', 'street_address', 'postal_code',
            'profile_picture', 'tax_file'
        ]


class OrganizationTaxPayerSerializer(serializers.ModelSerializer):
    """Organization Tax Payer serializer"""
    display_name = serializers.CharField(read_only=True)
    manager_full_name = serializers.CharField(source='get_manager_full_name', read_only=True)
    
    # Related field details
    tax_payer_level_details = serializers.SerializerMethodField()
    tax_payer_level_name = serializers.CharField(source='tax_payer_level.name', read_only=True)
    business_sector_name = serializers.CharField(source='business_sector.name', read_only=True)
    business_sub_sector_name = serializers.CharField(source='business_sub_sector.name', read_only=True)
    organization_business_type_name = serializers.CharField(source='organization_business_type.name', read_only=True)

    # Location hierarchy details
    country_name = serializers.CharField(source='country.name', read_only=True)
    region_name = serializers.CharField(source='region.name', read_only=True)
    zone_name = serializers.CharField(source='zone.name', read_only=True)
    city_name = serializers.CharField(source='city.name', read_only=True)
    subcity_name = serializers.CharField(source='subcity.name', read_only=True)
    kebele_name = serializers.CharField(source='kebele.name', read_only=True)

    tax_file_name = serializers.CharField(source='tax_file.name', read_only=True)
    tax_file_details = serializers.SerializerMethodField()
    
    # Location display
    location_display = serializers.SerializerMethodField()
    
    class Meta:
        model = OrganizationTaxPayer
        fields = [
            'id', 'tin', 'business_name', 'trade_name', 'display_name',
            'organization_business_type', 'organization_business_type_name',
            'tax_payer_level', 'tax_payer_level_name', 'tax_payer_level_details',
            'business_sector', 'business_sector_name',
            'business_sub_sector', 'business_sub_sector_name',
            'business_registration_date', 'business_license_number',
            'capital_amount', 'number_of_employees',
            'manager_first_name', 'manager_middle_name', 'manager_last_name',
            'manager_title', 'manager_full_name',
            'vat_registration_date', 'vat_number',
            'phone', 'phone_secondary', 'email',
            'country', 'country_name', 'region', 'region_name', 'zone', 'zone_name',
            'city', 'city_name', 'subcity', 'subcity_name', 'kebele', 'kebele_name',
            'house_number', 'street_address', 'postal_code', 'location_display',
            'tax_file', 'tax_file_name', 'tax_file_details',
            'is_active', 'registration_date', 'last_updated',
            'is_business_closed', 'business_closure_date', 'business_closure_reason', 'closed_by'
        ]
        read_only_fields = [
            'id', 'display_name', 'manager_full_name', 'location_display',
            'registration_date', 'last_updated'
        ]
    
    def get_location_display(self, obj):
        parts = []
        if obj.country:
            parts.append(obj.country.name)
        if obj.region:
            parts.append(obj.region.name)
        if obj.zone:
            parts.append(obj.zone.name)
        if obj.city:
            parts.append(obj.city.name)
        if obj.subcity:
            parts.append(obj.subcity.name)
        if obj.kebele:
            parts.append(obj.kebele.name)
        if obj.house_number:
            parts.append(f"House {obj.house_number}")
        return ', '.join(parts) if parts else 'Not specified'

    def get_tax_payer_level_details(self, obj):
        """Get detailed tax payer level information"""
        if obj.tax_payer_level:
            return {
                'id': str(obj.tax_payer_level.id),
                'code': obj.tax_payer_level.code,
                'name': obj.tax_payer_level.name,
                'daily_income_range': f"${obj.tax_payer_level.min_daily_income:,.2f}" +
                                    (f" - ${obj.tax_payer_level.max_daily_income:,.2f}" if obj.tax_payer_level.max_daily_income else "+"),
                'tax_rate_percentage': float(obj.tax_payer_level.tax_rate_percentage),
                'priority': obj.tax_payer_level.priority,
            }
        return None

    def get_tax_file_details(self, obj):
        """Get detailed tax file information"""
        if obj.tax_file:
            return {
                'id': str(obj.tax_file.id),
                'name': obj.tax_file.name,
                'file_number': obj.tax_file.file_number,
                'full_code': obj.tax_file.full_code,
                'location_path': obj.tax_file.location_path,
                'kent_name': obj.tax_file.kent.name if obj.tax_file.kent else None,
                'kent_code': obj.tax_file.kent.code if obj.tax_file.kent else None,
                'building_name': obj.tax_file.kent.box.shelf.building.name if obj.tax_file.kent and obj.tax_file.kent.box and obj.tax_file.kent.box.shelf and obj.tax_file.kent.box.shelf.building else None,
                'shelf_name': obj.tax_file.kent.box.shelf.name if obj.tax_file.kent and obj.tax_file.kent.box and obj.tax_file.kent.box.shelf else None,
            }
        return None


class BusinessClosureSerializer(serializers.Serializer):
    """Serializer for business closure operations"""
    closure_date = serializers.DateField(
        help_text="Date when the business was officially closed"
    )
    closure_reason = serializers.CharField(
        max_length=1000,
        help_text="Reason for business closure (e.g., Bankruptcy, Voluntary closure, Relocation, etc.)"
    )

    def validate_closure_date(self, value):
        from django.utils import timezone
        if value > timezone.now().date():
            raise serializers.ValidationError("Closure date cannot be in the future.")
        return value


class OrganizationTaxPayerCreateSerializer(serializers.ModelSerializer):
    """Organization Tax Payer creation serializer"""
    
    class Meta:
        model = OrganizationTaxPayer
        fields = [
            'tin', 'business_name', 'trade_name', 'organization_business_type',
            'tax_payer_level', 'business_sector', 'business_sub_sector',
            'business_registration_date', 'business_license_number',
            'capital_amount', 'number_of_employees',
            'manager_first_name', 'manager_middle_name', 'manager_last_name',
            'manager_title', 'vat_registration_date', 'vat_number',
            'phone', 'phone_secondary', 'email',
            'country', 'region', 'zone', 'city', 'subcity', 'kebele',
            'house_number', 'street_address', 'postal_code',
            'tax_file'
        ]


# Simplified serializers for dropdowns and selections
class BusinessSectorSimpleSerializer(serializers.ModelSerializer):
    class Meta:
        model = BusinessSector
        fields = ['id', 'code', 'name']


class BusinessSubSectorSimpleSerializer(serializers.ModelSerializer):
    class Meta:
        model = BusinessSubSector
        fields = ['id', 'code', 'name', 'business_sector']


class TaxPayerLevelSimpleSerializer(serializers.ModelSerializer):
    class Meta:
        model = TaxPayerLevel
        fields = ['id', 'code', 'name']


class OrganizationBusinessTypeSimpleSerializer(serializers.ModelSerializer):
    class Meta:
        model = OrganizationBusinessType
        fields = ['id', 'code', 'name', 'requires_vat_registration']


# Daily Income Analysis Serializers
class DailyIncomeAnalysisSerializer(serializers.ModelSerializer):
    """Serializer for daily income analysis"""
    taxpayer_name = serializers.ReadOnlyField()
    requires_level_upgrade = serializers.ReadOnlyField()
    current_level_name = serializers.CharField(source='current_level.name', read_only=True)
    recommended_level_name = serializers.CharField(source='recommended_level.name', read_only=True)

    class Meta:
        model = DailyIncomeAnalysis
        fields = [
            'id', 'analysis_year', 'analysis_date', 'taxpayer_type', 'taxpayer_id',
            'taxpayer_name', 'total_annual_income', 'average_daily_income', 'working_days',
            'current_level', 'current_level_name', 'recommended_level', 'recommended_level_name',
            'requires_level_upgrade', 'status', 'reviewed_by', 'reviewed_at', 'review_notes',
            'created_by', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'analysis_date', 'taxpayer_name', 'requires_level_upgrade', 'created_at', 'updated_at']


class DailyIncomeAnalysisCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating daily income analysis"""

    class Meta:
        model = DailyIncomeAnalysis
        fields = [
            'analysis_year', 'taxpayer_type', 'taxpayer_id',
            'total_annual_income', 'working_days', 'current_level'
        ]

    def validate(self, data):
        """Validate the analysis data"""
        # Check if analysis already exists for this year and taxpayer
        existing = DailyIncomeAnalysis.objects.filter(
            analysis_year=data['analysis_year'],
            taxpayer_type=data['taxpayer_type'],
            taxpayer_id=data['taxpayer_id']
        ).exists()

        if existing:
            raise serializers.ValidationError(
                f"Analysis for {data['taxpayer_type']} {data['taxpayer_id']} "
                f"already exists for year {data['analysis_year']}"
            )

        return data

    def create(self, validated_data):
        """Create analysis with calculated daily income and recommended level"""
        # Calculate average daily income
        total_income = validated_data['total_annual_income']
        working_days = validated_data.get('working_days', 365)
        average_daily_income = total_income / working_days

        # Get recommended level based on daily income
        recommended_level = TaxPayerLevel.get_appropriate_level(average_daily_income)

        # Create the analysis
        analysis = DailyIncomeAnalysis.objects.create(
            **validated_data,
            average_daily_income=average_daily_income,
            recommended_level=recommended_level,
            created_by=self.context['request'].user if 'request' in self.context else None
        )

        # Create notification if upgrade is recommended
        if analysis.requires_level_upgrade:
            LevelUpgradeNotification.create_for_analysis(analysis)

        return analysis


class LevelUpgradeNotificationSerializer(serializers.ModelSerializer):
    """Serializer for level upgrade notifications"""
    taxpayer_name = serializers.CharField(source='analysis.taxpayer_name', read_only=True)
    analysis_year = serializers.IntegerField(source='analysis.analysis_year', read_only=True)
    current_level_code = serializers.CharField(source='analysis.current_level.code', read_only=True)
    recommended_level_code = serializers.CharField(source='analysis.recommended_level.code', read_only=True)
    average_daily_income = serializers.DecimalField(
        source='analysis.average_daily_income',
        max_digits=15,
        decimal_places=2,
        read_only=True
    )

    class Meta:
        model = LevelUpgradeNotification
        fields = [
            'id', 'title', 'message', 'taxpayer_name', 'analysis_year',
            'current_level_code', 'recommended_level_code', 'average_daily_income',
            'is_read', 'is_dismissed', 'created_at', 'read_at', 'dismissed_at',
            'analysis'
        ]
        read_only_fields = [
            'id', 'title', 'message', 'taxpayer_name', 'analysis_year',
            'current_level_code', 'recommended_level_code', 'average_daily_income',
            'created_at', 'read_at', 'dismissed_at'
        ]
