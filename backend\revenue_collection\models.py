"""
Revenue Collection Models for Ethiopian Tax System

This module provides flexible models for revenue collection that integrate
with the existing taxpayer system. All revenue categories and sources are
stored as models rather than hardcoded choices for maximum flexibility.

Structure:
- RevenueCategory (abstract base)
- RevenueSource (abstract base)
- RegionalCategory/CityServiceCategory (concrete categories)
- RegionalRevenueSource/CityServiceRevenueSource (concrete sources)
- RevenuePeriod (time periods for collection)
- RevenueCollection (abstract base for collections)
- RegionalRevenueCollection/CityServiceRevenueCollection (concrete collections)
- RevenueSummary (aggregated reporting data)
"""

from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.conf import settings
from django.db.models import Sum
import uuid
from taxpayers.models import IndividualTaxPayer, OrganizationTaxPayer
from locations.location_hierarchy_models import Country, Region, Zone, City, SubCity, Kebele


class RevenueCategory(models.Model):
    """Abstract base category model for all revenue types"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, help_text="Category name")
    code = models.CharField(max_length=20, unique=True, help_text="Unique category code")
    description = models.TextField(blank=True, help_text="Detailed description of the category")
    is_active = models.BooleanField(default=True, help_text="Whether this category is active")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="User who created this category"
    )

    def save(self, *args, **kwargs):
        # Convert code to uppercase for case-insensitive storage
        if self.code:
            self.code = self.code.upper()
        super().save(*args, **kwargs)

    class Meta:
        abstract = True
        ordering = ['code', 'name']

    def __str__(self):
        return f"{self.code} - {self.name}"


class RevenueSource(models.Model):
    """Abstract base model for revenue sources"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, help_text="Revenue source name")
    code = models.CharField(max_length=20, help_text="Unique source code")
    description = models.TextField(blank=True, help_text="Detailed description of the revenue source")
    is_active = models.BooleanField(default=True, help_text="Whether this source is active")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="User who created this source"
    )

    def save(self, *args, **kwargs):
        # Convert code to uppercase for case-insensitive storage
        if self.code:
            self.code = self.code.upper()
        super().save(*args, **kwargs)

    class Meta:
        abstract = True
        ordering = ['code', 'name']

    def __str__(self):
        return f"{self.code} - {self.name}"


class RegionalCategory(RevenueCategory):
    """Categories specific to regional revenue"""

    class Meta(RevenueCategory.Meta):
        verbose_name = "Regional Category"
        verbose_name_plural = "Regional Categories"


class CityServiceCategory(RevenueCategory):
    """Categories specific to city service revenue"""

    class Meta(RevenueCategory.Meta):
        verbose_name = "City Service Category"
        verbose_name_plural = "City Service Categories"


class RegionalRevenueSource(RevenueSource):
    """Revenue sources for regional government"""
    category = models.ForeignKey(
        RegionalCategory,
        on_delete=models.PROTECT,
        related_name='revenue_sources',
        help_text="Regional category this source belongs to"
    )

    class Meta(RevenueSource.Meta):
        verbose_name = "Regional Revenue Source"
        verbose_name_plural = "Regional Revenue Sources"
        unique_together = ['category', 'code']

    def __str__(self):
        return f"{self.name} ({self.code}) - {self.category.name}"


class CityServiceRevenueSource(RevenueSource):
    """Revenue sources for city services"""
    category = models.ForeignKey(
        CityServiceCategory,
        on_delete=models.PROTECT,
        related_name='revenue_sources',
        help_text="City service category this source belongs to"
    )

    class Meta(RevenueSource.Meta):
        verbose_name = "City Service Revenue Source"
        verbose_name_plural = "City Service Revenue Sources"
        unique_together = ['category', 'code']

    def __str__(self):
        return f"{self.name} ({self.code}) - {self.category.name}"


class RevenuePeriod(models.Model):
    """Time period for revenue collection (monthly, quarterly, annual)"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, help_text="Period name (e.g., 'Q1 2024', 'January 2024')")
    code = models.CharField(max_length=20, unique=True, help_text="Period code (e.g., '2024Q1', '2024-01')")
    start_date = models.DateField(help_text="Period start date")
    end_date = models.DateField(help_text="Period end date")
    is_closed = models.BooleanField(default=False, help_text="Whether this period is closed for new entries")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="User who created this period"
    )

    def save(self, *args, **kwargs):
        # Convert code to uppercase for case-insensitive storage
        if self.code:
            self.code = self.code.upper()
        super().save(*args, **kwargs)

    class Meta:
        ordering = ['-start_date']
        verbose_name = "Revenue Period"
        verbose_name_plural = "Revenue Periods"

    def __str__(self):
        return self.name

    @property
    def is_current(self):
        """Check if this period is currently active"""
        if not self.start_date or not self.end_date:
            return False
        from django.utils import timezone
        today = timezone.now().date()
        return self.start_date <= today <= self.end_date


class RevenueCollection(models.Model):
    """Abstract base model for revenue collection"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Payment status choices
    PAYMENT_STATUS_CHOICES = [
        ('PENDING', 'Pending Payment'),
        ('PAID', 'Paid'),
        ('OVERDUE', 'Overdue'),
        ('PARTIAL', 'Partially Paid'),
        ('CANCELLED', 'Cancelled'),
    ]

    # Taxpayer can be either Individual or Organization
    individual_taxpayer = models.ForeignKey(
        IndividualTaxPayer,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        help_text="Individual taxpayer (if applicable)"
    )
    organization_taxpayer = models.ForeignKey(
        OrganizationTaxPayer,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        help_text="Organization taxpayer (if applicable)"
    )

    period = models.ForeignKey(
        RevenuePeriod,
        on_delete=models.PROTECT,
        help_text="Revenue collection period"
    )
    amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        help_text="Total amount to be collected"
    )
    paid_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)],
        help_text="Amount actually paid"
    )
    collection_date = models.DateField(help_text="Date when revenue was assessed/due")
    payment_date = models.DateField(
        null=True,
        blank=True,
        help_text="Date when payment was actually made"
    )
    due_date = models.DateField(
        null=True,
        blank=True,
        help_text="Payment due date"
    )
    payment_status = models.CharField(
        max_length=20,
        choices=PAYMENT_STATUS_CHOICES,
        default='PENDING',
        help_text="Current payment status"
    )

    # Penalty and Interest fields
    penalty_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)],
        help_text="Penalty amount for late payment"
    )
    interest_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)],
        help_text="Interest amount for late payment"
    )
    penalty_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Penalty rate percentage"
    )
    interest_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Interest rate percentage per month"
    )
    penalty_calculated_date = models.DateField(
        null=True,
        blank=True,
        help_text="Date when penalty was last calculated"
    )
    interest_calculated_date = models.DateField(
        null=True,
        blank=True,
        help_text="Date when interest was last calculated"
    )
    recorded_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='%(class)s_collections',
        help_text="User who recorded this collection"
    )
    recorded_date = models.DateTimeField(auto_now_add=True)
    last_modified = models.DateTimeField(auto_now=True)
    last_modified_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='%(class)s_modifications',
        null=True,
        blank=True,
        help_text="User who last modified this record"
    )
    notes = models.TextField(blank=True, help_text="Additional notes or comments")

    # Receipt/Reference information
    receipt_number = models.CharField(
        max_length=50,
        blank=True,
        help_text="Receipt or reference number"
    )

    class Meta:
        abstract = True
        ordering = ['-collection_date', '-recorded_date']

    def clean(self):
        """Ensure exactly one taxpayer type is selected"""
        from django.core.exceptions import ValidationError

        if not self.individual_taxpayer and not self.organization_taxpayer:
            raise ValidationError("Either individual_taxpayer or organization_taxpayer must be specified.")

        if self.individual_taxpayer and self.organization_taxpayer:
            raise ValidationError("Cannot specify both individual_taxpayer and organization_taxpayer.")

    @property
    def outstanding_amount(self):
        """Calculate outstanding amount including penalties and interest"""
        return (self.amount + self.penalty_amount + self.interest_amount) - self.paid_amount

    @property
    def total_amount_due(self):
        """Calculate total amount due including penalties and interest"""
        return self.amount + self.penalty_amount + self.interest_amount

    @property
    def is_fully_paid(self):
        """Check if fully paid including penalties and interest"""
        return self.paid_amount >= self.total_amount_due

    @property
    def is_overdue(self):
        """Check if payment is overdue"""
        if not self.due_date:
            return False
        from django.utils import timezone
        return timezone.now().date() > self.due_date and not self.is_fully_paid

    @property
    def days_overdue(self):
        """Calculate days overdue"""
        if not self.is_overdue:
            return 0
        from django.utils import timezone
        return (timezone.now().date() - self.due_date).days

    def get_organization_rates(self):
        """Get penalty and interest rates from organization settings"""
        try:
            # Get the organization from the taxpayer
            if self.individual_taxpayer:
                # For individual taxpayers, we might need to get default rates
                # or from a system-wide setting
                from organizations.models import Organization
                default_org = Organization.objects.filter(is_default=True).first()
                if default_org:
                    return {
                        'penalty_rate': getattr(default_org, 'individual_penalty_rate', 5.0),
                        'interest_rate': getattr(default_org, 'individual_interest_rate', 2.0)
                    }
            elif self.organization_taxpayer:
                # For organization taxpayers, get rates from their organization
                from organizations.models import Organization
                default_org = Organization.objects.filter(is_default=True).first()
                if default_org:
                    return {
                        'penalty_rate': getattr(default_org, 'organization_penalty_rate', 10.0),
                        'interest_rate': getattr(default_org, 'organization_interest_rate', 3.0)
                    }
        except Exception:
            pass

        # Default rates if no organization settings found
        return {
            'penalty_rate': 5.0,  # 5% penalty
            'interest_rate': 2.0  # 2% interest per month
        }

    def calculate_penalty(self):
        """Calculate penalty amount based on organization settings"""
        if not self.is_overdue or self.penalty_calculated_date:
            return

        rates = self.get_organization_rates()
        penalty_rate = rates['penalty_rate']

        # Calculate penalty as percentage of original amount
        self.penalty_rate = penalty_rate
        self.penalty_amount = (self.amount * penalty_rate) / 100
        self.penalty_calculated_date = timezone.now().date()

    def calculate_interest(self):
        """Calculate interest amount based on days overdue and organization settings"""
        if not self.is_overdue:
            return

        rates = self.get_organization_rates()
        interest_rate = rates['interest_rate']

        # Calculate monthly interest based on days overdue
        days_overdue = self.days_overdue
        months_overdue = days_overdue / 30.0  # Approximate months

        self.interest_rate = interest_rate
        self.interest_amount = (self.amount * interest_rate * months_overdue) / 100
        self.interest_calculated_date = timezone.now().date()

    def update_payment_status(self):
        """Update payment status based on current state"""
        # First calculate penalties and interest if overdue
        if self.is_overdue:
            if not self.penalty_calculated_date:
                self.calculate_penalty()
            self.calculate_interest()  # Recalculate interest each time

        # Update payment status
        if self.is_fully_paid:
            self.payment_status = 'PAID'
        elif self.paid_amount > 0:
            self.payment_status = 'PARTIAL'
        elif self.is_overdue:
            self.payment_status = 'OVERDUE'
        else:
            self.payment_status = 'PENDING'

    def save(self, *args, **kwargs):
        """Override save to update payment status and set due date"""
        # Set due date if not provided (30 days from collection date)
        if not self.due_date:
            from datetime import timedelta
            self.due_date = self.collection_date + timedelta(days=30)

        # Update payment status
        self.update_payment_status()

        # Set payment date if fully paid and not already set
        if self.is_fully_paid and not self.payment_date:
            from django.utils import timezone
            self.payment_date = timezone.now().date()

        super().save(*args, **kwargs)

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    @property
    def taxpayer(self):
        """Return the taxpayer (individual or organization)"""
        return self.individual_taxpayer or self.organization_taxpayer

    @property
    def taxpayer_name(self):
        """Return the taxpayer name"""
        if self.individual_taxpayer:
            return self.individual_taxpayer.get_full_name()
        elif self.organization_taxpayer:
            return self.organization_taxpayer.business_name
        return "Unknown"

    @property
    def taxpayer_tin(self):
        """Return the taxpayer TIN"""
        taxpayer = self.taxpayer
        return taxpayer.tin if taxpayer else "Unknown"


class RegionalRevenueCollection(RevenueCollection):
    """Concrete model for regional revenue collection"""
    revenue_source = models.ForeignKey(
        RegionalRevenueSource,
        on_delete=models.PROTECT,
        help_text="Regional revenue source"
    )

    class Meta:
        verbose_name = "Regional Revenue Collection"
        verbose_name_plural = "Regional Revenue Collections"
        indexes = [
            models.Index(fields=['collection_date']),
            models.Index(fields=['period', 'revenue_source']),
            models.Index(fields=['individual_taxpayer']),
            models.Index(fields=['organization_taxpayer']),
        ]

    def __str__(self):
        return f"{self.taxpayer_name} - {self.revenue_source} - {self.period} - {self.amount}"


class CityServiceRevenueCollection(RevenueCollection):
    """Concrete model for city service revenue collection"""
    revenue_source = models.ForeignKey(
        CityServiceRevenueSource,
        on_delete=models.PROTECT,
        help_text="City service revenue source"
    )

    class Meta:
        verbose_name = "City Service Revenue Collection"
        verbose_name_plural = "City Service Revenue Collections"
        indexes = [
            models.Index(fields=['collection_date']),
            models.Index(fields=['period', 'revenue_source']),
            models.Index(fields=['individual_taxpayer']),
            models.Index(fields=['organization_taxpayer']),
        ]

    def __str__(self):
        return f"{self.taxpayer_name} - {self.revenue_source} - {self.period} - {self.amount}"


class RevenueSummary(models.Model):
    """Aggregated revenue data for reporting and analytics"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    period = models.ForeignKey(
        RevenuePeriod,
        on_delete=models.PROTECT,
        help_text="Revenue period for this summary"
    )

    # Location-based aggregation
    country = models.ForeignKey(
        Country,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        help_text="Country for this summary"
    )
    region = models.ForeignKey(
        Region,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        help_text="Region for this summary"
    )
    zone = models.ForeignKey(
        Zone,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        help_text="Zone for this summary"
    )
    city = models.ForeignKey(
        City,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        help_text="City for this summary"
    )
    subcity = models.ForeignKey(
        SubCity,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        help_text="SubCity/Woreda for this summary"
    )
    kebele = models.ForeignKey(
        Kebele,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        help_text="Kebele for this summary"
    )

    # Summary totals
    regional_total = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        help_text="Total regional revenue for this period/location"
    )
    city_service_total = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        help_text="Total city service revenue for this period/location"
    )
    grand_total = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        help_text="Grand total revenue for this period/location"
    )

    # Metadata
    summary_date = models.DateField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="User who created this summary"
    )

    class Meta:
        verbose_name = "Revenue Summary"
        verbose_name_plural = "Revenue Summaries"
        ordering = ['-period__start_date', '-summary_date']
        indexes = [
            models.Index(fields=['period']),
            models.Index(fields=['region', 'period']),
            models.Index(fields=['city', 'period']),
            models.Index(fields=['subcity', 'period']),
            models.Index(fields=['kebele', 'period']),
        ]

    def update_totals(self):
        """Method to update summary totals based on actual collections"""
        # Build location filter
        location_filter = {}
        if self.kebele:
            location_filter['individual_taxpayer__kebele'] = self.kebele
            location_filter['organization_taxpayer__kebele'] = self.kebele
        elif self.subcity:
            location_filter['individual_taxpayer__subcity'] = self.subcity
            location_filter['organization_taxpayer__subcity'] = self.subcity
        elif self.city:
            location_filter['individual_taxpayer__city'] = self.city
            location_filter['organization_taxpayer__city'] = self.city
        elif self.zone:
            location_filter['individual_taxpayer__zone'] = self.zone
            location_filter['organization_taxpayer__zone'] = self.zone
        elif self.region:
            location_filter['individual_taxpayer__region'] = self.region
            location_filter['organization_taxpayer__region'] = self.region
        elif self.country:
            location_filter['individual_taxpayer__country'] = self.country
            location_filter['organization_taxpayer__country'] = self.country

        # Calculate regional total
        regional_individual = RegionalRevenueCollection.objects.filter(
            period=self.period,
            individual_taxpayer__isnull=False,
            **{k: v for k, v in location_filter.items() if 'individual_taxpayer' in k}
        ).aggregate(total=Sum('amount'))['total'] or 0

        regional_organization = RegionalRevenueCollection.objects.filter(
            period=self.period,
            organization_taxpayer__isnull=False,
            **{k: v for k, v in location_filter.items() if 'organization_taxpayer' in k}
        ).aggregate(total=Sum('amount'))['total'] or 0

        self.regional_total = regional_individual + regional_organization

        # Calculate city service total
        city_individual = CityServiceRevenueCollection.objects.filter(
            period=self.period,
            individual_taxpayer__isnull=False,
            **{k: v for k, v in location_filter.items() if 'individual_taxpayer' in k}
        ).aggregate(total=Sum('amount'))['total'] or 0

        city_organization = CityServiceRevenueCollection.objects.filter(
            period=self.period,
            organization_taxpayer__isnull=False,
            **{k: v for k, v in location_filter.items() if 'organization_taxpayer' in k}
        ).aggregate(total=Sum('amount'))['total'] or 0

        self.city_service_total = city_individual + city_organization

        # Calculate grand total
        self.grand_total = self.regional_total + self.city_service_total
        self.save()

    def __str__(self):
        location_parts = []
        if self.kebele:
            location_parts.append(f"Kebele {self.kebele.name}")
        if self.subcity:
            location_parts.append(f"SubCity {self.subcity.name}")
        if self.city:
            location_parts.append(f"City {self.city.name}")
        if self.region:
            location_parts.append(f"Region {self.region.name}")

        location_str = ", ".join(location_parts) if location_parts else "All Locations"
        return f"Revenue Summary for {self.period} - {location_str}"


class TaxpayerPaymentSummary(models.Model):
    """
    Model to track payment summaries for taxpayers across periods
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Taxpayer (either individual or organization)
    individual_taxpayer = models.ForeignKey(
        'taxpayers.IndividualTaxPayer',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text="Individual taxpayer"
    )
    organization_taxpayer = models.ForeignKey(
        'taxpayers.OrganizationTaxPayer',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text="Organization taxpayer"
    )

    period = models.ForeignKey(
        RevenuePeriod,
        on_delete=models.CASCADE,
        help_text="Revenue period"
    )

    # Summary amounts
    total_assessed = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        help_text="Total amount assessed for this period"
    )
    total_paid = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        help_text="Total amount paid for this period"
    )
    total_outstanding = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        help_text="Total outstanding amount"
    )
    total_overdue = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        help_text="Total overdue amount"
    )

    # Counts
    total_collections = models.IntegerField(
        default=0,
        help_text="Total number of collections"
    )
    paid_collections = models.IntegerField(
        default=0,
        help_text="Number of fully paid collections"
    )
    overdue_collections = models.IntegerField(
        default=0,
        help_text="Number of overdue collections"
    )

    # Dates
    last_payment_date = models.DateField(
        null=True,
        blank=True,
        help_text="Date of last payment"
    )
    next_due_date = models.DateField(
        null=True,
        blank=True,
        help_text="Next payment due date"
    )

    # Audit fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='taxpayer_summaries_created',
        help_text="User who created this summary"
    )

    class Meta:
        unique_together = [
            ['individual_taxpayer', 'period'],
            ['organization_taxpayer', 'period']
        ]
        ordering = ['-period__start_date', '-updated_at']
        indexes = [
            models.Index(fields=['individual_taxpayer', 'period']),
            models.Index(fields=['organization_taxpayer', 'period']),
            models.Index(fields=['period', 'total_overdue']),
        ]

    def clean(self):
        """Ensure exactly one taxpayer type is selected"""
        from django.core.exceptions import ValidationError

        if not self.individual_taxpayer and not self.organization_taxpayer:
            raise ValidationError("Either individual_taxpayer or organization_taxpayer must be specified.")

        if self.individual_taxpayer and self.organization_taxpayer:
            raise ValidationError("Cannot specify both individual_taxpayer and organization_taxpayer.")

    @property
    def taxpayer(self):
        """Get the taxpayer (individual or organization)"""
        return self.individual_taxpayer or self.organization_taxpayer

    @property
    def taxpayer_name(self):
        """Get taxpayer name"""
        if self.individual_taxpayer:
            return self.individual_taxpayer.get_full_name()
        elif self.organization_taxpayer:
            return self.organization_taxpayer.business_name
        return "Unknown"

    @property
    def taxpayer_tin(self):
        """Get taxpayer TIN"""
        taxpayer = self.taxpayer
        return taxpayer.tin if taxpayer else "N/A"

    @property
    def payment_completion_rate(self):
        """Calculate payment completion rate as percentage"""
        if self.total_assessed == 0:
            return 100
        return (self.total_paid / self.total_assessed) * 100

    def update_summary(self):
        """Update summary from related collections"""
        # Get all collections for this taxpayer and period
        if self.individual_taxpayer:
            regional_collections = RegionalRevenueCollection.objects.filter(
                individual_taxpayer=self.individual_taxpayer,
                period=self.period
            )
            city_collections = CityServiceRevenueCollection.objects.filter(
                individual_taxpayer=self.individual_taxpayer,
                period=self.period
            )
        else:
            regional_collections = RegionalRevenueCollection.objects.filter(
                organization_taxpayer=self.organization_taxpayer,
                period=self.period
            )
            city_collections = CityServiceRevenueCollection.objects.filter(
                organization_taxpayer=self.organization_taxpayer,
                period=self.period
            )

        all_collections = list(regional_collections) + list(city_collections)

        # Calculate totals
        self.total_assessed = sum(c.amount for c in all_collections)
        self.total_paid = sum(c.paid_amount for c in all_collections)
        self.total_outstanding = sum(c.outstanding_amount for c in all_collections)
        self.total_overdue = sum(c.outstanding_amount for c in all_collections if c.is_overdue)

        # Calculate counts
        self.total_collections = len(all_collections)
        self.paid_collections = sum(1 for c in all_collections if c.is_fully_paid)
        self.overdue_collections = sum(1 for c in all_collections if c.is_overdue)

        # Update dates
        paid_collections = [c for c in all_collections if c.payment_date]
        if paid_collections:
            self.last_payment_date = max(c.payment_date for c in paid_collections)

        unpaid_collections = [c for c in all_collections if not c.is_fully_paid and c.due_date]
        if unpaid_collections:
            self.next_due_date = min(c.due_date for c in unpaid_collections)

    def __str__(self):
        return f"Payment Summary: {self.taxpayer_name} - {self.period.name}"
