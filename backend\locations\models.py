from django.db import models
from django.core.validators import RegexValidator, MinValueValidator
from django.core.exceptions import ValidationError
from django.utils import timezone
from io import BytesIO
from django.core.files import File
import os
import uuid

# Import barcode and qrcode with error handling
try:
    import barcode
    from barcode.writer import ImageWriter
    import qrcode
    BARCODE_AVAILABLE = True
except ImportError:
    BARCODE_AVAILABLE = False

# Import location hierarchy models
from .location_hierarchy_models import Country, Region, Zone, City, SubCity, Kebele


class Building(models.Model):
    """
    Building model - Top level of location hierarchy
    """
    organization = models.ForeignKey(
        'organizations.Organization',
        on_delete=models.CASCADE,
        related_name='buildings'
    )

    name = models.CharField(
        max_length=100,
        help_text="Building name (e.g., Main Building, Annex)"
    )

    code = models.CharField(
        max_length=10,
        validators=[RegexValidator(
            regex=r'^[A-Z0-9]{1,10}$',
            message="Building code must contain only uppercase letters and numbers"
        )],
        help_text="Building code (e.g., B1, MAIN, ANNEX)"
    )

    description = models.TextField(null=True, blank=True)
    address = models.TextField(null=True, blank=True)

    # Barcode and QR code fields
    barcode_image = models.ImageField(
        upload_to='locations/barcodes/',
        null=True,
        blank=True
    )
    qr_code_image = models.ImageField(
        upload_to='locations/qrcodes/',
        null=True,
        blank=True
    )

    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'locations_building'
        verbose_name = 'Building'
        verbose_name_plural = 'Buildings'
        unique_together = ['organization', 'code']
        ordering = ['organization', 'code']

    def __str__(self):
        return f"{self.organization.short_name} - {self.name} ({self.code})"

    def save(self, *args, **kwargs):
        # Convert code to uppercase for case-insensitive storage
        if self.code:
            self.code = self.code.upper()
        super().save(*args, **kwargs)
        self.generate_codes()

    def generate_codes(self):
        """Generate barcode and QR code for the building"""
        if not BARCODE_AVAILABLE:
            return

        code_text = f"{self.organization.short_name}-{self.code}"

        # Generate barcode
        if not self.barcode_image:
            try:
                code128 = barcode.get_barcode_class('code128')
                barcode_instance = code128(code_text, writer=ImageWriter())
                buffer = BytesIO()
                barcode_instance.write(buffer)
                self.barcode_image.save(
                    f'building_{self.code}_barcode.png',
                    File(buffer),
                    save=False
                )
            except Exception as e:
                print(f"Error generating barcode: {e}")

        # Generate QR code
        if not self.qr_code_image:
            try:
                qr = qrcode.QRCode(version=1, box_size=10, border=5)
                qr.add_data(code_text)
                qr.make(fit=True)
                qr_img = qr.make_image(fill_color="black", back_color="white")
                buffer = BytesIO()
                qr_img.save(buffer, format='PNG')
                self.qr_code_image.save(
                    f'building_{self.code}_qr.png',
                    File(buffer),
                    save=False
                )
            except Exception as e:
                print(f"Error generating QR code: {e}")

        if self.barcode_image or self.qr_code_image:
            Building.objects.filter(pk=self.pk).update(
                barcode_image=self.barcode_image,
                qr_code_image=self.qr_code_image
            )


class Shelf(models.Model):
    """
    Shelf model - Second level of location hierarchy
    Enhanced with row/column grid system
    """
    building = models.ForeignKey(
        Building,
        on_delete=models.CASCADE,
        related_name='shelves'
    )

    name = models.CharField(
        max_length=100,
        help_text="Shelf name or identifier"
    )

    code = models.CharField(
        max_length=10,
        validators=[RegexValidator(
            regex=r'^[A-Z0-9]{1,10}$',
            message="Shelf code must contain only uppercase letters and numbers"
        )],
        help_text="Shelf code (e.g., S1, S2, SH01)"
    )

    description = models.TextField(null=True, blank=True)

    # Grid system for shelf organization
    rows = models.PositiveIntegerField(
        default=5,
        validators=[MinValueValidator(1)],
        help_text="Number of rows in this shelf"
    )

    columns = models.PositiveIntegerField(
        default=10,
        validators=[MinValueValidator(1)],
        help_text="Number of columns in this shelf"
    )

    # Legacy capacity field (calculated from rows * columns)
    capacity = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Maximum number of kents this shelf can hold (auto-calculated from rows * columns)"
    )

    # Barcode and QR code fields
    barcode_image = models.ImageField(
        upload_to='locations/barcodes/',
        null=True,
        blank=True
    )
    qr_code_image = models.ImageField(
        upload_to='locations/qrcodes/',
        null=True,
        blank=True
    )

    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'locations_shelf'
        verbose_name = 'Shelf'
        verbose_name_plural = 'Shelves'
        unique_together = ['building', 'code']
        ordering = ['building', 'code']

    def __str__(self):
        return f"{self.building} - {self.name} ({self.code})"

    def save(self, *args, **kwargs):
        # Convert code to uppercase for case-insensitive storage
        if self.code:
            self.code = self.code.upper()
        super().save(*args, **kwargs)

    @property
    def full_code(self):
        """Return full hierarchical code"""
        return f"{self.building.code}-{self.code}"

    @property
    def utilization(self):
        """Calculate utilization percentage based on kent capacity"""
        if not self.capacity:
            return 0

        # Get kents through boxes
        shelf_kents = Kent.objects.filter(box__shelf=self, is_active=True)

        total_documents = 0
        for kent in shelf_kents:
            total_documents += kent.get_document_count()

        total_capacity = sum(kent.capacity or 0 for kent in shelf_kents)
        if total_capacity == 0:
            return 0

        return (total_documents / total_capacity) * 100

    def save(self, *args, **kwargs):
        # Auto-calculate capacity from rows * columns
        self.capacity = self.rows * self.columns
        is_new = self.pk is None

        # Store old dimensions if updating
        old_rows, old_columns = None, None
        if not is_new:
            try:
                old_shelf = Shelf.objects.get(pk=self.pk)
                old_rows, old_columns = old_shelf.rows, old_shelf.columns
            except Shelf.DoesNotExist:
                pass

        super().save(*args, **kwargs)

        # Auto-create boxes (positions) for new shelves or when dimensions change
        if is_new:
            self.create_boxes()
        elif old_rows is not None and old_columns is not None:
            if old_rows != self.rows or old_columns != self.columns:
                self.recreate_boxes()

        self.generate_codes()

    def create_boxes(self):
        """Create boxes for all positions in the shelf grid"""
        boxes_to_create = []
        for row in range(1, self.rows + 1):
            for col in range(1, self.columns + 1):
                box = Box(
                    shelf=self,
                    row=row,
                    column=col,
                    name=f"Position R{row:02d}C{col:02d}",
                    description=f"Box at row {row}, column {col} in {self.name}"
                )
                boxes_to_create.append(box)

        Box.objects.bulk_create(boxes_to_create)

    def recreate_boxes(self):
        """Recreate boxes when shelf dimensions change"""
        # Delete existing boxes (this will cascade to kents if needed)
        self.boxes.all().delete()
        # Create new boxes
        self.create_boxes()

    def get_position_code(self, row, column):
        """Generate position code for a specific row/column"""
        return f"R{row:02d}C{column:02d}"

    def get_available_positions(self):
        """Get list of available positions (row, column) that don't have kents"""
        occupied_positions = set()
        for kent in self.kents.all():
            if kent.row and kent.column:
                occupied_positions.add((kent.row, kent.column))

        available = []
        for row in range(1, self.rows + 1):
            for col in range(1, self.columns + 1):
                if (row, col) not in occupied_positions:
                    available.append((row, col))
        return available

    def generate_codes(self):
        """Generate barcode and QR code for the shelf"""
        if not BARCODE_AVAILABLE:
            return

        code_text = f"{self.building.organization.short_name}-{self.full_code}"

        # Generate barcode
        if not self.barcode_image:
            try:
                code128 = barcode.get_barcode_class('code128')
                barcode_instance = code128(code_text, writer=ImageWriter())
                buffer = BytesIO()
                barcode_instance.write(buffer)
                self.barcode_image.save(
                    f'shelf_{self.full_code}_barcode.png',
                    File(buffer),
                    save=False
                )
            except Exception as e:
                print(f"Error generating barcode: {e}")

        # Generate QR code
        if not self.qr_code_image:
            try:
                qr = qrcode.QRCode(version=1, box_size=10, border=5)
                qr.add_data(code_text)
                qr.make(fit=True)
                qr_img = qr.make_image(fill_color="black", back_color="white")
                buffer = BytesIO()
                qr_img.save(buffer, format='PNG')
                self.qr_code_image.save(
                    f'shelf_{self.full_code}_qr.png',
                    File(buffer),
                    save=False
                )
            except Exception as e:
                print(f"Error generating QR code: {e}")

        if self.barcode_image or self.qr_code_image:
            Shelf.objects.filter(pk=self.pk).update(
                barcode_image=self.barcode_image,
                qr_code_image=self.qr_code_image
            )


class Box(models.Model):
    """
    Box model - Third level of location hierarchy
    Represents a specific position (Row + Column) within a shelf
    """
    shelf = models.ForeignKey(
        Shelf,
        on_delete=models.CASCADE,
        related_name='boxes'
    )

    # Position in shelf grid
    row = models.PositiveIntegerField(
        validators=[MinValueValidator(1)],
        help_text="Row position in shelf (1-based)"
    )

    column = models.PositiveIntegerField(
        validators=[MinValueValidator(1)],
        help_text="Column position in shelf (1-based)"
    )

    name = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        help_text="Optional box name or identifier"
    )

    description = models.TextField(null=True, blank=True)

    # Physical properties
    color = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        help_text="Physical color of the box for identification"
    )

    material = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        help_text="Material of the box (e.g., Cardboard, Metal, Plastic)"
    )

    # Barcode and QR code fields
    barcode_image = models.ImageField(
        upload_to='locations/barcodes/',
        null=True,
        blank=True
    )
    qr_code_image = models.ImageField(
        upload_to='locations/qrcodes/',
        null=True,
        blank=True
    )

    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'locations_box'
        verbose_name = 'Box (Position)'
        verbose_name_plural = 'Boxes (Positions)'
        unique_together = ['shelf', 'row', 'column']  # Ensure unique position in shelf
        ordering = ['shelf', 'row', 'column']

    def __str__(self):
        name_part = f" - {self.name}" if self.name else ""
        if self.row is not None and self.column is not None:
            return f"{self.shelf} - R{self.row:02d}C{self.column:02d}{name_part}"
        else:
            return f"{self.shelf} - New Box{name_part}"

    @property
    def position_code(self):
        """Return position code (R01C02)"""
        if self.row is not None and self.column is not None:
            return f"R{self.row:02d}C{self.column:02d}"
        else:
            return "NEW"

    @property
    def full_code(self):
        """Return full hierarchical code (Building-Shelf-Position)"""
        if self.shelf and self.shelf.building:
            return f"{self.shelf.building.code}-{self.shelf.code}-{self.position_code}"
        else:
            return f"NEW-{self.position_code}"

    def clean(self):
        """Validate that row and column are within shelf limits"""
        if self.shelf_id and self.row is not None and self.column is not None:
            if self.row > self.shelf.rows:
                raise ValidationError(f"Row {self.row} exceeds shelf capacity of {self.shelf.rows} rows")
            if self.column > self.shelf.columns:
                raise ValidationError(f"Column {self.column} exceeds shelf capacity of {self.shelf.columns} columns")

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)
        self.generate_codes()

    def generate_codes(self):
        """Generate barcode and QR code for the box"""
        from .utils import generate_barcode, generate_qr_code

        # Generate codes with full hierarchical information
        code_data = {
            'type': 'box',
            'id': str(self.id) if self.id else 'new',
            'organization': self.shelf.building.organization.short_name,
            'building': self.shelf.building.code,
            'shelf': self.shelf.code,
            'position': self.position_code,
            'full_code': self.full_code
        }

        try:
            self.barcode_image = generate_barcode(self.full_code, 'locations/barcodes/')
            self.qr_code_image = generate_qr_code(code_data, 'locations/qrcodes/')

            # Save without triggering save recursion
            Box.objects.filter(id=self.id).update(
                barcode_image=self.barcode_image,
                qr_code_image=self.qr_code_image
            )
        except Exception as e:
            print(f"Error generating codes for box {self}: {e}")

    def get_kent_count(self):
        """Get number of kents in this box"""
        return self.kents.filter(is_active=True).count()

    def get_file_count(self):
        """Get total number of files in all kents in this box"""
        return sum(kent.get_file_count() for kent in self.kents.filter(is_active=True))

    def get_document_count(self):
        """Get total number of documents in all files in all kents in this box"""
        try:
            return sum(kent.get_document_count() for kent in self.kents.filter(is_active=True))
        except Exception as e:
            print(f"Error in get_document_count for box {self.id}: {e}")
            # Fallback: return file count
            return sum(kent.get_file_count() for kent in self.kents.filter(is_active=True))


class Kent(models.Model):
    """
    Kent model - Fourth level of location hierarchy
    Physical containers within a box position
    """
    box = models.ForeignKey(
        Box,
        on_delete=models.CASCADE,
        related_name='kents'
    )

    name = models.CharField(
        max_length=100,
        help_text="Kent name or identifier"
    )

    code = models.CharField(
        max_length=10,
        validators=[RegexValidator(
            regex=r'^[A-Z0-9]{1,10}$',
            message="Kent code must contain only uppercase letters and numbers"
        )],
        help_text="Kent code (e.g., K1, K2, KT01)"
    )

    description = models.TextField(null=True, blank=True)

    capacity = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Maximum number of files this kent can hold"
    )

    # Physical properties
    color = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        help_text="Physical color of the kent for identification"
    )

    material = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        help_text="Material of the kent (e.g., Cardboard, Metal, Plastic)"
    )

    # Barcode and QR code fields
    barcode_image = models.ImageField(
        upload_to='locations/barcodes/',
        null=True,
        blank=True
    )
    qr_code_image = models.ImageField(
        upload_to='locations/qrcodes/',
        null=True,
        blank=True
    )

    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'locations_kent'
        verbose_name = 'Kent'
        verbose_name_plural = 'Kents'
        unique_together = ['box', 'code']
        ordering = ['box', 'code']

    def __str__(self):
        return f"{self.box} - {self.name} ({self.code})"

    def save(self, *args, **kwargs):
        # Convert code to uppercase for case-insensitive storage
        if self.code:
            self.code = self.code.upper()
        super().save(*args, **kwargs)

    @property
    def full_code(self):
        """Return full hierarchical code (Building-Shelf-Position-Kent)"""
        return f"{self.box.full_code}-{self.code}"

    @property
    def shelf(self):
        """Backward compatibility property to access shelf through box"""
        return self.box.shelf

    @property
    def building(self):
        """Convenience property to access building"""
        return self.box.shelf.building

    @property
    def organization(self):
        """Convenience property to access organization"""
        return self.box.shelf.building.organization

    @property
    def position_code(self):
        """Return position code (Row/Column) from box"""
        return self.box.position_code

    @property
    def location_path(self):
        """Return human-readable location path"""
        return f"{self.box.shelf.building.name} → {self.box.shelf.name} → {self.box.position_code} → {self.name}"

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        self.generate_codes()

    def generate_codes(self):
        """Generate barcode and QR code for the kent"""
        from .utils import generate_barcode, generate_qr_code

        # Generate codes with full hierarchical information
        code_data = {
            'type': 'kent',
            'id': str(self.id) if self.id else 'new',
            'organization': self.organization.short_name,
            'building': self.building.code,
            'shelf': self.shelf.code,
            'position': self.box.position_code,
            'kent': self.code,
            'full_code': self.full_code
        }

        try:
            self.barcode_image = generate_barcode(self.full_code, 'locations/barcodes/')
            self.qr_code_image = generate_qr_code(code_data, 'locations/qrcodes/')

            # Save without triggering save recursion
            Kent.objects.filter(id=self.id).update(
                barcode_image=self.barcode_image,
                qr_code_image=self.qr_code_image
            )
        except Exception as e:
            print(f"Error generating codes for kent {self}: {e}")

    def get_file_count(self):
        """Return number of files in this kent"""
        return self.files.filter(is_active=True).count()

    def get_document_count(self):
        """Return total number of documents in all files in this kent"""
        total = 0
        try:
            # Check if documents app is available
            from django.apps import apps
            if not apps.is_installed('documents'):
                # Fallback: return file count if documents app not available
                return self.files.filter(is_active=True).count()

            for file in self.files.filter(is_active=True):
                # Use getattr to safely access documents relationship
                if hasattr(file, 'documents'):
                    total += file.documents.filter(is_active=True).count()
        except Exception as e:
            print(f"Error in get_document_count for kent {self.id}: {e}")
            # Fallback: return file count
            return self.files.filter(is_active=True).count()
        return total

    @property
    def utilization(self):
        """Calculate utilization percentage based on document capacity"""
        if not self.capacity:
            return 0
        document_count = self.get_document_count()
        return (document_count / self.capacity) * 100

    @property
    def is_full(self):
        """Check if kent is at capacity (based on documents)"""
        if self.capacity:
            return self.get_document_count() >= self.capacity
        return False


class FileType(models.Model):
    """
    FileType model - Defines different types of files that can be stored
    Examples: Business Files, Personal Files, Legal Files, Tax Files, etc.
    """
    name = models.CharField(
        max_length=100,
        unique=True,
        help_text="Name of the file type (e.g., Business File, Tax File)"
    )

    code = models.CharField(
        max_length=10,
        unique=True,
        validators=[RegexValidator(
            regex=r'^[A-Z0-9]{1,10}$',
            message="File type code must contain only uppercase letters and numbers"
        )],
        help_text="Short code for the file type (e.g., BUS, TAX, LEG)"
    )

    description = models.TextField(
        null=True,
        blank=True,
        help_text="Description of what this file type contains"
    )

    color = models.CharField(
        max_length=7,
        default='#2196F3',
        help_text="Color code for visual identification (hex format)"
    )

    icon = models.CharField(
        max_length=50,
        default='folder',
        help_text="Icon name for visual representation"
    )

    # Required fields for this file type
    requires_business_name = models.BooleanField(
        default=False,
        help_text="Whether this file type requires a business name"
    )

    requires_tin_number = models.BooleanField(
        default=False,
        help_text="Whether this file type requires a TIN number"
    )

    requires_license_number = models.BooleanField(
        default=False,
        help_text="Whether this file type requires a license number"
    )

    requires_owner_name = models.BooleanField(
        default=False,
        help_text="Whether this file type requires an owner name"
    )

    # Default document types for this file type
    default_document_types = models.TextField(
        null=True,
        blank=True,
        help_text="Comma-separated list of default document types for this file type"
    )

    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'locations_filetype'
        verbose_name = 'File Type'
        verbose_name_plural = 'File Types'
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"

    def save(self, *args, **kwargs):
        # Convert code to uppercase for case-insensitive storage
        if self.code:
            self.code = self.code.upper()
        super().save(*args, **kwargs)

    def get_default_document_types_list(self):
        """Return list of default document types"""
        if self.default_document_types:
            return [dt.strip() for dt in self.default_document_types.split(',')]
        return []

    def get_file_count(self):
        """Get number of files using this file type"""
        return self.files.filter(is_active=True).count()


class File(models.Model):
    """
    File model - Fourth level of location hierarchy
    Groups related documents together (e.g., "Abebe's Hotel", "Kebede's Supermarket")
    """

    # Remove old FileType TextChoices - now using FileType model

    class Status(models.TextChoices):
        ACTIVE = 'active', 'Active'
        ARCHIVED = 'archived', 'Archived'
        CLOSED = 'closed', 'Closed'
        TRANSFERRED = 'transferred', 'Transferred'

    # Basic Information
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    kent = models.ForeignKey(
        Kent,
        on_delete=models.CASCADE,
        related_name='files',
        help_text="Kent (box) where this file is stored"
    )

    name = models.CharField(
        max_length=255,
        help_text="File name (e.g., 'Abebe's Hotel', 'Kebede's Supermarket')"
    )

    file_number = models.CharField(
        max_length=50,
        help_text="Unique file number or identifier"
    )

    file_type = models.ForeignKey(
        FileType,
        on_delete=models.PROTECT,
        related_name='files',
        help_text="Type of file (Business, Tax, Legal, etc.)"
    )

    description = models.TextField(
        null=True,
        blank=True,
        help_text="Description of the file contents"
    )

    # Business/Entity Information
    business_name = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        help_text="Official business name (if business file)"
    )

    tin_number = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        help_text="Tax Identification Number"
    )

    vat_number = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        help_text="VAT Registration Number"
    )

    owner_name = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        help_text="Owner or contact person name"
    )

    contact_phone = models.CharField(
        max_length=20,
        null=True,
        blank=True,
        help_text="Contact phone number"
    )

    contact_email = models.EmailField(
        null=True,
        blank=True,
        help_text="Contact email address"
    )

    address = models.TextField(
        null=True,
        blank=True,
        help_text="Business or property address"
    )

    # File Management
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.ACTIVE
    )

    tags = models.JSONField(
        default=list,
        blank=True,
        help_text="Tags for categorization and search"
    )

    # Dates
    created_date = models.DateField(
        null=True,
        blank=True,
        help_text="Date when the file was created/opened"
    )

    last_activity_date = models.DateField(
        null=True,
        blank=True,
        help_text="Date of last document activity in this file"
    )

    # Barcode and QR code fields
    barcode_image = models.ImageField(
        upload_to='files/barcodes/',
        null=True,
        blank=True
    )
    qr_code_image = models.ImageField(
        upload_to='files/qrcodes/',
        null=True,
        blank=True
    )

    # Taxpayer Linking (Reverse relationship)
    linked_individual_taxpayer = models.ForeignKey(
        'taxpayers.IndividualTaxPayer',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='linked_files',
        help_text="Individual taxpayer linked to this file"
    )

    linked_organization_taxpayer = models.ForeignKey(
        'taxpayers.OrganizationTaxPayer',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='linked_files',
        help_text="Organization taxpayer linked to this file"
    )

    # Metadata
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_files'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'locations_file'
        verbose_name = 'File'
        verbose_name_plural = 'Files'
        unique_together = ['kent', 'file_number']
        ordering = ['kent', 'file_number']

    def __str__(self):
        return f"{self.name} ({self.file_number})"

    @property
    def full_code(self):
        """Return full hierarchical code (Building-Shelf-Position-Kent-File)"""
        return f"{self.kent.full_code}-{self.file_number}"

    @property
    def location_path(self):
        """Return human-readable location path"""
        return f"{self.kent.location_path} → {self.name}"

    @property
    def linked_taxpayer(self):
        """Return the linked taxpayer (individual or organization)"""
        if self.linked_individual_taxpayer:
            return {
                'id': self.linked_individual_taxpayer.id,
                'type': 'individual',
                'name': self.linked_individual_taxpayer.full_name,
                'tin': self.linked_individual_taxpayer.tin,
                'phone': self.linked_individual_taxpayer.phone,
                'email': self.linked_individual_taxpayer.email,
            }
        elif self.linked_organization_taxpayer:
            return {
                'id': self.linked_organization_taxpayer.id,
                'type': 'organization',
                'name': self.linked_organization_taxpayer.business_name,
                'tin': self.linked_organization_taxpayer.tin,
                'phone': self.linked_organization_taxpayer.contact_phone,
                'email': self.linked_organization_taxpayer.contact_email,
            }
        return None

    def save(self, *args, **kwargs):
        # Update last activity date
        if not self.last_activity_date:
            self.last_activity_date = timezone.now().date()
        super().save(*args, **kwargs)
        self.generate_codes()

    def generate_codes(self):
        """Generate barcode and QR code for the file"""
        if not BARCODE_AVAILABLE:
            return

        code_text = f"{self.kent.shelf.building.organization.short_name}-{self.full_code}"

        # Generate barcode
        if not self.barcode_image:
            try:
                code128 = barcode.get_barcode_class('code128')
                barcode_instance = code128(code_text, writer=ImageWriter())
                buffer = BytesIO()
                barcode_instance.write(buffer)
                self.barcode_image.save(
                    f'file_{self.file_number}_barcode.png',
                    File(buffer),
                    save=False
                )
            except Exception as e:
                print(f"Error generating barcode: {e}")

        # Generate QR code
        if not self.qr_code_image:
            try:
                qr = qrcode.QRCode(version=1, box_size=10, border=5)
                qr.add_data(code_text)
                qr.make(fit=True)
                qr_img = qr.make_image(fill_color="black", back_color="white")
                buffer = BytesIO()
                qr_img.save(buffer, format='PNG')
                self.qr_code_image.save(
                    f'file_{self.file_number}_qr.png',
                    File(buffer),
                    save=False
                )
            except Exception as e:
                print(f"Error generating QR code: {e}")

        if self.barcode_image or self.qr_code_image:
            File.objects.filter(pk=self.pk).update(
                barcode_image=self.barcode_image,
                qr_code_image=self.qr_code_image
            )

    def get_document_count(self):
        """Return number of documents in this file"""
        try:
            # Check if documents app is available
            from django.apps import apps
            if not apps.is_installed('documents'):
                return 1  # Assume 1 document per file if documents app not available

            if hasattr(self, 'documents'):
                return self.documents.filter(is_active=True).count()
            return 0
        except Exception as e:
            print(f"Error in get_document_count for file {self.id}: {e}")
            return 1  # Fallback: assume 1 document per file

    def get_document_types(self):
        """Return list of document types in this file"""
        if hasattr(self, 'documents'):
            return list(self.documents.filter(is_active=True).values_list('document_type__name', flat=True).distinct())
        return []

    def update_last_activity(self):
        """Update last activity date to today"""
        self.last_activity_date = timezone.now().date()
        self.save(update_fields=['last_activity_date'])
