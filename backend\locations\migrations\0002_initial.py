# Generated by Django 5.2.3 on 2025-07-14 20:09

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("locations", "0001_initial"),
        ("organizations", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="building",
            name="organization",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="buildings",
                to="organizations.organization",
            ),
        ),
        migrations.AddField(
            model_name="file",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="created_files",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="file",
            name="file_type",
            field=models.ForeignKey(
                help_text="Type of file (Business, Tax, Legal, etc.)",
                on_delete=django.db.models.deletion.PROTECT,
                related_name="files",
                to="locations.filetype",
            ),
        ),
        migrations.AddField(
            model_name="kent",
            name="box",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="kents",
                to="locations.box",
            ),
        ),
        migrations.AddField(
            model_name="file",
            name="kent",
            field=models.ForeignKey(
                help_text="Kent (box) where this file is stored",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="files",
                to="locations.kent",
            ),
        ),
        migrations.AddField(
            model_name="region",
            name="country",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="regions",
                to="locations.country",
            ),
        ),
        migrations.AddField(
            model_name="shelf",
            name="building",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="shelves",
                to="locations.building",
            ),
        ),
        migrations.AddField(
            model_name="box",
            name="shelf",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="boxes",
                to="locations.shelf",
            ),
        ),
        migrations.AddField(
            model_name="speciallocation",
            name="kebele",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="special_locations",
                to="locations.kebele",
            ),
        ),
        migrations.AddField(
            model_name="subcity",
            name="city",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="subcities",
                to="locations.city",
            ),
        ),
        migrations.AddField(
            model_name="kebele",
            name="subcity",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="kebeles",
                to="locations.subcity",
            ),
        ),
        migrations.AddField(
            model_name="zone",
            name="region",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="zones",
                to="locations.region",
            ),
        ),
        migrations.AddField(
            model_name="city",
            name="zone",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="cities",
                to="locations.zone",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="building",
            unique_together={("organization", "code")},
        ),
        migrations.AlterUniqueTogether(
            name="kent",
            unique_together={("box", "code")},
        ),
        migrations.AlterUniqueTogether(
            name="file",
            unique_together={("kent", "file_number")},
        ),
        migrations.AlterUniqueTogether(
            name="region",
            unique_together={("country", "code")},
        ),
        migrations.AlterUniqueTogether(
            name="shelf",
            unique_together={("building", "code")},
        ),
        migrations.AlterUniqueTogether(
            name="box",
            unique_together={("shelf", "row", "column")},
        ),
        migrations.AlterUniqueTogether(
            name="speciallocation",
            unique_together={("kebele", "name")},
        ),
        migrations.AlterUniqueTogether(
            name="subcity",
            unique_together={("city", "code")},
        ),
        migrations.AlterUniqueTogether(
            name="kebele",
            unique_together={("subcity", "code")},
        ),
        migrations.AlterUniqueTogether(
            name="zone",
            unique_together={("region", "code")},
        ),
        migrations.AlterUniqueTogether(
            name="city",
            unique_together={("zone", "code")},
        ),
    ]
