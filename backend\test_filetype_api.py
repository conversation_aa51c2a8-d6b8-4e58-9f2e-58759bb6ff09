#!/usr/bin/env python
"""
Test script to verify FileType API endpoints
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'arada_dms.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
import json

User = get_user_model()

def test_filetype_api():
    print("Testing FileType API endpoints")
    print("=" * 60)
    
    # Create a test client
    client = Client()
    
    # Create a test user
    user, created = User.objects.get_or_create(
        username='testuser',
        defaults={
            'email': '<EMAIL>',
            'is_staff': True,
            'is_superuser': True
        }
    )
    
    # Login the user
    client.force_login(user)
    
    # Test GET /api/locations/file-types/
    print("1. Testing GET /api/locations/file-types/")
    response = client.get('/api/locations/file-types/')
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        file_types = data.get('results', data)
        print(f"   Found {len(file_types)} file types")
        
        for ft in file_types[:3]:  # Show first 3
            print(f"     {ft['code']}: {ft['name']} ({ft['file_count']} files)")
    else:
        print(f"   Error: {response.content}")
    
    print()
    
    # Test POST /api/locations/file-types/ (Create new file type)
    print("2. Testing POST /api/locations/file-types/ (Create)")
    new_file_type = {
        'name': 'Test File Type',
        'code': 'TEST',
        'description': 'Test file type for API testing',
        'color': '#FF5722',
        'icon': 'folder',
        'requires_business_name': True,
        'requires_tin_number': False,
        'default_document_types': 'Test Document, Sample Certificate'
    }
    
    response = client.post(
        '/api/locations/file-types/',
        data=json.dumps(new_file_type),
        content_type='application/json'
    )
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 201:
        created_ft = response.json()
        print(f"   Created: {created_ft['name']} ({created_ft['code']})")
        test_ft_id = created_ft['id']
        
        # Test GET specific file type
        print()
        print(f"3. Testing GET /api/locations/file-types/{test_ft_id}/")
        response = client.get(f'/api/locations/file-types/{test_ft_id}/')
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            ft_data = response.json()
            print(f"   Retrieved: {ft_data['name']}")
            print(f"   Default document types: {ft_data['default_document_types_list']}")
        
        # Test PUT (Update)
        print()
        print(f"4. Testing PATCH /api/locations/file-types/{test_ft_id}/ (Update)")
        update_data = {
            'description': 'Updated test file type description',
            'color': '#9C27B0'
        }
        
        response = client.patch(
            f'/api/locations/file-types/{test_ft_id}/',
            data=json.dumps(update_data),
            content_type='application/json'
        )
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            updated_ft = response.json()
            print(f"   Updated description: {updated_ft['description']}")
            print(f"   Updated color: {updated_ft['color']}")
        
        # Test DELETE
        print()
        print(f"5. Testing DELETE /api/locations/file-types/{test_ft_id}/")
        response = client.delete(f'/api/locations/file-types/{test_ft_id}/')
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 204:
            print("   ✅ File type deleted successfully")
        
    else:
        print(f"   Error creating file type: {response.content}")
    
    print()
    
    # Test file type requirements
    print("6. Testing file type requirements")
    response = client.get('/api/locations/file-types/')
    if response.status_code == 200:
        data = response.json()
        file_types = data.get('results', data)
        
        for ft in file_types:
            requirements = []
            if ft['requires_business_name']:
                requirements.append('Business Name')
            if ft['requires_tin_number']:
                requirements.append('TIN Number')
            if ft['requires_license_number']:
                requirements.append('License Number')
            if ft['requires_owner_name']:
                requirements.append('Owner Name')
            
            req_text = ', '.join(requirements) if requirements else 'None'
            print(f"   {ft['code']}: {req_text}")
    
    print()
    print("✅ FileType API testing completed!")

if __name__ == '__main__':
    test_filetype_api()
