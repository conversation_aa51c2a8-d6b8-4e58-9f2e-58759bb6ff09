import apiClient from './api';

export interface DocumentType {
  id: number;
  organization: number;
  organization_name: string;
  name: string;
  code: string;
  description?: string;
  retention_days?: number;
  retention_days_display: number;
  confidentiality_level: 'public' | 'internal' | 'confidential' | 'secret';
  requires_expiry_date: boolean;
  requires_approval: boolean;
  allowed_file_extensions: string[];
  max_file_size_mb?: number;
  is_active: boolean;
  document_count: number;
  created_at: string;
  updated_at: string;
}

export interface DocumentTypeCreate {
  organization: number;
  name: string;
  code: string;
  description?: string;
  retention_days?: number;
  confidentiality_level: 'public' | 'internal' | 'confidential' | 'secret';
  requires_expiry_date: boolean;
  requires_approval: boolean;
  allowed_file_extensions: string[];
  max_file_size_mb?: number;
}

export interface DocumentTypeUpdate extends Partial<DocumentTypeCreate> {
  is_active?: boolean;
}

export interface DocumentTypeListResponse {
  count: number;
  next?: string;
  previous?: string;
  results: DocumentType[];
}

class DocumentTypeService {
  private baseUrl = '/documents/types';

  async getDocumentTypes(params?: {
    page?: number;
    page_size?: number;
    search?: string;
    organization?: number;
    is_active?: boolean;
    confidentiality_level?: string;
  }): Promise<DocumentTypeListResponse> {
    const response = await apiClient.get(this.baseUrl, { params });
    return response.data;
  }

  async getDocumentType(id: number): Promise<DocumentType> {
    const response = await apiClient.get(`${this.baseUrl}/${id}/`);
    return response.data;
  }

  async createDocumentType(data: DocumentTypeCreate): Promise<DocumentType> {
    const response = await apiClient.post(this.baseUrl + '/', data);
    return response.data;
  }

  async updateDocumentType(id: number, data: DocumentTypeUpdate): Promise<DocumentType> {
    const response = await apiClient.patch(`${this.baseUrl}/${id}/`, data);
    return response.data;
  }

  async deleteDocumentType(id: number): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/${id}/`);
  }

  async toggleDocumentTypeStatus(id: number): Promise<DocumentType> {
    const documentType = await this.getDocumentType(id);
    return this.updateDocumentType(id, { is_active: !documentType.is_active });
  }

  // Utility methods
  getConfidentialityLevelOptions() {
    return [
      { value: 'public', label: 'Public', color: 'success' },
      { value: 'internal', label: 'Internal', color: 'info' },
      { value: 'confidential', label: 'Confidential', color: 'warning' },
      { value: 'secret', label: 'Secret', color: 'error' },
    ];
  }

  getDefaultFileExtensions() {
    return ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'jpeg', 'png'];
  }

  formatFileExtensions(extensions: string[]): string {
    return extensions.map(ext => `.${ext.toLowerCase()}`).join(', ');
  }

  validateFileExtensions(extensions: string[]): boolean {
    const validExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff'];
    return extensions.every(ext => validExtensions.includes(ext.toLowerCase()));
  }

  formatFileSize(sizeInMB?: number): string {
    if (!sizeInMB) return 'No limit';
    if (sizeInMB < 1) return `${sizeInMB * 1024} KB`;
    if (sizeInMB >= 1024) return `${(sizeInMB / 1024).toFixed(1)} GB`;
    return `${sizeInMB} MB`;
  }

  getRetentionPeriodDisplay(days?: number): string {
    if (!days) return 'Organization default';
    if (days < 30) return `${days} days`;
    if (days < 365) return `${Math.round(days / 30)} months`;
    return `${Math.round(days / 365)} years`;
  }
}

export default new DocumentTypeService();
