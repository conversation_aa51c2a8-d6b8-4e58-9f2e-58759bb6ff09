import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  IconButton,
  Button,
  Skeleton,
  Alert,
  Breadcrumbs,
  Link,
  Fade,
  Zoom,
  Tooltip,
} from '@mui/material';
import {
  Public,
  LocationOn,
  Business,
  LocationCity,
  Home,
  Map,
  NavigateNext,
  Refresh,
  Add,
  KeyboardArrowRight,
  Place,
} from '@mui/icons-material';
import geographicalLocationService from '../../services/geographicalLocationService';
import type { Country, Region, City, SubCity, Kebele } from '../../services/geographicalLocationService';

interface LocationHierarchyProps {
  onLocationSelect?: (location: {
    country?: Country;
    region?: Region;
    city?: City;
    subcity?: SubCity;
    kebele?: Kebele;
  }) => void;
  selectedLocation?: {
    countryId?: number;
    regionId?: number;
    cityId?: number;
    subcityId?: number;
    kebeleId?: number;
  };
  showBreadcrumbs?: boolean;
  allowMultipleSelection?: boolean;
  compact?: boolean;
}

const ModernLocationHierarchy: React.FC<LocationHierarchyProps> = ({
  onLocationSelect,
  selectedLocation,
  showBreadcrumbs = true,
  allowMultipleSelection = false,
  compact = false,
}) => {
  // State management
  const [countries, setCountries] = useState<Country[]>([]);
  const [regions, setRegions] = useState<Region[]>([]);
  const [cities, setCities] = useState<City[]>([]);
  const [subcities, setSubcities] = useState<SubCity[]>([]);
  const [kebeles, setKebeles] = useState<Kebele[]>([]);

  const [selectedCountry, setSelectedCountry] = useState<Country | null>(null);
  const [selectedRegion, setSelectedRegion] = useState<Region | null>(null);
  const [selectedCity, setSelectedCity] = useState<City | null>(null);
  const [selectedSubcity, setSelectedSubcity] = useState<SubCity | null>(null);
  const [selectedKebele, setSelectedKebele] = useState<Kebele | null>(null);

  const [loading, setLoading] = useState({
    countries: false,
    regions: false,
    cities: false,
    subcities: false,
    kebeles: false,
  });

  const [error, setError] = useState<string | null>(null);

  // Load data functions
  const loadCountries = async () => {
    setLoading(prev => ({ ...prev, countries: true }));
    try {
      const data = await geographicalLocationService.getCountries();
      setCountries(data);
      setError(null);
    } catch (error) {
      console.error('Error loading countries:', error);
      setError('Failed to load countries');
    } finally {
      setLoading(prev => ({ ...prev, countries: false }));
    }
  };

  const loadRegions = async (countryId?: number) => {
    setLoading(prev => ({ ...prev, regions: true }));
    try {
      const data = await geographicalLocationService.getRegions(countryId);
      setRegions(data);
    } catch (error) {
      console.error('Error loading regions:', error);
    } finally {
      setLoading(prev => ({ ...prev, regions: false }));
    }
  };

  const loadCities = async (regionId?: number) => {
    setLoading(prev => ({ ...prev, cities: true }));
    try {
      const data = await geographicalLocationService.getCities(regionId);
      setCities(data);
    } catch (error) {
      console.error('Error loading cities:', error);
    } finally {
      setLoading(prev => ({ ...prev, cities: false }));
    }
  };

  const loadSubcities = async (cityId?: number) => {
    setLoading(prev => ({ ...prev, subcities: true }));
    try {
      const data = await geographicalLocationService.getSubCities(cityId);
      setSubcities(data);
    } catch (error) {
      console.error('Error loading subcities:', error);
    } finally {
      setLoading(prev => ({ ...prev, subcities: false }));
    }
  };

  const loadKebeles = async (subcityId?: number) => {
    setLoading(prev => ({ ...prev, kebeles: true }));
    try {
      const data = await geographicalLocationService.getKebeles(subcityId);
      setKebeles(data);
    } catch (error) {
      console.error('Error loading kebeles:', error);
    } finally {
      setLoading(prev => ({ ...prev, kebeles: false }));
    }
  };

  // Initial load
  useEffect(() => {
    loadCountries();
    loadRegions(); // Load all regions initially
    loadCities(); // Load all cities initially
  }, []);

  // Selection handlers
  const handleCountrySelect = (country: Country) => {
    setSelectedCountry(country);
    setSelectedRegion(null);
    setSelectedCity(null);
    setSelectedSubcity(null);
    setSelectedKebele(null);
    
    // Load regions for selected country
    loadRegions(country.id);
    
    // Clear dependent data
    setCities([]);
    setSubcities([]);
    setKebeles([]);

    onLocationSelect?.({
      country,
      region: undefined,
      city: undefined,
      subcity: undefined,
      kebele: undefined,
    });
  };

  const handleRegionSelect = (region: Region) => {
    setSelectedRegion(region);
    setSelectedCity(null);
    setSelectedSubcity(null);
    setSelectedKebele(null);
    
    // Load cities for selected region
    loadCities(region.id);
    
    // Clear dependent data
    setSubcities([]);
    setKebeles([]);

    onLocationSelect?.({
      country: selectedCountry,
      region,
      city: undefined,
      subcity: undefined,
      kebele: undefined,
    });
  };

  const handleCitySelect = (city: City) => {
    setSelectedCity(city);
    setSelectedSubcity(null);
    setSelectedKebele(null);
    
    // Load subcities for selected city
    loadSubcities(city.id);
    
    // Clear dependent data
    setKebeles([]);

    onLocationSelect?.({
      country: selectedCountry,
      region: selectedRegion,
      city,
      subcity: undefined,
      kebele: undefined,
    });
  };

  const handleSubcitySelect = (subcity: SubCity) => {
    setSelectedSubcity(subcity);
    setSelectedKebele(null);
    
    // Load kebeles for selected subcity
    loadKebeles(subcity.id);

    onLocationSelect?.({
      country: selectedCountry,
      region: selectedRegion,
      city: selectedCity,
      subcity,
      kebele: undefined,
    });
  };

  const handleKebeleSelect = (kebele: Kebele) => {
    setSelectedKebele(kebele);

    onLocationSelect?.({
      country: selectedCountry,
      region: selectedRegion,
      city: selectedCity,
      subcity: selectedSubcity,
      kebele,
    });
  };

  // Reset selection
  const handleReset = () => {
    setSelectedCountry(null);
    setSelectedRegion(null);
    setSelectedCity(null);
    setSelectedSubcity(null);
    setSelectedKebele(null);
    
    loadCountries();
    loadRegions();
    loadCities();
    setSubcities([]);
    setKebeles([]);

    onLocationSelect?.({});
  };

  // Breadcrumb component
  const LocationBreadcrumbs = () => {
    if (!showBreadcrumbs) return null;

    const breadcrumbItems = [
      selectedCountry && { label: selectedCountry.name, icon: <Public /> },
      selectedRegion && { label: selectedRegion.name, icon: <LocationOn /> },
      selectedCity && { label: selectedCity.name, icon: <LocationCity /> },
      selectedSubcity && { label: selectedSubcity.name, icon: <Business /> },
      selectedKebele && { label: selectedKebele.name, icon: <Home /> },
    ].filter(Boolean);

    if (breadcrumbItems.length === 0) return null;

    return (
      <Card sx={{ mb: 3, bgcolor: 'primary.50' }}>
        <CardContent sx={{ py: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
            <Map color="primary" />
            <Typography variant="subtitle2" color="primary">
              Selected Location
            </Typography>
            <Button
              size="small"
              onClick={handleReset}
              sx={{ ml: 'auto' }}
            >
              Reset
            </Button>
          </Box>
          <Breadcrumbs separator={<NavigateNext fontSize="small" />}>
            {breadcrumbItems.map((item, index) => (
              <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                {item.icon}
                <Typography variant="body2">{item.label}</Typography>
              </Box>
            ))}
          </Breadcrumbs>
        </CardContent>
      </Card>
    );
  };

  // Location card component
  const LocationCard = ({ 
    title, 
    items, 
    loading: isLoading, 
    onSelect, 
    selectedId, 
    icon, 
    color = 'primary',
    emptyMessage 
  }: {
    title: string;
    items: any[];
    loading: boolean;
    onSelect: (item: any) => void;
    selectedId?: number;
    icon: React.ReactNode;
    color?: 'primary' | 'secondary' | 'success' | 'warning' | 'info';
    emptyMessage: string;
  }) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
          {icon}
          <Typography variant="h6" component="h3">
            {title}
          </Typography>
        </Box>

        {isLoading ? (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            {[...Array(3)].map((_, index) => (
              <Skeleton key={index} variant="rectangular" height={48} />
            ))}
          </Box>
        ) : items.length === 0 ? (
          <Alert severity="info" sx={{ textAlign: 'center' }}>
            {emptyMessage}
          </Alert>
        ) : (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, maxHeight: 300, overflow: 'auto' }}>
            {items.map((item) => (
              <Zoom key={item.id} in timeout={300}>
                <Card
                  variant={selectedId === item.id ? 'elevation' : 'outlined'}
                  sx={{
                    cursor: 'pointer',
                    transition: 'all 0.2s',
                    bgcolor: selectedId === item.id ? `${color}.50` : 'background.paper',
                    borderColor: selectedId === item.id ? `${color}.main` : 'divider',
                    '&:hover': {
                      bgcolor: selectedId === item.id ? `${color}.100` : 'action.hover',
                      transform: 'translateY(-1px)',
                      boxShadow: 2,
                    },
                  }}
                  onClick={() => onSelect(item)}
                >
                  <CardContent sx={{ py: 1.5, '&:last-child': { pb: 1.5 } }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <Box>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                          {item.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {item.code}
                        </Typography>
                      </Box>
                      {selectedId === item.id && (
                        <Chip
                          label="Selected"
                          size="small"
                          color={color}
                          variant="filled"
                        />
                      )}
                    </Box>
                  </CardContent>
                </Card>
              </Zoom>
            ))}
          </Box>
        )}
      </CardContent>
    </Card>
  );

  if (error) {
    return (
      <Alert 
        severity="error" 
        action={
          <Button color="inherit" size="small" onClick={loadCountries}>
            <Refresh />
            Retry
          </Button>
        }
      >
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <LocationBreadcrumbs />
      
      <Grid container spacing={3}>
        {/* Countries */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <LocationCard
            title="Countries"
            items={countries}
            loading={loading.countries}
            onSelect={handleCountrySelect}
            selectedId={selectedCountry?.id}
            icon={<Public color="primary" />}
            color="primary"
            emptyMessage="No countries available"
          />
        </Grid>

        {/* Regions */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <LocationCard
            title="Regions"
            items={regions}
            loading={loading.regions}
            onSelect={handleRegionSelect}
            selectedId={selectedRegion?.id}
            icon={<LocationOn color="secondary" />}
            color="secondary"
            emptyMessage={selectedCountry ? "No regions in selected country" : "Select a country to view regions"}
          />
        </Grid>

        {/* Cities */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <LocationCard
            title="Cities"
            items={cities}
            loading={loading.cities}
            onSelect={handleCitySelect}
            selectedId={selectedCity?.id}
            icon={<LocationCity color="success" />}
            color="success"
            emptyMessage={selectedRegion ? "No cities in selected region" : "Select a region to view cities"}
          />
        </Grid>

        {/* SubCities */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <LocationCard
            title="SubCities / Woredas"
            items={subcities}
            loading={loading.subcities}
            onSelect={handleSubcitySelect}
            selectedId={selectedSubcity?.id}
            icon={<Business color="warning" />}
            color="warning"
            emptyMessage={selectedCity ? "No subcities in selected city" : "Select a city to view subcities"}
          />
        </Grid>

        {/* Kebeles */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <LocationCard
            title="Kebeles"
            items={kebeles}
            loading={loading.kebeles}
            onSelect={handleKebeleSelect}
            selectedId={selectedKebele?.id}
            icon={<Home color="info" />}
            color="info"
            emptyMessage={selectedSubcity ? "No kebeles in selected subcity" : "Select a subcity to view kebeles"}
          />
        </Grid>
      </Grid>
    </Box>
  );
};

export default ModernLocationHierarchy;
