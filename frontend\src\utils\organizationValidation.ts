import type { OrganizationCreate } from '../services/organizationService';

export interface ValidationError {
  field: string;
  message: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
  fieldErrors: ValidationError[];
}

export class OrganizationValidator {
  // Validate individual fields
  static validateField(field: string, value: any, allData?: Partial<OrganizationCreate>): string | null {
    switch (field) {
      case 'name':
        if (!value?.trim()) return 'Organization name is required';
        if (value.length < 3) return 'Organization name must be at least 3 characters';
        if (value.length > 100) return 'Organization name must be less than 100 characters';
        return null;

      case 'short_name':
        if (!value?.trim()) return 'Short name is required';
        if (value.length < 2) return 'Short name must be at least 2 characters';
        if (value.length > 20) return 'Short name must be less than 20 characters';
        if (!/^[A-Z0-9_-]+$/.test(value)) return 'Short name can only contain uppercase letters, numbers, hyphens, and underscores';
        return null;

      case 'email':
        if (!value?.trim()) return 'Email is required';
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) return 'Please enter a valid email address';
        return null;

      case 'phone':
        if (!value?.trim()) return 'Phone number is required';
        const phoneRegex = /^\+?[\d\s\-\(\)]{10,15}$/;
        if (!phoneRegex.test(value)) return 'Please enter a valid phone number (10-15 digits)';
        return null;

      case 'website':
        if (value && value.trim()) {
          const urlRegex = /^https?:\/\/.+\..+/;
          if (!urlRegex.test(value)) return 'Website must be a valid URL starting with http:// or https://';
        }
        return null;

      case 'document_retention_days':
        if (value !== undefined && value !== null) {
          const num = Number(value);
          if (isNaN(num)) return 'Document retention must be a number';
          if (num < 30) return 'Document retention must be at least 30 days';
          if (num > 10000) return 'Document retention cannot exceed 10000 days';
        }
        return null;

      case 'max_file_size_mb':
        if (value !== undefined && value !== null) {
          const num = Number(value);
          if (isNaN(num)) return 'Max file size must be a number';
          if (num < 1) return 'Max file size must be at least 1 MB';
          if (num > 1000) return 'Max file size cannot exceed 1000 MB';
        }
        return null;

      case 'office_hours_start':
      case 'office_hours_end':
        if (value && value.trim()) {
          const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
          if (!timeRegex.test(value)) return 'Please enter a valid time in HH:MM format';
        }
        return null;

      case 'postal_code':
        if (value && value.trim()) {
          if (value.length > 20) return 'Postal code must be less than 20 characters';
        }
        return null;

      case 'primary_color':
      case 'secondary_color':
        if (value && value.trim()) {
          const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
          if (!colorRegex.test(value)) return 'Please enter a valid hex color (e.g., #FF0000)';
        }
        return null;

      default:
        return null;
    }
  }

  // Validate cross-field dependencies
  static validateCrossFields(data: Partial<OrganizationCreate>): Record<string, string> {
    const errors: Record<string, string> = {};

    // Office hours validation
    if (data.office_hours_start && data.office_hours_end) {
      if (data.office_hours_start >= data.office_hours_end) {
        errors.office_hours_end = 'End time must be after start time';
      }
    }

    // Location hierarchy validation
    if (data.state_province && !data.country) {
      errors.state_province = 'Country must be selected when region is specified';
    }

    if (data.city && !data.state_province) {
      errors.city = 'Region must be selected when city is specified';
    }

    if (data.subcity && !data.city) {
      errors.subcity = 'City must be selected when subcity is specified';
    }

    if (data.kebele && !data.subcity) {
      errors.kebele = 'Subcity must be selected when kebele is specified';
    }

    return errors;
  }

  // Validate entire organization object
  static validateOrganization(data: Partial<OrganizationCreate>): ValidationResult {
    const errors: Record<string, string> = {};
    const fieldErrors: ValidationError[] = [];

    // Validate individual fields
    const fieldsToValidate = [
      'name', 'short_name', 'email', 'phone', 'website',
      'document_retention_days', 'max_file_size_mb',
      'office_hours_start', 'office_hours_end',
      'postal_code', 'primary_color', 'secondary_color'
    ];

    fieldsToValidate.forEach(field => {
      const error = this.validateField(field, (data as any)[field], data);
      if (error) {
        errors[field] = error;
        fieldErrors.push({ field, message: error });
      }
    });

    // Validate cross-field dependencies
    const crossFieldErrors = this.validateCrossFields(data);
    Object.assign(errors, crossFieldErrors);

    Object.entries(crossFieldErrors).forEach(([field, message]) => {
      fieldErrors.push({ field, message });
    });

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
      fieldErrors
    };
  }

  // Validate specific sections
  static validateBasicInfo(data: Partial<OrganizationCreate>): ValidationResult {
    const basicFields = ['name', 'short_name', 'description', 'email', 'phone', 'website'];
    const errors: Record<string, string> = {};
    const fieldErrors: ValidationError[] = [];

    basicFields.forEach(field => {
      const error = this.validateField(field, (data as any)[field], data);
      if (error) {
        errors[field] = error;
        fieldErrors.push({ field, message: error });
      }
    });

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
      fieldErrors
    };
  }

  static validateLocationInfo(data: Partial<OrganizationCreate>): ValidationResult {
    const locationFields = ['country', 'state_province', 'city', 'subcity', 'kebele', 'address_line1', 'address_line2', 'postal_code'];
    const errors: Record<string, string> = {};
    const fieldErrors: ValidationError[] = [];

    // Validate location hierarchy
    const crossFieldErrors = this.validateCrossFields(data);
    Object.assign(errors, crossFieldErrors);

    Object.entries(crossFieldErrors).forEach(([field, message]) => {
      fieldErrors.push({ field, message });
    });

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
      fieldErrors
    };
  }

  static validateOperationalSettings(data: Partial<OrganizationCreate>): ValidationResult {
    const operationalFields = ['document_retention_days', 'max_file_size_mb', 'office_hours_start', 'office_hours_end'];
    const errors: Record<string, string> = {};
    const fieldErrors: ValidationError[] = [];

    operationalFields.forEach(field => {
      const error = this.validateField(field, (data as any)[field], data);
      if (error) {
        errors[field] = error;
        fieldErrors.push({ field, message: error });
      }
    });

    // Office hours cross-validation
    if (data.office_hours_start && data.office_hours_end) {
      if (data.office_hours_start >= data.office_hours_end) {
        errors.office_hours_end = 'End time must be after start time';
        fieldErrors.push({ field: 'office_hours_end', message: 'End time must be after start time' });
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
      fieldErrors
    };
  }

  static validateBrandingSettings(data: Partial<OrganizationCreate>): ValidationResult {
    const brandingFields = ['motto', 'primary_color', 'secondary_color'];
    const errors: Record<string, string> = {};
    const fieldErrors: ValidationError[] = [];

    brandingFields.forEach(field => {
      const error = this.validateField(field, (data as any)[field], data);
      if (error) {
        errors[field] = error;
        fieldErrors.push({ field, message: error });
      }
    });

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
      fieldErrors
    };
  }

  // Get validation summary
  static getValidationSummary(data: Partial<OrganizationCreate>): {
    basicInfo: boolean;
    locationInfo: boolean;
    operationalSettings: boolean;
    brandingSettings: boolean;
    overall: boolean;
  } {
    return {
      basicInfo: this.validateBasicInfo(data).isValid,
      locationInfo: this.validateLocationInfo(data).isValid,
      operationalSettings: this.validateOperationalSettings(data).isValid,
      brandingSettings: this.validateBrandingSettings(data).isValid,
      overall: this.validateOrganization(data).isValid
    };
  }
}
