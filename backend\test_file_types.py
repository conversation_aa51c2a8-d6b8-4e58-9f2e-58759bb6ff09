#!/usr/bin/env python
"""
Test script to verify FileType functionality
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'arada_dms.settings')
django.setup()

from locations.models import FileType, File
from locations.serializers import FileTypeSerializer, FileSerializer

def test_file_types():
    print("Testing FileType functionality")
    print("=" * 60)
    
    # Test FileType model
    print("1. Testing FileType model")
    file_types = FileType.objects.all()
    print(f"   Found {file_types.count()} file types:")
    for ft in file_types:
        print(f"     {ft.code}: {ft.name} (files: {ft.get_file_count()})")
    
    print()
    
    # Test FileType serializer
    print("2. Testing FileType serializer")
    business_type = FileType.objects.get(code='BUS')
    serializer = FileTypeSerializer(business_type)
    data = serializer.data
    print(f"   Business FileType serialized:")
    print(f"     Name: {data['name']}")
    print(f"     Code: {data['code']}")
    print(f"     Color: {data['color']}")
    print(f"     File count: {data['file_count']}")
    print(f"     Requires business name: {data['requires_business_name']}")
    print(f"     Default document types: {data['default_document_types_list']}")
    
    print()
    
    # Test File with FileType
    print("3. Testing File with FileType")
    files = File.objects.all()[:3]
    for file_obj in files:
        print(f"   File: {file_obj.name}")
        print(f"     Type: {file_obj.file_type.name} ({file_obj.file_type.code})")
        print(f"     Color: {file_obj.file_type.color}")
        
        # Test File serializer
        file_serializer = FileSerializer(file_obj)
        file_data = file_serializer.data
        print(f"     Serialized type info:")
        print(f"       file_type_name: {file_data['file_type_name']}")
        print(f"       file_type_code: {file_data['file_type_code']}")
        print(f"       file_type_color: {file_data['file_type_color']}")
        print()
    
    # Test creating a new FileType
    print("4. Testing FileType creation")
    try:
        new_file_type = FileType.objects.create(
            name='Test File Type',
            code='TEST',
            description='Test file type for testing',
            color='#FF5722',
            icon='test',
            requires_business_name=True,
            default_document_types='Test Document, Sample Certificate'
        )
        print(f"   ✅ Created new FileType: {new_file_type}")
        print(f"   Default document types: {new_file_type.get_default_document_types_list()}")
        
        # Clean up
        new_file_type.delete()
        print(f"   ✅ Cleaned up test FileType")
        
    except Exception as e:
        print(f"   ❌ Error creating FileType: {e}")
    
    print()
    
    # Test FileType requirements
    print("5. Testing FileType requirements")
    for ft in FileType.objects.all():
        requirements = []
        if ft.requires_business_name:
            requirements.append("Business Name")
        if ft.requires_tin_number:
            requirements.append("TIN Number")
        if ft.requires_license_number:
            requirements.append("License Number")
        if ft.requires_owner_name:
            requirements.append("Owner Name")
        
        req_text = ", ".join(requirements) if requirements else "None"
        print(f"   {ft.code} ({ft.name}): {req_text}")
    
    print()
    print("✅ FileType testing completed!")

if __name__ == '__main__':
    test_file_types()
