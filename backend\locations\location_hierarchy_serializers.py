"""
Serializers for Location Hierarchy Models
"""

from rest_framework import serializers
from .location_hierarchy_models import Country, Region, Zone, City, SubCity, Kebele, SpecialLocation


class CountrySerializer(serializers.ModelSerializer):
    """Country serializer"""
    region_count = serializers.SerializerMethodField()

    class Meta:
        model = Country
        fields = [
            'id', 'name', 'code', 'phone_code', 'currency', 'is_active',
            'region_count', 'created_at', 'updated_at'
        ]

    def get_region_count(self, obj):
        return obj.regions.filter(is_active=True).count()


class CountryCreateSerializer(serializers.ModelSerializer):
    """Country create serializer"""
    class Meta:
        model = Country
        fields = ['name', 'code', 'phone_code', 'currency', 'is_active']


class RegionSerializer(serializers.ModelSerializer):
    """Region serializer"""
    country_name = serializers.CharField(source='country.name', read_only=True)
    country_code = serializers.CharField(source='country.code', read_only=True)
    full_name = serializers.CharField(read_only=True)
    zone_count = serializers.SerializerMethodField()

    class Meta:
        model = Region
        fields = [
            'id', 'country', 'country_name', 'country_code', 'name', 'code',
            'population', 'area_km2', 'capital_city', 'is_active', 'full_name',
            'zone_count', 'created_at', 'updated_at'
        ]

    def get_zone_count(self, obj):
        return obj.zones.filter(is_active=True).count()


class RegionCreateSerializer(serializers.ModelSerializer):
    """Region create serializer"""
    class Meta:
        model = Region
        fields = ['country', 'name', 'code', 'population', 'area_km2', 'capital_city', 'is_active']


class ZoneSerializer(serializers.ModelSerializer):
    """Zone serializer"""
    region_name = serializers.CharField(source='region.name', read_only=True)
    country_name = serializers.CharField(source='region.country.name', read_only=True)
    full_name = serializers.CharField(read_only=True)
    city_count = serializers.SerializerMethodField()

    class Meta:
        model = Zone
        fields = [
            'id', 'region', 'region_name', 'country_name', 'name', 'code',
            'population', 'area_km2', 'is_active', 'full_name',
            'city_count', 'created_at', 'updated_at'
        ]

    def get_city_count(self, obj):
        return obj.cities.filter(is_active=True).count()


class ZoneCreateSerializer(serializers.ModelSerializer):
    """Zone create serializer"""
    class Meta:
        model = Zone
        fields = ['region', 'name', 'code', 'population', 'area_km2', 'is_active']


class CitySerializer(serializers.ModelSerializer):
    """City serializer"""
    zone_name = serializers.CharField(source='zone.name', read_only=True)
    region_name = serializers.CharField(source='zone.region.name', read_only=True)
    country_name = serializers.CharField(source='zone.region.country.name', read_only=True)
    full_name = serializers.CharField(read_only=True)
    subcity_count = serializers.SerializerMethodField()

    class Meta:
        model = City
        fields = [
            'id', 'zone', 'zone_name', 'region_name', 'country_name', 'name', 'code',
            'population', 'area_km2', 'is_capital', 'is_active', 'full_name',
            'subcity_count', 'created_at', 'updated_at'
        ]

    def get_subcity_count(self, obj):
        return obj.subcities.filter(is_active=True).count()


class CityCreateSerializer(serializers.ModelSerializer):
    """City create serializer"""
    class Meta:
        model = City
        fields = ['zone', 'name', 'code', 'population', 'area_km2', 'is_capital', 'is_active']


class SubCitySerializer(serializers.ModelSerializer):
    """SubCity serializer"""
    city_name = serializers.CharField(source='city.name', read_only=True)
    zone_name = serializers.CharField(source='city.zone.name', read_only=True)
    region_name = serializers.CharField(source='city.zone.region.name', read_only=True)
    country_name = serializers.CharField(source='city.zone.region.country.name', read_only=True)
    full_name = serializers.CharField(read_only=True)
    type_display = serializers.CharField(source='get_type_display', read_only=True)
    kebele_count = serializers.SerializerMethodField()

    class Meta:
        model = SubCity
        fields = [
            'id', 'city', 'city_name', 'zone_name', 'region_name', 'country_name',
            'name', 'code', 'type', 'type_display', 'population', 'area_km2',
            'is_active', 'full_name', 'kebele_count', 'created_at', 'updated_at'
        ]

    def get_kebele_count(self, obj):
        return obj.kebeles.filter(is_active=True).count()


class SubCityCreateSerializer(serializers.ModelSerializer):
    """SubCity create serializer"""
    class Meta:
        model = SubCity
        fields = ['city', 'name', 'code', 'type', 'population', 'area_km2', 'is_active']


class KebeleSerializer(serializers.ModelSerializer):
    """Kebele serializer"""
    subcity_name = serializers.CharField(source='subcity.name', read_only=True)
    city_name = serializers.CharField(source='subcity.city.name', read_only=True)
    zone_name = serializers.CharField(source='subcity.city.zone.name', read_only=True)
    region_name = serializers.CharField(source='subcity.city.zone.region.name', read_only=True)
    country_name = serializers.CharField(source='subcity.city.zone.region.country.name', read_only=True)
    full_name = serializers.CharField(read_only=True)
    display_name = serializers.CharField(read_only=True)

    class Meta:
        model = Kebele
        fields = [
            'id', 'subcity', 'subcity_name', 'city_name', 'zone_name', 'region_name', 'country_name',
            'name', 'code', 'number', 'population', 'area_km2', 'is_active',
            'full_name', 'display_name', 'created_at', 'updated_at'
        ]


class KebeleCreateSerializer(serializers.ModelSerializer):
    """Kebele create serializer"""
    class Meta:
        model = Kebele
        fields = ['subcity', 'name', 'code', 'number', 'population', 'area_km2', 'is_active']


# Hierarchy serializers for dropdown/select components
class CountrySelectSerializer(serializers.ModelSerializer):
    """Simplified country serializer for dropdowns"""
    class Meta:
        model = Country
        fields = ['id', 'name', 'code']


class RegionSelectSerializer(serializers.ModelSerializer):
    """Simplified region serializer for dropdowns"""
    class Meta:
        model = Region
        fields = ['id', 'name', 'code', 'country']


class ZoneSelectSerializer(serializers.ModelSerializer):
    """Simplified zone serializer for dropdowns"""
    class Meta:
        model = Zone
        fields = ['id', 'name', 'code', 'region']


class CitySelectSerializer(serializers.ModelSerializer):
    """Simplified city serializer for dropdowns"""
    class Meta:
        model = City
        fields = ['id', 'name', 'code', 'zone']


class SubCitySelectSerializer(serializers.ModelSerializer):
    """Simplified subcity serializer for dropdowns"""
    class Meta:
        model = SubCity
        fields = ['id', 'name', 'code', 'type', 'city']


class KebeleSelectSerializer(serializers.ModelSerializer):
    """Simplified kebele serializer for dropdowns"""
    class Meta:
        model = Kebele
        fields = ['id', 'name', 'code', 'number', 'display_name', 'subcity']


class SpecialLocationSerializer(serializers.ModelSerializer):
    """Special Location serializer"""
    kebele_name = serializers.CharField(source='kebele.display_name', read_only=True)
    kebele_full_name = serializers.CharField(source='kebele.full_name', read_only=True)

    class Meta:
        model = SpecialLocation
        fields = [
            'id', 'kebele', 'kebele_name', 'kebele_full_name', 'name', 'is_active',
            'location_path', 'display_name', 'full_name', 'created_at', 'updated_at'
        ]


class SpecialLocationCreateSerializer(serializers.ModelSerializer):
    """Special Location create serializer"""
    class Meta:
        model = SpecialLocation
        fields = ['kebele', 'name', 'is_active']


class SpecialLocationSelectSerializer(serializers.ModelSerializer):
    """Simplified special location serializer for dropdowns"""
    class Meta:
        model = SpecialLocation
        fields = ['id', 'name', 'display_name', 'kebele']
