import React from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  LinearProgress,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  Button,
} from '@mui/material';
import {
  Description,
  Assignment,
  People,
  LocationOn,
  TrendingUp,
  Warning,
  CheckCircle,
  Schedule,
  Business,
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '../../contexts/AuthContext';
import { requestsAPI, documentsAPI } from '../../services/api';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import { useNavigate } from 'react-router-dom';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactElement;
  color: string;
  trend?: string;
  onClick?: () => void;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, color, trend, onClick }) => (
  <Card 
    sx={{ 
      height: '100%', 
      cursor: onClick ? 'pointer' : 'default',
      '&:hover': onClick ? { boxShadow: 4 } : {},
    }}
    onClick={onClick}
  >
    <CardContent>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box>
          <Typography color="textSecondary" gutterBottom variant="h6">
            {title}
          </Typography>
          <Typography variant="h4" component="h2">
            {value}
          </Typography>
          {trend && (
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
              <TrendingUp sx={{ color: 'success.main', mr: 0.5 }} />
              <Typography variant="body2" color="success.main">
                {trend}
              </Typography>
            </Box>
          )}
        </Box>
        <Avatar sx={{ bgcolor: color, width: 56, height: 56 }}>
          {icon}
        </Avatar>
      </Box>
    </CardContent>
  </Card>
);

const DashboardPage: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();

  // Fetch dashboard stats
  const { data: dashboardStats, isLoading: statsLoading } = useQuery({
    queryKey: ['dashboardStats'],
    queryFn: requestsAPI.getDashboardStats,
  });

  // Fetch document stats
  const { data: documentStats, isLoading: docStatsLoading } = useQuery({
    queryKey: ['documentStats'],
    queryFn: documentsAPI.getDocumentStats,
  });

  // Fetch recent requests
  const { data: recentRequests, isLoading: requestsLoading } = useQuery({
    queryKey: ['recentRequests'],
    queryFn: () => requestsAPI.getRequests({ limit: 5, ordering: '-created_at' }),
  });

  if (statsLoading || docStatsLoading || requestsLoading) {
    return <LoadingSpinner />;
  }

  const stats = dashboardStats || {};
  const docStats = documentStats || {};
  const requests = recentRequests?.results || [];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'warning';
      case 'approved': return 'info';
      case 'checked_out': return 'primary';
      case 'returned': return 'success';
      case 'rejected': return 'error';
      case 'overdue': return 'error';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'error';
      case 'high': return 'warning';
      case 'normal': return 'info';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  return (
    <Box>
      {/* Welcome Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Welcome back, {user?.first_name || user?.username}!
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Here's what's happening with your documents today.
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <StatCard
            title="Total Documents"
            value={docStats.total_documents || 0}
            icon={<Description />}
            color="#1976d2"
            trend="+12% from last month"
            onClick={() => navigate('/documents')}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <StatCard
            title="Pending Requests"
            value={stats.pending_requests || 0}
            icon={<Assignment />}
            color="#ed6c02"
            onClick={() => navigate('/requests?status=pending')}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <StatCard
            title="Checked Out"
            value={stats.checked_out_documents || 0}
            icon={<Schedule />}
            color="#2e7d32"
            onClick={() => navigate('/documents?status=checked_out')}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <StatCard
            title="Overdue Items"
            value={stats.overdue_requests || 0}
            icon={<Warning />}
            color="#d32f2f"
            onClick={() => navigate('/requests?status=overdue')}
          />
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Document Overview */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Document Overview
              </Typography>
              <Box sx={{ mt: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Physical Documents</Typography>
                  <Typography variant="body2">
                    {docStats.physical_documents || 0}
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={docStats.total_documents ? (docStats.physical_documents / docStats.total_documents) * 100 : 0}
                  sx={{ mb: 2 }}
                />
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Digital Documents</Typography>
                  <Typography variant="body2">
                    {docStats.digital_documents || 0}
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={docStats.total_documents ? (docStats.digital_documents / docStats.total_documents) * 100 : 0}
                  color="secondary"
                  sx={{ mb: 2 }}
                />
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Hybrid Documents</Typography>
                  <Typography variant="body2">
                    {docStats.hybrid_documents || 0}
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={docStats.total_documents ? (docStats.hybrid_documents / docStats.total_documents) * 100 : 0}
                  color="success"
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Requests */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  Recent Requests
                </Typography>
                <Button
                  size="small"
                  onClick={() => navigate('/requests')}
                >
                  View All
                </Button>
              </Box>
              
              <List>
                {requests.length === 0 ? (
                  <ListItem>
                    <ListItemText
                      primary="No recent requests"
                      secondary="All caught up!"
                    />
                  </ListItem>
                ) : (
                  requests.map((request: any, index: number) => (
                    <React.Fragment key={request.id}>
                      <ListItem
                        sx={{ cursor: 'pointer' }}
                        onClick={() => navigate(`/requests/${request.id}`)}
                      >
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: 'primary.main' }}>
                            <Assignment />
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={
                            <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                              <Typography variant="body2" component="span">
                                {request.request_number}
                              </Typography>
                              <Chip
                                label={request.status}
                                size="small"
                                color={getStatusColor(request.status) as any}
                              />
                              <Chip
                                label={request.priority}
                                size="small"
                                variant="outlined"
                                color={getPriorityColor(request.priority) as any}
                              />
                            </span>
                          }
                          secondary={
                            <span>
                              <Typography variant="caption" display="block" component="span">
                                Requested by: {request.requested_by_name}
                              </Typography>
                              <br />
                              <Typography variant="caption" color="text.secondary" component="span">
                                {new Date(request.created_at).toLocaleDateString()}
                              </Typography>
                            </span>
                          }
                        />
                      </ListItem>
                      {index < requests.length - 1 && <Divider />}
                    </React.Fragment>
                  ))
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Actions */}
        <Grid size={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Actions
              </Typography>
              <Grid container spacing={2}>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<Description />}
                    onClick={() => navigate('/documents/create')}
                    disabled={user?.is_read_only}
                  >
                    Add Document
                  </Button>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<Assignment />}
                    onClick={() => navigate('/requests/create')}
                  >
                    New Request
                  </Button>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<LocationOn />}
                    onClick={() => navigate('/locations')}
                  >
                    View Locations
                  </Button>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<People />}
                    onClick={() => navigate('/users')}
                    disabled={!user?.can_approve_requests}
                  >
                    Manage Users
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DashboardPage;
