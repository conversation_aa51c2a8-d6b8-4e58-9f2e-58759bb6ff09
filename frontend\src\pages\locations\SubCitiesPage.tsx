import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  <PERSON><PERSON><PERSON>,
  Card,
  CardContent,
  Button,
  TextField,
  FormControlLabel,
  Switch,
  InputAdornment,
  Avatar,
  Divider,
  Stack,
  Fade,
  Alert,
  CircularProgress,
  IconButton,
  Breadcrumbs,
  Link,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TablePagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Business,
  ArrowBack,
  Home,
  Add,
  Edit,
  Delete,
  CheckCircle,
  Cancel,
  LocationCity,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useNotification } from '../../contexts/NotificationContext';
import locationHierarchyService from '../../services/locationHierarchyService';
import ConfirmationDialog from '../../components/common/ConfirmationDialog';

interface SubCity {
  id: number;
  name: string;
  code: string;
  city: number;
  city_name?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface City {
  id: number;
  name: string;
  code: string;
  zone_name?: string;
}

const SubCitiesPage: React.FC = () => {
  const navigate = useNavigate();
  const { showNotification } = useNotification();

  const [subcities, setSubcities] = useState<SubCity[]>([]);
  const [cities, setCities] = useState<City[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingSubCity, setEditingSubCity] = useState<SubCity | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    city: '',
    is_active: true,
  });
  const [formErrors, setFormErrors] = useState<any>({});
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [subcityToDelete, setSubcityToDelete] = useState<SubCity | null>(null);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    loadSubCities();
    loadCities();
  }, [page, rowsPerPage]);

  const loadSubCities = async () => {
    try {
      setLoading(true);
      const response = await locationHierarchyService.getSubCities({
        page: page + 1,
        page_size: rowsPerPage,
      });
      setSubcities(response.results);
      setTotalCount(response.count);
    } catch (error) {
      console.error('Error loading subcities:', error);
      showNotification('Failed to load subcities', 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadCities = async () => {
    try {
      const response = await locationHierarchyService.getCities({ page_size: 100 });
      setCities(response.results);
    } catch (error) {
      console.error('Error loading cities:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setSubmitting(true);
      setFormErrors({});

      if (editingSubCity) {
        await locationHierarchyService.updateSubCity(editingSubCity.id, formData);
        showNotification('SubCity updated successfully', 'success');
      } else {
        await locationHierarchyService.createSubCity(formData);
        showNotification('SubCity created successfully', 'success');
      }

      resetForm();
      loadSubCities();
    } catch (error: any) {
      console.error('Error submitting form:', error);
      if (error.response?.data) {
        setFormErrors(error.response.data);
      }
      showNotification('Failed to save subcity', 'error');
    } finally {
      setSubmitting(false);
    }
  };

  const handleEdit = (subcity: SubCity) => {
    setEditingSubCity(subcity);
    setFormData({
      name: subcity.name,
      code: subcity.code,
      city: subcity.city.toString(),
      is_active: subcity.is_active,
    });
    setShowForm(true);
  };

  const handleDelete = (subcity: SubCity) => {
    setSubcityToDelete(subcity);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!subcityToDelete) return;

    try {
      setDeleting(true);
      await locationHierarchyService.deleteSubCity(subcityToDelete.id);
      showNotification('SubCity deleted successfully', 'success');
      loadSubCities();
      handleCloseDeleteDialog();
    } catch (error) {
      console.error('Error deleting subcity:', error);
      showNotification('Failed to delete subcity', 'error');
    } finally {
      setDeleting(false);
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setSubcityToDelete(null);
    setDeleting(false);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      code: '',
      city: '',
      is_active: true,
    });
    setFormErrors({});
    setEditingSubCity(null);
    setShowForm(false);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Fade in timeout={800}>
        <Box sx={{ mb: 4 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs sx={{ mb: 2 }}>
            <Link
              component="button"
              variant="body1"
              onClick={() => navigate('/locations/hierarchy')}
              sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
            >
              <Home fontSize="small" />
              Location Hierarchy
            </Link>
            <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <Business fontSize="small" />
              SubCities (Woredas)
            </Typography>
          </Breadcrumbs>

          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'between', mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <IconButton onClick={() => navigate('/locations/hierarchy')} sx={{ mr: 1 }}>
                <ArrowBack />
              </IconButton>
              <Avatar sx={{ bgcolor: 'info.main' }}>
                <Business />
              </Avatar>
              <Box>
                <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
                  SubCities (Woredas) Management
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Manage subcities and woredas within cities
                </Typography>
              </Box>
            </Box>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setShowForm(true)}
              size="large"
              sx={{ ml: 'auto' }}
            >
              Add SubCity
            </Button>
          </Box>
        </Box>
      </Fade>

      {/* Form Section */}
      {showForm && (
        <Fade in timeout={600}>
          <Card sx={{ mb: 4 }}>
            <CardContent sx={{ p: 4 }}>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                {editingSubCity ? 'Edit SubCity' : 'Add New SubCity'}
              </Typography>
              <Divider sx={{ mb: 3 }} />

              <form onSubmit={handleSubmit}>
                <Stack spacing={3}>
                  <TextField
                    label="SubCity/Woreda Name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    error={!!formErrors.name}
                    helperText={formErrors.name || 'Enter the full subcity or woreda name'}
                    fullWidth
                    required
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Business color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />

                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                    <TextField
                      label="SubCity Code"
                      value={formData.code}
                      onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
                      error={!!formErrors.code}
                      helperText={formErrors.code || 'e.g., AK, YK, BL'}
                      required
                      inputProps={{ maxLength: 10 }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Business color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />

                    <FormControl fullWidth required error={!!formErrors.city}>
                      <InputLabel>City</InputLabel>
                      <Select
                        value={formData.city}
                        onChange={(e) => setFormData({ ...formData, city: e.target.value })}
                        label="City"
                        startAdornment={
                          <InputAdornment position="start">
                            <LocationCity color="action" />
                          </InputAdornment>
                        }
                      >
                        {cities.map((city) => (
                          <MenuItem key={city.id} value={city.id.toString()}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Chip label={city.code} size="small" />
                              {city.name}
                              {city.zone_name && (
                                <Typography variant="caption" color="text.secondary">
                                  ({city.zone_name})
                                </Typography>
                              )}
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                      {formErrors.city && (
                        <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 2 }}>
                          {formErrors.city}
                        </Typography>
                      )}
                    </FormControl>
                  </Box>

                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.is_active}
                        onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                        color="primary"
                      />
                    }
                    label="Active Status"
                  />

                  <Box sx={{ display: 'flex', gap: 2, pt: 2 }}>
                    <Button
                      type="submit"
                      variant="contained"
                      disabled={submitting}
                      startIcon={submitting ? <CircularProgress size={20} /> : <Add />}
                      size="large"
                    >
                      {submitting ? 'Saving...' : editingSubCity ? 'Update SubCity' : 'Create SubCity'}
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={resetForm}
                      size="large"
                    >
                      Cancel
                    </Button>
                  </Box>
                </Stack>
              </form>
            </CardContent>
          </Card>
        </Fade>
      )}

      {/* SubCities Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
            SubCities/Woredas List
          </Typography>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : subcities.length === 0 ? (
            <Alert severity="info">
              No subcities found. Click "Add SubCity" to create your first subcity.
            </Alert>
          ) : (
            <>
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>SubCity/Woreda</TableCell>
                      <TableCell>Code</TableCell>
                      <TableCell>City</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Created</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {subcities.map((subcity) => (
                      <TableRow key={subcity.id} hover>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <Avatar sx={{ bgcolor: 'info.main', width: 32, height: 32 }}>
                              <Business fontSize="small" />
                            </Avatar>
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {subcity.name}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip label={subcity.code} size="small" color="info" />
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <LocationCity fontSize="small" color="action" />
                            {subcity.city_name || `City ${subcity.city}`}
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={subcity.is_active ? 'Active' : 'Inactive'}
                            color={subcity.is_active ? 'success' : 'error'}
                            size="small"
                            icon={subcity.is_active ? <CheckCircle /> : <Cancel />}
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {new Date(subcity.created_at).toLocaleDateString()}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                            <IconButton
                              size="small"
                              onClick={() => handleEdit(subcity)}
                              color="primary"
                            >
                              <Edit fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleDelete(subcity)}
                              color="error"
                            >
                              <Delete fontSize="small" />
                            </IconButton>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              <TablePagination
                component="div"
                count={totalCount}
                page={page}
                onPageChange={handleChangePage}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                rowsPerPageOptions={[5, 10, 25, 50]}
              />
            </>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        title="Delete SubCity"
        itemName={subcityToDelete?.name}
        itemType="SubCity/Woreda"
        message={`Are you sure you want to delete "${subcityToDelete?.name}"? This will also delete all kebeles within this subcity. This action cannot be undone.`}
        confirmText="Delete SubCity"
        severity="error"
        loading={deleting}
      />
    </Container>
  );
};

export default SubCitiesPage;
