import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  TextField,
  InputAdornment,
  Avatar,
  Divider,
  Stack,
  Fade,
  Alert,
  CircularProgress,
  IconButton,
  Breadcrumbs,
  Link,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TablePagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Autocomplete,
  Switch,
  FormControlLabel,
  Tooltip,
  Badge,
} from '@mui/material';
import {
  Description,
  ArrowBack,
  Home,
  Add,
  Edit,
  Delete,
  Folder,
  CloudUpload,
  Visibility,
  Download,
  Search,
  FilterList,
  Print,
  QrCode,
  Archive,
  Business,
  CalendarToday,
  Warning,
  CheckCircle,
  Error,
  Info,
  Close,
  AttachFile,
  LocationOn,
  Category,
  Numbers,
  CameraAlt,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useNotification } from '../../contexts/NotificationContext';
import documentService, { type Document as DocumentType, type DocumentCreate } from '../../services/documentService';
import documentTypeService from '../../services/documentTypeService';
import fileService from '../../services/fileService';
import ConfirmationDialog from '../../components/common/ConfirmationDialog';
import CameraCapture from '../../components/CameraCapture';
import type { File, DocumentType as DocType } from '../../services/types';

const DocumentsPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { showNotification } = useNotification();

  const [documents, setDocuments] = useState<DocumentType[]>([]);
  const [files, setFiles] = useState<File[]>([]);
  const [documentTypes, setDocumentTypes] = useState<DocType[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingDocument, setEditingDocument] = useState<DocumentType | null>(null);
  const [formData, setFormData] = useState<DocumentCreate>({
    title: '',
    description: '',
    document_type: 0,
    mode: 'physical',
    tags: [],
    reference_number: '',
    document_date: '',
    expiry_date: '',
    document_file: '',
    kent: undefined,
    file: undefined,
    number_of_pages: undefined,
  });
  const [formErrors, setFormErrors] = useState<any>({});
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<DocumentType | null>(null);
  const [deleting, setDeleting] = useState(false);

  // Search and filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    document_type: '',
    mode: '',
    status: '',
    kent: '',
    is_expired: '',
  });
  const [showFilters, setShowFilters] = useState(false);

  // File upload state
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [showCamera, setShowCamera] = useState(false);

  useEffect(() => {
    loadDocuments();
    loadFiles();
    loadDocumentTypes();
  }, [page, rowsPerPage, searchTerm, filters]);

  // Handle navigation state from detail page
  useEffect(() => {
    const state = location.state as any;
    if (state?.editDocument && state?.showForm) {
      handleEdit(state.editDocument);
      // Clear the state to prevent re-triggering
      navigate(location.pathname, { replace: true });
    }
  }, [location.state]);

  const loadDocuments = async () => {
    try {
      setLoading(true);
      const params: any = {
        page: page + 1,
        page_size: rowsPerPage,
      };

      // Add search term
      if (searchTerm.trim()) {
        params.search = searchTerm.trim();
      }

      // Add filters
      if (filters.document_type) {
        params.document_type = parseInt(filters.document_type);
      }
      if (filters.mode) {
        params.mode = filters.mode;
      }
      if (filters.status) {
        params.status = filters.status;
      }
      if (filters.kent) {
        params.kent = parseInt(filters.kent);
      }
      if (filters.is_expired) {
        params.is_expired = filters.is_expired === 'true';
      }

      const response = await documentService.getDocuments(params);
      setDocuments(response.results);
      setTotalCount(response.count);
    } catch (error) {
      console.error('Error loading documents:', error);
      showNotification('Failed to load documents', 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadFiles = async () => {
    try {
      const response = await fileService.getFiles({ page_size: 100 });
      setFiles(response.results);
    } catch (error) {
      console.error('Error loading files:', error);
    }
  };

  const loadDocumentTypes = async () => {
    try {
      const response = await documentService.getDocumentTypes({ page_size: 100 });
      setDocumentTypes(response.results);
    } catch (error) {
      console.error('Error loading document types:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setSubmitting(true);
      setFormErrors({});

      if (editingDocument) {
        await documentService.updateDocument(editingDocument.id, formData);
        showNotification('Document updated successfully', 'success');
      } else {
        await documentService.createDocument(formData);
        showNotification('Document created successfully', 'success');
      }

      resetForm();
      loadDocuments();
    } catch (error: any) {
      console.error('Error submitting form:', error);
      console.error('Error response data:', error.response?.data);
      console.error('Error status:', error.response?.status);
      console.error('Error headers:', error.response?.headers);

      // Log the actual form data being sent
      console.group('Form Data Details:');
      console.log('Original formData object:', formData);
      console.log('FormData entries:');
      if (formData instanceof FormData) {
        for (const [key, value] of formData.entries()) {
          console.log(`${key}:`, value);
        }
      }
      console.groupEnd();

      if (error.response?.data) {
        setFormErrors(error.response.data);

        // Show specific validation errors
        const errorMessages = [];
        if (typeof error.response.data === 'object') {
          for (const [field, messages] of Object.entries(error.response.data)) {
            if (Array.isArray(messages)) {
              errorMessages.push(`${field}: ${messages.join(', ')}`);
            } else {
              errorMessages.push(`${field}: ${messages}`);
            }
          }
        }

        if (errorMessages.length > 0) {
          showNotification(`Validation errors: ${errorMessages.join('; ')}`, 'error');
        } else {
          showNotification('Failed to save document', 'error');
        }
      } else {
        showNotification('Failed to save document', 'error');
      }
    } finally {
      setSubmitting(false);
    }
  };

  const handleViewDetails = (document: DocumentType) => {
    navigate(`/document-center/documents/${document.id}`);
  };

  const handleEdit = (document: DocumentType) => {
    setEditingDocument(document);
    setFormData({
      title: document.title,
      description: document.description || '',
      document_type: document.document_type || 0,
      mode: document.mode || 'physical',
      tags: document.tags || [],
      reference_number: document.reference_number || '',
      document_date: document.document_date || '',
      expiry_date: document.expiry_date || '',
      document_file: document.document_file || '',
      kent: document.kent || undefined,
      file: undefined,
      number_of_pages: document.number_of_pages,
    });
    setShowForm(true);
  };

  const handleDelete = (document: DocumentType) => {
    setDocumentToDelete(document);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!documentToDelete) return;
    
    try {
      setDeleting(true);
      await documentService.deleteDocument(documentToDelete.id);
      showNotification('Document deleted successfully', 'success');
      loadDocuments();
      handleCloseDeleteDialog();
    } catch (error) {
      console.error('Error deleting document:', error);
      showNotification('Failed to delete document', 'error');
    } finally {
      setDeleting(false);
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDocumentToDelete(null);
    setDeleting(false);
  };

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setPage(0); // Reset to first page when searching
  };

  const handleFilterChange = (filterName: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value
    }));
    setPage(0); // Reset to first page when filtering
  };

  const clearFilters = () => {
    setFilters({
      document_type: '',
      mode: '',
      status: '',
      kent: '',
      is_expired: '',
    });
    setSearchTerm('');
    setPage(0);
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setFormData(prev => ({ ...prev, file }));
    }
  };

  const handleCameraCapture = (file: File) => {
    setSelectedFile(file);
    setFormData(prev => ({ ...prev, file }));
    setShowCamera(false);
  };

  const handleDownload = async (document: DocumentType) => {
    try {
      const blob = await documentService.downloadDocument(document.id);
      const url = window.URL.createObjectURL(blob);
      const a = window.document.createElement('a');
      a.href = url;
      a.download = `${document.title}.${document.file?.split('.').pop() || 'pdf'}`;
      window.document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      window.document.body.removeChild(a);
      showNotification('Document downloaded successfully', 'success');
    } catch (error) {
      console.error('Error downloading document:', error);
      showNotification('Failed to download document', 'error');
    }
  };

  const handleView = async (document: DocumentType) => {
    try {
      const url = await documentService.viewDocument(document.id);
      window.open(url, '_blank');
    } catch (error) {
      console.error('Error viewing document:', error);
      showNotification('Failed to view document', 'error');
    }
  };

  const handlePrint = async (document: DocumentType) => {
    try {
      await documentService.printDocument(document.id);
      showNotification('Document sent to printer', 'success');
    } catch (error) {
      console.error('Error printing document:', error);
      showNotification('Failed to print document', 'error');
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      document_type: 0,
      mode: 'physical',
      tags: [],
      reference_number: '',
      document_date: '',
      expiry_date: '',
      document_file: '',
      kent: undefined,
      file: undefined,
      number_of_pages: undefined,
    });
    setFormErrors({});
    setEditingDocument(null);
    setShowForm(false);
    setSelectedFile(null);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Fade in timeout={800}>
        <Box sx={{ mb: 4 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs sx={{ mb: 2 }}>
            <Link
              component="button"
              variant="body1"
              onClick={() => navigate('/document-center')}
              sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
            >
              <Home fontSize="small" />
              Document Management Center
            </Link>
            <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <Description fontSize="small" />
              Documents
            </Typography>
          </Breadcrumbs>

          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'between', mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <IconButton onClick={() => navigate('/document-center')} sx={{ mr: 1 }}>
                <ArrowBack />
              </IconButton>
              <Avatar sx={{ bgcolor: 'primary.main', width: 56, height: 56 }}>
                <Description />
              </Avatar>
              <Box>
                <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
                  Documents Management
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Manage individual documents and files
                </Typography>
              </Box>
            </Box>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setShowForm(true)}
              size="large"
              sx={{ ml: 'auto' }}
            >
              Add Document
            </Button>
          </Box>
        </Box>
      </Fade>

      {/* Form Section */}
      {showForm && (
        <Fade in timeout={600}>
          <Card sx={{ mb: 4 }}>
            <CardContent sx={{ p: 4 }}>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                {editingDocument ? 'Edit Document' : 'Add New Document'}
              </Typography>
              <Divider sx={{ mb: 3 }} />
              
              <form onSubmit={handleSubmit}>
                <Stack spacing={3}>
                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                    <TextField
                      label="Document Title"
                      value={formData.title}
                      onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                      error={!!formErrors.title}
                      helperText={formErrors.title || 'Enter the document title'}
                      required
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Description color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />

                    <TextField
                      label="Reference Number"
                      value={formData.reference_number}
                      onChange={(e) => setFormData({ ...formData, reference_number: e.target.value })}
                      error={!!formErrors.reference_number}
                      helperText={formErrors.reference_number || 'External reference or ID'}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Numbers color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>

                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                    <FormControl fullWidth error={!!formErrors.document_file}>
                      <InputLabel>Business File</InputLabel>
                      <Select
                        value={formData.document_file || ''}
                        onChange={(e) => setFormData({ ...formData, document_file: e.target.value })}
                        label="Business File"
                        startAdornment={
                          <InputAdornment position="start">
                            <Folder color="action" />
                          </InputAdornment>
                        }
                      >
                        <MenuItem value="">
                          <em>No Business File</em>
                        </MenuItem>
                        {files.map((file) => (
                          <MenuItem key={file.id} value={file.id}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Chip label={file.file_number} size="small" />
                              {file.name}
                              {file.location_path && (
                                <Typography variant="caption" color="text.secondary">
                                  ({file.location_path})
                                </Typography>
                              )}
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                      {formErrors.document_file && (
                        <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 2 }}>
                          {formErrors.document_file}
                        </Typography>
                      )}
                    </FormControl>

                    <FormControl fullWidth required error={!!formErrors.document_type}>
                      <InputLabel>Document Type</InputLabel>
                      <Select
                        value={formData.document_type || ''}
                        onChange={(e) => setFormData({ ...formData, document_type: Number(e.target.value) })}
                        label="Document Type"
                        startAdornment={
                          <InputAdornment position="start">
                            <Description color="action" />
                          </InputAdornment>
                        }
                      >
                        {documentTypes.map((docType) => (
                          <MenuItem key={docType.id} value={docType.id}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Chip label={docType.code} size="small" />
                              {docType.name}
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                      {formErrors.document_type && (
                        <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 2 }}>
                          {formErrors.document_type}
                        </Typography>
                      )}
                    </FormControl>
                  </Box>

                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                    <FormControl fullWidth required error={!!formErrors.mode}>
                      <InputLabel>Document Mode</InputLabel>
                      <Select
                        value={formData.mode}
                        onChange={(e) => setFormData({ ...formData, mode: e.target.value as 'physical' | 'digital' | 'hybrid' })}
                        label="Document Mode"
                        startAdornment={
                          <InputAdornment position="start">
                            <Archive color="action" />
                          </InputAdornment>
                        }
                      >
                        <MenuItem value="physical">
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Archive fontSize="small" />
                            Physical Only
                          </Box>
                        </MenuItem>
                        <MenuItem value="digital">
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <CloudUpload fontSize="small" />
                            Digital Only
                          </Box>
                        </MenuItem>
                        <MenuItem value="hybrid">
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Archive fontSize="small" />
                            <CloudUpload fontSize="small" />
                            Physical + Digital
                          </Box>
                        </MenuItem>
                      </Select>
                      {formErrors.mode && (
                        <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 2 }}>
                          {formErrors.mode}
                        </Typography>
                      )}
                    </FormControl>

                    <TextField
                      label="Number of Pages"
                      type="number"
                      value={formData.number_of_pages || ''}
                      onChange={(e) => setFormData({ ...formData, number_of_pages: e.target.value ? parseInt(e.target.value) : undefined })}
                      error={!!formErrors.number_of_pages}
                      helperText={formErrors.number_of_pages || 'Optional page count'}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Description color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>

                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                    <TextField
                      label="Document Date"
                      type="date"
                      value={formData.document_date}
                      onChange={(e) => setFormData({ ...formData, document_date: e.target.value })}
                      error={!!formErrors.document_date}
                      helperText={formErrors.document_date || 'Date when document was created'}
                      InputLabelProps={{ shrink: true }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <CalendarToday color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />

                    <TextField
                      label="Expiry Date"
                      type="date"
                      value={formData.expiry_date}
                      onChange={(e) => setFormData({ ...formData, expiry_date: e.target.value })}
                      error={!!formErrors.expiry_date}
                      helperText={formErrors.expiry_date || 'Optional expiry date'}
                      InputLabelProps={{ shrink: true }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Warning color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>

                  {(formData.mode === 'digital' || formData.mode === 'hybrid') && (
                    <Box sx={{ p: 2, border: '1px dashed', borderColor: 'grey.300', borderRadius: 1 }}>
                      <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 600 }}>
                        Digital File Upload
                      </Typography>
                      <input
                        type="file"
                        onChange={handleFileSelect}
                        style={{ display: 'none' }}
                        id="file-upload"
                        accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif,.txt"
                      />
                      <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
                        <label htmlFor="file-upload" style={{ flex: 1 }}>
                          <Button
                            variant="outlined"
                            component="span"
                            startIcon={<AttachFile />}
                            fullWidth
                          >
                            {selectedFile ? selectedFile.name : 'Choose File'}
                          </Button>
                        </label>
                        <Button
                          variant="outlined"
                          startIcon={<CameraAlt />}
                          onClick={() => setShowCamera(true)}
                          sx={{ minWidth: 'auto', px: 2 }}
                        >
                          Camera
                        </Button>
                      </Box>
                      {selectedFile && (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                          <Chip
                            label={`${(selectedFile.size / 1024 / 1024).toFixed(2)} MB`}
                            size="small"
                            color="info"
                          />
                          <IconButton size="small" onClick={() => {
                            setSelectedFile(null);
                            setFormData(prev => ({ ...prev, file: undefined }));
                          }}>
                            <Close fontSize="small" />
                          </IconButton>
                        </Box>
                      )}
                      <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
                        Supported formats: PDF, DOC, DOCX, JPG, PNG, GIF, TXT (Max: 10MB)
                      </Typography>
                    </Box>
                  )}

                  <TextField
                    label="Description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    error={!!formErrors.description}
                    helperText={formErrors.description || 'Optional description'}
                    multiline
                    rows={3}
                    fullWidth
                  />

                  <Box sx={{ display: 'flex', gap: 2, pt: 2 }}>
                    <Button
                      type="submit"
                      variant="contained"
                      disabled={submitting}
                      startIcon={submitting ? <CircularProgress size={20} /> : <Add />}
                      size="large"
                    >
                      {submitting ? 'Saving...' : editingDocument ? 'Update Document' : 'Create Document'}
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={resetForm}
                      size="large"
                    >
                      Cancel
                    </Button>
                  </Box>
                </Stack>
              </form>
            </CardContent>
          </Card>
        </Fade>
      )}

      {/* Documents Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
            Documents List
          </Typography>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : documents.length === 0 ? (
            <Alert severity="info">
              No documents found. Click "Add Document" to create your first document.
            </Alert>
          ) : (
            <>
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Document</TableCell>
                      <TableCell>Mode</TableCell>
                      <TableCell>Business File</TableCell>
                      <TableCell>Document Type</TableCell>
                      <TableCell>Pages</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Created</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {documents.map((document) => (
                      <TableRow key={document.id} hover>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <Avatar sx={{ bgcolor: 'primary.main', width: 32, height: 32 }}>
                              <Description fontSize="small" />
                            </Avatar>
                            <Box>
                              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                {document.title}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {document.reference_number || document.description || 'No reference'}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={document.mode?.charAt(0).toUpperCase() + document.mode?.slice(1) || 'Physical'}
                            size="small"
                            color={
                              document.mode === 'digital' ? 'info' :
                              document.mode === 'hybrid' ? 'warning' : 'default'
                            }
                            icon={
                              document.mode === 'digital' ? <CloudUpload fontSize="small" /> :
                              document.mode === 'hybrid' ? <Archive fontSize="small" /> :
                              <Archive fontSize="small" />
                            }
                          />
                        </TableCell>
                        <TableCell>
                          <Box>
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {document.document_file_name || 'No Business File'}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {document.document_file_location || ''}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={document.document_type_name || 'Unknown'}
                            size="small"
                            color="primary"
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {document.number_of_pages || 'Not specified'}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={document.status || 'active'}
                            size="small"
                            color={document.status === 'active' ? 'success' : 'default'}
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {new Date(document.created_at).toLocaleDateString()}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                            <Tooltip title="View Details">
                              <IconButton
                                size="small"
                                onClick={() => handleViewDetails(document)}
                                color="info"
                              >
                                <Visibility fontSize="small" />
                              </IconButton>
                            </Tooltip>
                            {document.is_digital && (
                              <Tooltip title="View Document">
                                <IconButton
                                  size="small"
                                  onClick={() => handleView(document)}
                                  color="secondary"
                                >
                                  <Visibility fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            )}
                            {document.is_digital && (
                              <Tooltip title="Download">
                                <IconButton
                                  size="small"
                                  onClick={() => handleDownload(document)}
                                  color="success"
                                >
                                  <Download fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            )}
                            {document.is_digital && (
                              <Tooltip title="Print">
                                <IconButton
                                  size="small"
                                  onClick={() => handlePrint(document)}
                                  color="warning"
                                >
                                  <Print fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            )}
                            <Tooltip title="Edit">
                              <IconButton
                                size="small"
                                onClick={() => handleEdit(document)}
                                color="primary"
                              >
                                <Edit fontSize="small" />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Delete">
                              <IconButton
                                size="small"
                                onClick={() => handleDelete(document)}
                                color="error"
                              >
                                <Delete fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              <TablePagination
                component="div"
                count={totalCount}
                page={page}
                onPageChange={handleChangePage}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                rowsPerPageOptions={[5, 10, 25, 50]}
              />
            </>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        title="Delete Document"
        itemName={documentToDelete?.title}
        itemType="Document"
        message={`Are you sure you want to delete "${documentToDelete?.title}"? This action cannot be undone.`}
        confirmText="Delete Document"
        severity="error"
        loading={deleting}
      />

      {/* Camera Capture Dialog */}
      <CameraCapture
        open={showCamera}
        onClose={() => setShowCamera(false)}
        onCapture={handleCameraCapture}
        title="Capture Document Photo"
      />
    </Container>
  );
};

export default DocumentsPage;
