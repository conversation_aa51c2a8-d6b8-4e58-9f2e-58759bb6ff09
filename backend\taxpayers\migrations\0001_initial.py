# Generated by Django 5.2.3 on 2025-07-14 20:09

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("locations", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="OrganizationBusinessType",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        choices=[
                            ("PLC", "Private Limited Company"),
                            ("SC", "Share Company"),
                            ("COOP", "Cooperative"),
                            ("NGO", "Non-Governmental Organization"),
                            ("ASSOC", "Association"),
                            ("PART", "Partnership"),
                            ("SOLE", "Sole Proprietorship"),
                            ("GOV", "Government Entity"),
                            ("OTHER", "Other"),
                        ],
                        max_length=10,
                        unique=True,
                    ),
                ),
                (
                    "name",
                    models.CharField(help_text="Business type name", max_length=100),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True, help_text="Description of the business type"
                    ),
                ),
                (
                    "requires_vat_registration",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this type requires VAT registration",
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Organization Business Type",
                "verbose_name_plural": "Organization Business Types",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="TaxPayerLevel",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Level name (e.g., Large Taxpayer)", max_length=100
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        choices=[
                            ("A", "Category A - Large Taxpayers"),
                            ("B", "Category B - Medium Taxpayers"),
                            ("C", "Category C - Small Taxpayers"),
                            ("D", "Category D - Micro Taxpayers"),
                        ],
                        help_text="Level code",
                        max_length=5,
                        unique=True,
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        help_text="Criteria and description for this level"
                    ),
                ),
                (
                    "minimum_annual_turnover",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Minimum annual turnover for this level",
                        max_digits=15,
                        null=True,
                    ),
                ),
                (
                    "maximum_annual_turnover",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Maximum annual turnover for this level",
                        max_digits=15,
                        null=True,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Tax Payer Level",
                "verbose_name_plural": "Tax Payer Levels",
                "ordering": ["code"],
            },
        ),
        migrations.CreateModel(
            name="BusinessSector",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        help_text="Unique sector code (e.g., AGR, MAN, SER)",
                        max_length=10,
                        unique=True,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Sector name (e.g., Agriculture, Manufacturing)",
                        max_length=100,
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True, help_text="Detailed description of the sector"
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Business Sector",
                "verbose_name_plural": "Business Sectors",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="BusinessSubSector",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        help_text="Sub-sector code (e.g., AGR001, MAN002)",
                        max_length=15,
                    ),
                ),
                ("name", models.CharField(help_text="Sub-sector name", max_length=150)),
                (
                    "description",
                    models.TextField(
                        blank=True, help_text="Detailed description of the sub-sector"
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "business_sector",
                    models.ForeignKey(
                        help_text="Parent business sector",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sub_sectors",
                        to="taxpayers.businesssector",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Business Sub-Sector",
                "verbose_name_plural": "Business Sub-Sectors",
                "ordering": ["business_sector__name", "name"],
                "unique_together": {("business_sector", "code")},
            },
        ),
        migrations.CreateModel(
            name="OrganizationTaxPayer",
            fields=[
                (
                    "phone",
                    models.CharField(
                        help_text="Primary phone number",
                        max_length=17,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Phone number must be entered in the format: '+*********'. Up to 15 digits allowed.",
                                regex="^\\+?1?\\d{9,15}$",
                            )
                        ],
                    ),
                ),
                (
                    "phone_secondary",
                    models.CharField(
                        blank=True,
                        help_text="Secondary phone number",
                        max_length=17,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Phone number must be entered in the format: '+*********'. Up to 15 digits allowed.",
                                regex="^\\+?1?\\d{9,15}$",
                            )
                        ],
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        blank=True, help_text="Email address", max_length=254
                    ),
                ),
                (
                    "house_number",
                    models.CharField(
                        blank=True, help_text="House/Building number", max_length=20
                    ),
                ),
                (
                    "street_address",
                    models.CharField(
                        blank=True, help_text="Street address", max_length=200
                    ),
                ),
                (
                    "postal_code",
                    models.CharField(
                        blank=True, help_text="Postal code", max_length=10
                    ),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "tin",
                    models.CharField(
                        help_text="Tax Identification Number (10 digits)",
                        max_length=10,
                        unique=True,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="TIN must be exactly 10 digits.",
                                regex="^\\d{10}$",
                            )
                        ],
                    ),
                ),
                (
                    "business_registration_date",
                    models.DateField(help_text="Date of business registration"),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("registration_date", models.DateTimeField(auto_now_add=True)),
                ("last_updated", models.DateTimeField(auto_now=True)),
                (
                    "business_name",
                    models.CharField(
                        help_text="Official business name", max_length=200
                    ),
                ),
                (
                    "trade_name",
                    models.CharField(
                        blank=True,
                        help_text="Trade name (if different from business name)",
                        max_length=200,
                    ),
                ),
                (
                    "manager_first_name",
                    models.CharField(
                        help_text="Manager/Owner first name", max_length=50
                    ),
                ),
                (
                    "manager_middle_name",
                    models.CharField(
                        blank=True, help_text="Manager/Owner middle name", max_length=50
                    ),
                ),
                (
                    "manager_last_name",
                    models.CharField(
                        help_text="Manager/Owner last name", max_length=50
                    ),
                ),
                (
                    "manager_title",
                    models.CharField(
                        default="Manager",
                        help_text="Manager's title/position",
                        max_length=100,
                    ),
                ),
                (
                    "vat_registration_date",
                    models.DateField(
                        blank=True, help_text="VAT registration date", null=True
                    ),
                ),
                (
                    "vat_number",
                    models.CharField(
                        blank=True,
                        help_text="VAT registration number (ET + 10 digits)",
                        max_length=12,
                        null=True,
                        unique=True,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="VAT number must be in format: ET followed by 10 digits (e.g., ET1234567890).",
                                regex="^ET\\d{10}$",
                            )
                        ],
                    ),
                ),
                (
                    "business_license_number",
                    models.CharField(
                        blank=True, help_text="Business license number", max_length=50
                    ),
                ),
                (
                    "capital_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Registered capital amount",
                        max_digits=15,
                        null=True,
                    ),
                ),
                (
                    "number_of_employees",
                    models.PositiveIntegerField(
                        blank=True, help_text="Number of employees", null=True
                    ),
                ),
                (
                    "business_sector",
                    models.ForeignKey(
                        help_text="Primary business sector",
                        on_delete=django.db.models.deletion.PROTECT,
                        to="taxpayers.businesssector",
                    ),
                ),
                (
                    "business_sub_sector",
                    models.ForeignKey(
                        help_text="Specific business sub-sector",
                        on_delete=django.db.models.deletion.PROTECT,
                        to="taxpayers.businesssubsector",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "kebele",
                    models.ForeignKey(
                        blank=True,
                        help_text="Kebele",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="locations.kebele",
                    ),
                ),
                (
                    "organization_business_type",
                    models.ForeignKey(
                        help_text="Type of business organization",
                        on_delete=django.db.models.deletion.PROTECT,
                        to="taxpayers.organizationbusinesstype",
                    ),
                ),
                (
                    "subcity",
                    models.ForeignKey(
                        blank=True,
                        help_text="SubCity/Woreda",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="locations.subcity",
                    ),
                ),
                (
                    "tax_file",
                    models.ForeignKey(
                        blank=True,
                        help_text="File containing tax-related documents",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_taxpayers",
                        to="locations.file",
                    ),
                ),
                (
                    "tax_payer_level",
                    models.ForeignKey(
                        help_text="Tax payer classification level",
                        on_delete=django.db.models.deletion.PROTECT,
                        to="taxpayers.taxpayerlevel",
                    ),
                ),
            ],
            options={
                "verbose_name": "Organization Tax Payer",
                "verbose_name_plural": "Organization Tax Payers",
                "ordering": ["business_name"],
                "indexes": [
                    models.Index(fields=["tin"], name="taxpayers_o_tin_83ea55_idx"),
                    models.Index(
                        fields=["business_name"], name="taxpayers_o_busines_905412_idx"
                    ),
                    models.Index(
                        fields=["vat_number"], name="taxpayers_o_vat_num_901bf1_idx"
                    ),
                    models.Index(
                        fields=["business_registration_date"],
                        name="taxpayers_o_busines_be06a4_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="IndividualTaxPayer",
            fields=[
                (
                    "phone",
                    models.CharField(
                        help_text="Primary phone number",
                        max_length=17,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Phone number must be entered in the format: '+*********'. Up to 15 digits allowed.",
                                regex="^\\+?1?\\d{9,15}$",
                            )
                        ],
                    ),
                ),
                (
                    "phone_secondary",
                    models.CharField(
                        blank=True,
                        help_text="Secondary phone number",
                        max_length=17,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Phone number must be entered in the format: '+*********'. Up to 15 digits allowed.",
                                regex="^\\+?1?\\d{9,15}$",
                            )
                        ],
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        blank=True, help_text="Email address", max_length=254
                    ),
                ),
                (
                    "house_number",
                    models.CharField(
                        blank=True, help_text="House/Building number", max_length=20
                    ),
                ),
                (
                    "street_address",
                    models.CharField(
                        blank=True, help_text="Street address", max_length=200
                    ),
                ),
                (
                    "postal_code",
                    models.CharField(
                        blank=True, help_text="Postal code", max_length=10
                    ),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "tin",
                    models.CharField(
                        help_text="Tax Identification Number (10 digits)",
                        max_length=10,
                        unique=True,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="TIN must be exactly 10 digits.",
                                regex="^\\d{10}$",
                            )
                        ],
                    ),
                ),
                (
                    "business_registration_date",
                    models.DateField(help_text="Date of business registration"),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("registration_date", models.DateTimeField(auto_now_add=True)),
                ("last_updated", models.DateTimeField(auto_now=True)),
                ("first_name", models.CharField(help_text="First name", max_length=50)),
                (
                    "middle_name",
                    models.CharField(
                        blank=True, help_text="Middle name", max_length=50
                    ),
                ),
                ("last_name", models.CharField(help_text="Last name", max_length=50)),
                (
                    "nationality",
                    models.CharField(
                        choices=[
                            ("ET", "Ethiopian"),
                            ("US", "American"),
                            ("UK", "British"),
                            ("CA", "Canadian"),
                            ("DE", "German"),
                            ("FR", "French"),
                            ("IT", "Italian"),
                            ("CN", "Chinese"),
                            ("IN", "Indian"),
                            ("KE", "Kenyan"),
                            ("UG", "Ugandan"),
                            ("TZ", "Tanzanian"),
                            ("OTHER", "Other"),
                        ],
                        default="ET",
                        max_length=10,
                    ),
                ),
                (
                    "gender",
                    models.CharField(
                        choices=[("M", "Male"), ("F", "Female"), ("O", "Other")],
                        max_length=1,
                    ),
                ),
                ("date_of_birth", models.DateField(help_text="Date of birth")),
                (
                    "profile_picture",
                    models.ImageField(
                        blank=True,
                        help_text="Profile picture/photo",
                        null=True,
                        upload_to="taxpayers/individuals/photos/",
                    ),
                ),
                (
                    "business_name",
                    models.CharField(
                        blank=True,
                        help_text="Business/Trade name (if applicable)",
                        max_length=200,
                    ),
                ),
                (
                    "business_license_number",
                    models.CharField(
                        blank=True, help_text="Business license number", max_length=50
                    ),
                ),
                (
                    "business_sector",
                    models.ForeignKey(
                        help_text="Primary business sector",
                        on_delete=django.db.models.deletion.PROTECT,
                        to="taxpayers.businesssector",
                    ),
                ),
                (
                    "business_sub_sector",
                    models.ForeignKey(
                        help_text="Specific business sub-sector",
                        on_delete=django.db.models.deletion.PROTECT,
                        to="taxpayers.businesssubsector",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "kebele",
                    models.ForeignKey(
                        blank=True,
                        help_text="Kebele",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="locations.kebele",
                    ),
                ),
                (
                    "subcity",
                    models.ForeignKey(
                        blank=True,
                        help_text="SubCity/Woreda",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="locations.subcity",
                    ),
                ),
                (
                    "tax_file",
                    models.ForeignKey(
                        blank=True,
                        help_text="File containing tax-related documents",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_taxpayers",
                        to="locations.file",
                    ),
                ),
                (
                    "tax_payer_level",
                    models.ForeignKey(
                        help_text="Tax payer classification level",
                        on_delete=django.db.models.deletion.PROTECT,
                        to="taxpayers.taxpayerlevel",
                    ),
                ),
            ],
            options={
                "verbose_name": "Individual Tax Payer",
                "verbose_name_plural": "Individual Tax Payers",
                "ordering": ["last_name", "first_name"],
                "indexes": [
                    models.Index(fields=["tin"], name="taxpayers_i_tin_d4a974_idx"),
                    models.Index(
                        fields=["last_name", "first_name"],
                        name="taxpayers_i_last_na_da053c_idx",
                    ),
                    models.Index(
                        fields=["business_registration_date"],
                        name="taxpayers_i_busines_6a753c_idx",
                    ),
                ],
            },
        ),
    ]
