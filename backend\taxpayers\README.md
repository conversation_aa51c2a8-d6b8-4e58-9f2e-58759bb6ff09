# Tax Payers Management System

A comprehensive tax payer registration and management system built with Django and React.

## Features

### Individual Tax Payers
- Personal information (name, nationality, gender, date of birth)
- TIN validation (10-digit Ethiopian format)
- Profile picture upload
- Business classification (sector/sub-sector)
- Tax payer level assignment
- Contact information and address
- File attachment for tax documents

### Organization Tax Payers
- Business information (name, trade name, business type)
- Manager/owner details
- VAT registration (automatic based on business type)
- Capital amount and employee count
- Business classification
- File attachment for organization documents

### Dynamic Business Classification
- Business sectors with hierarchical sub-sectors
- Users can create new sectors and sub-sectors
- Real-time validation and updates

### Professional Features
- Role-based access control (Admin/Manager)
- Comprehensive audit trail
- Statistics dashboard
- Advanced search and filtering
- File integration with document management system

## Installation

### 1. Database Setup
```bash
# Create and apply migrations
python manage.py makemigrations taxpayers
python manage.py migrate

# Or use the setup command
python manage.py setup_taxpayers
```

### 2. Load Initial Data
```bash
# Load sample business sectors, levels, and types
python manage.py loaddata taxpayers/fixtures/initial_data.json
```

### 3. Create Admin User (if needed)
```bash
python manage.py createsuperuser
```

## API Endpoints

### Business Management
- `GET/POST /api/taxpayers/business-sectors/` - Business sectors
- `GET/POST /api/taxpayers/business-sub-sectors/` - Business sub-sectors
- `GET /api/taxpayers/tax-payer-levels/` - Tax payer levels
- `GET /api/taxpayers/organization-business-types/` - Organization types

### Taxpayer Management
- `GET/POST /api/taxpayers/individuals/` - Individual taxpayers
- `GET/POST /api/taxpayers/organizations/` - Organization taxpayers
- `GET /api/taxpayers/statistics/` - Dashboard statistics
- `GET /api/taxpayers/search/` - Cross-model search

## Models

### BusinessSector
- Code, name, description
- Hierarchical sub-sectors
- Audit trail

### BusinessSubSector
- Parent sector relationship
- Code, name, description
- Dynamic creation capability

### TaxPayerLevel
- A/B/C/D classification
- Turnover criteria
- Description

### OrganizationBusinessType
- Business type classification
- VAT registration requirements
- Description

### IndividualTaxPayer
- Personal and business information
- File attachment capability
- Location integration

### OrganizationTaxPayer
- Organization and VAT information
- Manager details
- File attachment capability

## Usage

### Admin Interface
Access the Django admin at `/admin/` to manage:
- Business sectors and sub-sectors
- Tax payer levels
- Organization business types
- Individual and organization taxpayers

### Frontend Interface
Access the React frontend at `/taxpayers` for:
- Statistics dashboard
- Taxpayer registration and management
- Advanced search and filtering
- Dynamic business classification

### API Usage
```python
# Example: Create individual taxpayer
import requests

data = {
    'tin': '1234567890',
    'first_name': 'John',
    'last_name': 'Doe',
    'nationality': 'ET',
    'gender': 'M',
    'date_of_birth': '1985-01-15',
    'tax_payer_level': 'level_id',
    'business_sector': 'sector_id',
    'business_sub_sector': 'subsector_id',
    'business_registration_date': '2020-01-01',
    'phone': '+251911123456'
}

response = requests.post('/api/taxpayers/individuals/', data=data)
```

## File Integration

The system integrates with the document management system:
- Individual taxpayers can be linked to Files containing their documents
- Organization taxpayers can be linked to Files containing their documents
- Files are stored in the physical location hierarchy (Kent → Box → Shelf → Building)

## Validation

### TIN Validation
- Must be exactly 10 digits
- Unique across all taxpayers

### VAT Validation
- Format: ET followed by 10 digits (e.g., ET1234567890)
- Required for certain business types (PLC, SC)
- Automatic validation based on organization type

### Business Classification
- Sub-sectors must belong to selected sector
- Dynamic validation ensures data integrity

## Security

- Role-based access control
- Audit trail for all changes
- UUID primary keys for better security
- Soft delete (records marked inactive)

## Sample Data

The system includes sample data for:
- 5 business sectors (Agriculture, Manufacturing, Services, Trade, Construction)
- Multiple sub-sectors for each sector
- 4 tax payer levels (A, B, C, D)
- 5 organization business types
- Sample individual and organization taxpayers

## Development

### Adding New Business Sectors
Users can dynamically add new business sectors and sub-sectors through:
- Django admin interface
- Frontend forms with "+" button
- API endpoints

### Extending the System
The system is designed for extensibility:
- Add new taxpayer types by extending BaseTaxPayer
- Add new validation rules in model clean() methods
- Extend the API with custom endpoints
- Add new frontend components following the established patterns

## Support

For issues and questions:
1. Check the Django admin for data integrity
2. Review API documentation at `/api/taxpayers/`
3. Check frontend console for JavaScript errors
4. Verify database migrations are applied
