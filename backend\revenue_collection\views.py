"""
Revenue Collection Views

This module provides DRF viewsets for all revenue collection models
with proper permissions, filtering, and CRUD operations.
"""

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.db.models import Sum, Count, Q
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
from .payment_processor import PaymentProcessor

from .models import (
    RegionalCategory, CityServiceCategory,
    RegionalRevenueSource, CityServiceRevenueSource,
    RevenuePeriod,
    RegionalRevenueCollection, CityServiceRevenueCollection,
    RevenueSummary, TaxpayerPaymentSummary
)
from .serializers import (
    RegionalCategorySerializer, CityServiceCategorySerializer,
    RegionalRevenueSourceSerializer, CityServiceRevenueSourceSerializer,
    RevenuePeriodSerializer,
    RegionalRevenueCollectionSerializer, CityServiceRevenueCollectionSerializer,
    RevenueSummarySerializer, TaxpayerPaymentSummarySerializer,
    RegionalCategoryCreateSerializer, CityServiceCategoryCreateSerializer,
    RegionalRevenueSourceCreateSerializer, CityServiceRevenueSourceCreateSerializer,
    RevenuePeriodCreateSerializer,
    RegionalRevenueCollectionCreateSerializer, CityServiceRevenueCollectionCreateSerializer
)


class BaseRevenueViewSet(viewsets.ModelViewSet):
    """Base viewset with common functionality for revenue models"""
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]

    def perform_create(self, serializer):
        """Set created_by to current user"""
        serializer.save(created_by=self.request.user)

    def perform_update(self, serializer):
        """Set last_modified_by to current user for collections"""
        if hasattr(serializer.instance, 'last_modified_by'):
            serializer.save(last_modified_by=self.request.user)
        else:
            serializer.save()


class RegionalCategoryViewSet(BaseRevenueViewSet):
    """ViewSet for Regional Categories"""
    queryset = RegionalCategory.objects.all()
    serializer_class = RegionalCategorySerializer
    filterset_fields = ['is_active']
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['name', 'code', 'created_at']
    ordering = ['code']

    def get_serializer_class(self):
        if self.action in ['create', 'update', 'partial_update']:
            return RegionalCategoryCreateSerializer
        return RegionalCategorySerializer

    @action(detail=True, methods=['get'])
    def revenue_sources(self, request, pk=None):
        """Get all revenue sources for this category"""
        category = self.get_object()
        sources = category.revenue_sources.filter(is_active=True)
        serializer = RegionalRevenueSourceSerializer(sources, many=True)
        return Response(serializer.data)


class CityServiceCategoryViewSet(BaseRevenueViewSet):
    """ViewSet for City Service Categories"""
    queryset = CityServiceCategory.objects.all()
    serializer_class = CityServiceCategorySerializer
    filterset_fields = ['is_active']
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['name', 'code', 'created_at']
    ordering = ['code']

    def get_serializer_class(self):
        if self.action in ['create', 'update', 'partial_update']:
            return CityServiceCategoryCreateSerializer
        return CityServiceCategorySerializer

    @action(detail=True, methods=['get'])
    def revenue_sources(self, request, pk=None):
        """Get all revenue sources for this category"""
        category = self.get_object()
        sources = category.revenue_sources.filter(is_active=True)
        serializer = CityServiceRevenueSourceSerializer(sources, many=True)
        return Response(serializer.data)


class RegionalRevenueSourceViewSet(BaseRevenueViewSet):
    """ViewSet for Regional Revenue Sources"""
    queryset = RegionalRevenueSource.objects.select_related('category')
    serializer_class = RegionalRevenueSourceSerializer
    filterset_fields = ['category', 'is_active']
    search_fields = ['name', 'code', 'description', 'category__name']
    ordering_fields = ['name', 'code', 'created_at']
    ordering = ['category__name', 'code']

    def get_serializer_class(self):
        if self.action in ['create', 'update', 'partial_update']:
            return RegionalRevenueSourceCreateSerializer
        return RegionalRevenueSourceSerializer

    @action(detail=True, methods=['get'])
    def collections(self, request, pk=None):
        """Get all collections for this revenue source"""
        source = self.get_object()
        collections = source.regionalrevenuecollection_set.all()

        # Apply filtering
        period = request.query_params.get('period')
        if period:
            collections = collections.filter(period_id=period)

        serializer = RegionalRevenueCollectionSerializer(collections, many=True)
        return Response(serializer.data)


class CityServiceRevenueSourceViewSet(BaseRevenueViewSet):
    """ViewSet for City Service Revenue Sources"""
    queryset = CityServiceRevenueSource.objects.select_related('category')
    serializer_class = CityServiceRevenueSourceSerializer
    filterset_fields = ['category', 'is_active']
    search_fields = ['name', 'code', 'description', 'category__name']
    ordering_fields = ['name', 'code', 'created_at']
    ordering = ['category__name', 'code']

    def get_serializer_class(self):
        if self.action in ['create', 'update', 'partial_update']:
            return CityServiceRevenueSourceCreateSerializer
        return CityServiceRevenueSourceSerializer

    @action(detail=True, methods=['get'])
    def collections(self, request, pk=None):
        """Get all collections for this revenue source"""
        source = self.get_object()
        collections = source.cityservicerevenuecollection_set.all()

        # Apply filtering
        period = request.query_params.get('period')
        if period:
            collections = collections.filter(period_id=period)

        serializer = CityServiceRevenueCollectionSerializer(collections, many=True)
        return Response(serializer.data)


class RevenuePeriodViewSet(BaseRevenueViewSet):
    """ViewSet for Revenue Periods"""
    queryset = RevenuePeriod.objects.all()
    serializer_class = RevenuePeriodSerializer
    filterset_fields = ['is_closed']
    search_fields = ['name', 'code']
    ordering_fields = ['name', 'start_date', 'end_date', 'created_at']
    ordering = ['-start_date']

    def get_serializer_class(self):
        if self.action in ['create', 'update', 'partial_update']:
            return RevenuePeriodCreateSerializer
        return RevenuePeriodSerializer

    @action(detail=False, methods=['get'])
    def current(self, request):
        """Get the current active period"""
        today = timezone.now().date()
        current_period = RevenuePeriod.objects.filter(
            start_date__lte=today,
            end_date__gte=today
        ).first()

        if current_period:
            serializer = self.get_serializer(current_period)
            return Response(serializer.data)
        else:
            return Response(
                {'detail': 'No current period found'},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=True, methods=['get'])
    def collections_summary(self, request, pk=None):
        """Get collections summary for this period"""
        period = self.get_object()

        # Regional collections summary
        regional_summary = period.regionalrevenuecollection_set.aggregate(
            total_amount=Sum('amount'),
            total_count=Count('id')
        )

        # City service collections summary
        city_summary = period.cityservicerevenuecollection_set.aggregate(
            total_amount=Sum('amount'),
            total_count=Count('id')
        )

        # Category breakdown for regional
        regional_by_category = period.regionalrevenuecollection_set.values(
            'revenue_source__category__name',
            'revenue_source__category__code'
        ).annotate(
            total_amount=Sum('amount'),
            total_count=Count('id')
        )

        # Category breakdown for city service
        city_by_category = period.cityservicerevenuecollection_set.values(
            'revenue_source__category__name',
            'revenue_source__category__code'
        ).annotate(
            total_amount=Sum('amount'),
            total_count=Count('id')
        )

        return Response({
            'period': RevenuePeriodSerializer(period).data,
            'regional': {
                'total_amount': regional_summary['total_amount'] or 0,
                'total_count': regional_summary['total_count'] or 0,
                'by_category': list(regional_by_category)
            },
            'city_service': {
                'total_amount': city_summary['total_amount'] or 0,
                'total_count': city_summary['total_count'] or 0,
                'by_category': list(city_by_category)
            },
            'grand_total': {
                'amount': (regional_summary['total_amount'] or 0) + (city_summary['total_amount'] or 0),
                'count': (regional_summary['total_count'] or 0) + (city_summary['total_count'] or 0)
            }
        })


class BaseRevenueCollectionViewSet(BaseRevenueViewSet):
    """Base viewset for revenue collections"""
    filterset_fields = ['period', 'collection_date', 'recorded_by']
    search_fields = [
        'individual_taxpayer__first_name',
        'individual_taxpayer__last_name',
        'individual_taxpayer__tin',
        'organization_taxpayer__business_name',
        'organization_taxpayer__tin',
        'receipt_number'
    ]
    ordering_fields = ['collection_date', 'amount', 'recorded_date']
    ordering = ['-collection_date', '-recorded_date']

    def perform_create(self, serializer):
        """Set recorded_by to current user"""
        serializer.save(recorded_by=self.request.user)

    def perform_update(self, serializer):
        """Set last_modified_by to current user"""
        serializer.save(last_modified_by=self.request.user)


class RegionalRevenueCollectionViewSet(BaseRevenueCollectionViewSet):
    """ViewSet for Regional Revenue Collections"""
    queryset = RegionalRevenueCollection.objects.select_related(
        'revenue_source', 'revenue_source__category', 'period',
        'individual_taxpayer', 'organization_taxpayer', 'recorded_by'
    )
    serializer_class = RegionalRevenueCollectionSerializer

    def get_queryset(self):
        queryset = super().get_queryset()

        # Additional filtering
        revenue_source = self.request.query_params.get('revenue_source')
        if revenue_source:
            queryset = queryset.filter(revenue_source_id=revenue_source)

        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(revenue_source__category_id=category)

        taxpayer_type = self.request.query_params.get('taxpayer_type')
        if taxpayer_type == 'individual':
            queryset = queryset.filter(individual_taxpayer__isnull=False)
        elif taxpayer_type == 'organization':
            queryset = queryset.filter(organization_taxpayer__isnull=False)

        return queryset

    def get_serializer_class(self):
        if self.action in ['create', 'update', 'partial_update']:
            return RegionalRevenueCollectionCreateSerializer
        return RegionalRevenueCollectionSerializer

    @action(detail=True, methods=['post'])
    def process_payment(self, request, pk=None):
        """Process a payment for this collection"""
        collection = self.get_object()

        try:
            payment_amount = Decimal(str(request.data.get('payment_amount', 0)))
            payment_date = request.data.get('payment_date')

            if payment_amount <= 0:
                return Response(
                    {'error': 'Payment amount must be greater than 0'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            result = PaymentProcessor.process_payment(
                collection, payment_amount, payment_date
            )

            return Response(result, status=status.HTTP_200_OK)

        except (ValueError, TypeError) as e:
            return Response(
                {'error': f'Invalid payment amount: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {'error': f'Payment processing failed: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def calculate_penalties(self, request, pk=None):
        """Calculate and update penalties and interest"""
        collection = self.get_object()

        try:
            result = PaymentProcessor.calculate_penalties_and_interest(collection)
            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {'error': f'Penalty calculation failed: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get'])
    def payment_breakdown(self, request, pk=None):
        """Get detailed payment breakdown"""
        collection = self.get_object()

        try:
            breakdown = PaymentProcessor.get_payment_breakdown(collection)
            return Response(breakdown, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {'error': f'Failed to get payment breakdown: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def simulate_payment(self, request, pk=None):
        """Simulate a payment without processing it"""
        collection = self.get_object()

        try:
            payment_amount = Decimal(str(request.data.get('payment_amount', 0)))

            if payment_amount <= 0:
                return Response(
                    {'error': 'Payment amount must be greater than 0'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            simulation = PaymentProcessor.simulate_payment(collection, payment_amount)
            return Response(simulation, status=status.HTTP_200_OK)

        except (ValueError, TypeError) as e:
            return Response(
                {'error': f'Invalid payment amount: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {'error': f'Payment simulation failed: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CityServiceRevenueCollectionViewSet(BaseRevenueCollectionViewSet):
    """ViewSet for City Service Revenue Collections"""
    queryset = CityServiceRevenueCollection.objects.select_related(
        'revenue_source', 'revenue_source__category', 'period',
        'individual_taxpayer', 'organization_taxpayer', 'recorded_by'
    )
    serializer_class = CityServiceRevenueCollectionSerializer

    def get_queryset(self):
        queryset = super().get_queryset()

        # Additional filtering
        revenue_source = self.request.query_params.get('revenue_source')
        if revenue_source:
            queryset = queryset.filter(revenue_source_id=revenue_source)

        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(revenue_source__category_id=category)

        taxpayer_type = self.request.query_params.get('taxpayer_type')
        if taxpayer_type == 'individual':
            queryset = queryset.filter(individual_taxpayer__isnull=False)
        elif taxpayer_type == 'organization':
            queryset = queryset.filter(organization_taxpayer__isnull=False)

        return queryset

    def get_serializer_class(self):
        if self.action in ['create', 'update', 'partial_update']:
            return CityServiceRevenueCollectionCreateSerializer
        return CityServiceRevenueCollectionSerializer

    @action(detail=True, methods=['post'])
    def process_payment(self, request, pk=None):
        """Process a payment for this collection"""
        collection = self.get_object()

        try:
            payment_amount = Decimal(str(request.data.get('payment_amount', 0)))
            payment_date = request.data.get('payment_date')

            if payment_amount <= 0:
                return Response(
                    {'error': 'Payment amount must be greater than 0'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            result = PaymentProcessor.process_payment(
                collection, payment_amount, payment_date
            )

            return Response(result, status=status.HTTP_200_OK)

        except (ValueError, TypeError) as e:
            return Response(
                {'error': f'Invalid payment amount: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {'error': f'Payment processing failed: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def calculate_penalties(self, request, pk=None):
        """Calculate and update penalties and interest"""
        collection = self.get_object()

        try:
            result = PaymentProcessor.calculate_penalties_and_interest(collection)
            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {'error': f'Penalty calculation failed: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get'])
    def payment_breakdown(self, request, pk=None):
        """Get detailed payment breakdown"""
        collection = self.get_object()

        try:
            breakdown = PaymentProcessor.get_payment_breakdown(collection)
            return Response(breakdown, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {'error': f'Failed to get payment breakdown: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def simulate_payment(self, request, pk=None):
        """Simulate a payment without processing it"""
        collection = self.get_object()

        try:
            payment_amount = Decimal(str(request.data.get('payment_amount', 0)))

            if payment_amount <= 0:
                return Response(
                    {'error': 'Payment amount must be greater than 0'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            simulation = PaymentProcessor.simulate_payment(collection, payment_amount)
            return Response(simulation, status=status.HTTP_200_OK)

        except (ValueError, TypeError) as e:
            return Response(
                {'error': f'Invalid payment amount: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {'error': f'Payment simulation failed: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class RevenueSummaryViewSet(BaseRevenueViewSet):
    """ViewSet for Revenue Summaries"""
    queryset = RevenueSummary.objects.select_related(
        'period', 'country', 'region', 'zone', 'city', 'subcity', 'kebele'
    )
    serializer_class = RevenueSummarySerializer
    filterset_fields = ['period', 'region', 'city', 'subcity', 'kebele']
    search_fields = [
        'period__name',
        'region__name',
        'city__name',
        'subcity__name',
        'kebele__name'
    ]
    ordering_fields = ['period__start_date', 'grand_total', 'last_updated']
    ordering = ['-period__start_date', '-last_updated']

    @action(detail=False, methods=['post'])
    def bulk_update_overdue(self, request):
        """Bulk update all overdue collections with penalties and interest"""
        try:
            result = PaymentProcessor.bulk_update_overdue_collections()
            return Response({
                'message': 'Overdue collections updated successfully',
                'updated_count': result['updated_count'],
                'total_penalty_added': str(result['total_penalty_added']),
                'total_interest_added': str(result['total_interest_added']),
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {'error': f'Bulk update failed: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def generate_summaries(self, request):
        """Generate revenue summaries for a specific period and location"""
        period_id = request.data.get('period_id')
        location_level = request.data.get('location_level', 'region')  # region, city, subcity, kebele
        location_id = request.data.get('location_id')

        if not period_id:
            return Response(
                {'error': 'period_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            period = RevenuePeriod.objects.get(id=period_id)
        except RevenuePeriod.DoesNotExist:
            return Response(
                {'error': 'Period not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        summaries_created = 0

        if location_level == 'region' and location_id:
            # Generate summary for specific region
            from locations.location_hierarchy_models import Region
            try:
                region = Region.objects.get(id=location_id)
                summary, created = RevenueSummary.objects.get_or_create(
                    period=period,
                    region=region,
                    defaults={'created_by': request.user}
                )
                summary.update_totals()
                summaries_created += 1
            except Region.DoesNotExist:
                return Response(
                    {'error': 'Region not found'},
                    status=status.HTTP_404_NOT_FOUND
                )

        elif location_level == 'city' and location_id:
            # Generate summary for specific city
            from locations.location_hierarchy_models import City
            try:
                city = City.objects.get(id=location_id)
                summary, created = RevenueSummary.objects.get_or_create(
                    period=period,
                    region=city.zone.region,
                    zone=city.zone,
                    city=city,
                    defaults={'created_by': request.user}
                )
                summary.update_totals()
                summaries_created += 1
            except City.DoesNotExist:
                return Response(
                    {'error': 'City not found'},
                    status=status.HTTP_404_NOT_FOUND
                )

        elif location_level == 'all':
            # Generate summaries for all regions
            from locations.location_hierarchy_models import Region
            regions = Region.objects.filter(is_active=True)

            for region in regions:
                summary, created = RevenueSummary.objects.get_or_create(
                    period=period,
                    region=region,
                    defaults={'created_by': request.user}
                )
                summary.update_totals()
                summaries_created += 1

        return Response({
            'message': f'Generated {summaries_created} revenue summaries',
            'period': period.name,
            'summaries_created': summaries_created
        })

    @action(detail=True, methods=['post'])
    def update_totals(self, request, pk=None):
        """Update totals for a specific summary"""
        summary = self.get_object()
        summary.update_totals()

        return Response({
            'message': 'Summary totals updated successfully',
            'summary': RevenueSummarySerializer(summary).data
        })

    @action(detail=False, methods=['get'])
    def analytics(self, request):
        """Get revenue analytics across periods and locations"""
        # Get query parameters
        period_ids = request.query_params.getlist('periods')
        region_id = request.query_params.get('region')
        city_id = request.query_params.get('city')

        queryset = self.get_queryset()

        # Apply filters
        if period_ids:
            queryset = queryset.filter(period_id__in=period_ids)
        if region_id:
            queryset = queryset.filter(region_id=region_id)
        if city_id:
            queryset = queryset.filter(city_id=city_id)

        # Aggregate data
        analytics = queryset.aggregate(
            total_regional=Sum('regional_total'),
            total_city_service=Sum('city_service_total'),
            total_grand=Sum('grand_total'),
            count=Count('id')
        )

        # Period-wise breakdown
        period_breakdown = queryset.values(
            'period__name',
            'period__start_date',
            'period__end_date'
        ).annotate(
            regional_total=Sum('regional_total'),
            city_service_total=Sum('city_service_total'),
            grand_total=Sum('grand_total')
        ).order_by('-period__start_date')

        # Location-wise breakdown
        location_breakdown = queryset.values(
            'region__name',
            'city__name'
        ).annotate(
            regional_total=Sum('regional_total'),
            city_service_total=Sum('city_service_total'),
            grand_total=Sum('grand_total')
        ).order_by('-grand_total')

        return Response({
            'summary': analytics,
            'period_breakdown': list(period_breakdown),
            'location_breakdown': list(location_breakdown)
        })


class TaxpayerPaymentSummaryViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing taxpayer payment summaries

    Provides CRUD operations for taxpayer payment summaries with filtering
    by taxpayer type, period, and payment status.
    """
    queryset = TaxpayerPaymentSummary.objects.all()
    serializer_class = TaxpayerPaymentSummarySerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = [
        'individual_taxpayer', 'organization_taxpayer', 'period',
        'total_overdue', 'overdue_collections'
    ]
    search_fields = [
        'individual_taxpayer__first_name', 'individual_taxpayer__last_name', 'individual_taxpayer__tin',
        'organization_taxpayer__business_name', 'organization_taxpayer__tin'
    ]
    ordering_fields = [
        'period__start_date', 'total_assessed', 'total_paid',
        'total_outstanding', 'total_overdue', 'payment_completion_rate',
        'created_at', 'updated_at'
    ]
    ordering = ['-period__start_date', '-total_overdue']

    def perform_create(self, serializer):
        """Set the created_by field to the current user"""
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def update_summary(self, request, pk=None):
        """Update payment summary from related collections"""
        summary = self.get_object()
        summary.update_summary()
        summary.save()

        serializer = self.get_serializer(summary)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def overdue_taxpayers(self, request):
        """Get taxpayers with overdue payments"""
        overdue_summaries = self.queryset.filter(total_overdue__gt=0)

        # Apply additional filters if provided
        period = request.query_params.get('period')
        if period:
            overdue_summaries = overdue_summaries.filter(period=period)

        page = self.paginate_queryset(overdue_summaries)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(overdue_summaries, many=True)
        return Response(serializer.data)
