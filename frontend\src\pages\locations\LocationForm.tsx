import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box as MuiBox,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Alert,
  Stepper,
  Step,
  StepLabel,
  Paper,
  Divider,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Save,
  Cancel,
  Business,
  Shelves,
  Inventory,
  ArrowBack,
  Add,
  Remove,
  ColorLens,
  QrCode,
} from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import locationService from '../../services/locationService';
import organizationService from '../../services/organizationService';
import { normalizeCode, createCodeBlurHandler } from '../../utils/codeUtils';
import type {
  Building,
  BuildingCreate,
  Shelf,
  ShelfCreate,
  Box,
  BoxCreate,
  Kent,
  KentCreate
} from '../../services/locationService';
import type { Organization } from '../../services/types';

interface LocationFormProps {
  mode: 'create' | 'edit';
  type?: 'building' | 'shelf' | 'box' | 'kent';
}

const steps = ['Select Type', 'Basic Information', 'Configuration', 'Review'];

const LocationForm: React.FC<LocationFormProps> = ({ mode, type: initialType }) => {
  const navigate = useNavigate();
  const { id } = useParams();
  const { showSuccess, showError } = useNotification();

  const [activeStep, setActiveStep] = useState(initialType ? 1 : 0);
  const [locationType, setLocationType] = useState<'building' | 'shelf' | 'box' | 'kent'>(initialType || 'building');
  const [loading, setLoading] = useState(false);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [buildings, setBuildings] = useState<Building[]>([]);
  const [shelves, setShelves] = useState<Shelf[]>([]);
  const [boxes, setBoxes] = useState<Box[]>([]);

  // Form data states
  const [buildingData, setBuildingData] = useState<BuildingCreate>({
    organization: 0,
    name: '',
    code: '',
    description: '',
    address: '',
  });

  const [shelfData, setShelfData] = useState<ShelfCreate & { rows?: number; columns?: number }>({
    building: 0,
    name: '',
    code: '',
    description: '',
    rows: 5,
    columns: 10,
  });

  const [boxData, setBoxData] = useState<BoxCreate>({
    shelf: 0,
    row: 1,
    column: 1,
    name: '',
    description: '',
    color: '',
    material: '',
  });

  const [kentData, setKentData] = useState<KentCreate>({
    box: 0,
    name: '',
    code: '',
    description: '',
    capacity: 100,
    color: '',
    material: 'Cardboard',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    loadInitialData();
    if (mode === 'edit' && id) {
      loadExistingData();
    }
  }, [mode, id, locationType]);

  const loadInitialData = async () => {
    try {
      const [orgsData, buildingsData] = await Promise.all([
        organizationService.getOrganizations(),
        locationService.getBuildings(),
      ]);
      setOrganizations(orgsData.results);
      setBuildings(buildingsData.results);
    } catch (err) {
      showError('Failed to load initial data');
    }
  };

  const loadExistingData = async () => {
    if (!id) return;

    try {
      setLoading(true);
      if (locationType === 'building') {
        const building = await locationService.getBuilding(parseInt(id));
        setBuildingData({
          organization: building.organization,
          name: building.name,
          code: building.code,
          description: building.description || '',
          address: building.address || '',
        });
      } else if (locationType === 'shelf') {
        const shelf = await locationService.getShelf(parseInt(id));
        setShelfData({
          building: shelf.building,
          name: shelf.name,
          code: shelf.code,
          description: shelf.description || '',
          capacity: shelf.capacity || 50,
        });
      } else if (locationType === 'kent') {
        const kent = await locationService.getKent(parseInt(id));
        setKentData({
          shelf: kent.shelf,
          name: kent.name,
          code: kent.code,
          description: kent.description || '',
          capacity: kent.capacity || 100,
          color: kent.color || '',
          material: kent.material || 'Cardboard',
        });
      }
    } catch (err) {
      showError('Failed to load existing data');
    } finally {
      setLoading(false);
    }
  };

  const loadShelves = async (buildingId: number) => {
    try {
      const shelvesData = await locationService.getBuildingShelves(buildingId);
      setShelves(shelvesData);
    } catch (err) {
      showError('Failed to load shelves');
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (locationType === 'building') {
      if (!buildingData.organization) newErrors.organization = 'Organization is required';
      if (!buildingData.name.trim()) newErrors.name = 'Building name is required';
      if (!buildingData.code.trim()) newErrors.code = 'Building code is required';
      
      const codeValidation = locationService.validateCode(buildingData.code);
      if (!codeValidation.isValid) newErrors.code = codeValidation.error || 'Invalid code';
    } else if (locationType === 'shelf') {
      if (!shelfData.building) newErrors.building = 'Building is required';
      if (!shelfData.name.trim()) newErrors.name = 'Shelf name is required';
      if (!shelfData.code.trim()) newErrors.code = 'Shelf code is required';
      
      const codeValidation = locationService.validateCode(shelfData.code);
      if (!codeValidation.isValid) newErrors.code = codeValidation.error || 'Invalid code';
    } else if (locationType === 'kent') {
      if (!kentData.shelf) newErrors.shelf = 'Shelf is required';
      if (!kentData.name.trim()) newErrors.name = 'Kent name is required';
      if (!kentData.code.trim()) newErrors.code = 'Kent code is required';
      
      const codeValidation = locationService.validateCode(kentData.code);
      if (!codeValidation.isValid) newErrors.code = codeValidation.error || 'Invalid code';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);
      
      if (locationType === 'building') {
        if (mode === 'create') {
          await locationService.createBuilding(buildingData);
          showSuccess('Building created successfully');
        } else {
          await locationService.updateBuilding(parseInt(id!), buildingData);
          showSuccess('Building updated successfully');
        }
      } else if (locationType === 'shelf') {
        if (mode === 'create') {
          await locationService.createShelf(shelfData);
          showSuccess('Shelf created successfully');
        } else {
          await locationService.updateShelf(parseInt(id!), shelfData);
          showSuccess('Shelf updated successfully');
        }
      } else if (locationType === 'kent') {
        if (mode === 'create') {
          await locationService.createKent(kentData);
          showSuccess('Kent created successfully');
        } else {
          await locationService.updateKent(parseInt(id!), kentData);
          showSuccess('Kent updated successfully');
        }
      }

      navigate('/locations');
    } catch (err: any) {
      showError(err.response?.data?.message || `Failed to ${mode} ${locationType}`);
    } finally {
      setLoading(false);
    }
  };

  const handleNext = () => {
    if (activeStep === 0 && !initialType) {
      setActiveStep(1);
    } else if (validateForm()) {
      setActiveStep((prev) => prev + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prev) => prev - 1);
  };

  const renderTypeSelection = () => (
    <Grid container spacing={3}>
      {[
        { type: 'building', icon: <Business />, title: 'Building', description: 'Create a new building to house shelves' },
        { type: 'shelf', icon: <Shelves />, title: 'Shelf', description: 'Create a shelf within a building' },
        { type: 'kent', icon: <Inventory />, title: 'Kent (Box)', description: 'Create a kent within a shelf' },
      ].map((option) => (
        <Grid size={{ xs: 12, md: 4 }} key={option.type}>
          <Card
            sx={{
              cursor: 'pointer',
              border: locationType === option.type ? 2 : 1,
              borderColor: locationType === option.type ? 'primary.main' : 'divider',
              '&:hover': { borderColor: 'primary.main' },
            }}
            onClick={() => setLocationType(option.type as any)}
          >
            <CardContent sx={{ textAlign: 'center', p: 3 }}>
              <MuiBox sx={{ color: 'primary.main', mb: 2 }}>
                {React.cloneElement(option.icon, { sx: { fontSize: 48 } })}
              </MuiBox>
              <Typography variant="h6" gutterBottom>
                {option.title}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {option.description}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );

  const renderBuildingForm = () => (
    <Grid container spacing={3}>
      <Grid size={{ xs: 12 }}>
        <FormControl fullWidth error={!!errors.organization}>
          <InputLabel>Organization</InputLabel>
          <Select
            value={buildingData.organization}
            onChange={(e) => setBuildingData({ ...buildingData, organization: e.target.value as number })}
            label="Organization"
          >
            {organizations.map((org) => (
              <MenuItem key={org.id} value={org.id}>
                {org.name}
              </MenuItem>
            ))}
          </Select>
          {errors.organization && <Typography variant="caption" color="error">{errors.organization}</Typography>}
        </FormControl>
      </Grid>
      <Grid size={{ xs: 12, md: 6 }}>
        <TextField
          fullWidth
          label="Building Name"
          value={buildingData.name}
          onChange={(e) => setBuildingData({ ...buildingData, name: e.target.value })}
          error={!!errors.name}
          helperText={errors.name}
        />
      </Grid>
      <Grid size={{ xs: 12, md: 6 }}>
        <TextField
          fullWidth
          label="Building Code"
          value={buildingData.code}
          onChange={(e) => setBuildingData({ ...buildingData, code: e.target.value })}
          onBlur={createCodeBlurHandler(
            (field, value) => setBuildingData(prev => ({ ...prev, [field]: value })),
            'code'
          )}
          error={!!errors.code}
          helperText={errors.code || 'Code will be automatically converted to uppercase'}
        />
      </Grid>
      <Grid size={{ xs: 12 }}>
        <TextField
          fullWidth
          label="Description"
          multiline
          rows={3}
          value={buildingData.description}
          onChange={(e) => setBuildingData({ ...buildingData, description: e.target.value })}
        />
      </Grid>
      <Grid size={{ xs: 12 }}>
        <TextField
          fullWidth
          label="Address"
          multiline
          rows={2}
          value={buildingData.address}
          onChange={(e) => setBuildingData({ ...buildingData, address: e.target.value })}
        />
      </Grid>
    </Grid>
  );

  const renderShelfForm = () => (
    <Grid container spacing={3}>
      <Grid size={{ xs: 12 }}>
        <FormControl fullWidth error={!!errors.building}>
          <InputLabel>Building</InputLabel>
          <Select
            value={shelfData.building}
            onChange={(e) => {
              const buildingId = e.target.value as number;
              setShelfData({ ...shelfData, building: buildingId });
              if (buildingId) loadShelves(buildingId);
            }}
            label="Building"
          >
            {buildings.map((building) => (
              <MenuItem key={building.id} value={building.id}>
                {building.name} ({building.code}) - {building.organization_name}
              </MenuItem>
            ))}
          </Select>
          {errors.building && <Typography variant="caption" color="error">{errors.building}</Typography>}
        </FormControl>
      </Grid>
      <Grid size={{ xs: 12, md: 6 }}>
        <TextField
          fullWidth
          label="Shelf Name"
          value={shelfData.name}
          onChange={(e) => setShelfData({ ...shelfData, name: e.target.value })}
          error={!!errors.name}
          helperText={errors.name}
        />
      </Grid>
      <Grid size={{ xs: 12, md: 6 }}>
        <TextField
          fullWidth
          label="Shelf Code"
          value={shelfData.code}
          onChange={(e) => setShelfData({ ...shelfData, code: e.target.value.toUpperCase() })}
          error={!!errors.code}
          helperText={errors.code}
        />
      </Grid>
      <Grid size={{ xs: 12, md: 4 }}>
        <TextField
          fullWidth
          label="Rows"
          type="number"
          value={shelfData.rows || 5}
          onChange={(e) => {
            const rows = parseInt(e.target.value) || 5;
            setShelfData({
              ...shelfData,
              rows,
              capacity: rows * (shelfData.columns || 10)
            });
          }}
          inputProps={{ min: 1, max: 20 }}
        />
      </Grid>
      <Grid size={{ xs: 12, md: 4 }}>
        <TextField
          fullWidth
          label="Columns"
          type="number"
          value={shelfData.columns || 10}
          onChange={(e) => {
            const columns = parseInt(e.target.value) || 10;
            setShelfData({
              ...shelfData,
              columns,
              capacity: (shelfData.rows || 5) * columns
            });
          }}
          inputProps={{ min: 1, max: 50 }}
        />
      </Grid>
      <Grid size={{ xs: 12, md: 4 }}>
        <TextField
          fullWidth
          label="Capacity (Auto-calculated)"
          value={shelfData.capacity}
          disabled
          helperText={`${shelfData.rows || 5} rows × ${shelfData.columns || 10} columns`}
        />
      </Grid>
      <Grid size={{ xs: 12 }}>
        <TextField
          fullWidth
          label="Description"
          multiline
          rows={3}
          value={shelfData.description}
          onChange={(e) => setShelfData({ ...shelfData, description: e.target.value })}
        />
      </Grid>
    </Grid>
  );

  const renderKentForm = () => (
    <Grid container spacing={3}>
      <Grid size={{ xs: 12 }}>
        <FormControl fullWidth error={!!errors.shelf}>
          <InputLabel>Shelf</InputLabel>
          <Select
            value={kentData.shelf}
            onChange={(e) => setKentData({ ...kentData, shelf: e.target.value as number })}
            label="Shelf"
          >
            {shelves.map((shelf) => (
              <MenuItem key={shelf.id} value={shelf.id}>
                {shelf.full_code} - {shelf.name} ({shelf.building_name})
              </MenuItem>
            ))}
          </Select>
          {errors.shelf && <Typography variant="caption" color="error">{errors.shelf}</Typography>}
        </FormControl>
      </Grid>
      <Grid size={{ xs: 12, md: 6 }}>
        <TextField
          fullWidth
          label="Kent Name"
          value={kentData.name}
          onChange={(e) => setKentData({ ...kentData, name: e.target.value })}
          error={!!errors.name}
          helperText={errors.name}
        />
      </Grid>
      <Grid size={{ xs: 12, md: 6 }}>
        <TextField
          fullWidth
          label="Kent Code"
          value={kentData.code}
          onChange={(e) => setKentData({ ...kentData, code: e.target.value.toUpperCase() })}
          error={!!errors.code}
          helperText={errors.code}
        />
      </Grid>
      <Grid size={{ xs: 12, md: 3 }}>
        <TextField
          fullWidth
          label="Row"
          type="number"
          value={kentData.row || 1}
          onChange={(e) => setKentData({ ...kentData, row: parseInt(e.target.value) || 1 })}
          inputProps={{ min: 1 }}
        />
      </Grid>
      <Grid size={{ xs: 12, md: 3 }}>
        <TextField
          fullWidth
          label="Column"
          type="number"
          value={kentData.column || 1}
          onChange={(e) => setKentData({ ...kentData, column: parseInt(e.target.value) || 1 })}
          inputProps={{ min: 1 }}
        />
      </Grid>
      <Grid size={{ xs: 12, md: 3 }}>
        <TextField
          fullWidth
          label="Capacity"
          type="number"
          value={kentData.capacity}
          onChange={(e) => setKentData({ ...kentData, capacity: parseInt(e.target.value) || 100 })}
          inputProps={{ min: 1 }}
        />
      </Grid>
      <Grid size={{ xs: 12, md: 3 }}>
        <FormControl fullWidth>
          <InputLabel>Material</InputLabel>
          <Select
            value={kentData.material}
            onChange={(e) => setKentData({ ...kentData, material: e.target.value })}
            label="Material"
          >
            {locationService.getMaterialOptions().map((material) => (
              <MenuItem key={material.value} value={material.value}>
                {material.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid size={{ xs: 12 }}>
        <Typography variant="subtitle2" gutterBottom>
          Color
        </Typography>
        <MuiBox sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {locationService.getColorOptions().map((color) => (
            <Tooltip key={color.value} title={color.label}>
              <IconButton
                onClick={() => setKentData({ ...kentData, color: color.value })}
                sx={{
                  bgcolor: color.color,
                  border: kentData.color === color.value ? 3 : 1,
                  borderColor: kentData.color === color.value ? 'primary.main' : 'divider',
                  width: 40,
                  height: 40,
                  '&:hover': { bgcolor: color.color, opacity: 0.8 },
                }}
              >
                {kentData.color === color.value && <ColorLens sx={{ color: 'white' }} />}
              </IconButton>
            </Tooltip>
          ))}
        </MuiBox>
      </Grid>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Description"
          multiline
          rows={3}
          value={kentData.description}
          onChange={(e) => setKentData({ ...kentData, description: e.target.value })}
        />
      </Grid>
    </Grid>
  );

  return (
    <MuiBox sx={{ p: 3 }}>
      <MuiBox sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 2 }}>
        <IconButton onClick={() => navigate('/locations')}>
          <ArrowBack />
        </IconButton>
        <Typography variant="h4" component="h1">
          {mode === 'create' ? 'Create' : 'Edit'} Location
        </Typography>
      </MuiBox>

      <Card>
        <CardContent>
          {!initialType && (
            <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
              {steps.map((label) => (
                <Step key={label}>
                  <StepLabel>{label}</StepLabel>
                </Step>
              ))}
            </Stepper>
          )}

          {activeStep === 0 && renderTypeSelection()}
          {activeStep === 1 && locationType === 'building' && renderBuildingForm()}
          {activeStep === 1 && locationType === 'shelf' && renderShelfForm()}
          {activeStep === 1 && locationType === 'kent' && renderKentForm()}

          <MuiBox sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
            <Button
              onClick={activeStep === 0 ? () => navigate('/locations') : handleBack}
              startIcon={<Cancel />}
            >
              {activeStep === 0 ? 'Cancel' : 'Back'}
            </Button>
            
            <Button
              variant="contained"
              onClick={activeStep === steps.length - 1 ? handleSubmit : handleNext}
              disabled={loading}
              startIcon={activeStep === steps.length - 1 ? <Save /> : undefined}
            >
              {loading ? 'Processing...' : activeStep === steps.length - 1 ? 'Save' : 'Next'}
            </Button>
          </MuiBox>
        </CardContent>
      </Card>
    </MuiBox>
  );
};

export default LocationForm;
