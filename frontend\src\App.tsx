import React from 'react';
import { BrowserRouter as Router } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import CssBaseline from '@mui/material/CssBaseline';
import { AuthProvider } from './contexts/AuthContext';
import { NotificationProvider } from './contexts/NotificationContext';
import { BrandingProvider } from './contexts/BrandingContext';
import { DynamicThemeProvider } from './components/theme/DynamicThemeProvider';
import AppRoutes from './routes/AppRoutes';
// import DebugConsole from './components/common/DebugConsole';
import './App.css';

// Theme is now created dynamically in DynamicThemeProvider

// Create React Query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

function App() {

  return (
    <QueryClientProvider client={queryClient}>
      <BrandingProvider>
        <DynamicThemeProvider>
          <CssBaseline />
          <Router>
            <AuthProvider>
              <NotificationProvider>
                <AppRoutes />
                {/* Debug console temporarily disabled to prevent memory leak */}
                {/* {import.meta.env.DEV && <DebugConsole />} */}
              </NotificationProvider>
            </AuthProvider>
          </Router>
        </DynamicThemeProvider>
      </BrandingProvider>
    </QueryClientProvider>
  );
}

export default App;
