"""
Views for Location Hierarchy Management
"""

from rest_framework import generics, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.db.models import Q
from .location_hierarchy_models import Country, Region, Zone, City, SubCity, Kebele, SpecialLocation
from .location_hierarchy_serializers import (
    CountrySerializer, CountryCreateSerializer, CountrySelectSerializer,
    RegionSerializer, RegionCreateSerializer, RegionSelectSerializer,
    ZoneSerializer, ZoneCreateSerializer, ZoneSelectSerializer,
    CitySerializer, CityCreateSerializer, CitySelectSerializer,
    SubCitySerializer, SubCityCreateSerializer, SubCitySelectSerializer,
    KebeleSerializer, KebeleCreateSerializer, KebeleSelectSerializer,
    SpecialLocationSerializer, SpecialLocationCreateSerializer, SpecialLocationSelectSerializer,
)


# Country Views
class CountryListCreateView(generics.ListCreateAPIView):
    """List and create countries"""
    queryset = Country.objects.all()
    permission_classes = [permissions.AllowAny]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return CountryCreateSerializer
        return CountrySerializer

    def get_queryset(self):
        queryset = Country.objects.all()
        search = self.request.query_params.get('search', None)
        is_active = self.request.query_params.get('is_active', None)

        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) | Q(code__icontains=search)
            )
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        return queryset.order_by('name')


class CountryDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, and delete country"""
    queryset = Country.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return CountryCreateSerializer
        return CountrySerializer


# Region Views
class RegionListCreateView(generics.ListCreateAPIView):
    """List and create regions"""
    queryset = Region.objects.select_related('country')
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return RegionCreateSerializer
        return RegionSerializer

    def get_queryset(self):
        queryset = Region.objects.select_related('country')
        search = self.request.query_params.get('search', None)
        country = self.request.query_params.get('country', None)
        is_active = self.request.query_params.get('is_active', None)

        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) | Q(code__icontains=search) |
                Q(country__name__icontains=search)
            )
        if country:
            queryset = queryset.filter(country=country)
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        return queryset.order_by('country__name', 'name')


class RegionDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, and delete region"""
    queryset = Region.objects.select_related('country')
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return RegionCreateSerializer
        return RegionSerializer


# Zone Views
class ZoneListCreateView(generics.ListCreateAPIView):
    """List and create zones"""
    queryset = Zone.objects.select_related('region__country')
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return ZoneCreateSerializer
        return ZoneSerializer

    def get_queryset(self):
        queryset = Zone.objects.select_related('region__country')
        search = self.request.query_params.get('search', None)
        region = self.request.query_params.get('region', None)
        is_active = self.request.query_params.get('is_active', None)

        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) | Q(code__icontains=search) |
                Q(region__name__icontains=search)
            )
        if region:
            queryset = queryset.filter(region=region)
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        return queryset.order_by('region__name', 'name')


class ZoneDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, and delete zone"""
    queryset = Zone.objects.select_related('region__country')
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return ZoneCreateSerializer
        return ZoneSerializer


# City Views
class CityListCreateView(generics.ListCreateAPIView):
    """List and create cities"""
    queryset = City.objects.select_related('zone__region__country')
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return CityCreateSerializer
        return CitySerializer

    def get_queryset(self):
        queryset = City.objects.select_related('zone__region__country')
        search = self.request.query_params.get('search', None)
        zone = self.request.query_params.get('zone', None)
        is_active = self.request.query_params.get('is_active', None)

        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) | Q(code__icontains=search) |
                Q(zone__name__icontains=search)
            )
        if zone:
            queryset = queryset.filter(zone=zone)
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        return queryset.order_by('zone__name', 'name')


class CityDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, and delete city"""
    queryset = City.objects.select_related('zone__region__country')
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return CityCreateSerializer
        return CitySerializer


# SubCity Views
class SubCityListCreateView(generics.ListCreateAPIView):
    """List and create subcities"""
    queryset = SubCity.objects.select_related('city__zone__region__country')
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return SubCityCreateSerializer
        return SubCitySerializer

    def get_queryset(self):
        queryset = SubCity.objects.select_related('city__zone__region__country')
        search = self.request.query_params.get('search', None)
        city = self.request.query_params.get('city', None)
        type_filter = self.request.query_params.get('type', None)
        is_active = self.request.query_params.get('is_active', None)

        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) | Q(code__icontains=search) |
                Q(city__name__icontains=search)
            )
        if city:
            queryset = queryset.filter(city=city)
        if type_filter:
            queryset = queryset.filter(type=type_filter)
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        return queryset.order_by('city__name', 'name')


class SubCityDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, and delete subcity"""
    queryset = SubCity.objects.select_related('city__zone__region__country')
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return SubCityCreateSerializer
        return SubCitySerializer


# Kebele Views
class KebeleListCreateView(generics.ListCreateAPIView):
    """List and create kebeles"""
    queryset = Kebele.objects.select_related('subcity__city__zone__region__country')
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return KebeleCreateSerializer
        return KebeleSerializer

    def get_queryset(self):
        queryset = Kebele.objects.select_related('subcity__city__zone__region__country')
        search = self.request.query_params.get('search', None)
        subcity = self.request.query_params.get('subcity', None)
        is_active = self.request.query_params.get('is_active', None)

        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) | Q(code__icontains=search) |
                Q(subcity__name__icontains=search) | Q(number__icontains=search)
            )
        if subcity:
            queryset = queryset.filter(subcity=subcity)
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        return queryset.order_by('subcity__name', 'number', 'name')


class KebeleDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, and delete kebele"""
    queryset = Kebele.objects.select_related('subcity__city__zone__region__country')
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return KebeleCreateSerializer
        return KebeleSerializer


# Select/Dropdown Views
@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def countries_select(request):
    """Get countries for dropdown/select components"""
    countries = Country.objects.filter(is_active=True).order_by('name')
    serializer = CountrySelectSerializer(countries, many=True)
    return Response(serializer.data)


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def regions_select(request):
    """Get regions for dropdown/select components"""
    country_id = request.query_params.get('country')
    regions = Region.objects.filter(is_active=True)
    if country_id:
        regions = regions.filter(country=country_id)
    regions = regions.order_by('name')
    serializer = RegionSelectSerializer(regions, many=True)
    return Response(serializer.data)


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def zones_select(request):
    """Get zones for dropdown/select components"""
    region_id = request.query_params.get('region')
    zones = Zone.objects.filter(is_active=True)
    if region_id:
        zones = zones.filter(region=region_id)
    zones = zones.order_by('name')
    serializer = ZoneSelectSerializer(zones, many=True)
    return Response(serializer.data)


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def cities_select(request):
    """Get cities for dropdown/select components"""
    zone_id = request.query_params.get('zone')
    region_id = request.query_params.get('region')
    cities = City.objects.filter(is_active=True)

    if zone_id:
        cities = cities.filter(zone=zone_id)
    elif region_id:
        # Filter cities by region (via zone relationship)
        cities = cities.filter(zone__region=region_id)

    cities = cities.order_by('name')
    serializer = CitySelectSerializer(cities, many=True)
    return Response(serializer.data)


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def subcities_select(request):
    """Get subcities for dropdown/select components"""
    city_id = request.query_params.get('city')
    subcities = SubCity.objects.filter(is_active=True)
    if city_id:
        subcities = subcities.filter(city=city_id)
    subcities = subcities.order_by('name')
    serializer = SubCitySelectSerializer(subcities, many=True)
    return Response(serializer.data)


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def kebeles_select(request):
    """Get kebeles for dropdown/select components"""
    subcity_id = request.query_params.get('subcity')
    kebeles = Kebele.objects.filter(is_active=True)
    if subcity_id:
        kebeles = kebeles.filter(subcity=subcity_id)
    kebeles = kebeles.order_by('number', 'name')
    serializer = KebeleSelectSerializer(kebeles, many=True)
    return Response(serializer.data)


# Special Location Views
class SpecialLocationListCreateView(generics.ListCreateAPIView):
    """List and create special locations"""
    queryset = SpecialLocation.objects.all()
    permission_classes = [permissions.AllowAny]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return SpecialLocationCreateSerializer
        return SpecialLocationSerializer

    def get_queryset(self):
        queryset = SpecialLocation.objects.select_related('kebele__subcity__city__zone__region__country')
        search = self.request.query_params.get('search', None)
        kebele_id = self.request.query_params.get('kebele', None)

        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(kebele__name__icontains=search)
            )

        if kebele_id:
            queryset = queryset.filter(kebele=kebele_id)

        return queryset.filter(is_active=True)


class SpecialLocationDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, or delete a special location"""
    queryset = SpecialLocation.objects.select_related('kebele__subcity__city__zone__region__country')
    serializer_class = SpecialLocationSerializer
    permission_classes = [permissions.AllowAny]


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def special_location_select_list(request):
    """Get special locations for dropdown selection"""
    special_locations = SpecialLocation.objects.filter(is_active=True)

    # Filter by kebele if provided
    kebele_id = request.query_params.get('kebele', None)

    if kebele_id:
        special_locations = special_locations.filter(kebele=kebele_id)

    special_locations = special_locations.order_by('name')
    serializer = SpecialLocationSelectSerializer(special_locations, many=True)
    return Response(serializer.data)
