import React from 'react';
import {
  FormControl,
  Select,
  MenuItem,
  Box,
  Typography,
} from '@mui/material';
import { Language, Public } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

const languages = [
  {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
  },
  {
    code: 'am',
    name: 'Amharic',
    nativeName: 'አማርኛ',
    flag: '🇪🇹',
  },
];

interface LanguageSwitcherProps {
  variant?: 'header' | 'settings';
  size?: 'small' | 'medium';
}

const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({ 
  variant = 'header',
  size = 'small'
}) => {
  const { i18n, t } = useTranslation();

  const handleLanguageChange = (event: any) => {
    const newLanguage = event.target.value as string;
    i18n.changeLanguage(newLanguage);
  };

  // Handle language codes like 'en-US' by extracting the base language 'en'
  const baseLanguage = i18n.language.split('-')[0];
  const currentLanguage = languages.find(lang => lang.code === baseLanguage) || languages[0];

  if (variant === 'settings') {
    return (
      <Box sx={{ minWidth: 200 }}>
        <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Language color="primary" />
          {t('settings.language')}
        </Typography>
        <FormControl fullWidth size={size}>
          <Select
            value={baseLanguage}
            onChange={handleLanguageChange}
            displayEmpty
            renderValue={(selected) => {
              const lang = languages.find(l => l.code === selected);
              return (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <span style={{ fontSize: '1.2em' }}>{lang?.flag}</span>
                  <Typography variant="body2">
                    {lang?.nativeName} ({lang?.name})
                  </Typography>
                </Box>
              );
            }}
          >
            {languages.map((language) => (
              <MenuItem key={language.code} value={language.code}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                  <span style={{ fontSize: '1.5em' }}>{language.flag}</span>
                  <Box>
                    <Typography variant="body2" fontWeight="medium">
                      {language.nativeName}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {language.name}
                    </Typography>
                  </Box>
                </Box>
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>
    );
  }

  // Header variant - compact
  return (
    <FormControl size={size} sx={{ minWidth: 120 }}>
      <Select
        value={baseLanguage}
        onChange={handleLanguageChange}
        displayEmpty
        variant="outlined"
        sx={{
          '& .MuiSelect-select': {
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            py: 1,
          },
          '& .MuiOutlinedInput-notchedOutline': {
            borderColor: 'rgba(255, 255, 255, 0.3)',
          },
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: 'rgba(255, 255, 255, 0.5)',
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderColor: 'primary.main',
          },
          color: 'inherit',
        }}
        renderValue={(selected) => {
          const lang = languages.find(l => l.code === selected);
          return (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <span style={{ fontSize: '1em' }}>{lang?.flag}</span>
              <Typography variant="body2" sx={{ color: 'inherit' }}>
                {lang?.code.toUpperCase()}
              </Typography>
            </Box>
          );
        }}
        MenuProps={{
          PaperProps: {
            sx: {
              mt: 1,
              '& .MuiMenuItem-root': {
                px: 2,
                py: 1,
              },
            },
          },
        }}
      >
        {languages.map((language) => (
          <MenuItem key={language.code} value={language.code}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
              <span style={{ fontSize: '1.2em' }}>{language.flag}</span>
              <Box>
                <Typography variant="body2" fontWeight="medium">
                  {language.nativeName}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {language.name}
                </Typography>
              </Box>
            </Box>
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export default LanguageSwitcher;
