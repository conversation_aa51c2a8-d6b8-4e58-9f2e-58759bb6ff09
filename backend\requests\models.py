from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator
import uuid


class DocumentRequest(models.Model):
    """
    Document request model for checkout workflow
    """

    class Status(models.TextChoices):
        PENDING = 'pending', 'Pending Approval'
        APPROVED = 'approved', 'Approved'
        REJECTED = 'rejected', 'Rejected'
        CHECKED_OUT = 'checked_out', 'Checked Out'
        RETURNED = 'returned', 'Returned'
        OVERDUE = 'overdue', 'Overdue'

    class Priority(models.TextChoices):
        LOW = 'low', 'Low'
        NORMAL = 'normal', 'Normal'
        HIGH = 'high', 'High'
        URGENT = 'urgent', 'Urgent'

    # Basic Information
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    request_number = models.CharField(
        max_length=20,
        unique=True,
        help_text="Auto-generated request number"
    )

    # Requester information
    requested_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.CASCADE,
        related_name='document_requests'
    )

    # Files being requested (new primary way)
    files = models.ManyToManyField(
        'locations.File',
        related_name='requests',
        blank=True,
        help_text="Files being requested (e.g., 'Abebe's Hotel File')"
    )

    # Legacy: Individual documents being requested (for backward compatibility)
    documents = models.ManyToManyField(
        'documents.Document',
        related_name='requests',
        blank=True,
        help_text="Individual documents being requested (legacy)"
    )

    # Request details
    purpose = models.TextField(
        help_text="Purpose or reason for requesting the documents"
    )

    priority = models.CharField(
        max_length=10,
        choices=Priority.choices,
        default=Priority.NORMAL
    )

    # Dates
    requested_date = models.DateTimeField(auto_now_add=True)
    required_date = models.DateTimeField(
        help_text="Date and time when documents are needed"
    )
    due_date = models.DateTimeField(
        help_text="Date and time when documents should be returned"
    )

    # Approval workflow
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.PENDING
    )

    approved_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_requests',
        help_text="User who approved the request"
    )

    approved_date = models.DateTimeField(null=True, blank=True)

    rejection_reason = models.TextField(
        null=True,
        blank=True,
        help_text="Reason for rejection (if applicable)"
    )

    # Checkout information
    checked_out_date = models.DateTimeField(null=True, blank=True)
    checked_out_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='checked_out_requests',
        help_text="Staff member who processed the checkout"
    )

    # Return information
    returned_date = models.DateTimeField(null=True, blank=True)
    returned_to = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='received_returns',
        help_text="Staff member who processed the return"
    )

    # Notes and comments
    notes = models.TextField(
        null=True,
        blank=True,
        help_text="Additional notes or comments"
    )

    # Audit fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'requests_document_request'
        verbose_name = 'Document Request'
        verbose_name_plural = 'Document Requests'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'requested_date']),
            models.Index(fields=['requested_by']),
            models.Index(fields=['due_date']),
        ]

    def __str__(self):
        return f"Request {self.request_number} by {self.requested_by.username}"

    def save(self, *args, **kwargs):
        if not self.request_number:
            self.request_number = self.generate_request_number()
        super().save(*args, **kwargs)

    def generate_request_number(self):
        """Generate unique request number"""
        from datetime import datetime
        year = datetime.now().year
        # Get the count of requests this year
        count = DocumentRequest.objects.filter(
            created_at__year=year
        ).count() + 1
        return f"REQ-{year}-{count:05d}"

    @property
    def all_documents(self):
        """Get all documents from both files and individual document requests"""
        documents = list(self.documents.all())

        # Add all documents from requested files
        for file in self.files.all():
            documents.extend(file.documents.filter(is_active=True))

        # Remove duplicates
        return list(set(documents))

    @property
    def total_document_count(self):
        """Get total count of all documents in this request"""
        return len(self.all_documents)

    @property
    def file_count(self):
        """Get count of files in this request"""
        return self.files.count()

    @property
    def individual_document_count(self):
        """Get count of individual documents in this request"""
        return self.documents.count()

    @property
    def request_summary(self):
        """Get a summary of what's being requested"""
        summary = []
        if self.file_count > 0:
            summary.append(f"{self.file_count} file(s)")
        if self.individual_document_count > 0:
            summary.append(f"{self.individual_document_count} individual document(s)")
        return ", ".join(summary) if summary else "No items"

    @property
    def is_overdue(self):
        """Check if request is overdue"""
        if self.status == self.Status.CHECKED_OUT and self.due_date:
            return timezone.now() > self.due_date
        return False

    @property
    def days_until_due(self):
        """Calculate days until due date"""
        if self.due_date:
            delta = self.due_date - timezone.now()
            return delta.days
        return None

    def can_be_approved(self):
        """Check if request can be approved"""
        return self.status == self.Status.PENDING

    def can_be_checked_out(self):
        """Check if request can be checked out"""
        return self.status == self.Status.APPROVED

    def can_be_returned(self):
        """Check if request can be returned"""
        return self.status == self.Status.CHECKED_OUT

    def approve(self, approved_by):
        """Approve the request"""
        self.status = self.Status.APPROVED
        self.approved_by = approved_by
        self.approved_date = timezone.now()
        self.save()

    def reject(self, rejected_by, reason):
        """Reject the request"""
        self.status = self.Status.REJECTED
        self.approved_by = rejected_by
        self.approved_date = timezone.now()
        self.rejection_reason = reason
        self.save()

    def checkout(self, checked_out_by):
        """Process checkout"""
        self.status = self.Status.CHECKED_OUT
        self.checked_out_by = checked_out_by
        self.checked_out_date = timezone.now()

        # Update document status
        for document in self.documents.all():
            if document.is_physical:
                document.status = document.Status.CHECKED_OUT
                document.save()

        self.save()

    def return_documents(self, returned_to, condition_notes=None):
        """Process return"""
        self.status = self.Status.RETURNED
        self.returned_to = returned_to
        self.returned_date = timezone.now()

        if condition_notes:
            self.notes = f"{self.notes or ''}\nReturn condition: {condition_notes}".strip()

        # Update document status
        for document in self.documents.all():
            if document.status == document.Status.CHECKED_OUT:
                document.status = document.Status.ACTIVE
                document.save()

        self.save()


class RequestApproval(models.Model):
    """
    Approval workflow tracking for document requests
    """
    request = models.ForeignKey(
        DocumentRequest,
        on_delete=models.CASCADE,
        related_name='approvals'
    )

    approver = models.ForeignKey(
        'accounts.User',
        on_delete=models.CASCADE,
        related_name='given_approvals'
    )

    action = models.CharField(
        max_length=25,
        choices=[
            ('approved', 'Approved'),
            ('rejected', 'Rejected'),
            ('returned_for_revision', 'Returned for Revision'),
        ]
    )

    comments = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'requests_request_approval'
        verbose_name = 'Request Approval'
        verbose_name_plural = 'Request Approvals'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.request.request_number} - {self.action} by {self.approver.username}"


class AuditLog(models.Model):
    """
    Comprehensive audit logging for all system activities
    """

    class Action(models.TextChoices):
        CREATE = 'create', 'Create'
        UPDATE = 'update', 'Update'
        DELETE = 'delete', 'Delete'
        VIEW = 'view', 'View'
        DOWNLOAD = 'download', 'Download'
        CHECKOUT = 'checkout', 'Checkout'
        RETURN = 'return', 'Return'
        APPROVE = 'approve', 'Approve'
        REJECT = 'reject', 'Reject'
        LOGIN = 'login', 'Login'
        LOGOUT = 'logout', 'Logout'

    # Who performed the action
    user = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='audit_logs'
    )

    # What action was performed
    action = models.CharField(
        max_length=20,
        choices=Action.choices
    )

    # What object was affected
    content_type = models.ForeignKey(
        'contenttypes.ContentType',
        on_delete=models.CASCADE,
        null=True,
        blank=True
    )
    object_id = models.CharField(max_length=255, null=True, blank=True)
    object_repr = models.CharField(max_length=255, null=True, blank=True)

    # Additional details
    description = models.TextField(
        null=True,
        blank=True,
        help_text="Detailed description of the action"
    )

    # Technical details
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(null=True, blank=True)

    # Changes made (for update actions)
    changes = models.JSONField(
        null=True,
        blank=True,
        help_text="JSON representation of changes made"
    )

    # Timestamp
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'requests_audit_log'
        verbose_name = 'Audit Log'
        verbose_name_plural = 'Audit Logs'
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['action', 'timestamp']),
            models.Index(fields=['content_type', 'object_id']),
        ]

    def __str__(self):
        return f"{self.user} - {self.action} - {self.timestamp}"

    @classmethod
    def log_action(cls, user, action, obj=None, description=None, ip_address=None, user_agent=None, changes=None):
        """Convenience method to create audit log entries"""
        from django.contrib.contenttypes.models import ContentType

        content_type = None
        object_id = None
        object_repr = None

        if obj:
            content_type = ContentType.objects.get_for_model(obj)
            object_id = str(obj.pk)
            object_repr = str(obj)

        return cls.objects.create(
            user=user,
            action=action,
            content_type=content_type,
            object_id=object_id,
            object_repr=object_repr,
            description=description,
            ip_address=ip_address,
            user_agent=user_agent,
            changes=changes
        )


class Notification(models.Model):
    """
    System notifications for users
    """

    class Type(models.TextChoices):
        INFO = 'info', 'Information'
        WARNING = 'warning', 'Warning'
        ERROR = 'error', 'Error'
        SUCCESS = 'success', 'Success'

    recipient = models.ForeignKey(
        'accounts.User',
        on_delete=models.CASCADE,
        related_name='notifications'
    )

    title = models.CharField(max_length=255)
    message = models.TextField()

    notification_type = models.CharField(
        max_length=10,
        choices=Type.choices,
        default=Type.INFO
    )

    # Related object (optional)
    content_type = models.ForeignKey(
        'contenttypes.ContentType',
        on_delete=models.CASCADE,
        null=True,
        blank=True
    )
    object_id = models.CharField(max_length=255, null=True, blank=True)

    # Status
    is_read = models.BooleanField(default=False)
    read_at = models.DateTimeField(null=True, blank=True)

    # Email notification
    email_sent = models.BooleanField(default=False)
    email_sent_at = models.DateTimeField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'requests_notification'
        verbose_name = 'Notification'
        verbose_name_plural = 'Notifications'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['recipient', 'is_read']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.recipient.username} - {self.title}"

    def mark_as_read(self):
        """Mark notification as read"""
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save()

    @classmethod
    def create_notification(cls, recipient, title, message, notification_type=Type.INFO, related_object=None):
        """Convenience method to create notifications"""
        from django.contrib.contenttypes.models import ContentType

        content_type = None
        object_id = None

        if related_object:
            content_type = ContentType.objects.get_for_model(related_object)
            object_id = str(related_object.pk)

        return cls.objects.create(
            recipient=recipient,
            title=title,
            message=message,
            notification_type=notification_type,
            content_type=content_type,
            object_id=object_id
        )
