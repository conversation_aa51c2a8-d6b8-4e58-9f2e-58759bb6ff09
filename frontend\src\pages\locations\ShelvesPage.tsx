import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  TextField,
  InputAdornment,
  Avatar,
  Divider,
  Stack,
  Fade,
  Alert,
  CircularProgress,
  IconButton,
  Breadcrumbs,
  Link,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TablePagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Shelves,
  ArrowBack,
  Home,
  Add,
  Edit,
  Delete,
  Visibility,
  Business,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useNotification } from '../../contexts/NotificationContext';
import locationService from '../../services/locationService';
import { normalizeCode, createCodeBlurHandler } from '../../utils/codeUtils';
import ConfirmationDialog from '../../components/common/ConfirmationDialog';
import type { <PERSON><PERSON>, <PERSON><PERSON><PERSON>reate, Building } from '../../services/types';

const ShelvesPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { showNotification } = useNotification();

  const [shelves, setShelves] = useState<Shelf[]>([]);
  const [buildings, setBuildings] = useState<Building[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingShelf, setEditingShelf] = useState<Shelf | null>(null);
  const [formData, setFormData] = useState<ShelfCreate>({
    building: 0,
    name: '',
    code: '',
    description: '',
    rows: 5,
    columns: 10,
  });
  const [formErrors, setFormErrors] = useState<any>({});
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [shelfToDelete, setShelfToDelete] = useState<Shelf | null>(null);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    loadShelves();
    loadBuildings();
  }, [page, rowsPerPage]);

  // Handle navigation state from detail page
  useEffect(() => {
    const state = location.state as any;
    if (state?.editShelf && state?.showForm) {
      handleEdit(state.editShelf);
      // Clear the state to prevent re-triggering
      navigate(location.pathname, { replace: true });
    }
  }, [location.state]);

  const loadShelves = async () => {
    try {
      setLoading(true);
      const response = await locationService.getShelves({
        page: page + 1,
        page_size: rowsPerPage,
      });
      setShelves(response.results);
      setTotalCount(response.count);
    } catch (error) {
      console.error('Error loading shelves:', error);
      showNotification('Failed to load shelves', 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadBuildings = async () => {
    try {
      const response = await locationService.getBuildings({ page_size: 100 });
      setBuildings(response.results);
    } catch (error) {
      console.error('Error loading buildings:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setSubmitting(true);
      setFormErrors({});

      if (editingShelf) {
        await locationService.updateShelf(editingShelf.id, formData);
        showNotification('Shelf updated successfully', 'success');
      } else {
        await locationService.createShelf(formData);
        showNotification('Shelf created successfully', 'success');
      }

      resetForm();
      loadShelves();
    } catch (error: any) {
      console.error('Error submitting form:', error);
      if (error.response?.data) {
        setFormErrors(error.response.data);
      }
      showNotification('Failed to save shelf', 'error');
    } finally {
      setSubmitting(false);
    }
  };

  const handleViewDetails = (shelf: Shelf) => {
    navigate(`/locations/shelves/${shelf.id}`);
  };

  const handleEdit = (shelf: Shelf) => {
    setEditingShelf(shelf);
    setFormData({
      building: shelf.building,
      name: shelf.name,
      code: shelf.code,
      description: shelf.description || '',
      rows: shelf.rows,
      columns: shelf.columns,
    });
    setShowForm(true);
  };

  const handleDelete = (shelf: Shelf) => {
    setShelfToDelete(shelf);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!shelfToDelete) return;
    
    try {
      setDeleting(true);
      await locationService.deleteShelf(shelfToDelete.id);
      showNotification('Shelf deleted successfully', 'success');
      loadShelves();
      handleCloseDeleteDialog();
    } catch (error) {
      console.error('Error deleting shelf:', error);
      showNotification('Failed to delete shelf', 'error');
    } finally {
      setDeleting(false);
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setShelfToDelete(null);
    setDeleting(false);
  };

  const resetForm = () => {
    setFormData({
      building: 0,
      name: '',
      code: '',
      description: '',
      rows: 5,
      columns: 10,
    });
    setFormErrors({});
    setEditingShelf(null);
    setShowForm(false);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Fade in timeout={800}>
        <Box sx={{ mb: 4 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs sx={{ mb: 2 }}>
            <Link
              component="button"
              variant="body1"
              onClick={() => navigate('/locations')}
              sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
            >
              <Home fontSize="small" />
              Physical Location Center
            </Link>
            <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <Shelves fontSize="small" />
              Shelves
            </Typography>
          </Breadcrumbs>

          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'between', mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <IconButton onClick={() => navigate('/locations')} sx={{ mr: 1 }}>
                <ArrowBack />
              </IconButton>
              <Avatar sx={{ bgcolor: 'secondary.main' }}>
                <Shelves />
              </Avatar>
              <Box>
                <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
                  Shelves Management
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Organize shelves within buildings
                </Typography>
              </Box>
            </Box>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setShowForm(true)}
              size="large"
              sx={{ ml: 'auto' }}
            >
              Add Shelf
            </Button>
          </Box>
        </Box>
      </Fade>

      {/* Form Section */}
      {showForm && (
        <Fade in timeout={600}>
          <Card sx={{ mb: 4 }}>
            <CardContent sx={{ p: 4 }}>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                {editingShelf ? 'Edit Shelf' : 'Add New Shelf'}
              </Typography>
              <Divider sx={{ mb: 3 }} />
              
              <form onSubmit={handleSubmit}>
                <Stack spacing={3}>
                  <TextField
                    label="Shelf Name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    error={!!formErrors.name}
                    helperText={formErrors.name || 'Enter the shelf name'}
                    fullWidth
                    required
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Shelves color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />
                  
                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                    <TextField
                      label="Shelf Code"
                      value={formData.code}
                      onChange={(e) => setFormData({ ...formData, code: e.target.value })}
                      onBlur={createCodeBlurHandler(
                        (field, value) => setFormData(prev => ({ ...prev, [field]: value })),
                        'code'
                      )}
                      error={!!formErrors.code}
                      helperText={formErrors.code || 'Code will be automatically converted to uppercase (e.g., S1, SHELF-A, SH01)'}
                      required
                      inputProps={{ maxLength: 10 }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Shelves color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                    
                    <FormControl fullWidth required error={!!formErrors.building}>
                      <InputLabel>Building</InputLabel>
                      <Select
                        value={formData.building}
                        onChange={(e) => setFormData({ ...formData, building: Number(e.target.value) })}
                        label="Building"
                        startAdornment={
                          <InputAdornment position="start">
                            <Business color="action" />
                          </InputAdornment>
                        }
                      >
                        {buildings.map((building) => (
                          <MenuItem key={building.id} value={building.id}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Chip label={building.code} size="small" />
                              {building.name}
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                      {formErrors.building && (
                        <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 2 }}>
                          {formErrors.building}
                        </Typography>
                      )}
                    </FormControl>
                  </Box>

                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                    <TextField
                      label="Rows"
                      type="number"
                      value={formData.rows}
                      onChange={(e) => setFormData({ ...formData, rows: parseInt(e.target.value) || 1 })}
                      error={!!formErrors.rows}
                      helperText={formErrors.rows || 'Number of rows'}
                      required
                      inputProps={{ min: 1, max: 50 }}
                    />

                    <TextField
                      label="Columns"
                      type="number"
                      value={formData.columns}
                      onChange={(e) => setFormData({ ...formData, columns: parseInt(e.target.value) || 1 })}
                      error={!!formErrors.columns}
                      helperText={formErrors.columns || 'Number of columns'}
                      required
                      inputProps={{ min: 1, max: 50 }}
                    />
                  </Box>

                  <TextField
                    label="Description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    error={!!formErrors.description}
                    helperText={formErrors.description || 'Optional description'}
                    multiline
                    rows={3}
                    fullWidth
                  />

                  <Box sx={{ display: 'flex', gap: 2, pt: 2 }}>
                    <Button
                      type="submit"
                      variant="contained"
                      disabled={submitting}
                      startIcon={submitting ? <CircularProgress size={20} /> : <Add />}
                      size="large"
                    >
                      {submitting ? 'Saving...' : editingShelf ? 'Update Shelf' : 'Create Shelf'}
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={resetForm}
                      size="large"
                    >
                      Cancel
                    </Button>
                  </Box>
                </Stack>
              </form>
            </CardContent>
          </Card>
        </Fade>
      )}

      {/* Shelves Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
            Shelves List
          </Typography>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : shelves.length === 0 ? (
            <Alert severity="info">
              No shelves found. Click "Add Shelf" to create your first shelf.
            </Alert>
          ) : (
            <>
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Shelf</TableCell>
                      <TableCell>Building</TableCell>
                      <TableCell>Capacity</TableCell>
                      <TableCell>Utilization</TableCell>
                      <TableCell>Created</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {shelves.map((shelf) => (
                      <TableRow key={shelf.id} hover>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <Avatar sx={{ bgcolor: 'secondary.main', width: 32, height: 32 }}>
                              <Shelves fontSize="small" />
                            </Avatar>
                            <Box>
                              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                {shelf.name}
                              </Typography>
                              <Chip label={shelf.code} size="small" color="secondary" />
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Business fontSize="small" color="action" />
                            {shelf.building_name || `Building ${shelf.building}`}
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box>
                            <Typography variant="body2">
                              {shelf.rows} × {shelf.columns} grid
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {shelf.rows * shelf.columns} positions
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="body2">
                              {shelf.box_count || 0} / {shelf.rows * shelf.columns}
                            </Typography>
                            <Chip
                              label={`${Math.round(((shelf.box_count || 0) / (shelf.rows * shelf.columns)) * 100)}%`}
                              color={(shelf.box_count || 0) >= (shelf.rows * shelf.columns) ? 'error' : 'success'}
                              size="small"
                            />
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {new Date(shelf.created_at).toLocaleDateString()}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                            <IconButton
                              size="small"
                              onClick={() => handleViewDetails(shelf)}
                              color="info"
                              title="View Details"
                            >
                              <Visibility fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleEdit(shelf)}
                              color="primary"
                              title="Edit"
                            >
                              <Edit fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleDelete(shelf)}
                              color="error"
                              title="Delete"
                            >
                              <Delete fontSize="small" />
                            </IconButton>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              <TablePagination
                component="div"
                count={totalCount}
                page={page}
                onPageChange={handleChangePage}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                rowsPerPageOptions={[5, 10, 25, 50]}
              />
            </>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        title="Delete Shelf"
        itemName={shelfToDelete?.name}
        itemType="Shelf"
        message={`Are you sure you want to delete "${shelfToDelete?.name}"? This will also delete all boxes, kents, and files within this shelf. This action cannot be undone.`}
        confirmText="Delete Shelf"
        severity="error"
        loading={deleting}
      />
    </Container>
  );
};

export default ShelvesPage;
