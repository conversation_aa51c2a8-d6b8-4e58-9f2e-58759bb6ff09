import React, { useState, useEffect } from 'react';
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
} from '@mui/material';
import geographicalLocationService from '../services/geographicalLocationService';

// Local interface definitions to avoid import issues
interface Country {
  id: number;
  name: string;
  code: string;
}

interface Region {
  id: number;
  name: string;
  code: string;
}

interface Zone {
  id: number;
  name: string;
  code: string;
}

interface City {
  id: number;
  name: string;
  code: string;
}

interface SubCity {
  id: number;
  name: string;
  code: string;
  type: 'subcity' | 'woreda' | 'district';
}

interface Kebele {
  id: number;
  name: string;
  code: string;
  number: number;
}

interface LocationDropdownsProps {
  country?: string;
  region?: string;
  zone?: string;
  city?: string;
  subCity?: string;
  kebele?: string;
  onCountryChange?: (country: string) => void;
  onRegionChange?: (region: string) => void;
  onZoneChange?: (zone: string) => void;
  onCityChange?: (city: string) => void;
  onSubCityChange?: (subCity: string) => void;
  onKebeleChange?: (kebele: string) => void;
  disabled?: boolean;
  size?: 'small' | 'medium';
}

const LocationDropdowns: React.FC<LocationDropdownsProps> = ({
  country,
  region,
  zone,
  city,
  subCity,
  kebele,
  onCountryChange,
  onRegionChange,
  onZoneChange,
  onCityChange,
  onSubCityChange,
  onKebeleChange,
  disabled = false,
  size = 'medium',
}) => {
  const [countries, setCountries] = useState<Country[]>([]);
  const [regions, setRegions] = useState<Region[]>([]);
  const [zones, setZones] = useState<Zone[]>([]);
  const [cities, setCities] = useState<City[]>([]);
  const [subCities, setSubCities] = useState<SubCity[]>([]);
  const [kebeles, setKebeles] = useState<Kebele[]>([]);
  const [loading, setLoading] = useState(false);

  // Load countries on mount
  useEffect(() => {
    loadCountries();
  }, []);

  // Load regions when country changes
  useEffect(() => {
    if (country) {
      const selectedCountry = countries.find(c => c.name === country);
      if (selectedCountry) {
        loadRegions(selectedCountry.id);
      }
    } else {
      setRegions([]);
      setZones([]);
      setCities([]);
      setSubCities([]);
      setKebeles([]);
    }
  }, [country, countries]);

  // Load zones when region changes
  useEffect(() => {
    if (region) {
      const selectedRegion = regions.find(r => r.name === region);
      if (selectedRegion) {
        loadZones(selectedRegion.id);
      }
    } else {
      setZones([]);
      setCities([]);
      setSubCities([]);
      setKebeles([]);
    }
  }, [region, regions]);

  // Load cities when zone changes
  useEffect(() => {
    if (zone) {
      const selectedZone = zones.find(z => z.name === zone);
      if (selectedZone) {
        loadCities(selectedZone.id);
      }
    } else {
      setCities([]);
      setSubCities([]);
      setKebeles([]);
    }
  }, [zone, zones]);

  // Load subcities when city changes
  useEffect(() => {
    if (city) {
      const selectedCity = cities.find(c => c.name === city);
      if (selectedCity) {
        loadSubCities(selectedCity.id);
      }
    } else {
      setSubCities([]);
      setKebeles([]);
    }
  }, [city, cities]);

  // Load kebeles when subcity changes
  useEffect(() => {
    if (subCity) {
      const selectedSubCity = subCities.find(sc => sc.name === subCity);
      if (selectedSubCity) {
        loadKebeles(selectedSubCity.id);
      }
    } else {
      setKebeles([]);
    }
  }, [subCity, subCities]);

  const loadCountries = async () => {
    try {
      setLoading(true);
      const data = await geographicalLocationService.getCountries();
      setCountries(data);
    } catch (error) {
      console.error('Error loading countries:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadRegions = async (countryId: number) => {
    try {
      const data = await geographicalLocationService.getRegions(countryId);
      setRegions(data);
    } catch (error) {
      console.error('Error loading regions:', error);
    }
  };

  const loadZones = async (regionId: number) => {
    try {
      const data = await geographicalLocationService.getZones(regionId);
      setZones(data);
    } catch (error) {
      console.error('Error loading zones:', error);
    }
  };

  const loadCities = async (zoneId: number) => {
    try {
      const data = await geographicalLocationService.getCities(zoneId);
      setCities(data);
    } catch (error) {
      console.error('Error loading cities:', error);
    }
  };

  const loadSubCities = async (cityId: number) => {
    try {
      const data = await geographicalLocationService.getSubCities(cityId);
      setSubCities(data);
    } catch (error) {
      console.error('Error loading subcities:', error);
    }
  };

  const loadKebeles = async (subCityId: number) => {
    try {
      const data = await geographicalLocationService.getKebeles(subCityId);
      setKebeles(data);
    } catch (error) {
      console.error('Error loading kebeles:', error);
    }
  };

  const handleCountryChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    const value = event.target.value as string;
    onCountryChange?.(value);
    // Reset dependent fields
    onRegionChange?.('');
    onZoneChange?.('');
    onCityChange?.('');
    onSubCityChange?.('');
    onKebeleChange?.('');
  };

  const handleRegionChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    const value = event.target.value as string;
    onRegionChange?.(value);
    // Reset dependent fields
    onZoneChange?.('');
    onCityChange?.('');
    onSubCityChange?.('');
    onKebeleChange?.('');
  };

  const handleZoneChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    const value = event.target.value as string;
    onZoneChange?.(value);
    // Reset dependent fields
    onCityChange?.('');
    onSubCityChange?.('');
    onKebeleChange?.('');
  };

  const handleCityChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    const value = event.target.value as string;
    onCityChange?.(value);
    // Reset dependent fields
    onSubCityChange?.('');
    onKebeleChange?.('');
  };

  const handleSubCityChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    const value = event.target.value as string;
    onSubCityChange?.(value);
    // Reset dependent fields
    onKebeleChange?.('');
  };

  const handleKebeleChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    const value = event.target.value as string;
    onKebeleChange?.(value);
  };

  return (
    <Grid container spacing={2}>
      <Grid size={{ xs: 12, sm: 6 }}>
        <FormControl fullWidth size={size}>
          <InputLabel>Country</InputLabel>
          <Select
            value={country || ''}
            onChange={handleCountryChange}
            label="Country"
            disabled={disabled || loading}
          >
            {countries.map((c) => (
              <MenuItem key={c.id} value={c.name}>
                {c.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>

      <Grid size={{ xs: 12, sm: 6 }}>
        <FormControl fullWidth size={size}>
          <InputLabel>Region/State</InputLabel>
          <Select
            value={region || ''}
            onChange={handleRegionChange}
            label="Region/State"
            disabled={disabled || !country || regions.length === 0}
          >
            {regions.map((r) => (
              <MenuItem key={r.id} value={r.name}>
                {r.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>

      <Grid size={{ xs: 12, sm: 6 }}>
        <FormControl fullWidth size={size}>
          <InputLabel>Zone</InputLabel>
          <Select
            value={zone || ''}
            onChange={handleZoneChange}
            label="Zone"
            disabled={disabled || !region || zones.length === 0}
          >
            {zones.map((z) => (
              <MenuItem key={z.id} value={z.name}>
                {z.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>

      <Grid size={{ xs: 12, sm: 6 }}>
        <FormControl fullWidth size={size}>
          <InputLabel>City</InputLabel>
          <Select
            value={city || ''}
            onChange={handleCityChange}
            label="City"
            disabled={disabled || !zone || cities.length === 0}
          >
            {cities.map((c) => (
              <MenuItem key={c.id} value={c.name}>
                {c.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>

      <Grid size={{ xs: 12, sm: 6 }}>
        <FormControl fullWidth size={size}>
          <InputLabel>SubCity/Woreda</InputLabel>
          <Select
            value={subCity || ''}
            onChange={handleSubCityChange}
            label="SubCity/Woreda"
            disabled={disabled || !city || subCities.length === 0}
          >
            {subCities.map((sc) => (
              <MenuItem key={sc.id} value={sc.name}>
                {sc.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>

      <Grid size={{ xs: 12, sm: 6 }}>
        <FormControl fullWidth size={size}>
          <InputLabel>Kebele</InputLabel>
          <Select
            value={kebele || ''}
            onChange={handleKebeleChange}
            label="Kebele"
            disabled={disabled || !subCity || kebeles.length === 0}
          >
            {kebeles.map((k) => (
              <MenuItem key={k.id} value={k.name}>
                {k.name} (#{k.number})
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
    </Grid>
  );
};

export default LocationDropdowns;
