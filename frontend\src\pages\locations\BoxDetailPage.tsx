import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
} from '@mui/material';
import {
  Inventory,
  Edit,
  Delete,
  Home,
  LocationOn,
  Info,
  Storage,
  Business,
  Archive,
  QrCode,
  GridView,
  Folder,
} from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import locationService from '../../services/locationService';
import type { Box as BoxType } from '../../services/locationService';
import DetailPageTemplate from '../../components/common/DetailPageTemplate';
import ConfirmationDialog from '../../components/common/ConfirmationDialog';

const BoxDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showNotification } = useNotification();
  
  const [box, setBox] = useState<BoxType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    if (id) {
      loadBox();
    }
  }, [id]);

  const loadBox = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await locationService.getBox(Number(id));
      setBox(data);
    } catch (error) {
      console.error('Error loading box:', error);
      setError('Failed to load box');
      showNotification('Failed to load box', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    // Navigate back to boxes page with edit state
    navigate('/locations/boxes', {
      state: {
        editBox: box,
        showForm: true
      }
    });
  };

  const handleDelete = () => {
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!box) return;
    
    try {
      setDeleting(true);
      await locationService.deleteBox(box.id);
      showNotification('Box deleted successfully', 'success');
      navigate('/locations/boxes');
    } catch (error) {
      console.error('Error deleting box:', error);
      showNotification('Failed to delete box', 'error');
    } finally {
      setDeleting(false);
      setDeleteDialogOpen(false);
    }
  };

  const handleBack = () => {
    navigate('/locations/boxes');
  };

  if (!box && !loading && !error) {
    setError('Box not found');
  }

  const breadcrumbs = [
    { label: 'Dashboard', path: '/dashboard', icon: <Home fontSize="small" /> },
    { label: 'Locations', path: '/locations', icon: <LocationOn fontSize="small" /> },
    { label: 'Boxes', path: '/locations/boxes', icon: <Archive fontSize="small" /> },
    { label: box?.name || 'Box', path: undefined, icon: <Inventory fontSize="small" /> },
  ];

  const actions = [
    {
      label: 'Edit',
      icon: <Edit />,
      onClick: handleEdit,
      color: 'primary' as const,
    },
    {
      label: 'Delete',
      icon: <Delete />,
      onClick: handleDelete,
      color: 'error' as const,
    },
  ];

  const chips = box ? [
    {
      label: box.is_active ? 'Active' : 'Inactive',
      color: box.is_active ? 'success' as const : 'error' as const,
    },
    ...(box.position_code ? [{
      label: `Position: ${box.position_code}`,
      color: 'info' as const,
    }] : []),
    ...(box.shelf_name ? [{
      label: box.shelf_name,
      color: 'primary' as const,
    }] : []),
  ] : [];

  const sections = box ? [
    {
      title: 'Basic Information',
      icon: <Info />,
      content: (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'primary.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'primary.200'
          }}>
            <Archive sx={{ color: 'primary.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="primary.main" sx={{ fontWeight: 600 }}>
                Box Name
              </Typography>
              <Typography variant="body2" color="primary.dark">
                {box.name || 'Unnamed Box'}
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'info.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'info.200'
          }}>
            <QrCode sx={{ color: 'info.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="info.main" sx={{ fontWeight: 600 }}>
                Full Code
              </Typography>
              <Typography variant="body2" color="info.dark">
                {box.full_code}
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'warning.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'warning.200'
          }}>
            <GridView sx={{ color: 'warning.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="warning.main" sx={{ fontWeight: 600 }}>
                Position
              </Typography>
              <Typography variant="body2" color="warning.dark">
                Row {box.row}, Column {box.column}
              </Typography>
            </Box>
          </Box>
          
          {box.description && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" color="text.secondary" sx={{ fontWeight: 600, mb: 1 }}>
                Description
              </Typography>
              <Typography variant="body2" sx={{ 
                p: 2, 
                bgcolor: 'grey.50', 
                borderRadius: 1,
                border: '1px solid',
                borderColor: 'grey.200'
              }}>
                {box.description}
              </Typography>
            </Box>
          )}
        </Box>
      ),
    },
    {
      title: 'Location Hierarchy',
      icon: <LocationOn />,
      content: (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'secondary.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'secondary.200'
          }}>
            <Business sx={{ color: 'secondary.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="secondary.main" sx={{ fontWeight: 600 }}>
                Building
              </Typography>
              <Typography variant="body2" color="secondary.dark">
                {box.building_name}
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'success.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'success.200'
          }}>
            <Storage sx={{ color: 'success.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="success.main" sx={{ fontWeight: 600 }}>
                Shelf
              </Typography>
              <Typography variant="body2" color="success.dark">
                {box.shelf_name}
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'warning.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'warning.200'
          }}>
            <Business sx={{ color: 'warning.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="warning.main" sx={{ fontWeight: 600 }}>
                Organization
              </Typography>
              <Typography variant="body2" color="warning.dark">
                {box.organization_name}
              </Typography>
            </Box>
          </Box>
        </Box>
      ),
    },
    {
      title: 'Storage Statistics',
      icon: <Folder />,
      content: (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'primary.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'primary.200'
          }}>
            <Folder sx={{ color: 'primary.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="primary.main" sx={{ fontWeight: 600 }}>
                Kents
              </Typography>
              <Typography variant="body2" color="primary.dark">
                {box.kent_count || 0} kents
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'success.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'success.200'
          }}>
            <Archive sx={{ color: 'success.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="success.main" sx={{ fontWeight: 600 }}>
                Files
              </Typography>
              <Typography variant="body2" color="success.dark">
                {box.file_count || 0} files
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'info.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'info.200'
          }}>
            <Inventory sx={{ color: 'info.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="info.main" sx={{ fontWeight: 600 }}>
                Documents
              </Typography>
              <Typography variant="body2" color="info.dark">
                {box.document_count || 0} documents
              </Typography>
            </Box>
          </Box>
        </Box>
      ),
    },
  ] : [];

  return (
    <>
      <DetailPageTemplate
        loading={loading}
        error={error}
        breadcrumbs={breadcrumbs}
        title={box?.name || 'Box'}
        subtitle={box?.full_code ? `Code: ${box.full_code}` : undefined}
        avatar={{
          fallbackIcon: <Archive sx={{ fontSize: 40 }} />,
          alt: box?.name || 'Box',
        }}
        chips={chips}
        actions={actions}
        sections={sections}
        onBack={handleBack}
      />

      <ConfirmationDialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleConfirmDelete}
        title="Delete Box"
        itemName={box?.name}
        itemType="Box"
        message={`Are you sure you want to delete "${box?.name || 'this box'}"? This will also delete all kents within this box.`}
        confirmText="Delete Box"
        severity="error"
        loading={deleting}
      />
    </>
  );
};

export default BoxDetailPage;
