/**
 * Revenue Summaries Management Page
 * 
 * Manages revenue summaries with generation and location-based filtering
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Tooltip,
  Grid,
} from '@mui/material';
import {
  Add,
  Refresh,
  Analytics,
  LocationOn,
  TrendingUp,
  AccountBalance,
  Business,
} from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import revenueCollectionService from '../../services/revenueCollectionService';
import locationService from '../../services/locationService';
import type {
  RevenueSummary,
  RevenuePeriod,
} from '../../services/revenueCollectionService';
import type { Region, City } from '../../services/locationService';

const SummariesPage: React.FC = () => {
  const { showSuccess, showError } = useNotification();
  
  const [loading, setLoading] = useState(true);
  const [summaries, setSummaries] = useState<RevenueSummary[]>([]);
  const [periods, setPeriods] = useState<RevenuePeriod[]>([]);
  const [regions, setRegions] = useState<Region[]>([]);
  const [cities, setCities] = useState<City[]>([]);
  
  // Dialog states
  const [generateDialogOpen, setGenerateDialogOpen] = useState(false);
  const [generating, setGenerating] = useState(false);
  
  // Form states for generation
  const [generateForm, setGenerateForm] = useState({
    period_id: '',
    location_level: 'region' as 'region' | 'city' | 'subcity' | 'kebele' | 'all',
    location_id: '',
  });

  // Statistics
  const [stats, setStats] = useState({
    totalSummaries: 0,
    totalRegionalRevenue: 0,
    totalCityServiceRevenue: 0,
    totalGrandRevenue: 0,
  });

  useEffect(() => {
    loadData();
    loadOptions();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const response = await revenueCollectionService.getRevenueSummaries({
        ordering: '-period__start_date',
      });
      
      setSummaries(response.results);
      
      // Calculate statistics
      const totalRegionalRevenue = response.results.reduce((sum, s) => sum + s.regional_total, 0);
      const totalCityServiceRevenue = response.results.reduce((sum, s) => sum + s.city_service_total, 0);
      const totalGrandRevenue = response.results.reduce((sum, s) => sum + s.grand_total, 0);
      
      setStats({
        totalSummaries: response.results.length,
        totalRegionalRevenue,
        totalCityServiceRevenue,
        totalGrandRevenue,
      });
    } catch (error) {
      console.error('Error loading summaries:', error);
      showError('Failed to load revenue summaries');
    } finally {
      setLoading(false);
    }
  };

  const loadOptions = async () => {
    try {
      const periodsResponse = await revenueCollectionService.getRevenuePeriods();
      setPeriods(periodsResponse.results);

      // For now, we'll create mock regions and cities since location service might not be available
      setRegions([
        { id: '1', name: 'Addis Ababa' },
        { id: '2', name: 'Oromia' },
        { id: '3', name: 'Amhara' },
        { id: '4', name: 'Tigray' },
        { id: '5', name: 'SNNP' },
      ] as any);

      setCities([
        { id: '1', name: 'Addis Ababa' },
        { id: '2', name: 'Adama' },
        { id: '3', name: 'Bahir Dar' },
        { id: '4', name: 'Mekelle' },
        { id: '5', name: 'Hawassa' },
      ] as any);
    } catch (error) {
      console.error('Error loading options:', error);
    }
  };

  const handleGenerateSummaries = async () => {
    if (!generateForm.period_id) {
      showError('Please select a period');
      return;
    }

    try {
      setGenerating(true);
      const result = await revenueCollectionService.generateRevenueSummaries(generateForm);
      showSuccess(result.message);
      setGenerateDialogOpen(false);
      setGenerateForm({
        period_id: '',
        location_level: 'region',
        location_id: '',
      });
      loadData();
    } catch (error) {
      console.error('Error generating summaries:', error);
      showError('Failed to generate revenue summaries');
    } finally {
      setGenerating(false);
    }
  };

  const handleUpdateTotals = async (id: string) => {
    try {
      await revenueCollectionService.updateSummaryTotals(id);
      showSuccess('Summary totals updated successfully');
      loadData();
    } catch (error) {
      console.error('Error updating totals:', error);
      showError('Failed to update summary totals');
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ET', {
      style: 'currency',
      currency: 'ETB',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-ET', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getLocationOptions = () => {
    switch (generateForm.location_level) {
      case 'region':
        return regions;
      case 'city':
        return cities;
      default:
        return [];
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Revenue Summaries
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Generate and manage aggregated revenue summaries
        </Typography>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Analytics color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6" component="div">
                  Total Summaries
                </Typography>
              </Box>
              <Typography variant="h4" component="div" color="primary">
                {stats.totalSummaries}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <AccountBalance color="secondary" sx={{ mr: 1 }} />
                <Typography variant="h6" component="div">
                  Regional Revenue
                </Typography>
              </Box>
              <Typography variant="h4" component="div" color="secondary">
                {formatCurrency(stats.totalRegionalRevenue)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Business color="success" sx={{ mr: 1 }} />
                <Typography variant="h6" component="div">
                  City Service Revenue
                </Typography>
              </Box>
              <Typography variant="h4" component="div" color="success.main">
                {formatCurrency(stats.totalCityServiceRevenue)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <TrendingUp color="warning" sx={{ mr: 1 }} />
                <Typography variant="h6" component="div">
                  Grand Total
                </Typography>
              </Box>
              <Typography variant="h4" component="div" color="warning.main">
                {formatCurrency(stats.totalGrandRevenue)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Summaries Table */}
      <Card>
        <CardContent>
          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">
              Revenue Summaries ({summaries.length})
            </Typography>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setGenerateDialogOpen(true)}
            >
              Generate Summaries
            </Button>
          </Box>

          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Period</TableCell>
                  <TableCell>Location</TableCell>
                  <TableCell align="right">Regional Revenue</TableCell>
                  <TableCell align="right">City Service Revenue</TableCell>
                  <TableCell align="right">Grand Total</TableCell>
                  <TableCell>Last Updated</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {summaries.map((summary) => (
                  <TableRow key={summary.id}>
                    <TableCell>
                      <Chip
                        label={summary.period_name}
                        color="primary"
                        variant="outlined"
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <LocationOn sx={{ mr: 1, fontSize: 16 }} color="action" />
                        <Typography variant="body2">
                          {summary.location_display}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell align="right">
                      <Typography variant="body2" color="primary" fontWeight="medium">
                        {formatCurrency(summary.regional_total)}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      <Typography variant="body2" color="secondary" fontWeight="medium">
                        {formatCurrency(summary.city_service_total)}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      <Typography variant="body2" fontWeight="bold">
                        {formatCurrency(summary.grand_total)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="text.secondary">
                        {formatDate(summary.last_updated)}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        by {summary.created_by_name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Tooltip title="Update Totals">
                        <IconButton
                          size="small"
                          onClick={() => handleUpdateTotals(summary.id)}
                        >
                          <Refresh />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
                {summaries.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      <Typography variant="body2" color="text.secondary">
                        No revenue summaries found. Generate summaries to get started.
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Generate Summaries Dialog */}
      <Dialog open={generateDialogOpen} onClose={() => setGenerateDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          Generate Revenue Summaries
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <FormControl fullWidth margin="normal" required>
              <InputLabel>Period</InputLabel>
              <Select
                value={generateForm.period_id}
                onChange={(e) => setGenerateForm({ ...generateForm, period_id: e.target.value })}
                label="Period"
              >
                {periods.map((period) => (
                  <MenuItem key={period.id} value={period.id}>
                    {period.name} ({formatDate(period.start_date)} - {formatDate(period.end_date)})
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth margin="normal" required>
              <InputLabel>Location Level</InputLabel>
              <Select
                value={generateForm.location_level}
                onChange={(e) => {
                  const newLevel = e.target.value as typeof generateForm.location_level;
                  setGenerateForm({ 
                    ...generateForm, 
                    location_level: newLevel,
                    location_id: '' 
                  });
                }}
                label="Location Level"
              >
                <MenuItem value="all">All Locations</MenuItem>
                <MenuItem value="region">By Region</MenuItem>
                <MenuItem value="city">By City</MenuItem>
                <MenuItem value="subcity">By SubCity</MenuItem>
                <MenuItem value="kebele">By Kebele</MenuItem>
              </Select>
            </FormControl>

            {generateForm.location_level !== 'all' && (
              <FormControl fullWidth margin="normal">
                <InputLabel>Specific Location</InputLabel>
                <Select
                  value={generateForm.location_id}
                  onChange={(e) => setGenerateForm({ ...generateForm, location_id: e.target.value })}
                  label="Specific Location"
                >
                  <MenuItem value="">All {generateForm.location_level}s</MenuItem>
                  {getLocationOptions().map((location) => (
                    <MenuItem key={location.id} value={location.id}>
                      {location.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}

            <Alert severity="info" sx={{ mt: 2 }}>
              This will generate revenue summaries for the selected period and location level. 
              Existing summaries for the same period and location will be updated.
            </Alert>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setGenerateDialogOpen(false)} disabled={generating}>
            Cancel
          </Button>
          <Button
            onClick={handleGenerateSummaries}
            variant="contained"
            disabled={generating || !generateForm.period_id}
          >
            {generating ? 'Generating...' : 'Generate Summaries'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SummariesPage;
