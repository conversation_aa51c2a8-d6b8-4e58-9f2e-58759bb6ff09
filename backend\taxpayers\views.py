from rest_framework import generics, permissions, status, filters
from rest_framework.decorators import api_view, permission_classes
from rest_framework.views import APIView
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Count, F
from django.utils import timezone
from .models import (
    BusinessSector, BusinessSubSector, TaxPayerLevel,
    OrganizationBusinessType, IndividualTaxPayer, OrganizationTaxPayer,
    DailyIncomeAnalysis, LevelUpgradeNotification
)
from .serializers import (
    BusinessSectorSerializer, BusinessSubSectorSerializer, BusinessSubSectorCreateSerializer,
    TaxPayerLevelSerializer, OrganizationBusinessTypeSerializer,
    IndividualTaxPayerSerializer, IndividualTaxPayerCreateSerializer,
    OrganizationTaxPayerSerializer, OrganizationTaxPayerCreateSerializer,
    BusinessSectorSimpleSerializer, BusinessSubSectorSimpleSerializer,
    TaxPayerLevelSimpleSerializer, OrganizationBusinessTypeSimpleSerializer,
    BusinessClosureSerializer, DailyIncomeAnalysisSerializer,
    DailyIncomeAnalysisCreateSerializer, LevelUpgradeNotificationSerializer
)


# Business Sector Views
class BusinessSectorListCreateView(generics.ListCreateAPIView):
    """List and create business sectors"""
    queryset = BusinessSector.objects.filter(is_active=True)
    serializer_class = BusinessSectorSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['code', 'name', 'description']
    ordering_fields = ['code', 'name', 'created_at']
    ordering = ['code']
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)


class BusinessSectorDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, or delete a business sector"""
    queryset = BusinessSector.objects.all()
    serializer_class = BusinessSectorSerializer
    permission_classes = [permissions.IsAuthenticated]


class BusinessSectorSimpleListView(generics.ListAPIView):
    """Simple list of business sectors for dropdowns"""
    queryset = BusinessSector.objects.filter(is_active=True)
    serializer_class = BusinessSectorSimpleSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None


# Business Sub-Sector Views
class BusinessSubSectorListCreateView(generics.ListCreateAPIView):
    """List and create business sub-sectors"""
    queryset = BusinessSubSector.objects.filter(is_active=True)
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['business_sector']
    search_fields = ['code', 'name', 'description']
    ordering_fields = ['code', 'name', 'created_at']
    ordering = ['business_sector__code', 'code']
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return BusinessSubSectorCreateSerializer
        return BusinessSubSectorSerializer
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)


class BusinessSubSectorDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, or delete a business sub-sector"""
    queryset = BusinessSubSector.objects.all()
    serializer_class = BusinessSubSectorSerializer
    permission_classes = [permissions.IsAuthenticated]


class BusinessSubSectorSimpleListView(generics.ListAPIView):
    """Simple list of business sub-sectors for dropdowns"""
    queryset = BusinessSubSector.objects.filter(is_active=True)
    serializer_class = BusinessSubSectorSimpleSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['business_sector']
    pagination_class = None


# Tax Payer Level Views
class TaxPayerLevelListCreateView(generics.ListCreateAPIView):
    """List and create tax payer levels"""
    queryset = TaxPayerLevel.objects.filter(is_active=True)
    serializer_class = TaxPayerLevelSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['code', 'name', 'description']
    ordering_fields = ['code', 'name', 'created_at']
    ordering = ['code']


class TaxPayerLevelDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, or delete a tax payer level"""
    queryset = TaxPayerLevel.objects.all()
    serializer_class = TaxPayerLevelSerializer
    permission_classes = [permissions.IsAuthenticated]


class TaxPayerLevelSimpleListView(generics.ListAPIView):
    """Simple list of tax payer levels for dropdowns"""
    queryset = TaxPayerLevel.objects.filter(is_active=True)
    serializer_class = TaxPayerLevelSimpleSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None


# Organization Business Type Views
class OrganizationBusinessTypeListCreateView(generics.ListCreateAPIView):
    """List and create organization business types"""
    queryset = OrganizationBusinessType.objects.filter(is_active=True)
    serializer_class = OrganizationBusinessTypeSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['code', 'name', 'description']
    ordering_fields = ['code', 'name', 'created_at']
    ordering = ['name']


class OrganizationBusinessTypeDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, or delete an organization business type"""
    queryset = OrganizationBusinessType.objects.all()
    serializer_class = OrganizationBusinessTypeSerializer
    permission_classes = [permissions.IsAuthenticated]


class OrganizationBusinessTypeSimpleListView(generics.ListAPIView):
    """Simple list of organization business types for dropdowns"""
    queryset = OrganizationBusinessType.objects.filter(is_active=True)
    serializer_class = OrganizationBusinessTypeSimpleSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None


# Individual Tax Payer Views
class IndividualTaxPayerListCreateView(generics.ListCreateAPIView):
    """List and create individual tax payers"""
    queryset = IndividualTaxPayer.objects.filter(is_active=True)
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = [
        'tax_payer_level', 'business_sector', 'business_sub_sector',
        'nationality', 'gender', 'subcity', 'kebele'
    ]
    search_fields = [
        'tin', 'first_name', 'last_name', 'business_name',
        'phone', 'email'
    ]
    ordering_fields = ['tin', 'last_name', 'registration_date', 'business_registration_date']
    ordering = ['-registration_date']
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return IndividualTaxPayerCreateSerializer
        return IndividualTaxPayerSerializer
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)


class IndividualTaxPayerDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, or delete an individual tax payer"""
    queryset = IndividualTaxPayer.objects.all()
    serializer_class = IndividualTaxPayerSerializer
    permission_classes = [permissions.IsAuthenticated]


class IndividualTaxPayerCloseBusinessView(APIView):
    """Close an individual taxpayer's business"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk):
        try:
            taxpayer = IndividualTaxPayer.objects.get(pk=pk)
        except IndividualTaxPayer.DoesNotExist:
            return Response(
                {'error': 'Taxpayer not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        if taxpayer.is_business_closed:
            return Response(
                {'error': 'Business is already closed'},
                status=status.HTTP_400_BAD_REQUEST
            )

        serializer = BusinessClosureSerializer(data=request.data)
        if serializer.is_valid():
            try:
                taxpayer.close_business(
                    closure_date=serializer.validated_data['closure_date'],
                    closure_reason=serializer.validated_data['closure_reason'],
                    closed_by_user=request.user
                )
                return Response(
                    {'message': 'Business closed successfully'},
                    status=status.HTTP_200_OK
                )
            except ValueError as e:
                return Response(
                    {'error': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class IndividualTaxPayerReopenBusinessView(APIView):
    """Reopen an individual taxpayer's business"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk):
        try:
            taxpayer = IndividualTaxPayer.objects.get(pk=pk)
        except IndividualTaxPayer.DoesNotExist:
            return Response(
                {'error': 'Taxpayer not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        if not taxpayer.is_business_closed:
            return Response(
                {'error': 'Business is not closed'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            taxpayer.reopen_business(reopened_by_user=request.user)
            return Response(
                {'message': 'Business reopened successfully'},
                status=status.HTTP_200_OK
            )
        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


# Organization Tax Payer Views
class OrganizationTaxPayerListCreateView(generics.ListCreateAPIView):
    """List and create organization tax payers"""
    queryset = OrganizationTaxPayer.objects.filter(is_active=True)
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = [
        'tax_payer_level', 'business_sector', 'business_sub_sector',
        'organization_business_type', 'subcity', 'kebele'
    ]
    search_fields = [
        'tin', 'business_name', 'trade_name', 'vat_number',
        'manager_first_name', 'manager_last_name', 'phone', 'email'
    ]
    ordering_fields = ['tin', 'business_name', 'registration_date', 'business_registration_date']
    ordering = ['-registration_date']
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return OrganizationTaxPayerCreateSerializer
        return OrganizationTaxPayerSerializer
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)


class OrganizationTaxPayerDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, or delete an organization tax payer"""
    queryset = OrganizationTaxPayer.objects.all()
    serializer_class = OrganizationTaxPayerSerializer
    permission_classes = [permissions.IsAuthenticated]


class OrganizationTaxPayerCloseBusinessView(APIView):
    """Close an organization taxpayer's business"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk):
        try:
            taxpayer = OrganizationTaxPayer.objects.get(pk=pk)
        except OrganizationTaxPayer.DoesNotExist:
            return Response(
                {'error': 'Taxpayer not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        if taxpayer.is_business_closed:
            return Response(
                {'error': 'Business is already closed'},
                status=status.HTTP_400_BAD_REQUEST
            )

        serializer = BusinessClosureSerializer(data=request.data)
        if serializer.is_valid():
            try:
                taxpayer.close_business(
                    closure_date=serializer.validated_data['closure_date'],
                    closure_reason=serializer.validated_data['closure_reason'],
                    closed_by_user=request.user
                )
                return Response(
                    {'message': 'Business closed successfully'},
                    status=status.HTTP_200_OK
                )
            except ValueError as e:
                return Response(
                    {'error': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class OrganizationTaxPayerReopenBusinessView(APIView):
    """Reopen an organization taxpayer's business"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk):
        try:
            taxpayer = OrganizationTaxPayer.objects.get(pk=pk)
        except OrganizationTaxPayer.DoesNotExist:
            return Response(
                {'error': 'Taxpayer not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        if not taxpayer.is_business_closed:
            return Response(
                {'error': 'Business is not closed'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            taxpayer.reopen_business(reopened_by_user=request.user)
            return Response(
                {'message': 'Business reopened successfully'},
                status=status.HTTP_200_OK
            )
        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


# Statistics and Dashboard Views
@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def taxpayer_statistics(request):
    """Get tax payer statistics"""
    
    # Individual statistics
    individual_stats = {
        'total': IndividualTaxPayer.objects.filter(is_active=True).count(),
        'by_level': list(
            IndividualTaxPayer.objects.filter(is_active=True)
            .values('tax_payer_level__name', 'tax_payer_level__code')
            .annotate(count=Count('id'))
            .order_by('tax_payer_level__code')
        ),
        'by_sector': list(
            IndividualTaxPayer.objects.filter(is_active=True)
            .values('business_sector__name', 'business_sector__code')
            .annotate(count=Count('id'))
            .order_by('-count')[:10]
        ),
        'by_gender': list(
            IndividualTaxPayer.objects.filter(is_active=True)
            .values('gender')
            .annotate(count=Count('id'))
        ),
    }
    
    # Organization statistics
    organization_stats = {
        'total': OrganizationTaxPayer.objects.filter(is_active=True).count(),
        'by_level': list(
            OrganizationTaxPayer.objects.filter(is_active=True)
            .values('tax_payer_level__name', 'tax_payer_level__code')
            .annotate(count=Count('id'))
            .order_by('tax_payer_level__code')
        ),
        'by_sector': list(
            OrganizationTaxPayer.objects.filter(is_active=True)
            .values('business_sector__name', 'business_sector__code')
            .annotate(count=Count('id'))
            .order_by('-count')[:10]
        ),
        'by_type': list(
            OrganizationTaxPayer.objects.filter(is_active=True)
            .values('organization_business_type__name', 'organization_business_type__code')
            .annotate(count=Count('id'))
            .order_by('-count')
        ),
        'with_vat': OrganizationTaxPayer.objects.filter(
            is_active=True, vat_number__isnull=False
        ).count(),
    }
    
    # Overall statistics
    overall_stats = {
        'total_taxpayers': individual_stats['total'] + organization_stats['total'],
        'total_sectors': BusinessSector.objects.filter(is_active=True).count(),
        'total_sub_sectors': BusinessSubSector.objects.filter(is_active=True).count(),
    }
    
    return Response({
        'individual': individual_stats,
        'organization': organization_stats,
        'overall': overall_stats
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def search_taxpayers(request):
    """Search across both individual and organization tax payers"""
    query = request.GET.get('q', '').strip()
    
    if not query:
        return Response({'results': []})
    
    # Search individuals
    individuals = IndividualTaxPayer.objects.filter(
        Q(tin__icontains=query) |
        Q(first_name__icontains=query) |
        Q(last_name__icontains=query) |
        Q(business_name__icontains=query),
        is_active=True
    )[:10]
    
    # Search organizations
    organizations = OrganizationTaxPayer.objects.filter(
        Q(tin__icontains=query) |
        Q(business_name__icontains=query) |
        Q(trade_name__icontains=query) |
        Q(vat_number__icontains=query),
        is_active=True
    )[:10]
    
    results = []
    
    # Add individuals to results
    for individual in individuals:
        results.append({
            'id': individual.id,
            'type': 'individual',
            'tin': individual.tin,
            'name': individual.get_full_name(),
            'business_name': individual.business_name,
            'sector': individual.business_sector.name,
            'level': individual.tax_payer_level.name,
        })
    
    # Add organizations to results
    for org in organizations:
        results.append({
            'id': org.id,
            'type': 'organization',
            'tin': org.tin,
            'name': org.business_name,
            'trade_name': org.trade_name,
            'vat_number': org.vat_number,
            'sector': org.business_sector.name,
            'level': org.tax_payer_level.name,
        })
    
    return Response({'results': results})


# Daily Income Analysis Views
class DailyIncomeAnalysisListCreateView(generics.ListCreateAPIView):
    """List and create daily income analyses"""
    queryset = DailyIncomeAnalysis.objects.all()
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['analysis_year', 'taxpayer_type', 'status', 'current_level', 'recommended_level']
    search_fields = ['taxpayer_id', 'review_notes']
    ordering_fields = ['analysis_year', 'average_daily_income', 'created_at']
    ordering = ['-analysis_year', '-created_at']

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return DailyIncomeAnalysisCreateSerializer
        return DailyIncomeAnalysisSerializer


class DailyIncomeAnalysisDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, or delete a daily income analysis"""
    queryset = DailyIncomeAnalysis.objects.all()
    serializer_class = DailyIncomeAnalysisSerializer
    permission_classes = [permissions.IsAuthenticated]


class ApproveLevelUpgradeView(APIView):
    """Approve a level upgrade recommendation"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk):
        try:
            analysis = DailyIncomeAnalysis.objects.get(pk=pk)
        except DailyIncomeAnalysis.DoesNotExist:
            return Response(
                {'error': 'Analysis not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        if analysis.status != 'pending':
            return Response(
                {'error': 'Analysis is not pending approval'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not analysis.requires_level_upgrade:
            return Response(
                {'error': 'No level upgrade recommended'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            analysis.approve_level_change(request.user)

            # Mark related notification as read and dismissed
            if hasattr(analysis, 'notification'):
                analysis.notification.mark_as_read()
                analysis.notification.dismiss()

            return Response(
                {'message': 'Level upgrade approved successfully'},
                status=status.HTTP_200_OK
            )
        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


class RejectLevelUpgradeView(APIView):
    """Reject a level upgrade recommendation"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk):
        try:
            analysis = DailyIncomeAnalysis.objects.get(pk=pk)
        except DailyIncomeAnalysis.DoesNotExist:
            return Response(
                {'error': 'Analysis not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        if analysis.status != 'pending':
            return Response(
                {'error': 'Analysis is not pending approval'},
                status=status.HTTP_400_BAD_REQUEST
            )

        review_notes = request.data.get('review_notes', '')

        analysis.status = 'rejected'
        analysis.reviewed_by = request.user
        analysis.reviewed_at = timezone.now()
        analysis.review_notes = review_notes
        analysis.save()

        # Mark related notification as read and dismissed
        if hasattr(analysis, 'notification'):
            analysis.notification.mark_as_read()
            analysis.notification.dismiss()

        return Response(
            {'message': 'Level upgrade rejected'},
            status=status.HTTP_200_OK
        )


# Level Upgrade Notification Views
class LevelUpgradeNotificationListView(generics.ListAPIView):
    """List level upgrade notifications"""
    queryset = LevelUpgradeNotification.objects.filter(is_dismissed=False)
    serializer_class = LevelUpgradeNotificationSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['is_read', 'analysis__analysis_year', 'analysis__taxpayer_type']
    ordering_fields = ['created_at']
    ordering = ['-created_at']


class LevelUpgradeNotificationDetailView(generics.RetrieveUpdateAPIView):
    """Retrieve and update a level upgrade notification"""
    queryset = LevelUpgradeNotification.objects.all()
    serializer_class = LevelUpgradeNotificationSerializer
    permission_classes = [permissions.IsAuthenticated]


class MarkNotificationReadView(APIView):
    """Mark a notification as read"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk):
        try:
            notification = LevelUpgradeNotification.objects.get(pk=pk)
            notification.mark_as_read()
            return Response(
                {'message': 'Notification marked as read'},
                status=status.HTTP_200_OK
            )
        except LevelUpgradeNotification.DoesNotExist:
            return Response(
                {'error': 'Notification not found'},
                status=status.HTTP_404_NOT_FOUND
            )


class DismissNotificationView(APIView):
    """Dismiss a notification"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk):
        try:
            notification = LevelUpgradeNotification.objects.get(pk=pk)
            notification.dismiss()
            return Response(
                {'message': 'Notification dismissed'},
                status=status.HTTP_200_OK
            )
        except LevelUpgradeNotification.DoesNotExist:
            return Response(
                {'error': 'Notification not found'},
                status=status.HTTP_404_NOT_FOUND
            )


# Dashboard Statistics for Level Upgrades
@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def level_upgrade_dashboard_stats(request):
    """Get dashboard statistics for level upgrades"""

    # Pending upgrades count
    pending_upgrades = DailyIncomeAnalysis.objects.filter(
        status='pending',
        recommended_level__priority__lt=F('current_level__priority')
    ).count()

    # Unread notifications count
    unread_notifications = LevelUpgradeNotification.objects.filter(
        is_read=False,
        is_dismissed=False
    ).count()

    # Recent analyses (last 30 days)
    from datetime import datetime, timedelta
    thirty_days_ago = datetime.now() - timedelta(days=30)
    recent_analyses = DailyIncomeAnalysis.objects.filter(
        created_at__gte=thirty_days_ago
    ).count()

    # Level distribution of pending upgrades
    level_distribution = DailyIncomeAnalysis.objects.filter(
        status='pending',
        recommended_level__priority__lt=F('current_level__priority')
    ).values(
        'recommended_level__code',
        'recommended_level__name'
    ).annotate(
        count=Count('id')
    ).order_by('recommended_level__priority')

    return Response({
        'pending_upgrades': pending_upgrades,
        'unread_notifications': unread_notifications,
        'recent_analyses': recent_analyses,
        'level_distribution': list(level_distribution)
    })
