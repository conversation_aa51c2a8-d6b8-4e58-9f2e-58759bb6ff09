import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  Typography,
  IconButton,
  Button,
  Chip,
  Grid,
  Divider,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Skeleton,
  Avatar,
  Tooltip,
  Paper,
} from '@mui/material';
import {
  ArrowBack,
  MoreVert,
  Edit,
  Delete,
  Download,
  Visibility,
  Print,
  Description,
  CloudDownload,
  QrCode,
  CropFree,
  LocationOn,
  CalendarToday,
  Person,
  Business,
  Tag,
  Schedule,
  Warning,
  CheckCircle,
  Info,
} from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import documentService from '../../services/documentService';
import type { Document } from '../../services/documentService';

const DocumentDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const { showSuccess, showError } = useNotification();

  // Determine the back navigation path based on current route
  const getBackPath = () => {
    if (location.pathname.includes('/document-center/')) {
      return '/document-center/documents';
    }
    return '/documents';
  };

  const [document, setDocument] = useState<Document | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [downloading, setDownloading] = useState(false);

  useEffect(() => {
    loadDocument();
  }, [id]);

  const loadDocument = async () => {
    if (!id) return;

    try {
      setLoading(true);
      setError(null);
      const data = await documentService.getDocument(id);
      setDocument(data);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to load document');
    } finally {
      setLoading(false);
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleEdit = () => {
    // Navigate to the documents list page with edit state
    const backPath = getBackPath();
    navigate(backPath, {
      state: {
        editDocument: document,
        showForm: true
      }
    });
    handleMenuClose();
  };

  const handleDownload = async () => {
    if (!document || !document.file) {
      showError('No file available for download');
      return;
    }

    try {
      setDownloading(true);
      console.log('Starting download for document:', document.id);

      const blob = await documentService.downloadDocument(document.id);
      console.log('Download successful, blob size:', blob.size, 'type:', blob.type);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = window.document.createElement('a'); // Fixed: use window.document
      link.href = url;

      // Extract filename from document title or use default
      const filename = document.title ?
        `${document.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.${document.file.split('.').pop()}` :
        `document-${document.id}.${document.file.split('.').pop()}`;

      console.log('Download filename:', filename);
      link.download = filename;
      window.document.body.appendChild(link); // Fixed: use window.document
      link.click();
      window.document.body.removeChild(link); // Fixed: use window.document
      window.URL.revokeObjectURL(url);

      showSuccess('Document downloaded successfully');
    } catch (err: any) {
      console.error('Download failed:', err);
      showError(`Failed to download document: ${err.message || 'Unknown error'}`);
    } finally {
      setDownloading(false);
    }
    handleMenuClose();
  };

  const handlePrint = async () => {
    if (!document || !document.file) {
      showError('No file available for printing');
      return;
    }

    try {
      await documentService.printDocument(document.id);
      showSuccess('Document sent to printer');
    } catch (err: any) {
      console.error('Print failed:', err);
      showError(`Failed to print document: ${err.message || 'Unknown error'}`);
    }
  };

  const handleDelete = async () => {
    if (!document) return;

    try {
      await documentService.deleteDocument(document.id);
      showSuccess('Document deleted successfully');
      navigate(getBackPath());
    } catch (err: any) {
      showError(err.response?.data?.detail || 'Failed to delete document');
    }
    setDeleteDialogOpen(false);
    handleMenuClose();
  };

  const handleGenerateBarcode = async () => {
    if (!document) return;

    try {
      const updatedDoc = await documentService.generateBarcode(document.id);
      setDocument(updatedDoc);
      showSuccess('Barcode generated successfully');
    } catch (err: any) {
      showError('Failed to generate barcode');
    }
    handleMenuClose();
  };

  const handleView = async () => {
    if (!document || !document.file) {
      showError('No file available for viewing');
      return;
    }

    try {
      const url = await documentService.viewDocument(document.id);
      const viewWindow = window.open(url, '_blank');

      // Clean up the URL after a delay to prevent memory leaks
      setTimeout(() => {
        window.URL.revokeObjectURL(url);
      }, 60000); // Clean up after 1 minute

      if (!viewWindow) {
        showError('Could not open document. Please check your popup blocker settings.');
      }
    } catch (err: any) {
      showError('Failed to view document');
    }
    handleMenuClose();
  };

  const handlePrint = async () => {
    if (!document || !document.file) {
      showError('No file available for printing');
      return;
    }

    try {
      await documentService.printDocument(document.id);
    } catch (err: any) {
      showError('Failed to print document');
    }
    handleMenuClose();
  };

  const handleGenerateQR = async () => {
    if (!document) return;

    try {
      const updatedDoc = await documentService.generateQRCode(document.id);
      setDocument(updatedDoc);
      showSuccess('QR code generated successfully');
    } catch (err: any) {
      showError('Failed to generate QR code');
    }
    handleMenuClose();
  };

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Skeleton variant="rectangular" width="100%" height={200} />
        <Box sx={{ mt: 2 }}>
          <Skeleton variant="text" width="60%" height={40} />
          <Skeleton variant="text" width="40%" height={30} />
        </Box>
      </Box>
    );
  }

  if (error || !document) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error || 'Document not found'}</Alert>
      </Box>
    );
  }

  const daysUntilExpiry = documentService.getDaysUntilExpiry(document.expiry_date);
  const isExpiringSoon = documentService.isExpiringSoon(document.expiry_date);

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <IconButton onClick={() => navigate(getBackPath())}>
            <ArrowBack />
          </IconButton>
          <Avatar sx={{ bgcolor: 'primary.main', width: 48, height: 48 }}>
            <Description />
          </Avatar>
          <Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="h4" component="h1">
                {document.title}
              </Typography>
              <Chip
                label={document.status}
                color={documentService.getStatusColor(document.status)}
                size="small"
              />
              <Chip
                label={document.mode}
                color={documentService.getModeColor(document.mode)}
                size="small"
              />
              {isExpiringSoon && (
                <Chip
                  icon={<Warning />}
                  label={`Expires in ${daysUntilExpiry} days`}
                  color="warning"
                  size="small"
                />
              )}
            </Box>
            <Typography variant="subtitle1" color="text.secondary">
              {document.document_type_name} • {document.reference_number || 'No reference'}
            </Typography>
          </Box>
        </Box>

        <Box sx={{ display: 'flex', gap: 1 }}>
          {document.file && (
            <Button
              variant="outlined"
              startIcon={<Visibility />}
              onClick={handleView}
            >
              View
            </Button>
          )}
          <Button
            variant="outlined"
            startIcon={<Edit />}
            onClick={handleEdit}
          >
            Edit
          </Button>
          <IconButton onClick={handleMenuOpen}>
            <MoreVert />
          </IconButton>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Main Information */}
        <Grid item xs={12} md={8}>
          <Card sx={{ mb: 2 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Description color="primary" />
                Document Information
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="caption" color="text.secondary">
                      Description
                    </Typography>
                    <Typography variant="body1">
                      {document.description || 'No description provided'}
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <Business color="action" />
                    <Box>
                      <Typography variant="caption" color="text.secondary">
                        Organization
                      </Typography>
                      <Typography variant="body2">
                        {document.organization_name}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <Person color="action" />
                    <Box>
                      <Typography variant="caption" color="text.secondary">
                        Created By
                      </Typography>
                      <Typography variant="body2">
                        {document.created_by_name}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                {document.document_date && (
                  <Grid item xs={12} sm={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                      <CalendarToday color="action" />
                      <Box>
                        <Typography variant="caption" color="text.secondary">
                          Document Date
                        </Typography>
                        <Typography variant="body2">
                          {documentService.formatDate(document.document_date)}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>
                )}

                {document.expiry_date && (
                  <Grid item xs={12} sm={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                      <Schedule color="action" />
                      <Box>
                        <Typography variant="caption" color="text.secondary">
                          Expiry Date
                        </Typography>
                        <Typography variant="body2" color={document.is_expired ? 'error' : 'inherit'}>
                          {documentService.formatDate(document.expiry_date)}
                          {document.is_expired && ' (Expired)'}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>
                )}

                {document.kent_location && (
                  <Grid item xs={12} sm={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                      <LocationOn color="action" />
                      <Box>
                        <Typography variant="caption" color="text.secondary">
                          Physical Location
                        </Typography>
                        <Typography variant="body2">
                          {document.kent_location}
                        </Typography>
                        {document.kent_code && (
                          <Typography variant="caption" color="text.secondary">
                            Code: {document.kent_code}
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  </Grid>
                )}

                {document.tags && document.tags.length > 0 && (
                  <Grid item xs={12}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                      <Tag color="action" />
                      <Box>
                        <Typography variant="caption" color="text.secondary">
                          Tags
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap', mt: 0.5 }}>
                          {document.tags.map((tag, index) => (
                            <Chip
                              key={index}
                              label={tag}
                              size="small"
                              variant="outlined"
                            />
                          ))}
                        </Box>
                      </Box>
                    </Box>
                  </Grid>
                )}
              </Grid>
            </CardContent>
          </Card>

          {/* File Information */}
          {document.file && (
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <CloudDownload color="primary" />
                  File Information
                </Typography>

                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="caption" color="text.secondary">
                      File Name
                    </Typography>
                    <Typography variant="body2">
                      {document.file_name || 'Unknown'}
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Typography variant="caption" color="text.secondary">
                      File Size
                    </Typography>
                    <Typography variant="body2">
                      {documentService.formatFileSize(document.file_size)}
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Typography variant="caption" color="text.secondary">
                      File Type
                    </Typography>
                    <Typography variant="body2">
                      {document.file_type || 'Unknown'}
                    </Typography>
                  </Grid>

                  <Grid item xs={12}>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Button
                        variant="contained"
                        startIcon={<Visibility />}
                        onClick={handleView}
                        fullWidth
                      >
                        View Document
                      </Button>
                      <Button
                        variant="outlined"
                        startIcon={<Print />}
                        onClick={handlePrint}
                        fullWidth
                      >
                        Print
                      </Button>
                      <Button
                        variant="outlined"
                        startIcon={<Download />}
                        onClick={handleDownload}
                        disabled={downloading}
                        fullWidth
                      >
                        {downloading ? 'Downloading...' : 'Download'}
                      </Button>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          )}
        </Grid>

        {/* Sidebar */}
        <Grid item xs={12} md={4}>
          <Card sx={{ mb: 2 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Info color="primary" />
                Status & Metadata
              </Typography>

              <Box sx={{ mb: 2 }}>
                <Typography variant="caption" color="text.secondary">
                  Status
                </Typography>
                <Box sx={{ mt: 0.5 }}>
                  <Chip
                    label={document.status.replace('_', ' ').toUpperCase()}
                    color={documentService.getStatusColor(document.status)}
                    size="small"
                  />
                </Box>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="caption" color="text.secondary">
                  Mode
                </Typography>
                <Box sx={{ mt: 0.5 }}>
                  <Chip
                    label={document.mode.toUpperCase()}
                    color={documentService.getModeColor(document.mode)}
                    size="small"
                  />
                </Box>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="caption" color="text.secondary">
                  Can be Requested
                </Typography>
                <Box sx={{ mt: 0.5 }}>
                  <Chip
                    icon={document.can_be_requested ? <CheckCircle /> : <Warning />}
                    label={document.can_be_requested ? 'Yes' : 'No'}
                    color={document.can_be_requested ? 'success' : 'warning'}
                    size="small"
                  />
                </Box>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Box sx={{ mb: 2 }}>
                <Typography variant="caption" color="text.secondary">
                  Created
                </Typography>
                <Typography variant="body2">
                  {documentService.formatDateTime(document.created_at)}
                </Typography>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="caption" color="text.secondary">
                  Last Updated
                </Typography>
                <Typography variant="body2">
                  {documentService.formatDateTime(document.updated_at)}
                </Typography>
              </Box>

              {document.retention_date && (
                <Box>
                  <Typography variant="caption" color="text.secondary">
                    Retention Until
                  </Typography>
                  <Typography variant="body2">
                    {documentService.formatDate(document.retention_date)}
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>

          {/* Codes & Identifiers */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <QrCode color="primary" />
                Codes & Identifiers
              </Typography>

              {document.reference_number && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" color="text.secondary">
                    Reference Number
                  </Typography>
                  <Typography variant="body2" fontFamily="monospace">
                    {document.reference_number}
                  </Typography>
                </Box>
              )}

              <Box sx={{ mb: 2 }}>
                <Typography variant="caption" color="text.secondary">
                  Document ID
                </Typography>
                <Typography variant="body2" fontFamily="monospace">
                  {document.id}
                </Typography>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Box sx={{ display: 'flex', gap: 1, flexDirection: 'column' }}>
                {document.barcode_image ? (
                  <Paper sx={{ p: 1, textAlign: 'center' }}>
                    <img
                      src={document.barcode_image}
                      alt="Barcode"
                      style={{ maxWidth: '100%', height: 'auto' }}
                    />
                    <Typography variant="caption" display="block">
                      Barcode
                    </Typography>
                  </Paper>
                ) : (
                  <Button
                    variant="outlined"
                    startIcon={<CropFree />}
                    onClick={handleGenerateBarcode}
                    size="small"
                  >
                    Generate Barcode
                  </Button>
                )}

                {document.qr_code_image ? (
                  <Paper sx={{ p: 1, textAlign: 'center' }}>
                    <img
                      src={document.qr_code_image}
                      alt="QR Code"
                      style={{ maxWidth: '100%', height: 'auto' }}
                    />
                    <Typography variant="caption" display="block">
                      QR Code
                    </Typography>
                  </Paper>
                ) : (
                  <Button
                    variant="outlined"
                    startIcon={<QrCode />}
                    onClick={handleGenerateQR}
                    size="small"
                  >
                    Generate QR Code
                  </Button>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleEdit}>
          <Edit fontSize="small" sx={{ mr: 1 }} />
          Edit Document
        </MenuItem>
        {document.file && [
          <MenuItem key="view" onClick={handleView}>
            <Visibility fontSize="small" sx={{ mr: 1 }} />
            View File
          </MenuItem>,
          <MenuItem key="download" onClick={handleDownload}>
            <Download fontSize="small" sx={{ mr: 1 }} />
            Download File
          </MenuItem>,
          <MenuItem key="print" onClick={handlePrint}>
            <Print fontSize="small" sx={{ mr: 1 }} />
            Print File
          </MenuItem>
        ]}
        <MenuItem onClick={handleGenerateBarcode}>
          <CropFree fontSize="small" sx={{ mr: 1 }} />
          Generate Barcode
        </MenuItem>
        <MenuItem onClick={handleGenerateQR}>
          <QrCode fontSize="small" sx={{ mr: 1 }} />
          Generate QR Code
        </MenuItem>
        <MenuItem onClick={() => setDeleteDialogOpen(true)} sx={{ color: 'error.main' }}>
          <Delete fontSize="small" sx={{ mr: 1 }} />
          Delete Document
        </MenuItem>
      </Menu>

      {/* Delete Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Delete Document</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete "{document.title}"? This action cannot be undone.
          </Typography>
          {document.file && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              This will also permanently delete the associated file.
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default DocumentDetailPage;
