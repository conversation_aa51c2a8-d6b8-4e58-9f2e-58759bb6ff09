import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Alert,
  Skeleton,

  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,

  Paper,
} from '@mui/material';
import {
  ArrowBack,
  Save,
  Add,
  Remove,
  Search,
  Description,
  Assignment,
  PriorityHigh,

} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { useNotification } from '../../contexts/NotificationContext';
import requestService from '../../services/requestService';
import documentService from '../../services/documentService';
import type { RequestCreate, RequestUpdate, DocumentRequest } from '../../services/requestService';
import type { Document } from '../../services/documentService';

interface RequestFormPageProps {
  mode: 'create' | 'edit';
}

const RequestFormPage: React.FC<RequestFormPageProps> = ({ mode }) => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showSuccess, showError } = useNotification();

  // Initialize with current time for required_date and 15 minutes later for due_date (only for create mode)
  const now = new Date();
  const minimumDueDate = new Date(now.getTime() + 15 * 60 * 1000); // 15 minutes from now

  const [formData, setFormData] = useState<RequestCreate>({
    documents: [],
    purpose: '',
    priority: 'normal',
    required_date: mode === 'create' ? now.toISOString() : '',
    due_date: mode === 'create' ? minimumDueDate.toISOString() : '',
  });

  const [selectedDocuments, setSelectedDocuments] = useState<Document[]>([]);
  const [availableDocuments, setAvailableDocuments] = useState<Document[]>([]);
  const [documentSearchQuery, setDocumentSearchQuery] = useState('');
  const [documentDialogOpen, setDocumentDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const priorityOptions = [
    { value: 'low', label: 'Low', color: 'success' },
    { value: 'normal', label: 'Normal', color: 'info' },
    { value: 'high', label: 'High', color: 'warning' },
    { value: 'urgent', label: 'Urgent', color: 'error' },
  ];

  useEffect(() => {
    if (mode === 'edit' && id) {
      loadRequest();
    }
    loadAvailableDocuments();
  }, [mode, id]);

  const loadRequest = async () => {
    if (!id) return;

    try {
      setLoading(true);
      const data = await requestService.getRequest(id);
      setFormData({
        documents: data.documents,
        purpose: data.purpose,
        priority: data.priority,
        required_date: data.required_date,
        due_date: data.due_date,
      });

      // Load selected documents details
      const documentsDetails = await Promise.all(
        data.documents.map(docId => documentService.getDocument(docId))
      );
      setSelectedDocuments(documentsDetails);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to load request');
    } finally {
      setLoading(false);
    }
  };

  const loadAvailableDocuments = async () => {
    try {
      const response = await documentService.getDocuments({
        status: 'active',
        page_size: 100,
        search: documentSearchQuery || undefined,
      });
      setAvailableDocuments(response.results.filter(doc => doc.can_be_requested));
    } catch (err: any) {
      showError('Failed to load available documents');
    }
  };

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      loadAvailableDocuments();
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [documentSearchQuery]);

  const handleInputChange = (field: keyof RequestCreate) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | { target: { value: unknown } }
  ) => {
    const value = event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleDateChange = (field: 'required_date' | 'due_date') => (date: Date | null) => {
    setFormData(prev => ({
      ...prev,
      [field]: date ? date.toISOString() : ''
    }));
  };

  const handleAddDocument = (document: Document) => {
    if (!formData.documents.includes(document.id)) {
      setFormData(prev => ({
        ...prev,
        documents: [...prev.documents, document.id]
      }));
      setSelectedDocuments(prev => [...prev, document]);
    }
    setDocumentDialogOpen(false);
  };

  const handleRemoveDocument = (documentId: string) => {
    setFormData(prev => ({
      ...prev,
      documents: prev.documents.filter(id => id !== documentId)
    }));
    setSelectedDocuments(prev => prev.filter(doc => doc.id !== documentId));
  };

  const validateForm = () => {
    if (formData.documents.length === 0) {
      showError('At least one document must be selected');
      return false;
    }
    if (!formData.purpose.trim()) {
      showError('Purpose is required');
      return false;
    }
    if (!formData.required_date) {
      showError('Required date is required');
      return false;
    }
    if (!formData.due_date) {
      showError('Due date is required');
      return false;
    }

    // Validate dates with time
    const requiredDate = new Date(formData.required_date);
    const dueDate = new Date(formData.due_date);

    // Due date must be at least 15 minutes after required date
    const minimumDueTime = new Date(requiredDate.getTime() + 15 * 60 * 1000);

    if (dueDate < minimumDueTime) {
      showError('Due date must be at least 15 minutes after required date');
      return false;
    }

    return true;
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!validateForm()) return;

    try {
      setSaving(true);
      setError(null);

      if (mode === 'create') {
        const newRequest = await requestService.createRequest(formData);
        showSuccess('Request created successfully');
        navigate(`/requests/${newRequest.id}`);
      } else {
        const updateData: RequestUpdate = {
          documents: formData.documents,
          purpose: formData.purpose,
          priority: formData.priority,
          required_date: formData.required_date,
          due_date: formData.due_date,
        };

        await requestService.updateRequest(id!, updateData);
        showSuccess('Request updated successfully');
        navigate(`/requests/${id}`);
      }
    } catch (err: any) {
      setError(err.response?.data?.detail || `Failed to ${mode} request`);
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    if (mode === 'edit' && id) {
      navigate(`/requests/${id}`);
    } else {
      navigate('/requests');
    }
  };

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Skeleton variant="rectangular" width="100%" height={400} />
      </Box>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <IconButton onClick={handleCancel}>
              <ArrowBack />
            </IconButton>
            <Typography variant="h4" component="h1">
              {mode === 'create' ? 'Create Request' : 'Edit Request'}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button variant="outlined" onClick={handleCancel}>
              Cancel
            </Button>
            <Button
              variant="contained"
              startIcon={<Save />}
              onClick={handleSubmit}
              disabled={saving}
            >
              {saving ? 'Saving...' : 'Save'}
            </Button>
          </Box>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <form onSubmit={handleSubmit}>
          <Grid container spacing={3}>
            {/* Main Form */}
            <Grid item xs={12} md={8}>
              <Card sx={{ mb: 2 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Assignment color="primary" />
                    Request Information
                  </Typography>

                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Purpose"
                        multiline
                        rows={3}
                        value={formData.purpose}
                        onChange={handleInputChange('purpose')}
                        required
                        placeholder="Explain why you need these documents..."
                      />
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <FormControl fullWidth required>
                        <InputLabel>Priority</InputLabel>
                        <Select
                          value={formData.priority}
                          label="Priority"
                          onChange={handleInputChange('priority')}
                        >
                          {priorityOptions.map((option) => (
                            <MenuItem key={option.value} value={option.value}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Chip
                                  label={option.label}
                                  color={option.color as any}
                                  size="small"
                                  icon={<PriorityHigh />}
                                />
                              </Box>
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <DateTimePicker
                        label="Required Date & Time"
                        value={formData.required_date ? new Date(formData.required_date) : null}
                        onChange={handleDateChange('required_date')}
                        slotProps={{
                          textField: {
                            fullWidth: true,
                            required: true,
                            placeholder: 'When do you need the documents?'
                          }
                        }}
                        minDateTime={new Date()} // Cannot select past dates
                      />
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <DateTimePicker
                        label="Due Date & Time"
                        value={formData.due_date ? new Date(formData.due_date) : null}
                        onChange={handleDateChange('due_date')}
                        slotProps={{
                          textField: {
                            fullWidth: true,
                            required: true,
                            placeholder: 'When will you return the documents?'
                          }
                        }}
                        minDateTime={
                          formData.required_date
                            ? new Date(new Date(formData.required_date).getTime() + 15 * 60 * 1000)
                            : new Date(new Date().getTime() + 15 * 60 * 1000)
                        } // Minimum 15 minutes after required date
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              {/* Selected Documents */}
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Description color="primary" />
                      Selected Documents ({selectedDocuments.length})
                    </Typography>
                    <Button
                      variant="outlined"
                      startIcon={<Add />}
                      onClick={() => setDocumentDialogOpen(true)}
                    >
                      Add Documents
                    </Button>
                  </Box>

                  {selectedDocuments.length === 0 ? (
                    <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'grey.50' }}>
                      <Description sx={{ fontSize: 48, color: 'text.secondary', mb: 1 }} />
                      <Typography variant="h6" color="text.secondary" gutterBottom>
                        No documents selected
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        Click "Add Documents" to select documents for this request
                      </Typography>
                      <Button
                        variant="contained"
                        startIcon={<Add />}
                        onClick={() => setDocumentDialogOpen(true)}
                      >
                        Add Documents
                      </Button>
                    </Paper>
                  ) : (
                    <List>
                      {selectedDocuments.map((document, index) => (
                        <ListItem key={document.id} divider={index < selectedDocuments.length - 1}>
                          <ListItemAvatar>
                            <Avatar sx={{ bgcolor: 'primary.main' }}>
                              <Description />
                            </Avatar>
                          </ListItemAvatar>
                          <ListItemText
                            primary={document.title}
                            secondary={
                              <Box>
                                <Typography variant="caption" color="text.secondary">
                                  Type: {document.document_type_name} • Mode: {document.mode}
                                </Typography>
                                <br />
                                <Typography variant="caption" color="text.secondary">
                                  Location: {document.location_display}
                                </Typography>
                              </Box>
                            }
                          />
                          <ListItemSecondaryAction>
                            <IconButton
                              edge="end"
                              onClick={() => handleRemoveDocument(document.id)}
                              color="error"
                            >
                              <Remove />
                            </IconButton>
                          </ListItemSecondaryAction>
                        </ListItem>
                      ))}
                    </List>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* Sidebar */}
            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Request Guidelines
                  </Typography>

                  <Typography variant="body2" color="text.secondary" paragraph>
                    <strong>Purpose:</strong> Clearly explain why you need these documents. This helps with approval decisions.
                  </Typography>

                  <Typography variant="body2" color="text.secondary" paragraph>
                    <strong>Priority:</strong> Select the appropriate priority level:
                  </Typography>

                  <Box sx={{ ml: 2, mb: 2 }}>
                    {priorityOptions.map((option) => (
                      <Box key={option.value} sx={{ mb: 1 }}>
                        <Chip
                          label={option.label}
                          color={option.color as any}
                          size="small"
                          sx={{ mr: 1 }}
                        />
                        <Typography variant="caption" color="text.secondary">
                          {option.value === 'low' && 'Standard processing time'}
                          {option.value === 'normal' && 'Regular business need'}
                          {option.value === 'high' && 'Important business need'}
                          {option.value === 'urgent' && 'Critical business need'}
                        </Typography>
                      </Box>
                    ))}
                  </Box>

                  <Typography variant="body2" color="text.secondary" paragraph>
                    <strong>Dates & Times:</strong> Required date/time is when you need the documents. Due date/time is when you'll return them (minimum 15 minutes after required time).
                  </Typography>

                  <Typography variant="body2" color="text.secondary">
                    <strong>Documents:</strong> Only active documents that can be requested are available for selection.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </form>

        {/* Document Selection Dialog */}
        <Dialog open={documentDialogOpen} onClose={() => setDocumentDialogOpen(false)} maxWidth="md" fullWidth>
          <DialogTitle>Select Documents</DialogTitle>
          <DialogContent>
            <TextField
              fullWidth
              placeholder="Search documents..."
              value={documentSearchQuery}
              onChange={(e) => setDocumentSearchQuery(e.target.value)}
              sx={{ mb: 2 }}
              InputProps={{
                startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />,
              }}
            />

            <List sx={{ maxHeight: 400, overflow: 'auto' }}>
              {availableDocuments
                .filter(doc => !formData.documents.includes(doc.id))
                .map((document) => (
                  <ListItem
                    key={document.id}
                    button
                    onClick={() => handleAddDocument(document)}
                  >
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'primary.main' }}>
                        <Description />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={document.title}
                      secondary={
                        <Box>
                          <Typography variant="caption" color="text.secondary">
                            Type: {document.document_type_name} • Mode: {document.mode}
                          </Typography>
                          <br />
                          <Typography variant="caption" color="text.secondary">
                            Location: {document.location_display}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              {availableDocuments.filter(doc => !formData.documents.includes(doc.id)).length === 0 && (
                <ListItem>
                  <ListItemText
                    primary="No available documents"
                    secondary="All documents are either already selected or not available for request"
                  />
                </ListItem>
              )}
            </List>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDocumentDialogOpen(false)}>Close</Button>
          </DialogActions>
        </Dialog>
      </Box>
    </LocalizationProvider>
  );
};

// Create a wrapper component that determines the mode
const CreateRequestPage: React.FC = () => {
  return <RequestFormPage mode="create" />;
};

export default CreateRequestPage;
export { RequestFormPage };
