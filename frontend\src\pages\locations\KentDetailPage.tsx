import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
} from '@mui/material';
import {
  Inventory,
  Edit,
  Delete,
  Home,
  LocationOn,
  Info,
  Storage,
  Business,
  Folder,
  QrCode,
  Description,
} from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import locationService from '../../services/locationService';
import type { Kent } from '../../services/locationService';
import DetailPageTemplate from '../../components/common/DetailPageTemplate';
import ConfirmationDialog from '../../components/common/ConfirmationDialog';

const KentDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showNotification } = useNotification();
  
  const [kent, setKent] = useState<Kent | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    if (id) {
      loadKent();
    }
  }, [id]);

  const loadKent = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await locationService.getKent(Number(id));
      setKent(data);
    } catch (error) {
      console.error('Error loading kent:', error);
      setError('Failed to load kent');
      showNotification('Failed to load kent', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    // Navigate back to kents page with edit state
    navigate('/locations/kents', {
      state: {
        editKent: kent,
        showForm: true
      }
    });
  };

  const handleDelete = () => {
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!kent) return;
    
    try {
      setDeleting(true);
      await locationService.deleteKent(kent.id);
      showNotification('Kent deleted successfully', 'success');
      navigate('/locations/kents');
    } catch (error) {
      console.error('Error deleting kent:', error);
      showNotification('Failed to delete kent', 'error');
    } finally {
      setDeleting(false);
      setDeleteDialogOpen(false);
    }
  };

  const handleBack = () => {
    navigate('/locations/kents');
  };

  if (!kent && !loading && !error) {
    setError('Kent not found');
  }

  const breadcrumbs = [
    { label: 'Dashboard', path: '/dashboard', icon: <Home fontSize="small" /> },
    { label: 'Locations', path: '/locations', icon: <LocationOn fontSize="small" /> },
    { label: 'Kents', path: '/locations/kents', icon: <Inventory fontSize="small" /> },
    { label: kent?.name || 'Kent', path: undefined, icon: <Folder fontSize="small" /> },
  ];

  const actions = [
    {
      label: 'Edit',
      icon: <Edit />,
      onClick: handleEdit,
      color: 'primary' as const,
    },
    {
      label: 'Delete',
      icon: <Delete />,
      onClick: handleDelete,
      color: 'error' as const,
    },
  ];

  const chips = kent ? [
    {
      label: kent.is_active ? 'Active' : 'Inactive',
      color: kent.is_active ? 'success' as const : 'error' as const,
    },
    {
      label: kent.is_full ? 'Full' : 'Available',
      color: kent.is_full ? 'error' as const : 'success' as const,
    },
  ] : [];

  const sections = kent ? [
    {
      title: 'Basic Information',
      icon: <Info />,
      content: (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'primary.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'primary.200'
          }}>
            <Folder sx={{ color: 'primary.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="primary.main" sx={{ fontWeight: 600 }}>
                Kent Name
              </Typography>
              <Typography variant="body2" color="primary.dark">
                {kent.name}
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'info.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'info.200'
          }}>
            <QrCode sx={{ color: 'info.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="info.main" sx={{ fontWeight: 600 }}>
                Full Code
              </Typography>
              <Typography variant="body2" color="info.dark">
                {kent.full_code}
              </Typography>
            </Box>
          </Box>
          
          {kent.description && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" color="text.secondary" sx={{ fontWeight: 600, mb: 1 }}>
                Description
              </Typography>
              <Typography variant="body2" sx={{ 
                p: 2, 
                bgcolor: 'grey.50', 
                borderRadius: 1,
                border: '1px solid',
                borderColor: 'grey.200'
              }}>
                {kent.description}
              </Typography>
            </Box>
          )}
        </Box>
      ),
    },
    {
      title: 'Location Hierarchy',
      icon: <LocationOn />,
      content: (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'secondary.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'secondary.200'
          }}>
            <Business sx={{ color: 'secondary.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="secondary.main" sx={{ fontWeight: 600 }}>
                Building
              </Typography>
              <Typography variant="body2" color="secondary.dark">
                {kent.building_name}
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'success.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'success.200'
          }}>
            <Storage sx={{ color: 'success.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="success.main" sx={{ fontWeight: 600 }}>
                Shelf
              </Typography>
              <Typography variant="body2" color="success.dark">
                {kent.shelf_name}
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'warning.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'warning.200'
          }}>
            <Inventory sx={{ color: 'warning.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="warning.main" sx={{ fontWeight: 600 }}>
                Box Position
              </Typography>
              <Typography variant="body2" color="warning.dark">
                {kent.box_position}
              </Typography>
            </Box>
          </Box>
        </Box>
      ),
    },
    {
      title: 'File Statistics',
      icon: <Description />,
      content: (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'primary.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'primary.200'
          }}>
            <Description sx={{ color: 'primary.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="primary.main" sx={{ fontWeight: 600 }}>
                Files
              </Typography>
              <Typography variant="body2" color="primary.dark">
                {kent.file_count || 0} files stored
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'success.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'success.200'
          }}>
            <Folder sx={{ color: 'success.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="success.main" sx={{ fontWeight: 600 }}>
                Documents
              </Typography>
              <Typography variant="body2" color="success.dark">
                {kent.document_count || 0} documents
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'info.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'info.200'
          }}>
            <Inventory sx={{ color: 'info.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="info.main" sx={{ fontWeight: 600 }}>
                Utilization
              </Typography>
              <Typography variant="body2" color="info.dark">
                {kent.utilization?.toFixed(1) || '0.0'}% utilized
              </Typography>
            </Box>
          </Box>
        </Box>
      ),
    },
  ] : [];

  return (
    <>
      <DetailPageTemplate
        loading={loading}
        error={error}
        breadcrumbs={breadcrumbs}
        title={kent?.name || 'Kent'}
        subtitle={kent?.full_code ? `Code: ${kent.full_code}` : undefined}
        avatar={{
          fallbackIcon: <Folder sx={{ fontSize: 40 }} />,
          alt: kent?.name || 'Kent',
        }}
        chips={chips}
        actions={actions}
        sections={sections}
        onBack={handleBack}
      />

      <ConfirmationDialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleConfirmDelete}
        title="Delete Kent"
        itemName={kent?.name}
        itemType="Kent"
        message={`Are you sure you want to delete "${kent?.name}"?`}
        confirmText="Delete Kent"
        severity="error"
        loading={deleting}
      />
    </>
  );
};

export default KentDetailPage;
