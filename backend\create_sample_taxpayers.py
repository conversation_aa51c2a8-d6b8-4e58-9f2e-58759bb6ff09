#!/usr/bin/env python
"""
<PERSON>ript to create sample taxpayer data for analytics testing
"""
import os
import sys
import django
import random
from datetime import datetime, timedelta
from faker import Faker

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'document_management.settings')
django.setup()

from taxpayers.models import (
    TaxPayerLevel, BusinessSector, BusinessSubSector, OrganizationBusinessType,
    IndividualTaxPayer, OrganizationTaxPayer
)
from geographical_locations.models import Country, Region, Zone, City, SubCity, Kebele

fake = Faker()

def create_sample_taxpayers():
    print("Creating sample taxpayer data for analytics...")
    
    # Get existing data
    try:
        tax_levels = list(TaxPayerLevel.objects.all())
        business_sectors = list(BusinessSector.objects.all())
        business_sub_sectors = list(BusinessSubSector.objects.all())
        org_business_types = list(OrganizationBusinessType.objects.all())
        
        # Get location data
        countries = list(Country.objects.all())
        regions = list(Region.objects.all())
        cities = list(City.objects.all())
        subcities = list(SubCity.objects.all())
        kebeles = list(Kebele.objects.all())
        
        if not all([tax_levels, business_sectors, business_sub_sectors]):
            print("❌ Missing required reference data. Please run the basic data creation script first.")
            return
            
        print(f"📊 Found {len(tax_levels)} tax levels, {len(business_sectors)} sectors, {len(business_sub_sectors)} sub-sectors")
        
        # Create Individual Taxpayers
        individual_count = 0
        for i in range(200):  # Create 200 individual taxpayers
            try:
                # Generate unique TIN
                tin = f"{random.randint(**********, **********)}"
                
                # Check if TIN already exists
                if IndividualTaxPayer.objects.filter(tin=tin).exists():
                    continue
                
                # Random location selection
                country = random.choice(countries) if countries else None
                region = random.choice(regions) if regions else None
                city = random.choice(cities) if cities else None
                subcity = random.choice(subcities) if subcities else None
                kebele = random.choice(kebeles) if kebeles else None
                
                individual = IndividualTaxPayer.objects.create(
                    tin=tin,
                    first_name=fake.first_name(),
                    middle_name=fake.first_name() if random.choice([True, False]) else '',
                    last_name=fake.last_name(),
                    nationality=random.choice(['ET', 'US', 'UK', 'CA', 'DE', 'FR']),
                    gender=random.choice(['M', 'F', 'O']),
                    date_of_birth=fake.date_of_birth(minimum_age=18, maximum_age=70),
                    tax_payer_level=random.choice(tax_levels),
                    business_sector=random.choice(business_sectors),
                    business_sub_sector=random.choice(business_sub_sectors),
                    business_registration_date=fake.date_between(start_date='-5y', end_date='today'),
                    business_name=fake.company() if random.choice([True, False]) else '',
                    business_license_number=f"BL{random.randint(100000, 999999)}" if random.choice([True, False]) else '',
                    phone=f"+251{random.choice(['9', '7'])}{random.randint(10000000, 99999999)}",
                    phone_secondary=f"+251{random.choice(['9', '7'])}{random.randint(10000000, 99999999)}" if random.choice([True, False]) else '',
                    email=fake.email() if random.choice([True, False]) else '',
                    country=country,
                    region=region,
                    city=city,
                    subcity=subcity,
                    kebele=kebele,
                    house_number=str(random.randint(1, 9999)) if random.choice([True, False]) else '',
                    street_address=fake.street_address() if random.choice([True, False]) else '',
                    postal_code=str(random.randint(1000, 9999)) if random.choice([True, False]) else '',
                    is_active=random.choice([True, True, True, False])  # 75% active
                )
                individual_count += 1
                
                if individual_count % 50 == 0:
                    print(f"✅ Created {individual_count} individual taxpayers...")
                    
            except Exception as e:
                print(f"❌ Error creating individual taxpayer: {e}")
                continue
        
        # Create Organization Taxpayers
        organization_count = 0
        for i in range(100):  # Create 100 organization taxpayers
            try:
                # Generate unique TIN
                tin = f"{random.randint(**********, **********)}"
                
                # Check if TIN already exists
                if OrganizationTaxPayer.objects.filter(tin=tin).exists():
                    continue
                
                # Random location selection
                country = random.choice(countries) if countries else None
                region = random.choice(regions) if regions else None
                city = random.choice(cities) if cities else None
                subcity = random.choice(subcities) if subcities else None
                kebele = random.choice(kebeles) if kebeles else None
                
                org_type = random.choice(org_business_types)
                requires_vat = org_type.requires_vat_registration
                
                organization = OrganizationTaxPayer.objects.create(
                    tin=tin,
                    business_name=fake.company(),
                    trade_name=fake.company() if random.choice([True, False]) else '',
                    organization_business_type=org_type,
                    tax_payer_level=random.choice(tax_levels),
                    business_sector=random.choice(business_sectors),
                    business_sub_sector=random.choice(business_sub_sectors),
                    business_registration_date=fake.date_between(start_date='-10y', end_date='today'),
                    business_license_number=f"BL{random.randint(100000, 999999)}" if random.choice([True, False]) else '',
                    capital_amount=random.randint(50000, 10000000) if random.choice([True, False]) else None,
                    number_of_employees=random.randint(1, 500) if random.choice([True, False]) else None,
                    manager_first_name=fake.first_name(),
                    manager_middle_name=fake.first_name() if random.choice([True, False]) else '',
                    manager_last_name=fake.last_name(),
                    manager_title=random.choice(['Manager', 'CEO', 'Director', 'Owner', 'President']),
                    vat_registration_date=fake.date_between(start_date='-5y', end_date='today') if requires_vat else None,
                    vat_number=f"ET{random.randint(**********, **********)}" if requires_vat else None,
                    phone=f"+251{random.choice(['9', '7'])}{random.randint(10000000, 99999999)}",
                    phone_secondary=f"+251{random.choice(['9', '7'])}{random.randint(10000000, 99999999)}" if random.choice([True, False]) else '',
                    email=fake.email() if random.choice([True, False]) else '',
                    country=country,
                    region=region,
                    city=city,
                    subcity=subcity,
                    kebele=kebele,
                    house_number=str(random.randint(1, 9999)) if random.choice([True, False]) else '',
                    street_address=fake.street_address() if random.choice([True, False]) else '',
                    postal_code=str(random.randint(1000, 9999)) if random.choice([True, False]) else '',
                    is_active=random.choice([True, True, True, False])  # 75% active
                )
                organization_count += 1
                
                if organization_count % 25 == 0:
                    print(f"✅ Created {organization_count} organization taxpayers...")
                    
            except Exception as e:
                print(f"❌ Error creating organization taxpayer: {e}")
                continue
        
        print(f"\n🎉 Sample taxpayer data creation completed!")
        print(f"📊 Summary:")
        print(f"   - Individual Taxpayers: {IndividualTaxPayer.objects.count()}")
        print(f"   - Organization Taxpayers: {OrganizationTaxPayer.objects.count()}")
        print(f"   - Total Taxpayers: {IndividualTaxPayer.objects.count() + OrganizationTaxPayer.objects.count()}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    create_sample_taxpayers()
