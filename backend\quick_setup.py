#!/usr/bin/env python
"""
Quick setup script for taxpayers database
This will create the tables directly using Django's database connection
"""

import os
import sys
import django
from django.db import connection, transaction

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'arada_dms.settings')
django.setup()

def create_tables():
    """Create taxpayers tables using raw SQL"""
    print("Creating taxpayers database tables...")
    
    sql_commands = [
        # Create taxpayers_businesssector table
        """
        CREATE TABLE IF NOT EXISTS taxpayers_businesssector (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            code VARCHAR(10) UNIQUE NOT NULL,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            created_by_id INTEGER
        );
        """,
        
        # Create taxpayers_taxpayerlevel table
        """
        CREATE TABLE IF NOT EXISTS taxpayers_taxpayerlevel (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            name VARCHAR(100) NOT NULL,
            code VARCHAR(5) UNIQUE NOT NULL,
            description TEXT,
            minimum_annual_turnover DECIMAL(15,2),
            maximum_annual_turnover DECIMAL(15,2),
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        """,
        
        # Create taxpayers_organizationbusinesstype table
        """
        CREATE TABLE IF NOT EXISTS taxpayers_organizationbusinesstype (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            code VARCHAR(10) UNIQUE NOT NULL,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            requires_vat_registration BOOLEAN DEFAULT FALSE,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        """,
        
        # Create taxpayers_businesssubsector table
        """
        CREATE TABLE IF NOT EXISTS taxpayers_businesssubsector (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            code VARCHAR(10) NOT NULL,
            name VARCHAR(150) NOT NULL,
            description TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            business_sector_id UUID NOT NULL,
            created_by_id INTEGER,
            UNIQUE(business_sector_id, code)
        );
        """,
        
        # Create taxpayers_individualtaxpayer table
        """
        CREATE TABLE IF NOT EXISTS taxpayers_individualtaxpayer (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            tin VARCHAR(10) UNIQUE NOT NULL,
            first_name VARCHAR(50) NOT NULL,
            middle_name VARCHAR(50),
            last_name VARCHAR(50) NOT NULL,
            nationality VARCHAR(10) DEFAULT 'ET',
            gender VARCHAR(1) NOT NULL,
            date_of_birth DATE NOT NULL,
            profile_picture VARCHAR(100),
            business_registration_date DATE NOT NULL,
            business_name VARCHAR(200),
            business_license_number VARCHAR(50),
            phone VARCHAR(20) NOT NULL,
            secondary_phone VARCHAR(20),
            email VARCHAR(254),
            house_number VARCHAR(20),
            street_address VARCHAR(200),
            is_active BOOLEAN DEFAULT TRUE,
            registration_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            business_sector_id UUID NOT NULL,
            business_sub_sector_id UUID,
            created_by_id INTEGER,
            file_id UUID,
            kebele_id UUID,
            subcity_id UUID,
            tax_payer_level_id UUID NOT NULL
        );
        """,
        
        # Create taxpayers_organizationtaxpayer table
        """
        CREATE TABLE IF NOT EXISTS taxpayers_organizationtaxpayer (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            tin VARCHAR(10) UNIQUE NOT NULL,
            business_name VARCHAR(200) NOT NULL,
            trade_name VARCHAR(200),
            business_registration_date DATE NOT NULL,
            business_license_number VARCHAR(50),
            capital_amount DECIMAL(15,2),
            number_of_employees INTEGER,
            manager_first_name VARCHAR(50) NOT NULL,
            manager_middle_name VARCHAR(50),
            manager_last_name VARCHAR(50) NOT NULL,
            manager_title VARCHAR(100),
            vat_registration_date DATE,
            vat_number VARCHAR(12),
            phone VARCHAR(20) NOT NULL,
            secondary_phone VARCHAR(20),
            email VARCHAR(254),
            house_number VARCHAR(20),
            street_address VARCHAR(200),
            is_active BOOLEAN DEFAULT TRUE,
            registration_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            business_sector_id UUID NOT NULL,
            business_sub_sector_id UUID,
            created_by_id INTEGER,
            file_id UUID,
            kebele_id UUID,
            organization_business_type_id UUID NOT NULL,
            subcity_id UUID,
            tax_payer_level_id UUID NOT NULL
        );
        """,
        
        # Record migration as applied
        """
        INSERT INTO django_migrations (app, name, applied) 
        VALUES ('taxpayers', '0001_initial', NOW())
        ON CONFLICT (app, name) DO NOTHING;
        """
    ]
    
    with transaction.atomic():
        with connection.cursor() as cursor:
            for i, sql in enumerate(sql_commands, 1):
                try:
                    cursor.execute(sql)
                    print(f"✓ Step {i}: Table created successfully")
                except Exception as e:
                    print(f"✗ Step {i}: Error - {e}")
                    # Continue with other tables even if one fails

def load_sample_data():
    """Load sample data"""
    print("\nLoading sample data...")
    
    sample_data = [
        # Tax Payer Levels
        """
        INSERT INTO taxpayers_taxpayerlevel (id, name, code, description, minimum_annual_turnover, maximum_annual_turnover, is_active, created_at, updated_at)
        VALUES 
        ('550e8400-e29b-41d4-a716-446655440001', 'Category A - Large Taxpayers', 'A', 'Large taxpayers with annual turnover above 10 million ETB', 10000000.00, NULL, TRUE, NOW(), NOW()),
        ('550e8400-e29b-41d4-a716-446655440002', 'Category B - Medium Taxpayers', 'B', 'Medium taxpayers with annual turnover between 1 million and 10 million ETB', 1000000.00, 10000000.00, TRUE, NOW(), NOW()),
        ('550e8400-e29b-41d4-a716-446655440003', 'Category C - Small Taxpayers', 'C', 'Small taxpayers with annual turnover between 100,000 and 1 million ETB', 100000.00, 1000000.00, TRUE, NOW(), NOW()),
        ('550e8400-e29b-41d4-a716-446655440004', 'Category D - Micro Taxpayers', 'D', 'Micro taxpayers with annual turnover below 100,000 ETB', NULL, 100000.00, TRUE, NOW(), NOW())
        ON CONFLICT (code) DO NOTHING;
        """,
        
        # Organization Business Types
        """
        INSERT INTO taxpayers_organizationbusinesstype (id, code, name, description, requires_vat_registration, is_active, created_at, updated_at)
        VALUES 
        ('550e8400-e29b-41d4-a716-446655440011', 'PLC', 'Private Limited Company', 'Private limited company with limited liability', TRUE, TRUE, NOW(), NOW()),
        ('550e8400-e29b-41d4-a716-446655440012', 'SC', 'Share Company', 'Share company with publicly traded shares', TRUE, TRUE, NOW(), NOW()),
        ('550e8400-e29b-41d4-a716-446655440013', 'COOP', 'Cooperative', 'Cooperative organization owned by members', FALSE, TRUE, NOW(), NOW()),
        ('550e8400-e29b-41d4-a716-446655440014', 'NGO', 'Non-Governmental Organization', 'Non-profit organization', FALSE, TRUE, NOW(), NOW()),
        ('550e8400-e29b-41d4-a716-446655440015', 'SOLE', 'Sole Proprietorship', 'Business owned by single individual', FALSE, TRUE, NOW(), NOW())
        ON CONFLICT (code) DO NOTHING;
        """,
        
        # Business Sectors
        """
        INSERT INTO taxpayers_businesssector (id, code, name, description, is_active, created_at, updated_at)
        VALUES 
        ('550e8400-e29b-41d4-a716-************', 'AGR', 'Agriculture', 'Agricultural activities including crop production, livestock, forestry', TRUE, NOW(), NOW()),
        ('550e8400-e29b-41d4-a716-446655440022', 'MAN', 'Manufacturing', 'Manufacturing and industrial production activities', TRUE, NOW(), NOW()),
        ('550e8400-e29b-41d4-a716-************', 'SER', 'Services', 'Service-based businesses including consulting, healthcare, education', TRUE, NOW(), NOW()),
        ('550e8400-e29b-41d4-a716-************', 'TRA', 'Trade', 'Trading activities including wholesale and retail trade', TRUE, NOW(), NOW()),
        ('550e8400-e29b-41d4-a716-446655440025', 'CON', 'Construction', 'Construction and real estate development activities', TRUE, NOW(), NOW())
        ON CONFLICT (code) DO NOTHING;
        """,
        
        # Business Sub-Sectors
        """
        INSERT INTO taxpayers_businesssubsector (id, business_sector_id, code, name, description, is_active, created_at, updated_at)
        VALUES 
        ('550e8400-e29b-41d4-a716-446*********', '550e8400-e29b-41d4-a716-************', '001', 'Crop Production', 'Growing of crops including cereals, vegetables, fruits', TRUE, NOW(), NOW()),
        ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '002', 'Livestock', 'Raising of livestock including cattle, sheep, goats, poultry', TRUE, NOW(), NOW()),
        ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '001', 'Professional Services', 'Professional services including legal, accounting, consulting', TRUE, NOW(), NOW()),
        ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '001', 'Retail Trade', 'Retail sale of goods to consumers', TRUE, NOW(), NOW())
        ON CONFLICT (business_sector_id, code) DO NOTHING;
        """
    ]
    
    with transaction.atomic():
        with connection.cursor() as cursor:
            for i, sql in enumerate(sample_data, 1):
                try:
                    cursor.execute(sql)
                    print(f"✓ Sample data {i}: Loaded successfully")
                except Exception as e:
                    print(f"✗ Sample data {i}: Error - {e}")

def main():
    """Main setup function"""
    print("🚀 Quick Setup for Tax Payers Database")
    print("=" * 50)
    
    try:
        # Create tables
        create_tables()
        
        # Load sample data
        load_sample_data()
        
        print("\n" + "=" * 50)
        print("🎉 Setup completed successfully!")
        print("\nYou can now:")
        print("1. Access admin at: http://localhost:8000/admin/")
        print("2. Access API at: http://localhost:8000/api/taxpayers/")
        print("3. Access frontend at: http://localhost:3000/taxpayers")
        
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        print("Please check your database connection and try again.")

if __name__ == '__main__':
    main()
