from django.db import connection

def create_taxpayers_tables():
    """Create taxpayers tables using Django's database connection"""
    
    with connection.cursor() as cursor:
        # Create taxpayers_businesssector table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS taxpayers_businesssector (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                code VARCHAR(10) UNIQUE NOT NULL,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                created_by_id INTEGER
            );
        """)
        print("✓ Created taxpayers_businesssector table")
        
        # Create taxpayers_taxpayerlevel table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS taxpayers_taxpayerlevel (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                name VARCHAR(100) NOT NULL,
                code VARCHAR(5) UNIQUE NOT NULL,
                description TEXT,
                minimum_annual_turnover DECIMAL(15,2),
                maximum_annual_turnover DECIMAL(15,2),
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
        """)
        print("✓ Created taxpayers_taxpayerlevel table")
        
        # Create taxpayers_organizationbusinesstype table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS taxpayers_organizationbusinesstype (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                code VARCHAR(10) UNIQUE NOT NULL,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                requires_vat_registration BOOLEAN DEFAULT FALSE,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
        """)
        print("✓ Created taxpayers_organizationbusinesstype table")
        
        # Create taxpayers_businesssubsector table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS taxpayers_businesssubsector (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                code VARCHAR(10) NOT NULL,
                name VARCHAR(150) NOT NULL,
                description TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                business_sector_id UUID NOT NULL,
                created_by_id INTEGER,
                UNIQUE(business_sector_id, code)
            );
        """)
        print("✓ Created taxpayers_businesssubsector table")
        
        # Create taxpayers_individualtaxpayer table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS taxpayers_individualtaxpayer (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                tin VARCHAR(10) UNIQUE NOT NULL,
                first_name VARCHAR(50) NOT NULL,
                middle_name VARCHAR(50),
                last_name VARCHAR(50) NOT NULL,
                nationality VARCHAR(10) DEFAULT 'ET',
                gender VARCHAR(1) NOT NULL,
                date_of_birth DATE NOT NULL,
                profile_picture VARCHAR(100),
                business_registration_date DATE NOT NULL,
                business_name VARCHAR(200),
                business_license_number VARCHAR(50),
                phone VARCHAR(20) NOT NULL,
                secondary_phone VARCHAR(20),
                email VARCHAR(254),
                house_number VARCHAR(20),
                street_address VARCHAR(200),
                is_active BOOLEAN DEFAULT TRUE,
                registration_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                business_sector_id UUID NOT NULL,
                business_sub_sector_id UUID,
                created_by_id INTEGER,
                file_id UUID,
                kebele_id UUID,
                subcity_id UUID,
                tax_payer_level_id UUID NOT NULL
            );
        """)
        print("✓ Created taxpayers_individualtaxpayer table")
        
        # Create taxpayers_organizationtaxpayer table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS taxpayers_organizationtaxpayer (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                tin VARCHAR(10) UNIQUE NOT NULL,
                business_name VARCHAR(200) NOT NULL,
                trade_name VARCHAR(200),
                business_registration_date DATE NOT NULL,
                business_license_number VARCHAR(50),
                capital_amount DECIMAL(15,2),
                number_of_employees INTEGER,
                manager_first_name VARCHAR(50) NOT NULL,
                manager_middle_name VARCHAR(50),
                manager_last_name VARCHAR(50) NOT NULL,
                manager_title VARCHAR(100),
                vat_registration_date DATE,
                vat_number VARCHAR(12),
                phone VARCHAR(20) NOT NULL,
                secondary_phone VARCHAR(20),
                email VARCHAR(254),
                house_number VARCHAR(20),
                street_address VARCHAR(200),
                is_active BOOLEAN DEFAULT TRUE,
                registration_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                business_sector_id UUID NOT NULL,
                business_sub_sector_id UUID,
                created_by_id INTEGER,
                file_id UUID,
                kebele_id UUID,
                organization_business_type_id UUID NOT NULL,
                subcity_id UUID,
                tax_payer_level_id UUID NOT NULL
            );
        """)
        print("✓ Created taxpayers_organizationtaxpayer table")
        
        # Record migration as applied
        cursor.execute("""
            INSERT INTO django_migrations (app, name, applied) 
            VALUES ('taxpayers', '0001_initial', NOW())
            ON CONFLICT (app, name) DO NOTHING;
        """)
        print("✓ Recorded migration as applied")

# Run the function
create_taxpayers_tables()
print("\n🎉 All taxpayers tables created successfully!")
