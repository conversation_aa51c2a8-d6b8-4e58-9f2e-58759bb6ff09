import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Chip,
} from '@mui/material';
import {
  Category,
  Edit,
  Delete,
  Home,
  Description,
  Info,
  Settings,
  Business,
  Folder,
  ColorLens,
} from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import fileService, { type FileType } from '../../services/fileService';
import DetailPageTemplate from '../../components/common/DetailPageTemplate';
import ConfirmationDialog from '../../components/common/ConfirmationDialog';

const FileTypeDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showNotification } = useNotification();
  
  const [fileType, setFileType] = useState<FileType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    if (id) {
      loadFileType();
    }
  }, [id]);

  const loadFileType = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await fileService.getFileType(Number(id));
      setFileType(data);
    } catch (error) {
      console.error('Error loading file type:', error);
      setError('Failed to load file type');
      showNotification('Failed to load file type', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    // Navigate back to file types page with edit state
    navigate('/document-center/file-types', { 
      state: { 
        editFileType: fileType,
        showForm: true 
      } 
    });
  };

  const handleDelete = () => {
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!fileType) return;
    
    try {
      setDeleting(true);
      await fileService.deleteFileType(fileType.id);
      showNotification('File type deleted successfully', 'success');
      navigate('/document-center/file-types');
    } catch (error) {
      console.error('Error deleting file type:', error);
      showNotification('Failed to delete file type', 'error');
    } finally {
      setDeleting(false);
      setDeleteDialogOpen(false);
    }
  };

  const handleBack = () => {
    navigate('/document-center/file-types');
  };

  if (!fileType && !loading && !error) {
    setError('File type not found');
  }

  const breadcrumbs = [
    { label: 'Dashboard', path: '/dashboard', icon: <Home fontSize="small" /> },
    { label: 'Document Center', path: '/document-center', icon: <Description fontSize="small" /> },
    { label: 'File Types', path: '/document-center/file-types', icon: <Category fontSize="small" /> },
    { label: fileType?.name || 'File Type', path: undefined, icon: <Folder fontSize="small" /> },
  ];

  const actions = [
    {
      label: 'Edit',
      icon: <Edit />,
      onClick: handleEdit,
      color: 'primary' as const,
    },
    {
      label: 'Delete',
      icon: <Delete />,
      onClick: handleDelete,
      color: 'error' as const,
    },
  ];

  const chips = fileType ? [
    {
      label: fileType.is_active ? 'Active' : 'Inactive',
      color: fileType.is_active ? 'success' as const : 'error' as const,
    },
    {
      label: `Code: ${fileType.code}`,
      color: 'info' as const,
    },
  ] : [];

  const sections = fileType ? [
    {
      title: 'Basic Information',
      icon: <Info />,
      content: (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'primary.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'primary.200'
          }}>
            <Category sx={{ color: 'primary.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="primary.main" sx={{ fontWeight: 600 }}>
                File Type Name
              </Typography>
              <Typography variant="body2" color="primary.dark">
                {fileType.name}
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'info.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'info.200'
          }}>
            <Folder sx={{ color: 'info.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="info.main" sx={{ fontWeight: 600 }}>
                Code
              </Typography>
              <Typography variant="body2" color="info.dark">
                {fileType.code}
              </Typography>
            </Box>
          </Box>
          
          {fileType.description && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" color="text.secondary" sx={{ fontWeight: 600, mb: 1 }}>
                Description
              </Typography>
              <Typography variant="body2" sx={{ 
                p: 2, 
                bgcolor: 'grey.50', 
                borderRadius: 1,
                border: '1px solid',
                borderColor: 'grey.200'
              }}>
                {fileType.description}
              </Typography>
            </Box>
          )}
        </Box>
      ),
    },
    {
      title: 'Visual Settings',
      icon: <ColorLens />,
      content: (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'secondary.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'secondary.200'
          }}>
            <ColorLens sx={{ color: 'secondary.main', mr: 2 }} />
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Box>
                <Typography variant="subtitle2" color="secondary.main" sx={{ fontWeight: 600 }}>
                  Color
                </Typography>
                <Typography variant="body2" color="secondary.dark">
                  {fileType.color}
                </Typography>
              </Box>
              <Box
                sx={{
                  width: 32,
                  height: 32,
                  borderRadius: 1,
                  bgcolor: fileType.color,
                  border: '2px solid',
                  borderColor: 'grey.300',
                }}
              />
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'warning.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'warning.200'
          }}>
            <Settings sx={{ color: 'warning.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="warning.main" sx={{ fontWeight: 600 }}>
                Icon
              </Typography>
              <Typography variant="body2" color="warning.dark">
                {fileType.icon || 'Default'}
              </Typography>
            </Box>
          </Box>
        </Box>
      ),
    },
    {
      title: 'Required Fields',
      icon: <Settings />,
      content: (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: fileType.requires_business_name ? 'success.50' : 'grey.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: fileType.requires_business_name ? 'success.200' : 'grey.200'
          }}>
            <Business sx={{ color: fileType.requires_business_name ? 'success.main' : 'grey.500', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color={fileType.requires_business_name ? 'success.main' : 'grey.600'} sx={{ fontWeight: 600 }}>
                Business Name
              </Typography>
              <Typography variant="body2" color={fileType.requires_business_name ? 'success.dark' : 'grey.600'}>
                {fileType.requires_business_name ? 'Required' : 'Optional'}
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: fileType.requires_tin_number ? 'success.50' : 'grey.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: fileType.requires_tin_number ? 'success.200' : 'grey.200'
          }}>
            <Settings sx={{ color: fileType.requires_tin_number ? 'success.main' : 'grey.500', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color={fileType.requires_tin_number ? 'success.main' : 'grey.600'} sx={{ fontWeight: 600 }}>
                TIN Number
              </Typography>
              <Typography variant="body2" color={fileType.requires_tin_number ? 'success.dark' : 'grey.600'}>
                {fileType.requires_tin_number ? 'Required' : 'Optional'}
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: fileType.requires_license_number ? 'success.50' : 'grey.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: fileType.requires_license_number ? 'success.200' : 'grey.200'
          }}>
            <Description sx={{ color: fileType.requires_license_number ? 'success.main' : 'grey.500', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color={fileType.requires_license_number ? 'success.main' : 'grey.600'} sx={{ fontWeight: 600 }}>
                License Number
              </Typography>
              <Typography variant="body2" color={fileType.requires_license_number ? 'success.dark' : 'grey.600'}>
                {fileType.requires_license_number ? 'Required' : 'Optional'}
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: fileType.requires_owner_name ? 'success.50' : 'grey.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: fileType.requires_owner_name ? 'success.200' : 'grey.200'
          }}>
            <Business sx={{ color: fileType.requires_owner_name ? 'success.main' : 'grey.500', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color={fileType.requires_owner_name ? 'success.main' : 'grey.600'} sx={{ fontWeight: 600 }}>
                Owner Name
              </Typography>
              <Typography variant="body2" color={fileType.requires_owner_name ? 'success.dark' : 'grey.600'}>
                {fileType.requires_owner_name ? 'Required' : 'Optional'}
              </Typography>
            </Box>
          </Box>
        </Box>
      ),
    },
    {
      title: 'Default Settings & Statistics',
      icon: <Folder />,
      content: (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          {fileType.default_document_types_list && fileType.default_document_types_list.length > 0 && (
            <Box sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              p: 2, 
              bgcolor: 'info.50',
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'info.200'
            }}>
              <Description sx={{ color: 'info.main', mr: 2 }} />
              <Box>
                <Typography variant="subtitle2" color="info.main" sx={{ fontWeight: 600 }}>
                  Default Document Types
                </Typography>
                <Typography variant="body2" color="info.dark">
                  {fileType.default_document_types_list.join(', ')}
                </Typography>
              </Box>
            </Box>
          )}
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'primary.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'primary.200'
          }}>
            <Folder sx={{ color: 'primary.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="primary.main" sx={{ fontWeight: 600 }}>
                Files
              </Typography>
              <Typography variant="body2" color="primary.dark">
                {fileType.file_count} files
              </Typography>
            </Box>
          </Box>
        </Box>
      ),
    },
  ] : [];

  return (
    <>
      <DetailPageTemplate
        loading={loading}
        error={error}
        breadcrumbs={breadcrumbs}
        title={fileType?.name || 'File Type'}
        subtitle={fileType?.code ? `Code: ${fileType.code}` : undefined}
        avatar={{
          fallbackIcon: <Category sx={{ fontSize: 40 }} />,
          alt: fileType?.name || 'File Type',
        }}
        chips={chips}
        actions={actions}
        sections={sections}
        onBack={handleBack}
      />

      <ConfirmationDialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleConfirmDelete}
        title="Delete File Type"
        itemName={fileType?.name}
        itemType="File Type"
        message={`Are you sure you want to delete "${fileType?.name}"? This will affect ${fileType?.file_count || 0} files.`}
        confirmText="Delete File Type"
        severity="error"
        loading={deleting}
      />
    </>
  );
};

export default FileTypeDetailPage;
