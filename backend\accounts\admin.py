from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from .models import User, UserSession


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """Custom User admin with role-based fields"""

    list_display = ['username', 'email', 'first_name', 'last_name', 'role', 'organization', 'is_active', 'date_joined']
    list_filter = ['role', 'organization', 'is_active', 'is_staff', 'date_joined']
    search_fields = ['username', 'email', 'first_name', 'last_name', 'employee_id']
    ordering = ['username']

    fieldsets = BaseUserAdmin.fieldsets + (
        ('Additional Information', {
            'fields': ('role', 'employee_id', 'phone_number', 'department', 'profile_picture', 'organization')
        }),
        ('Audit Information', {
            'fields': ('last_login_ip', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ['created_at', 'updated_at', 'last_login_ip']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('organization')


@admin.register(UserSession)
class UserSessionAdmin(admin.ModelAdmin):
    """User session tracking admin"""

    list_display = ['user', 'ip_address', 'login_time', 'logout_time', 'is_active', 'session_duration']
    list_filter = ['is_active', 'login_time']
    search_fields = ['user__username', 'ip_address']
    readonly_fields = ['session_key', 'login_time', 'logout_time', 'session_duration']
    ordering = ['-login_time']

    fieldsets = (
        ('Session Information', {
            'fields': ('user', 'session_key', 'ip_address', 'user_agent', 'is_active')
        }),
        ('Timing', {
            'fields': ('login_time', 'logout_time', 'session_duration')
        }),
    )

    def session_duration(self, obj):
        """Calculate session duration"""
        if obj.logout_time:
            duration = obj.logout_time - obj.login_time
            return str(duration).split('.')[0]  # Remove microseconds
        elif obj.is_active:
            from django.utils import timezone
            duration = timezone.now() - obj.login_time
            return f"{str(duration).split('.')[0]} (Active)"
        return "Unknown"

    session_duration.short_description = 'Duration'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')
