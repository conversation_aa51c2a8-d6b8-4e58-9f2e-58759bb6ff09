#!/usr/bin/env python
"""
Test script to verify the API endpoints work correctly
"""

import os
import sys
import django
import requests
import json

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'arada_dms.settings')
django.setup()

from django.contrib.auth import get_user_model
try:
    from rest_framework.authtoken.models import Token
except ImportError:
    Token = None

User = get_user_model()

def test_api_endpoints():
    print("Testing API endpoints for the new hierarchy")
    print("=" * 60)

    base_url = 'http://127.0.0.1:8000/api/locations'
    headers = {
        'Content-Type': 'application/json'
    }

    print("Testing without authentication (assuming DEBUG=True)")
    print()
    
    # Test Buildings endpoint
    print("1. Testing Buildings endpoint")
    try:
        response = requests.get(f'{base_url}/buildings/', headers=headers)
        print(f"   GET /buildings/ - Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Found {len(data.get('results', []))} buildings")
            if data.get('results'):
                building = data['results'][0]
                print(f"   First building: {building['name']} ({building['code']})")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Error: {e}")
    
    print()
    
    # Test Shelves endpoint
    print("2. Testing Shelves endpoint")
    try:
        response = requests.get(f'{base_url}/shelves/', headers=headers)
        print(f"   GET /shelves/ - Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Found {len(data.get('results', []))} shelves")
            if data.get('results'):
                shelf = data['results'][0]
                print(f"   First shelf: {shelf['name']} ({shelf['code']}) - {shelf.get('rows', 'N/A')}x{shelf.get('columns', 'N/A')}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Error: {e}")
    
    print()
    
    # Test Boxes endpoint
    print("3. Testing Boxes endpoint")
    try:
        response = requests.get(f'{base_url}/boxes/', headers=headers)
        print(f"   GET /boxes/ - Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Found {len(data.get('results', []))} boxes")
            if data.get('results'):
                box = data['results'][0]
                print(f"   First box: {box['position_code']} - {box.get('name', 'Unnamed')} (R{box['row']}C{box['column']})")
                print(f"   Full code: {box['full_code']}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Error: {e}")
    
    print()
    
    # Test Kents endpoint
    print("4. Testing Kents endpoint")
    try:
        response = requests.get(f'{base_url}/kents/', headers=headers)
        print(f"   GET /kents/ - Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Found {len(data.get('results', []))} kents")
            if data.get('results'):
                kent = data['results'][0]
                print(f"   First kent: {kent['name']} ({kent['code']}) in {kent['box_position']}")
                print(f"   Full code: {kent['full_code']}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Error: {e}")
    
    print()
    
    # Test Files endpoint
    print("5. Testing Files endpoint")
    try:
        response = requests.get(f'{base_url}/files/', headers=headers)
        print(f"   GET /files/ - Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Found {len(data.get('results', []))} files")
            if data.get('results'):
                file_obj = data['results'][0]
                print(f"   First file: {file_obj['name']} ({file_obj['file_number']})")
                print(f"   Full code: {file_obj['full_code']}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Error: {e}")
    
    print()
    
    # Test hierarchical endpoints
    print("6. Testing Hierarchical endpoints")
    
    # Get shelf boxes
    try:
        response = requests.get(f'{base_url}/shelves/1/boxes/', headers=headers)
        print(f"   GET /shelves/1/boxes/ - Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Found {len(data)} boxes in shelf 1")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Get box kents
    try:
        response = requests.get(f'{base_url}/boxes/1/kents/', headers=headers)
        print(f"   GET /boxes/1/kents/ - Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Found {len(data)} kents in box 1")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Error: {e}")
    
    print()
    print("✅ API testing completed!")

if __name__ == '__main__':
    test_api_endpoints()
