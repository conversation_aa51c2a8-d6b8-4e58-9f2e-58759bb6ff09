"""
Utility functions for location management
"""
import json
from io import BytesIO
from django.core.files import File

# Import barcode and qrcode with error handling
try:
    import barcode
    from barcode.writer import ImageWriter
    import qrcode
    BARCODE_AVAILABLE = True
except ImportError:
    BARCODE_AVAILABLE = False


def generate_barcode(code_text, upload_path):
    """
    Generate barcode image for given text
    
    Args:
        code_text (str): Text to encode in barcode
        upload_path (str): Upload path for the image
        
    Returns:
        File object or None if generation fails
    """
    if not BARCODE_AVAILABLE:
        return None
        
    try:
        code128 = barcode.get_barcode_class('code128')
        barcode_instance = code128(code_text, writer=ImageWriter())
        buffer = BytesIO()
        barcode_instance.write(buffer)
        
        # Create Django File object
        file_obj = File(buffer)
        file_obj.name = f'{code_text}_barcode.png'
        return file_obj
        
    except Exception as e:
        print(f"Error generating barcode for {code_text}: {e}")
        return None


def generate_qr_code(data, upload_path):
    """
    Generate QR code image for given data
    
    Args:
        data (dict or str): Data to encode in QR code
        upload_path (str): Upload path for the image
        
    Returns:
        File object or None if generation fails
    """
    if not BARCODE_AVAILABLE:
        return None
        
    try:
        # Convert data to JSON string if it's a dict
        if isinstance(data, dict):
            qr_data = json.dumps(data)
        else:
            qr_data = str(data)
            
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(qr_data)
        qr.make(fit=True)
        
        qr_img = qr.make_image(fill_color="black", back_color="white")
        buffer = BytesIO()
        qr_img.save(buffer, format='PNG')
        
        # Create Django File object
        file_obj = File(buffer)
        if isinstance(data, dict) and 'full_code' in data:
            file_obj.name = f'{data["full_code"]}_qr.png'
        else:
            file_obj.name = f'{str(data)[:20]}_qr.png'
        return file_obj
        
    except Exception as e:
        print(f"Error generating QR code for {data}: {e}")
        return None


def get_location_hierarchy_path(location_obj):
    """
    Get the full hierarchy path for any location object
    
    Args:
        location_obj: Any location model instance (Building, Shelf, Box, Kent, File)
        
    Returns:
        str: Human-readable hierarchy path
    """
    if hasattr(location_obj, 'organization'):
        # Building
        return f"{location_obj.organization.name} → {location_obj.name}"
    elif hasattr(location_obj, 'building'):
        # Shelf
        return f"{location_obj.building.organization.name} → {location_obj.building.name} → {location_obj.name}"
    elif hasattr(location_obj, 'shelf'):
        if hasattr(location_obj, 'position_code'):
            # Box
            return f"{location_obj.shelf.building.organization.name} → {location_obj.shelf.building.name} → {location_obj.shelf.name} → {location_obj.position_code}"
        elif hasattr(location_obj, 'box'):
            # Kent
            return f"{location_obj.box.shelf.building.organization.name} → {location_obj.box.shelf.building.name} → {location_obj.box.shelf.name} → {location_obj.box.position_code} → {location_obj.name}"
    elif hasattr(location_obj, 'kent'):
        # File
        kent = location_obj.kent
        return f"{kent.box.shelf.building.organization.name} → {kent.box.shelf.building.name} → {kent.box.shelf.name} → {kent.box.position_code} → {kent.name} → {location_obj.name}"
    
    return str(location_obj)


def validate_location_hierarchy(location_obj):
    """
    Validate that location hierarchy is consistent
    
    Args:
        location_obj: Any location model instance
        
    Returns:
        tuple: (is_valid, error_message)
    """
    try:
        if hasattr(location_obj, 'box') and location_obj.box:
            # Kent validation
            box = location_obj.box
            shelf = box.shelf
            
            # Check if box position is within shelf limits
            if box.row > shelf.rows:
                return False, f"Box row {box.row} exceeds shelf capacity of {shelf.rows} rows"
            if box.column > shelf.columns:
                return False, f"Box column {box.column} exceeds shelf capacity of {shelf.columns} columns"
                
        return True, None
        
    except Exception as e:
        return False, f"Validation error: {str(e)}"
