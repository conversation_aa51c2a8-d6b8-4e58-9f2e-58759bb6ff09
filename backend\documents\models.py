from django.db import models
from django.core.validators import FileExtensionValidator
from django.utils import timezone
from io import BytesIO
from django.core.files import File
import uuid
import os

# Import barcode and qrcode with error handling
try:
    import barcode
    from barcode.writer import ImageWriter
    import qrcode
    BARCODE_AVAILABLE = True
except ImportError:
    BARCODE_AVAILABLE = False


class DocumentType(models.Model):
    """
    Document type classification system
    Configurable by administrators
    """
    organization = models.ForeignKey(
        'organizations.Organization',
        on_delete=models.CASCADE,
        related_name='document_types'
    )

    name = models.CharField(
        max_length=100,
        help_text="Document type name (e.g., Tax Record, Business License)"
    )

    code = models.CharField(
        max_length=20,
        help_text="Short code for document type (e.g., TAX, BLI, PRP)"
    )

    description = models.TextField(null=True, blank=True)

    # Retention settings
    retention_days = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Document retention period in days (overrides organization default)"
    )

    # Classification
    confidentiality_level = models.CharField(
        max_length=20,
        choices=[
            ('public', 'Public'),
            ('internal', 'Internal'),
            ('confidential', 'Confidential'),
            ('restricted', 'Restricted'),
        ],
        default='internal'
    )

    # Metadata requirements
    requires_expiry_date = models.BooleanField(
        default=False,
        help_text="Whether documents of this type require an expiry date"
    )

    requires_approval = models.BooleanField(
        default=False,
        help_text="Whether documents of this type require approval before access"
    )

    # File settings for digital documents
    allowed_file_extensions = models.JSONField(
        default=list,
        blank=True,
        help_text="Allowed file extensions for digital documents (e.g., ['pdf', 'jpg', 'png'])"
    )

    max_file_size_mb = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Maximum file size in MB (overrides organization default)"
    )

    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_document_types'
    )

    class Meta:
        db_table = 'documents_document_type'
        verbose_name = 'Document Type'
        verbose_name_plural = 'Document Types'
        unique_together = ['organization', 'code']
        ordering = ['organization', 'name']

    def __str__(self):
        return f"{self.organization.short_name} - {self.name}"

    def save(self, *args, **kwargs):
        # Convert code to uppercase for case-insensitive storage
        if self.code:
            self.code = self.code.upper()
        super().save(*args, **kwargs)

    def get_retention_days(self):
        """Get retention days, falling back to organization default"""
        return self.retention_days or self.organization.document_retention_days

    def get_max_file_size_mb(self):
        """Get max file size, falling back to organization default"""
        return self.max_file_size_mb or self.organization.max_file_size_mb


class Document(models.Model):
    """
    Main document model supporting both physical and digital documents
    """

    class DocumentMode(models.TextChoices):
        PHYSICAL = 'physical', 'Physical'
        DIGITAL = 'digital', 'Digital'
        HYBRID = 'hybrid', 'Hybrid (Both Physical and Digital)'

    class Status(models.TextChoices):
        ACTIVE = 'active', 'Active'
        ARCHIVED = 'archived', 'Archived'
        DESTROYED = 'destroyed', 'Destroyed'
        CHECKED_OUT = 'checked_out', 'Checked Out'
        LOST = 'lost', 'Lost'

    # Basic Information
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    title = models.CharField(
        max_length=255,
        help_text="Document title or name"
    )

    description = models.TextField(
        null=True,
        blank=True,
        help_text="Detailed description of the document"
    )

    document_type = models.ForeignKey(
        DocumentType,
        on_delete=models.PROTECT,
        related_name='documents'
    )

    # Document mode and status
    mode = models.CharField(
        max_length=20,
        choices=DocumentMode.choices,
        default=DocumentMode.PHYSICAL
    )

    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.ACTIVE
    )

    # Metadata
    tags = models.JSONField(
        default=list,
        blank=True,
        help_text="Tags for categorization and search"
    )

    reference_number = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        help_text="External reference number or identifier"
    )

    # Dates
    document_date = models.DateField(
        null=True,
        blank=True,
        help_text="Date when the document was created/issued"
    )

    expiry_date = models.DateField(
        null=True,
        blank=True,
        help_text="Document expiry date (if applicable)"
    )

    # Physical document location - now belongs to a File
    document_file = models.ForeignKey(
        'locations.File',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='documents',
        help_text="File where this document is grouped (e.g., 'Abebe's Hotel File')"
    )

    # Legacy kent field for backward compatibility (will be auto-populated from file.kent)
    kent = models.ForeignKey(
        'locations.Kent',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='legacy_documents',
        help_text="Physical location (Kent/Box) - auto-populated from file"
    )

    # Digital document file
    file = models.FileField(
        upload_to='documents/',
        null=True,
        blank=True,
        validators=[FileExtensionValidator(
            allowed_extensions=['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx', 'xls', 'xlsx']
        )],
        help_text="Digital file (for digital or hybrid documents)"
    )

    file_size = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="File size in bytes"
    )

    number_of_pages = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Total number of pages in this document (for audit and tracking purposes)"
    )

    # Barcode and QR code for tracking
    barcode_image = models.ImageField(
        upload_to='documents/barcodes/',
        null=True,
        blank=True
    )
    qr_code_image = models.ImageField(
        upload_to='documents/qrcodes/',
        null=True,
        blank=True
    )

    # Audit and tracking
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_documents'
    )

    class Meta:
        db_table = 'documents_document'
        verbose_name = 'Document'
        verbose_name_plural = 'Documents'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['document_type', 'status']),
            models.Index(fields=['kent']),
            models.Index(fields=['reference_number']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.title} ({self.document_type.code})"

    @property
    def file_name(self):
        """Return the file name this document belongs to"""
        return self.document_file.name if self.document_file else None

    @property
    def file_number(self):
        """Return the file number this document belongs to"""
        return self.document_file.file_number if self.document_file else None

    @property
    def kent_location(self):
        """Return kent location information"""
        if self.document_file and self.document_file.kent:
            return self.document_file.kent.location_path
        elif self.kent:
            return self.kent.location_path
        return "Digital Only"

    @property
    def full_location_path(self):
        """Return complete location path including file"""
        if self.document_file:
            return f"{self.document_file.location_path} → {self.title}"
        elif self.kent:
            return f"{self.kent.location_path} → {self.title}"
        return f"Digital Only → {self.title}"

    def save(self, *args, **kwargs):
        # Set file size if file is uploaded
        if self.file:
            self.file_size = self.file.size

        # Auto-populate kent from document_file
        if self.document_file and self.document_file.kent:
            self.kent = self.document_file.kent
            # Update file's last activity date
            self.document_file.update_last_activity()

        super().save(*args, **kwargs)
        self.generate_codes()

    def generate_codes(self):
        """Generate barcode and QR code for the document"""
        if not BARCODE_AVAILABLE:
            return

        code_text = f"DOC-{str(self.id)[:8]}"

        # Generate barcode
        if not self.barcode_image:
            try:
                code128 = barcode.get_barcode_class('code128')
                barcode_instance = code128(code_text, writer=ImageWriter())
                buffer = BytesIO()
                barcode_instance.write(buffer)
                self.barcode_image.save(
                    f'document_{str(self.id)[:8]}_barcode.png',
                    File(buffer),
                    save=False
                )
            except Exception as e:
                print(f"Error generating barcode: {e}")

        # Generate QR code with document details
        if not self.qr_code_image:
            try:
                qr_data = {
                    'id': str(self.id),
                    'title': self.title,
                    'type': self.document_type.code,
                    'location': self.kent.full_code if self.kent else 'Digital',
                    'created': self.created_at.isoformat()
                }

                qr = qrcode.QRCode(version=1, box_size=10, border=5)
                qr.add_data(str(qr_data))
                qr.make(fit=True)
                qr_img = qr.make_image(fill_color="black", back_color="white")
                buffer = BytesIO()
                qr_img.save(buffer, format='PNG')
                self.qr_code_image.save(
                    f'document_{str(self.id)[:8]}_qr.png',
                    File(buffer),
                    save=False
                )
            except Exception as e:
                print(f"Error generating QR code: {e}")

        if self.barcode_image or self.qr_code_image:
            Document.objects.filter(pk=self.pk).update(
                barcode_image=self.barcode_image,
                qr_code_image=self.qr_code_image
            )

    @property
    def is_expired(self):
        """Check if document is expired"""
        if self.expiry_date:
            return timezone.now().date() > self.expiry_date
        return False

    @property
    def is_physical(self):
        """Check if document has physical component"""
        return self.mode in [self.DocumentMode.PHYSICAL, self.DocumentMode.HYBRID]

    @property
    def is_digital(self):
        """Check if document has digital component"""
        return self.mode in [self.DocumentMode.DIGITAL, self.DocumentMode.HYBRID]

    @property
    def location_display(self):
        """Return human-readable location"""
        if self.kent:
            return self.kent.location_path
        return "Digital Only"

    def can_be_requested(self):
        """Check if document can be requested"""
        return self.status == self.Status.ACTIVE and self.is_active

    def get_retention_date(self):
        """Calculate document retention expiry date"""
        retention_days = self.document_type.get_retention_days()
        return self.created_at.date() + timezone.timedelta(days=retention_days)


class DocumentVersion(models.Model):
    """
    Version control for digital documents
    """
    document = models.ForeignKey(
        Document,
        on_delete=models.CASCADE,
        related_name='versions'
    )

    version_number = models.PositiveIntegerField()
    file = models.FileField(
        upload_to='documents/versions/',
        validators=[FileExtensionValidator(
            allowed_extensions=['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx', 'xls', 'xlsx']
        )]
    )

    file_size = models.PositiveIntegerField()
    change_summary = models.TextField(
        null=True,
        blank=True,
        help_text="Summary of changes in this version"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_document_versions'
    )

    class Meta:
        db_table = 'documents_document_version'
        verbose_name = 'Document Version'
        verbose_name_plural = 'Document Versions'
        unique_together = ['document', 'version_number']
        ordering = ['-version_number']

    def __str__(self):
        return f"{self.document.title} - v{self.version_number}"

    def save(self, *args, **kwargs):
        if self.file:
            self.file_size = self.file.size
        super().save(*args, **kwargs)


class DocumentTag(models.Model):
    """
    Predefined tags for document categorization
    """
    organization = models.ForeignKey(
        'organizations.Organization',
        on_delete=models.CASCADE,
        related_name='document_tags'
    )

    name = models.CharField(max_length=50)
    color = models.CharField(
        max_length=7,
        default='#007bff',
        help_text="Hex color code for tag display"
    )
    description = models.TextField(null=True, blank=True)

    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'documents_document_tag'
        verbose_name = 'Document Tag'
        verbose_name_plural = 'Document Tags'
        unique_together = ['organization', 'name']
        ordering = ['name']

    def __str__(self):
        return f"{self.organization.short_name} - {self.name}"
