import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  InputAdornment,
  Typography,
  Menu,
  Alert,
  Grid,
} from '@mui/material';
import {
  Add,
  Search,
  Edit,
  Delete,
  MoreVert,
  Map,
  Home,
} from '@mui/icons-material';
import { useNotification } from '../../../contexts/NotificationContext';
import locationHierarchyService from '../../../services/locationHierarchyService';
import type {
  Kebele,
  KebeleCreate,
  SubCitySelect
} from '../../../services/locationHierarchyService';

interface KebelesTabProps {
  onStatsChange: () => void;
}

const KebelesTab: React.FC<KebelesTabProps> = ({ onStatsChange }) => {
  const { showSuccess, showError } = useNotification();

  const [kebeles, setKebeles] = useState<Kebele[]>([]);
  const [subCities, setSubCities] = useState<SubCitySelect[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSubCity, setSelectedSubCity] = useState<number | ''>('');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingKebele, setEditingKebele] = useState<Kebele | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedKebele, setSelectedKebele] = useState<Kebele | null>(null);
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);

  const [formData, setFormData] = useState<KebeleCreate>({
    subcity: 0,
    name: '',
    code: '',
    number: 1,
    population: undefined,
    area_km2: undefined,
    is_active: true,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    loadSubCities();
    loadKebeles();
  }, []);

  useEffect(() => {
    loadKebeles();
  }, [searchTerm, selectedSubCity]);

  const loadSubCities = async () => {
    try {
      const response = await locationHierarchyService.getSubCitiesSelect();
      setSubCities(response);
    } catch (error) {
      showError('Failed to load subcities');
    }
  };

  const loadKebeles = async () => {
    try {
      setLoading(true);
      const response = await locationHierarchyService.getKebeles({
        search: searchTerm,
        subcity: selectedSubCity || undefined,
        page_size: 100,
      });
      setKebeles(response.results);
    } catch (error) {
      showError('Failed to load kebeles');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (kebele?: Kebele) => {
    if (kebele) {
      setEditingKebele(kebele);
      setFormData({
        subcity: kebele.subcity,
        name: kebele.name,
        code: kebele.code,
        number: kebele.number,
        population: kebele.population,
        area_km2: kebele.area_km2,
        is_active: kebele.is_active,
      });
    } else {
      setEditingKebele(null);
      setFormData({
        subcity: selectedSubCity as number || 2,
        name: '',
        code: '',
        number: 1,
        population: undefined,
        area_km2: undefined,
        is_active: true,
      });
    }
    setErrors({});
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingKebele(null);
    setErrors({});
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.subcity) {
      newErrors.subcity = 'SubCity/Woreda is required';
    }
    if (!formData.name.trim()) {
      newErrors.name = 'Kebele name is required';
    }
    if (!formData.code.trim()) {
      newErrors.code = 'Kebele code is required';
    }
    if (!formData.number || formData.number < 1) {
      newErrors.number = 'Kebele number is required and must be positive';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      if (editingKebele) {
        await locationHierarchyService.updateKebele(editingKebele.id, formData);
        showSuccess('Kebele updated successfully');
      } else {
        await locationHierarchyService.createKebele(formData);
        showSuccess('Kebele created successfully');
      }

      handleCloseDialog();
      loadKebeles();
      onStatsChange();
    } catch (error: any) {
      showError(error.response?.data?.detail || 'Failed to save kebele');
    }
  };

  const handleDelete = async () => {
    if (!selectedKebele) return;

    try {
      await locationHierarchyService.deleteKebele(selectedKebele.id);
      showSuccess('Kebele deleted successfully');
      setDeleteDialogOpen(false);
      setSelectedKebele(null);
      loadKebeles();
      onStatsChange();
    } catch (error: any) {
      showError(error.response?.data?.detail || 'Failed to delete kebele');
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, kebele: Kebele) => {
    setMenuAnchor(event.currentTarget);
    setSelectedKebele(kebele);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
    setSelectedKebele(null);
  };

  const handleInputChange = (field: keyof KebeleCreate) => (
    event: React.ChangeEvent<HTMLInputElement> | { target: { value: unknown } }
  ) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">Kebeles Management</Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => handleOpenDialog()}
          disabled={subCities.length === 0}
        >
          Add Kebele
        </Button>
      </Box>

      {subCities.length === 0 && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          No subcities/woredas available. Please create subcities/woredas first before adding kebeles.
        </Alert>
      )}

      {/* Search and Filters */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid size={{ xs: 12, md: 4 }}>
          <TextField
            fullWidth
            placeholder="Search kebeles..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
          />
        </Grid>
        <Grid size={{ xs: 12, md: 4 }}>
          <FormControl fullWidth>
            <InputLabel>Filter by SubCity/Woreda</InputLabel>
            <Select
              value={selectedSubCity}
              onChange={(e) => setSelectedSubCity(e.target.value as number)}
              label="Filter by SubCity/Woreda"
            >
              <MenuItem value="">All SubCities/Woredas</MenuItem>
              {subCities.map((subCity) => (
                <MenuItem key={subCity.id} value={subCity.id}>
                  {subCity.name} ({subCity.code}) - {subCity.type}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
      </Grid>

      {/* Kebeles Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Kebele</TableCell>
              <TableCell>Code</TableCell>
              <TableCell>Number</TableCell>
              <TableCell>SubCity/Woreda</TableCell>
              <TableCell>Population</TableCell>
              <TableCell>Status</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {kebeles.map((kebele) => (
              <TableRow key={kebele.id}>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Map fontSize="small" />
                    {kebele.display_name}
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip label={kebele.code} size="small" />
                </TableCell>
                <TableCell>
                  <Chip
                    label={kebele.number.toString().padStart(2, '0')}
                    size="small"
                    color="info"
                  />
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Home fontSize="small" />
                    {kebele.subcity_name}
                  </Box>
                </TableCell>
                <TableCell>
                  {kebele.population ? kebele.population.toLocaleString() : '-'}
                </TableCell>
                <TableCell>
                  <Chip
                    label={kebele.is_active ? 'Active' : 'Inactive'}
                    color={kebele.is_active ? 'success' : 'default'}
                    size="small"
                  />
                </TableCell>
                <TableCell align="right">
                  <IconButton
                    onClick={(e) => handleMenuOpen(e, kebele)}
                    size="small"
                  >
                    <MoreVert />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Action Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
      >
        <MenuItem
          onClick={() => {
            handleOpenDialog(selectedKebele!);
            handleMenuClose();
          }}
        >
          <Edit fontSize="small" sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        <MenuItem
          onClick={() => {
            setDeleteDialogOpen(true);
            handleMenuClose();
          }}
          sx={{ color: 'error.main' }}
        >
          <Delete fontSize="small" sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>

      {/* Create/Edit Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingKebele ? 'Edit Kebele' : 'Add New Kebele'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required error={!!errors.subcity}>
                <InputLabel>SubCity/Woreda</InputLabel>
                <Select
                  value={formData.subcity || ''}
                  onChange={handleInputChange('subcity')}
                  label="SubCity/Woreda"
                >
                  {subCities.map((subCity) => (
                    <MenuItem key={subCity.id} value={subCity.id}>
                      {subCity.name} ({subCity.code}) - {subCity.type}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Kebele Number"
                type="number"
                value={formData.number}
                onChange={handleInputChange('number')}
                error={!!errors.number}
                helperText={errors.number}
                required
                inputProps={{ min: 1 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Kebele Name"
                value={formData.name}
                onChange={handleInputChange('name')}
                error={!!errors.name}
                helperText={errors.name}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Kebele Code"
                value={formData.code}
                onChange={handleInputChange('code')}
                error={!!errors.code}
                helperText={errors.code}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Population"
                type="number"
                value={formData.population || ''}
                onChange={handleInputChange('population')}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Area (km²)"
                type="number"
                value={formData.area_km2 || ''}
                onChange={handleInputChange('area_km2')}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.is_active}
                    onChange={handleInputChange('is_active')}
                  />
                }
                label="Active"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSubmit} variant="contained">
            {editingKebele ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 2 }}>
            Are you sure you want to delete "{selectedKebele?.display_name}"? This action cannot be undone.
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleDelete}
            color="error"
            variant="contained"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default KebelesTab;
