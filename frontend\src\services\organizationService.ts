import apiClient from './api';

// Organization interfaces matching backend model exactly
export interface Organization {
  id: number;
  name: string;
  short_name: string;
  logo?: string;
  motto?: string;
  tagline?: string;
  description?: string;
  website?: string;

  // Contact Information
  email?: string;
  phone?: string;
  fax?: string;

  // Address fields
  address_line1?: string;
  address_line2?: string;
  postal_code?: string;

  // Location hierarchy fields (ForeignKey IDs)
  country?: number | null;
  state_province?: number | null;
  city?: number | null;
  subcity?: number | null;
  kebele?: number | null;

  // Location hierarchy names (read-only)
  country_name?: string;
  region_name?: string;
  city_name?: string;
  subcity_name?: string;
  kebele_name?: string;
  full_address?: string;
  location_hierarchy_display?: string;

  // Office Hours
  office_hours_start?: string;
  office_hours_end?: string;
  office_hours_display?: string;

  // Additional Information
  established_date?: string;
  registration_number?: string;
  tax_id?: string;
  license_number?: string;

  // Branding
  primary_color?: string;
  secondary_color?: string;
  accent_color?: string;
  social_media?: {
    facebook?: string;
    twitter?: string;
    linkedin?: string;
    youtube?: string;
  };

  // System settings
  is_active: boolean;
  is_default: boolean;
  document_retention_days: number;
  max_file_size_mb: number;

  // Tax Collection Settings
  individual_penalty_rate: number;
  individual_interest_rate: number;
  organization_penalty_rate: number;
  organization_interest_rate: number;

  // Counts (computed fields)
  user_count?: number;
  department_count?: number;

  // Audit fields
  created_at: string;
  updated_at: string;
  created_by?: number;
}

export interface OrganizationCreate {
  name: string;
  short_name: string;
  logo?: File | null;
  motto?: string;
  tagline?: string;
  description?: string;
  website?: string;

  // Contact Information
  email?: string;
  phone?: string;
  fax?: string;

  // Address fields
  address_line1?: string;
  address_line2?: string;
  postal_code?: string;

  // Location hierarchy fields
  country?: number | null;
  state_province?: number | null;
  city?: number | null;
  subcity?: number | null;
  kebele?: number | null;

  // Office Hours
  office_hours_start?: string;
  office_hours_end?: string;

  // Additional Information
  established_date?: string;
  registration_number?: string;
  tax_id?: string;
  license_number?: string;

  // Branding
  primary_color?: string;
  secondary_color?: string;
  accent_color?: string;
  social_media?: {
    facebook?: string;
    twitter?: string;
    linkedin?: string;
    youtube?: string;
  };

  // System settings
  document_retention_days?: number;
  max_file_size_mb?: number;

  // Tax Collection Settings
  individual_penalty_rate?: number;
  individual_interest_rate?: number;
  organization_penalty_rate?: number;
  organization_interest_rate?: number;
}

export interface OrganizationUpdate extends Partial<OrganizationCreate> {
  is_active?: boolean;
  is_default?: boolean;
}

export interface OrganizationListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Organization[];
}

class OrganizationService {
  private baseUrl = '/organizations';

  // Get all organizations with pagination and filtering
  async getOrganizations(params?: {
    page?: number;
    page_size?: number;
    search?: string;
    is_active?: boolean;
    country?: number;
    state_province?: number;
    ordering?: string;
  }): Promise<OrganizationListResponse> {
    const response = await apiClient.get(`${this.baseUrl}/`, { params });
    return response.data;
  }

  // Get organization by ID
  async getOrganization(id: number): Promise<Organization> {
    const response = await apiClient.get(`${this.baseUrl}/${id}/`);
    return response.data;
  }

  // Create new organization
  async createOrganization(data: OrganizationCreate): Promise<Organization> {
    const response = await apiClient.post(`${this.baseUrl}/`, data);
    return response.data;
  }

  // Create organization with FormData (for file uploads)
  async createOrganizationWithFormData(formData: FormData): Promise<Organization> {
    const response = await apiClient.post(`${this.baseUrl}/`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  // Update organization
  async updateOrganization(id: number, data: OrganizationUpdate): Promise<Organization> {
    const response = await apiClient.patch(`${this.baseUrl}/${id}/`, data);
    return response.data;
  }

  // Update organization with FormData (for file uploads)
  async updateOrganizationWithFormData(id: number, formData: FormData): Promise<Organization> {
    const response = await apiClient.patch(`${this.baseUrl}/${id}/`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  // Delete organization
  async deleteOrganization(id: number): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/${id}/`);
  }

  // Set organization as default
  async setAsDefault(id: number): Promise<Organization> {
    const response = await apiClient.post(`${this.baseUrl}/${id}/set-default/`);
    return response.data;
  }

  // Get default organization
  async getDefaultOrganization(): Promise<Organization | null> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/default/`);
      return response.data;
    } catch (error) {
      return null;
    }
  }

  // Get default organization (public endpoint - no authentication required)
  async getDefaultOrganizationPublic(): Promise<Organization | null> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/default/public/`);
      return response.data;
    } catch (error) {
      return null;
    }
  }

  // Upload organization logo
  async uploadLogo(id: number, file: File): Promise<Organization> {
    const formData = new FormData();
    formData.append('logo', file);
    
    const response = await apiClient.patch(`${this.baseUrl}/${id}/`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  // Validate organization data
  validateOrganization(data: Partial<OrganizationCreate>): { isValid: boolean; errors: Record<string, string> } {
    const errors: Record<string, string> = {};

    // Required fields
    if (!data.name?.trim()) {
      errors.name = 'Organization name is required';
    } else if (data.name.length < 3) {
      errors.name = 'Organization name must be at least 3 characters';
    }

    if (!data.short_name?.trim()) {
      errors.short_name = 'Short name is required';
    } else if (data.short_name.length < 2) {
      errors.short_name = 'Short name must be at least 2 characters';
    }

    if (!data.email?.trim()) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!data.phone?.trim()) {
      errors.phone = 'Phone number is required';
    } else if (!/^\+?[\d\s\-\(\)]{10,15}$/.test(data.phone)) {
      errors.phone = 'Please enter a valid phone number';
    }

    // Optional website validation
    if (data.website && !/^https?:\/\/.+/.test(data.website)) {
      errors.website = 'Website must be a valid URL starting with http:// or https://';
    }

    // Operational settings validation
    if (data.document_retention_days !== undefined) {
      if (data.document_retention_days < 30 || data.document_retention_days > 10000) {
        errors.document_retention_days = 'Document retention must be between 30 and 10000 days';
      }
    }

    if (data.max_file_size_mb !== undefined) {
      if (data.max_file_size_mb < 1 || data.max_file_size_mb > 1000) {
        errors.max_file_size_mb = 'Max file size must be between 1 and 1000 MB';
      }
    }

    // Office hours validation
    if (data.office_hours_start && data.office_hours_end) {
      if (data.office_hours_start >= data.office_hours_end) {
        errors.office_hours_end = 'End time must be after start time';
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }

  // Format organization for display
  formatOrganizationSummary(org: Organization): string {
    const parts = [org.name];
    if (org.city_name) parts.push(org.city_name);
    if (org.country_name) parts.push(org.country_name);
    return parts.join(', ');
  }

  // Get organization status color
  getStatusColor(org: Organization): 'success' | 'warning' | 'error' | 'default' {
    if (!org.is_active) return 'error';
    if (org.is_default) return 'success';
    return 'default';
  }

  // Get organization status text
  getStatusText(org: Organization): string {
    if (!org.is_active) return 'Inactive';
    if (org.is_default) return 'Default';
    return 'Active';
  }
}

export default new OrganizationService();
