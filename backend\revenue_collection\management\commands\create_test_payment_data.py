"""
Management command to create test payment data for testing the payment system
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
import random

from revenue_collection.models import (
    RegionalCategory, RegionalRevenueSource, RevenuePeriod,
    RegionalRevenueCollection, TaxpayerPaymentSummary
)
from taxpayers.models import IndividualTaxPayer, OrganizationTaxPayer
from accounts.models import User


class Command(BaseCommand):
    help = 'Create test payment data for testing the payment system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=10,
            help='Number of test collections to create',
        )

    def handle(self, *args, **options):
        count = options['count']
        
        self.stdout.write('Creating test payment data...')
        
        # Get or create test user
        user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'first_name': 'Admin',
                'last_name': 'User',
                'is_staff': True,
                'is_superuser': True,
            }
        )
        if created:
            user.set_password('admin123')
            user.save()
            self.stdout.write(f'Created admin user: {user.email}')
        
        # Get or create test category
        category, created = RegionalCategory.objects.get_or_create(
            code='TEST',
            defaults={
                'name': 'Test Category',
                'description': 'Test category for payment testing',
                'created_by': user,
            }
        )
        if created:
            self.stdout.write(f'Created category: {category.name}')
        
        # Get or create test revenue source
        source, created = RegionalRevenueSource.objects.get_or_create(
            code='TEST-SRC',
            defaults={
                'name': 'Test Revenue Source',
                'description': 'Test revenue source for payment testing',
                'category': category,
                'created_by': user,
            }
        )
        if created:
            self.stdout.write(f'Created revenue source: {source.name}')
        
        # Get or create test period
        current_year = timezone.now().year
        period, created = RevenuePeriod.objects.get_or_create(
            name=f'Test Period {current_year}',
            defaults={
                'code': f'TEST{current_year}',
                'start_date': datetime(current_year, 1, 1).date(),
                'end_date': datetime(current_year, 12, 31).date(),
                'is_closed': False,
                'created_by': user,
            }
        )
        if created:
            self.stdout.write(f'Created period: {period.name}')
        
        # Get some taxpayers
        individual_taxpayers = list(IndividualTaxPayer.objects.all()[:5])
        organization_taxpayers = list(OrganizationTaxPayer.objects.all()[:5])
        
        if not individual_taxpayers and not organization_taxpayers:
            self.stdout.write(self.style.WARNING('No taxpayers found. Please create some taxpayers first.'))
            return
        
        # Create test collections
        collections_created = 0
        payment_statuses = ['PENDING', 'PARTIAL', 'OVERDUE', 'PAID']
        
        for i in range(count):
            # Randomly choose taxpayer
            if individual_taxpayers and organization_taxpayers:
                use_individual = random.choice([True, False])
            elif individual_taxpayers:
                use_individual = True
            else:
                use_individual = False
            
            if use_individual and individual_taxpayers:
                taxpayer = random.choice(individual_taxpayers)
                individual_taxpayer = taxpayer
                organization_taxpayer = None
            elif organization_taxpayers:
                taxpayer = random.choice(organization_taxpayers)
                individual_taxpayer = None
                organization_taxpayer = taxpayer
            else:
                continue
            
            # Random amounts and dates
            amount = Decimal(random.randint(1000, 50000))
            collection_date = timezone.now().date() - timedelta(days=random.randint(1, 90))
            due_date = collection_date + timedelta(days=30)
            
            # Random payment status
            payment_status = random.choice(payment_statuses)
            
            # Calculate paid amount based on status
            if payment_status == 'PAID':
                paid_amount = amount
                payment_date = due_date - timedelta(days=random.randint(1, 10))
            elif payment_status == 'PARTIAL':
                paid_amount = amount * Decimal(random.uniform(0.3, 0.8))
                payment_date = due_date - timedelta(days=random.randint(1, 5))
            elif payment_status == 'OVERDUE':
                paid_amount = Decimal('0')
                payment_date = None
            else:  # PENDING
                paid_amount = Decimal('0')
                payment_date = None
            
            # Create collection
            collection = RegionalRevenueCollection.objects.create(
                individual_taxpayer=individual_taxpayer,
                organization_taxpayer=organization_taxpayer,
                revenue_source=source,
                period=period,
                amount=amount,
                collection_date=collection_date,
                due_date=due_date,
                payment_date=payment_date,
                paid_amount=paid_amount,
                payment_status=payment_status,
                receipt_number=f'TEST-{i+1:04d}',
                notes=f'Test collection {i+1}',
                recorded_by=user,
            )
            
            # Add penalties and interest for overdue collections
            if payment_status == 'OVERDUE':
                days_overdue = (timezone.now().date() - due_date).days
                if days_overdue > 0:
                    # Calculate penalty (5% for individuals, 10% for organizations)
                    penalty_rate = 5.0 if individual_taxpayer else 10.0
                    collection.penalty_rate = penalty_rate
                    collection.penalty_amount = (amount * Decimal(penalty_rate)) / 100
                    collection.penalty_calculated_date = timezone.now().date()
                    
                    # Calculate interest (2% monthly for individuals, 3% for organizations)
                    interest_rate = 2.0 if individual_taxpayer else 3.0
                    months_overdue = days_overdue / 30.0
                    collection.interest_rate = interest_rate
                    collection.interest_amount = (amount * Decimal(interest_rate) * Decimal(months_overdue)) / 100
                    collection.interest_calculated_date = timezone.now().date()
                    
                    collection.save()
            
            collections_created += 1
            
            # Create or update taxpayer payment summary
            if individual_taxpayer:
                summary, created = TaxpayerPaymentSummary.objects.get_or_create(
                    individual_taxpayer=individual_taxpayer,
                    period=period,
                    defaults={'created_by': user}
                )
            else:
                summary, created = TaxpayerPaymentSummary.objects.get_or_create(
                    organization_taxpayer=organization_taxpayer,
                    period=period,
                    defaults={'created_by': user}
                )
            
            # Update summary
            summary.update_summary()
            summary.save()
        
        self.stdout.write(
            self.style.SUCCESS(f'Successfully created {collections_created} test collections')
        )
        
        # Show summary
        total_collections = RegionalRevenueCollection.objects.count()
        pending_count = RegionalRevenueCollection.objects.filter(payment_status='PENDING').count()
        partial_count = RegionalRevenueCollection.objects.filter(payment_status='PARTIAL').count()
        overdue_count = RegionalRevenueCollection.objects.filter(payment_status='OVERDUE').count()
        paid_count = RegionalRevenueCollection.objects.filter(payment_status='PAID').count()
        
        self.stdout.write('\nPayment Status Summary:')
        self.stdout.write(f'Total Collections: {total_collections}')
        self.stdout.write(f'PENDING: {pending_count}')
        self.stdout.write(f'PARTIAL: {partial_count}')
        self.stdout.write(f'OVERDUE: {overdue_count}')
        self.stdout.write(f'PAID: {paid_count}')
        
        self.stdout.write('\nTest data created successfully!')
        self.stdout.write('You can now test the payment system with real data.')
