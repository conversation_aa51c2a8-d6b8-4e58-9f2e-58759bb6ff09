# Generated by Django 5.2.3 on 2025-07-14 20:09

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("documents", "0001_initial"),
        ("locations", "0001_initial"),
        ("organizations", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="document",
            name="document_file",
            field=models.ForeignKey(
                blank=True,
                help_text="File where this document is grouped (e.g., 'Abebe's Hotel File')",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="documents",
                to="locations.file",
            ),
        ),
        migrations.AddField(
            model_name="document",
            name="kent",
            field=models.ForeignKey(
                blank=True,
                help_text="Physical location (Kent/Box) - auto-populated from file",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="legacy_documents",
                to="locations.kent",
            ),
        ),
        migrations.AddField(
            model_name="documenttag",
            name="organization",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="document_tags",
                to="organizations.organization",
            ),
        ),
        migrations.AddField(
            model_name="documenttype",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="created_document_types",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="documenttype",
            name="organization",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="document_types",
                to="organizations.organization",
            ),
        ),
        migrations.AddField(
            model_name="document",
            name="document_type",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="documents",
                to="documents.documenttype",
            ),
        ),
        migrations.AddField(
            model_name="documentversion",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="created_document_versions",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="documentversion",
            name="document",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="versions",
                to="documents.document",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="documenttag",
            unique_together={("organization", "name")},
        ),
        migrations.AlterUniqueTogether(
            name="documenttype",
            unique_together={("organization", "code")},
        ),
        migrations.AddIndex(
            model_name="document",
            index=models.Index(
                fields=["document_type", "status"],
                name="documents_d_documen_174e8d_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="document",
            index=models.Index(fields=["kent"], name="documents_d_kent_id_601056_idx"),
        ),
        migrations.AddIndex(
            model_name="document",
            index=models.Index(
                fields=["reference_number"], name="documents_d_referen_2dd071_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="document",
            index=models.Index(
                fields=["created_at"], name="documents_d_created_3b0a51_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="documentversion",
            unique_together={("document", "version_number")},
        ),
    ]
