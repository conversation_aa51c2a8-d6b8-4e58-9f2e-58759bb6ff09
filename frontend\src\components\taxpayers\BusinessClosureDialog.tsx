import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
  Typography,
  Alert,
  CircularProgress,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { Close, Business } from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import taxpayerService from '../../services/taxpayerService';

interface BusinessClosureDialogProps {
  open: boolean;
  onClose: () => void;
  taxpayerId: string;
  taxpayerType: 'individual' | 'organization';
  taxpayerName: string;
  onSuccess: () => void;
}

interface ClosureFormData {
  closure_date: Date | null;
  closure_reason: string;
}

const BusinessClosureDialog: React.FC<BusinessClosureDialogProps> = ({
  open,
  onClose,
  taxpayerId,
  taxpayerType,
  taxpayerName,
  onSuccess,
}) => {
  const { showNotification } = useNotification();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<ClosureFormData>({
    closure_date: new Date(),
    closure_reason: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange = (field: keyof ClosureFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.closure_date) {
      newErrors.closure_date = 'Closure date is required';
    } else if (formData.closure_date > new Date()) {
      newErrors.closure_date = 'Closure date cannot be in the future';
    }

    if (!formData.closure_reason.trim()) {
      newErrors.closure_reason = 'Closure reason is required';
    } else if (formData.closure_reason.trim().length < 10) {
      newErrors.closure_reason = 'Closure reason must be at least 10 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      const closureData = {
        closure_date: formData.closure_date!.toISOString().split('T')[0], // Format as YYYY-MM-DD
        closure_reason: formData.closure_reason.trim(),
      };

      if (taxpayerType === 'individual') {
        await taxpayerService.closeIndividualBusiness(taxpayerId, closureData);
      } else {
        await taxpayerService.closeOrganizationBusiness(taxpayerId, closureData);
      }

      showNotification('Business closed successfully', 'success');
      onSuccess();
      handleClose();
    } catch (error: any) {
      console.error('Failed to close business:', error);
      const errorMessage = error.response?.data?.error || 'Failed to close business';
      showNotification(errorMessage, 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setFormData({
        closure_date: new Date(),
        closure_reason: '',
      });
      setErrors({});
      onClose();
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Dialog 
        open={open} 
        onClose={handleClose}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
          }
        }}
      >
        <DialogTitle sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: 2,
          pb: 1,
          borderBottom: '1px solid',
          borderColor: 'divider'
        }}>
          <Business color="error" />
          <Box>
            <Typography variant="h6" component="div">
              Close Business
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {taxpayerName}
            </Typography>
          </Box>
        </DialogTitle>

        <DialogContent sx={{ pt: 3 }}>
          <Alert severity="warning" sx={{ mb: 3 }}>
            <Typography variant="body2">
              <strong>Warning:</strong> Closing a business will mark it as permanently closed. 
              This action will deactivate the taxpayer and should only be done for legitimate 
              business closures such as bankruptcy, voluntary closure, or permanent relocation.
            </Typography>
          </Alert>

          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            <DatePicker
              label="Closure Date *"
              value={formData.closure_date}
              onChange={(date) => handleInputChange('closure_date', date)}
              maxDate={new Date()}
              slotProps={{
                textField: {
                  fullWidth: true,
                  error: !!errors.closure_date,
                  helperText: errors.closure_date || 'Date when the business was officially closed',
                }
              }}
            />

            <TextField
              label="Closure Reason *"
              multiline
              rows={4}
              value={formData.closure_reason}
              onChange={(e) => handleInputChange('closure_reason', e.target.value)}
              error={!!errors.closure_reason}
              helperText={errors.closure_reason || 'Provide detailed reason for business closure (minimum 10 characters)'}
              placeholder="e.g., Voluntary business closure due to retirement, Bankruptcy proceedings initiated, Permanent relocation to another jurisdiction, etc."
              fullWidth
            />
          </Box>
        </DialogContent>

        <DialogActions sx={{ p: 3, pt: 2 }}>
          <Button
            onClick={handleClose}
            disabled={loading}
            startIcon={<Close />}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            color="error"
            disabled={loading}
            startIcon={loading ? <CircularProgress size={16} /> : <Business />}
          >
            {loading ? 'Closing Business...' : 'Close Business'}
          </Button>
        </DialogActions>
      </Dialog>
    </LocalizationProvider>
  );
};

export default BusinessClosureDialog;
