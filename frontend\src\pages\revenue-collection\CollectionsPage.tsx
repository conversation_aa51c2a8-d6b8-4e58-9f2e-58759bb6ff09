/**
 * Revenue Collections Management Page
 * 
 * Manages both Regional and City Service revenue collections
 * with tabbed interface and CRUD operations.
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  TextField,
  InputAdornment,
  CircularProgress,
  Alert,
  Tooltip,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Visibility,
  Search,
  FilterList,
  AccountBalance,
  Business,
  Receipt,
  TrendingUp,
  Payment,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { useNavigate } from 'react-router-dom';
import { useNotification } from '../../contexts/NotificationContext';
import revenueCollectionService from '../../services/revenueCollectionService';
import type {
  RegionalRevenueCollection,
  CityServiceRevenueCollection,
  RevenuePeriod,
} from '../../services/revenueCollectionService';
import PaymentStatusManager from '../../components/revenue-collection/PaymentStatusManager';
import PaymentProcessor from '../../components/revenue-collection/PaymentProcessor';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`collection-tabpanel-${index}`}
      aria-labelledby={`collection-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const CollectionsPage: React.FC = () => {
  const navigate = useNavigate();
  const { showSuccess, showError } = useNotification();
  
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(true);
  const [regionalCollections, setRegionalCollections] = useState<RegionalRevenueCollection[]>([]);
  const [cityServiceCollections, setCityServiceCollections] = useState<CityServiceRevenueCollection[]>([]);
  const [periods, setPeriods] = useState<RevenuePeriod[]>([]);
  
  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPeriod, setSelectedPeriod] = useState('');
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [taxpayerType, setTaxpayerType] = useState('');

  // Payment processing states
  const [paymentProcessorOpen, setPaymentProcessorOpen] = useState(false);
  const [selectedCollection, setSelectedCollection] = useState<any>(null);
  const [selectedCollectionType, setSelectedCollectionType] = useState<'regional' | 'city-service'>('regional');

  // Statistics
  const [stats, setStats] = useState({
    regionalTotal: 0,
    regionalCount: 0,
    cityServiceTotal: 0,
    cityServiceCount: 0,
  });

  useEffect(() => {
    loadData();
    loadPeriods();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [searchTerm, selectedPeriod, selectedDate, taxpayerType]);

  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (searchTerm || selectedPeriod || selectedDate || taxpayerType) {
        loadFilteredData();
      } else {
        loadData();
      }
    }, 300); // Debounce search

    return () => clearTimeout(delayedSearch);
  }, [searchTerm, selectedPeriod, selectedDate, taxpayerType]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [regionalResponse, cityServiceResponse] = await Promise.all([
        revenueCollectionService.getRegionalRevenueCollections({
          ordering: '-collection_date',
        }),
        revenueCollectionService.getCityServiceRevenueCollections({
          ordering: '-collection_date',
        }),
      ]);
      
      setRegionalCollections(regionalResponse.results);
      setCityServiceCollections(cityServiceResponse.results);
      
      // Calculate statistics with proper number conversion
      const regionalTotal = regionalResponse.results.reduce((sum, c) => sum + (Number(c.amount) || 0), 0);
      const cityServiceTotal = cityServiceResponse.results.reduce((sum, c) => sum + (Number(c.amount) || 0), 0);

      setStats({
        regionalTotal,
        regionalCount: regionalResponse.count || regionalResponse.results.length,
        cityServiceTotal,
        cityServiceCount: cityServiceResponse.count || cityServiceResponse.results.length,
      });
    } catch (error) {
      console.error('Error loading collections:', error);
      showError('Failed to load collections');
    } finally {
      setLoading(false);
    }
  };

  const loadPeriods = async () => {
    try {
      const response = await revenueCollectionService.getRevenuePeriods();
      setPeriods(response.results);
    } catch (error) {
      console.error('Error loading periods:', error);
    }
  };

  const loadFilteredData = async () => {
    try {
      setLoading(true);

      // Build filter parameters
      const params: any = {
        ordering: '-collection_date',
      };

      if (searchTerm) {
        params.search = searchTerm;
      }

      if (selectedPeriod) {
        params.period = selectedPeriod;
      }

      if (selectedDate) {
        params.collection_date = selectedDate.toISOString().split('T')[0];
      }

      if (taxpayerType) {
        params.taxpayer_type = taxpayerType;
      }

      const [regionalResponse, cityServiceResponse] = await Promise.all([
        revenueCollectionService.getRegionalRevenueCollections(params),
        revenueCollectionService.getCityServiceRevenueCollections(params),
      ]);

      setRegionalCollections(regionalResponse.results);
      setCityServiceCollections(cityServiceResponse.results);

      // Calculate filtered statistics
      const regionalTotal = regionalResponse.results.reduce((sum, c) => sum + c.amount, 0);
      const cityServiceTotal = cityServiceResponse.results.reduce((sum, c) => sum + c.amount, 0);

      setStats({
        regionalTotal,
        regionalCount: regionalResponse.results.length,
        cityServiceTotal,
        cityServiceCount: cityServiceResponse.results.length,
      });
    } catch (error) {
      console.error('Error loading filtered collections:', error);
      showError('Failed to load filtered collections');
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    // This function is now handled by the useEffect above
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleDelete = async (type: 'regional' | 'city_service', id: string) => {
    if (!window.confirm('Are you sure you want to delete this collection?')) {
      return;
    }

    try {
      if (type === 'regional') {
        await revenueCollectionService.deleteRegionalRevenueCollection(id);
        showSuccess('Regional collection deleted successfully');
      } else {
        await revenueCollectionService.deleteCityServiceRevenueCollection(id);
        showSuccess('City service collection deleted successfully');
      }

      loadData();
    } catch (error) {
      console.error('Error deleting collection:', error);
      showError('Failed to delete collection');
    }
  };

  const handleProcessPayment = (collection: any, type: 'regional' | 'city-service') => {
    setSelectedCollection(collection);
    setSelectedCollectionType(type);
    setPaymentProcessorOpen(true);
  };

  const handlePaymentProcessed = () => {
    loadData(); // Refresh data after payment processing
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ET', {
      style: 'currency',
      currency: 'ETB',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-ET', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const renderCollectionTable = (
    collections: (RegionalRevenueCollection | CityServiceRevenueCollection)[],
    type: 'regional' | 'city_service'
  ) => (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Date</TableCell>
            <TableCell>Taxpayer</TableCell>
            <TableCell>Revenue Source</TableCell>
            <TableCell>Period</TableCell>
            <TableCell align="right">Amount</TableCell>
            <TableCell>Payment Status</TableCell>
            <TableCell>Receipt</TableCell>
            <TableCell>Recorded By</TableCell>
            <TableCell>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {collections.map((collection) => (
            <TableRow key={collection.id}>
              <TableCell>{formatDate(collection.collection_date)}</TableCell>
              <TableCell>
                <Box>
                  <Typography variant="body2" fontWeight="medium">
                    {collection.taxpayer_name}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    TIN: {collection.taxpayer_tin}
                  </Typography>
                  <Chip
                    label={collection.taxpayer_type === 'individual' ? 'Individual' : 'Organization'}
                    size="small"
                    color={collection.taxpayer_type === 'individual' ? 'primary' : 'secondary'}
                    sx={{ ml: 1 }}
                  />
                </Box>
              </TableCell>
              <TableCell>
                <Typography variant="body2" fontWeight="medium">
                  {collection.revenue_source_name}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {collection.category_name}
                </Typography>
              </TableCell>
              <TableCell>
                <Chip
                  label={collection.period_name}
                  size="small"
                  variant="outlined"
                />
              </TableCell>
              <TableCell align="right">
                <Typography variant="body2" fontWeight="medium" color="success.main">
                  {formatCurrency(collection.amount)}
                </Typography>
              </TableCell>
              <TableCell>
                <Chip
                  label={collection.payment_status || 'PENDING'}
                  size="small"
                  color={
                    collection.payment_status === 'PAID' ? 'success' :
                    collection.payment_status === 'PARTIAL' ? 'warning' :
                    collection.payment_status === 'OVERDUE' ? 'error' : 'default'
                  }
                />
                {collection.paid_amount > 0 && (
                  <Typography variant="caption" color="text.secondary" display="block">
                    Paid: {formatCurrency(collection.paid_amount)}
                  </Typography>
                )}
              </TableCell>
              <TableCell>
                <Typography variant="body2">
                  {collection.receipt_number || 'N/A'}
                </Typography>
              </TableCell>
              <TableCell>
                <Typography variant="body2">
                  {collection.recorded_by_name}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {formatDate(collection.recorded_date)}
                </Typography>
              </TableCell>
              <TableCell>
                <Tooltip title="View Details">
                  <IconButton
                    size="small"
                    onClick={() => {
                      const routeType = type === 'regional' ? 'regional' : 'city-service';
                      navigate(`/revenue-collection/collections/${routeType}/${collection.id}`);
                    }}
                  >
                    <Visibility />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Process Payment">
                  <IconButton
                    size="small"
                    color="primary"
                    onClick={() => handleProcessPayment(collection, type === 'regional' ? 'regional' : 'city-service')}
                  >
                    <Payment />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Edit">
                  <IconButton
                    size="small"
                    onClick={() => {
                      const routeType = type === 'regional' ? 'regional' : 'city-service';
                      navigate(`/revenue-collection/collections/${routeType}/${collection.id}/edit`);
                    }}
                  >
                    <Edit />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Delete">
                  <IconButton
                    size="small"
                    color="error"
                    onClick={() => handleDelete(type, collection.id)}
                  >
                    <Delete />
                  </IconButton>
                </Tooltip>
              </TableCell>
            </TableRow>
          ))}
          {collections.length === 0 && (
            <TableRow>
              <TableCell colSpan={8} align="center">
                <Typography variant="body2" color="text.secondary">
                  No collections found
                </Typography>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </TableContainer>
  );

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box sx={{ flexGrow: 1, p: 3 }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Revenue Collections
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Manage and track all revenue collections
          </Typography>
        </Box>

        {/* Statistics Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <AccountBalance color="primary" sx={{ mr: 1 }} />
                  <Typography variant="h6" component="div">
                    Regional Revenue
                  </Typography>
                </Box>
                <Typography variant="h4" component="div" color="primary">
                  {formatCurrency(stats.regionalTotal)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {stats.regionalCount} collections
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Business color="secondary" sx={{ mr: 1 }} />
                  <Typography variant="h6" component="div">
                    City Services
                  </Typography>
                </Box>
                <Typography variant="h4" component="div" color="secondary">
                  {formatCurrency(stats.cityServiceTotal)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {stats.cityServiceCount} collections
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Receipt color="success" sx={{ mr: 1 }} />
                  <Typography variant="h6" component="div">
                    Total Collections
                  </Typography>
                </Box>
                <Typography variant="h4" component="div" color="success.main">
                  {stats.regionalCount + stats.cityServiceCount}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  All records
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <TrendingUp color="warning" sx={{ mr: 1 }} />
                  <Typography variant="h6" component="div">
                    Grand Total
                  </Typography>
                </Box>
                <Typography variant="h4" component="div" color="warning.main">
                  {formatCurrency(stats.regionalTotal + stats.cityServiceTotal)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  All revenue
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Filters */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Filters
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="Search"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Search />
                      </InputAdornment>
                    ),
                  }}
                  placeholder="Search by taxpayer, TIN, or receipt"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Period</InputLabel>
                  <Select
                    value={selectedPeriod}
                    onChange={(e) => setSelectedPeriod(e.target.value)}
                    label="Period"
                  >
                    <MenuItem value="">All Periods</MenuItem>
                    {periods.map((period) => (
                      <MenuItem key={period.id} value={period.id}>
                        {period.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <DatePicker
                  label="Collection Date"
                  value={selectedDate}
                  onChange={(date) => setSelectedDate(date)}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Taxpayer Type</InputLabel>
                  <Select
                    value={taxpayerType}
                    onChange={(e) => setTaxpayerType(e.target.value)}
                    label="Taxpayer Type"
                  >
                    <MenuItem value="">All Types</MenuItem>
                    <MenuItem value="individual">Individual</MenuItem>
                    <MenuItem value="organization">Organization</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Tabs */}
        <Card>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs value={tabValue} onChange={handleTabChange}>
              <Tab
                label="Regional Collections"
                icon={<AccountBalance />}
                iconPosition="start"
              />
              <Tab
                label="City Service Collections"
                icon={<Business />}
                iconPosition="start"
              />
            </Tabs>
          </Box>

          {/* Regional Collections Tab */}
          <TabPanel value={tabValue} index={0}>
            <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="h6">
                Regional Revenue Collections ({regionalCollections.length})
              </Typography>
              <Button
                variant="contained"
                startIcon={<Add />}
                onClick={() => navigate('/revenue-collection/collections/regional/create')}
              >
                New Regional Collection
              </Button>
            </Box>
            {renderCollectionTable(regionalCollections, 'regional')}
          </TabPanel>

          {/* City Service Collections Tab */}
          <TabPanel value={tabValue} index={1}>
            <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="h6">
                City Service Revenue Collections ({cityServiceCollections.length})
              </Typography>
              <Button
                variant="contained"
                color="secondary"
                startIcon={<Add />}
                onClick={() => navigate('/revenue-collection/collections/city-service/create')}
              >
                New City Service Collection
              </Button>
            </Box>
            {renderCollectionTable(cityServiceCollections, 'city_service')}
          </TabPanel>
        </Card>

        {/* Payment Status Manager */}
        <Box sx={{ mt: 3 }}>
          <PaymentStatusManager
            collections={tabValue === 0 ? regionalCollections : cityServiceCollections}
            onRefresh={loadData}
          />
        </Box>
      </Box>

      {/* Payment Processor Dialog */}
      {selectedCollection && (
        <PaymentProcessor
          open={paymentProcessorOpen}
          onClose={() => setPaymentProcessorOpen(false)}
          collection={selectedCollection}
          collectionType={selectedCollectionType}
          onPaymentProcessed={handlePaymentProcessed}
        />
      )}
    </LocalizationProvider>
  );
};

export default CollectionsPage;
