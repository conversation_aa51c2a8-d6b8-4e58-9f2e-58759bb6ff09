from django.urls import path
from . import views

app_name = 'organizations'

urlpatterns = [
    # Organization endpoints
    path('', views.OrganizationListCreateView.as_view(), name='organization_list_create'),
    path('default/', views.DefaultOrganizationView.as_view(), name='default_organization'),
    path('default/public/', views.DefaultOrganizationPublicView.as_view(), name='default_organization_public'),
    path('<int:pk>/', views.OrganizationDetailView.as_view(), name='organization_detail'),
    path('<int:pk>/set-default/', views.SetDefaultOrganizationView.as_view(), name='set_default_organization'),
    path('<int:pk>/upload-logo/', views.OrganizationLogoUploadView.as_view(), name='upload_organization_logo'),

    # Department endpoints
    path('departments/', views.DepartmentListCreateView.as_view(), name='department_list_create'),
    path('departments/<int:pk>/', views.DepartmentDetailView.as_view(), name='department_detail'),
    path('<int:org_pk>/departments/', views.OrganizationDepartmentListView.as_view(), name='organization_departments'),
]
