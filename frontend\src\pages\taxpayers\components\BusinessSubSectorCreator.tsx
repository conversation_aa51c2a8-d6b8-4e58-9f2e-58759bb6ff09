import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Alert,
  CircularProgress,
  Typography,
  Box,
} from '@mui/material';
import {
  Add,
  Save,
  Cancel,
} from '@mui/icons-material';
import { useNotification } from '../../../contexts/NotificationContext';
// Import the service
import taxpayerService from '../../../services/taxpayerService';

// Define interfaces locally to avoid import issues
interface BusinessSector {
  id: string;
  code: string;
  name: string;
  description: string;
  is_active: boolean;
  sub_sectors_count: number;
  created_at: string;
  updated_at: string;
}

interface BusinessSubSector {
  id: string;
  business_sector: string;
  business_sector_name: string;
  business_sector_code: string;
  code: string;
  name: string;
  description: string;
  full_code: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface BusinessSubSectorCreate {
  business_sector: string;
  code: string;
  name: string;
  description?: string;
}

interface BusinessSubSectorCreatorProps {
  open: boolean;
  onClose: () => void;
  onCreated: (subSector: BusinessSubSector) => void;
  preSelectedSector?: string;
}

const BusinessSubSectorCreator: React.FC<BusinessSubSectorCreatorProps> = ({
  open,
  onClose,
  onCreated,
  preSelectedSector,
}) => {
  const { showNotification } = useNotification();
  const [loading, setLoading] = useState(false);
  const [sectors, setSectors] = useState<BusinessSector[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const [formData, setFormData] = useState<BusinessSubSectorCreate>({
    business_sector: preSelectedSector || '',
    code: '',
    name: '',
    description: '',
  });

  useEffect(() => {
    if (open) {
      loadSectors();
      if (preSelectedSector) {
        setFormData(prev => ({ ...prev, business_sector: preSelectedSector }));
      }
    }
  }, [open, preSelectedSector]);

  const loadSectors = async () => {
    try {
      const sectorsData = await taxpayerService.getBusinessSectorsSimple();
      setSectors(sectorsData);
    } catch (error) {
      console.error('Failed to load sectors:', error);
      showNotification('Failed to load business sectors', 'error');
    }
  };

  const handleInputChange = (field: keyof BusinessSubSectorCreate, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.business_sector) newErrors.business_sector = 'Business sector is required';
    if (!formData.code) newErrors.code = 'Code is required';
    if (!formData.name) newErrors.name = 'Name is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!validateForm()) {
      showNotification('Please fix the form errors', 'error');
      return;
    }

    try {
      setLoading(true);
      const newSubSector = await taxpayerService.createBusinessSubSector(formData);
      showNotification('Business sub-sector created successfully', 'success');
      onCreated(newSubSector);
      handleClose();
    } catch (error: any) {
      console.error('Failed to create sub-sector:', error);
      
      if (error.response?.data) {
        setErrors(error.response.data);
        showNotification('Please fix the form errors', 'error');
      } else {
        showNotification('Failed to create sub-sector', 'error');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setFormData({
      business_sector: preSelectedSector || '',
      code: '',
      name: '',
      description: '',
    });
    setErrors({});
    onClose();
  };

  const selectedSector = sectors.find(sector => sector.id === formData.business_sector);

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Add color="primary" />
          Create New Business Sub-Sector
        </Box>
      </DialogTitle>
      
      <DialogContent>
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="body2">
            Create a new business sub-sector to classify taxpayers more specifically. 
            This will be available for all future taxpayer registrations.
          </Typography>
        </Alert>

        <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
          <Grid container spacing={3}>
            <Grid size={{ xs: 12 }}>
              <FormControl fullWidth error={!!errors.business_sector}>
                <InputLabel>Business Sector *</InputLabel>
                <Select
                  value={formData.business_sector}
                  onChange={(e) => handleInputChange('business_sector', e.target.value)}
                  label="Business Sector *"
                  disabled={!!preSelectedSector}
                >
                  {sectors.map((sector) => (
                    <MenuItem key={sector.id} value={sector.id}>
                      {sector.code} - {sector.name}
                    </MenuItem>
                  ))}
                </Select>
                {errors.business_sector && (
                  <Typography variant="caption" color="error" sx={{ mt: 0.5 }}>
                    {errors.business_sector}
                  </Typography>
                )}
              </FormControl>
            </Grid>

            {selectedSector && (
              <Grid size={{ xs: 12 }}>
                <Alert severity="success" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    <strong>Selected Sector:</strong> {selectedSector.name}
                    <br />
                    <strong>Code Prefix:</strong> {selectedSector.code}-
                    <br />
                    <Typography variant="caption">
                      Your sub-sector code will be: {selectedSector.code}-{formData.code}
                    </Typography>
                  </Typography>
                </Alert>
              </Grid>
            )}

            <Grid size={{ xs: 12, md: 6 }}>
              <TextField
                fullWidth
                label="Sub-Sector Code *"
                value={formData.code}
                onChange={(e) => handleInputChange('code', e.target.value.toUpperCase())}
                error={!!errors.code}
                helperText={errors.code || `Will be: ${selectedSector?.code || 'XXX'}-${formData.code}`}
                inputProps={{ maxLength: 15 }}
                placeholder="001, 002, etc."
              />
            </Grid>

            <Grid size={{ xs: 12, md: 6 }}>
              <TextField
                fullWidth
                label="Sub-Sector Name *"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                error={!!errors.name}
                helperText={errors.name}
                placeholder="e.g., Crop Production, Retail Trade"
              />
            </Grid>

            <Grid size={{ xs: 12 }}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={3}
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Detailed description of this sub-sector..."
              />
            </Grid>
          </Grid>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button 
          onClick={handleClose}
          disabled={loading}
          startIcon={<Cancel />}
        >
          Cancel
        </Button>
        <Button 
          onClick={handleSubmit}
          variant="contained"
          disabled={loading}
          startIcon={loading ? <CircularProgress size={20} /> : <Save />}
        >
          {loading ? 'Creating...' : 'Create Sub-Sector'}
        </Button>
      </DialogActions>

      {Object.keys(errors).length > 0 && (
        <Alert severity="error" sx={{ m: 2 }}>
          Please fix the errors above and try again.
        </Alert>
      )}
    </Dialog>
  );
};

export default BusinessSubSectorCreator;
