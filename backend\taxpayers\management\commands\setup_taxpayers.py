from django.core.management.base import BaseCommand
from django.core.management import call_command
from django.db import transaction
import uuid


class Command(BaseCommand):
    help = 'Set up the taxpayers app with initial data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--skip-migrations',
            action='store_true',
            help='Skip running migrations',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Setting up Tax Payers Application...')
        )

        try:
            # Run migrations if not skipped
            if not options['skip_migrations']:
                self.stdout.write('Creating database tables...')
                call_command('makemigrations', 'taxpayers', verbosity=0)
                call_command('migrate', 'taxpayers', verbosity=0)
                self.stdout.write(
                    self.style.SUCCESS('✓ Database tables created successfully')
                )

            # Load initial data
            self.stdout.write('Loading initial data...')
            call_command('loaddata', 'taxpayers/fixtures/initial_data.json', verbosity=0)
            self.stdout.write(
                self.style.SUCCESS('✓ Initial data loaded successfully')
            )

            # Create sample taxpayers
            self.create_sample_taxpayers()

            self.stdout.write(
                self.style.SUCCESS(
                    '\n🎉 Tax Payers Application setup completed successfully!\n'
                    '\nYou can now:\n'
                    '1. Access the admin interface at /admin/\n'
                    '2. Use the API endpoints at /api/taxpayers/\n'
                    '3. Access the frontend at /taxpayers\n'
                )
            )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error during setup: {str(e)}')
            )
            raise

    def create_sample_taxpayers(self):
        """Create sample taxpayers for demonstration"""
        from taxpayers.models import (
            BusinessSector, BusinessSubSector, TaxPayerLevel,
            OrganizationBusinessType, IndividualTaxPayer, OrganizationTaxPayer
        )
        from locations.location_hierarchy_models import SubCity, Kebele

        self.stdout.write('Creating sample taxpayers...')

        try:
            # Get required objects
            agriculture_sector = BusinessSector.objects.filter(code='AGR').first()
            services_sector = BusinessSector.objects.filter(code='SER').first()
            crop_subsector = BusinessSubSector.objects.filter(code='001', business_sector=agriculture_sector).first()
            professional_subsector = BusinessSubSector.objects.filter(code='001', business_sector=services_sector).first()
            
            level_c = TaxPayerLevel.objects.filter(code='C').first()
            level_b = TaxPayerLevel.objects.filter(code='B').first()
            
            plc_type = OrganizationBusinessType.objects.filter(code='PLC').first()
            sole_type = OrganizationBusinessType.objects.filter(code='SOLE').first()

            # Get first available subcity and kebele
            subcity = SubCity.objects.first()
            kebele = Kebele.objects.first() if subcity else None

            if not all([agriculture_sector, services_sector, crop_subsector, professional_subsector, level_c, level_b, plc_type, sole_type]):
                self.stdout.write(
                    self.style.WARNING('Some required data not found. Skipping sample taxpayer creation.')
                )
                return

            # Create sample individual taxpayer
            individual, created = IndividualTaxPayer.objects.get_or_create(
                tin='1234567890',
                defaults={
                    'first_name': 'John',
                    'middle_name': 'Doe',
                    'last_name': 'Smith',
                    'nationality': 'ET',
                    'gender': 'M',
                    'date_of_birth': '1985-01-15',
                    'tax_payer_level': level_c,
                    'business_sector': agriculture_sector,
                    'business_sub_sector': crop_subsector,
                    'business_registration_date': '2020-01-01',
                    'business_name': 'Smith Farm',
                    'phone': '+251911123456',
                    'email': '<EMAIL>',
                    'subcity': subcity,
                    'kebele': kebele,
                    'house_number': '123',
                    'street_address': 'Main Street',
                }
            )

            if created:
                self.stdout.write('✓ Sample individual taxpayer created')

            # Create sample organization taxpayer
            organization, created = OrganizationTaxPayer.objects.get_or_create(
                tin='**********',
                defaults={
                    'business_name': 'Tech Solutions PLC',
                    'trade_name': 'TechSol',
                    'organization_business_type': plc_type,
                    'tax_payer_level': level_b,
                    'business_sector': services_sector,
                    'business_sub_sector': professional_subsector,
                    'business_registration_date': '2018-06-01',
                    'business_license_number': 'BL-2018-001',
                    'capital_amount': 5000000.00,
                    'number_of_employees': 25,
                    'manager_first_name': 'Jane',
                    'manager_middle_name': 'Mary',
                    'manager_last_name': 'Johnson',
                    'manager_title': 'CEO',
                    'vat_registration_date': '2018-07-01',
                    'vat_number': 'ET**********',
                    'phone': '+251911987654',
                    'email': '<EMAIL>',
                    'subcity': subcity,
                    'kebele': kebele,
                    'house_number': '456',
                    'street_address': 'Business District',
                }
            )

            if created:
                self.stdout.write('✓ Sample organization taxpayer created')

        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'Could not create sample taxpayers: {str(e)}')
            )
