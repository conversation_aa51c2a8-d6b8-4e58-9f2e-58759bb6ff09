import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  TextField,
  InputAdornment,
  Avatar,
  Divider,
  Stack,
  Fade,
  Alert,
  CircularProgress,
  IconButton,
  Breadcrumbs,
  Link,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TablePagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Folder,
  ArrowBack,
  Home,
  Add,
  Edit,
  Delete,
  Visibility,
  Inventory,
  Business,
  Description,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useNotification } from '../../contexts/NotificationContext';
import locationService from '../../services/locationService';
import fileService from '../../services/fileService';
import ConfirmationDialog from '../../components/common/ConfirmationDialog';
import TaxpayerSelector from '../../components/taxpayers/TaxpayerSelector';
import type { File, FileCreate } from '../../services/fileService';
import type { Kent, FileType } from '../../services/types';

const BusinessFilesPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { showNotification } = useNotification();

  const [files, setFiles] = useState<File[]>([]);
  const [kents, setKents] = useState<Kent[]>([]);
  const [fileTypes, setFileTypes] = useState<FileType[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingFile, setEditingFile] = useState<File | null>(null);
  const [formData, setFormData] = useState<FileCreate>({
    kent: '',
    name: '',
    file_number: '',
    file_type: '',
    description: '',
    business_name: '',
    tin_number: '',
    owner_name: '',
    contact_phone: '',
    contact_email: '',
    address: '',
  });
  const [formErrors, setFormErrors] = useState<any>({});
  const [selectedTaxpayer, setSelectedTaxpayer] = useState<any>(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [fileToDelete, setFileToDelete] = useState<File | null>(null);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    loadFiles();
    loadKents();
    loadFileTypes();
  }, [page, rowsPerPage]);

  // Handle navigation state from detail page
  useEffect(() => {
    const state = location.state as any;
    if (state?.editFile && state?.showForm) {
      handleEdit(state.editFile);
      // Clear the state to prevent re-triggering
      navigate(location.pathname, { replace: true });
    }
  }, [location.state]);

  const loadFiles = async () => {
    try {
      setLoading(true);
      const response = await fileService.getFiles({
        page: page + 1,
        page_size: rowsPerPage,
      });
      setFiles(response.results);
      setTotalCount(response.count);
    } catch (error) {
      console.error('Error loading files:', error);
      showNotification('Failed to load files', 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadKents = async () => {
    try {
      const response = await locationService.getKents({ page_size: 100 });
      setKents(response.results);
    } catch (error) {
      console.error('Error loading kents:', error);
    }
  };

  const loadFileTypes = async () => {
    try {
      const response = await fileService.getFileTypes({ page_size: 100 });
      setFileTypes(response.results);
    } catch (error) {
      console.error('Error loading file types:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setSubmitting(true);
      setFormErrors({});

      // Convert string values to numbers for API and include taxpayer data
      const submitData = {
        ...formData,
        kent: Number(formData.kent),
        file_type: Number(formData.file_type),
        // Include taxpayer linking data
        taxpayer_id: selectedTaxpayer?.id || null,
        taxpayer_type: selectedTaxpayer?.type || null,
      };

      if (editingFile) {
        await fileService.updateFile(editingFile.id, submitData);
        showNotification('File updated successfully', 'success');
      } else {
        await fileService.createFile(submitData);
        showNotification('File created successfully', 'success');
      }

      resetForm();
      loadFiles();
    } catch (error: any) {
      console.error('Error submitting form:', error);
      if (error.response?.data) {
        setFormErrors(error.response.data);
      }
      showNotification('Failed to save file', 'error');
    } finally {
      setSubmitting(false);
    }
  };

  const handleViewDetails = (file: File) => {
    navigate(`/document-center/business-files/${file.id}`);
  };

  const handleEdit = (file: File) => {
    setEditingFile(file);
    setFormData({
      kent: file.kent,
      name: file.name,
      file_number: file.file_number,
      file_type: file.file_type || 0,
      description: file.description || '',
      business_name: file.business_name || '',
      tin_number: file.tin_number || '',
      owner_name: file.owner_name || '',
      contact_phone: file.contact_phone || '',
      contact_email: file.contact_email || '',
      address: file.address || '',
    });
    setShowForm(true);
  };

  const handleDelete = (file: File) => {
    setFileToDelete(file);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!fileToDelete) return;
    
    try {
      setDeleting(true);
      await fileService.deleteFile(fileToDelete.id);
      showNotification('File deleted successfully', 'success');
      loadFiles();
      handleCloseDeleteDialog();
    } catch (error) {
      console.error('Error deleting file:', error);
      showNotification('Failed to delete file', 'error');
    } finally {
      setDeleting(false);
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setFileToDelete(null);
    setDeleting(false);
  };

  const resetForm = () => {
    setFormData({
      kent: '',
      name: '',
      file_number: '',
      file_type: '',
      description: '',
      business_name: '',
      tin_number: '',
      owner_name: '',
      contact_phone: '',
      contact_email: '',
      address: '',
    });
    setFormErrors({});
    setSelectedTaxpayer(null);
    setEditingFile(null);
    setShowForm(false);
  };

  // Auto-populate form fields when taxpayer is selected
  const handleTaxpayerChange = (taxpayer: any) => {
    setSelectedTaxpayer(taxpayer);

    if (taxpayer) {
      // Auto-populate form fields based on taxpayer data
      setFormData(prev => ({
        ...prev,
        business_name: taxpayer.type === 'organization' ? taxpayer.business_name : taxpayer.full_name,
        tin_number: taxpayer.tin,
        owner_name: taxpayer.type === 'individual' ? taxpayer.full_name : taxpayer.business_name,
        contact_phone: taxpayer.phone || '',
        contact_email: taxpayer.email || '',
        // Generate suggested file name
        name: `${taxpayer.display_name} - Tax File`,
      }));
    }
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Fade in timeout={800}>
        <Box sx={{ mb: 4 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs sx={{ mb: 2 }}>
            <Link
              component="button"
              variant="body1"
              onClick={() => navigate('/document-center')}
              sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
            >
              <Home fontSize="small" />
              Document Management Center
            </Link>
            <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <Folder fontSize="small" />
              Business Files
            </Typography>
          </Breadcrumbs>

          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'between', mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <IconButton onClick={() => navigate('/document-center')} sx={{ mr: 1 }}>
                <ArrowBack />
              </IconButton>
              <Avatar sx={{ bgcolor: 'info.main', width: 56, height: 56 }}>
                <Folder />
              </Avatar>
              <Box>
                <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
                  Business Files Management
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Organize business files within storage containers
                </Typography>
              </Box>
            </Box>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setShowForm(true)}
              size="large"
              sx={{ ml: 'auto' }}
            >
              Add Business File
            </Button>
          </Box>
        </Box>
      </Fade>

      {/* Form Section */}
      {showForm && (
        <Fade in timeout={600}>
          <Card sx={{ mb: 4 }}>
            <CardContent sx={{ p: 4 }}>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                {editingFile ? 'Edit Business File' : 'Add New Business File'}
              </Typography>
              <Divider sx={{ mb: 3 }} />
              
              <form onSubmit={handleSubmit}>
                <Stack spacing={3}>
                  {/* Taxpayer Selection Section */}
                  <Box sx={{
                    p: 3,
                    bgcolor: 'primary.50',
                    borderRadius: 2,
                    border: '1px solid',
                    borderColor: 'primary.200'
                  }}>
                    <Typography variant="h6" sx={{ mb: 2, color: 'primary.main', fontWeight: 600 }}>
                      📋 Link to Taxpayer (Recommended)
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                      Select a taxpayer to automatically populate file details and create a link between the taxpayer and their file.
                    </Typography>

                    <TaxpayerSelector
                      value={selectedTaxpayer}
                      onChange={handleTaxpayerChange}
                      label="Select File Owner (Taxpayer)"
                      placeholder="Search by name, TIN, or business name..."
                      helperText="Optional: Link this file to a taxpayer for better organization"
                    />
                  </Box>

                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                    <TextField
                      label="File Name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      error={!!formErrors.name}
                      helperText={formErrors.name || 'Enter the file name or identifier'}
                      required
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Folder color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                    
                    <TextField
                      label="File Number"
                      value={formData.file_number}
                      onChange={(e) => setFormData({ ...formData, file_number: e.target.value })}
                      error={!!formErrors.file_number}
                      helperText={formErrors.file_number || 'Unique file number'}
                      required
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Description color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>
                  
                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                    <FormControl fullWidth required error={!!formErrors.kent}>
                      <InputLabel>Kent (Storage Container)</InputLabel>
                      <Select
                        value={formData.kent}
                        onChange={(e) => setFormData({ ...formData, kent: e.target.value })}
                        label="Kent (Storage Container)"
                        startAdornment={
                          <InputAdornment position="start">
                            <Inventory color="action" />
                          </InputAdornment>
                        }
                      >
                        {kents.map((kent) => (
                          <MenuItem key={kent.id} value={kent.id}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Box
                                sx={{
                                  width: 16,
                                  height: 16,
                                  borderRadius: '50%',
                                  bgcolor: kent.color || '#2196f3',
                                  border: '1px solid #ccc',
                                }}
                              />
                              {kent.name}
                              {kent.location_path && (
                                <Typography variant="caption" color="text.secondary">
                                  ({kent.location_path})
                                </Typography>
                              )}
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                      {formErrors.kent && (
                        <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 2 }}>
                          {formErrors.kent}
                        </Typography>
                      )}
                    </FormControl>
                    
                    <FormControl fullWidth required error={!!formErrors.file_type}>
                      <InputLabel>File Type</InputLabel>
                      <Select
                        value={formData.file_type}
                        onChange={(e) => setFormData({ ...formData, file_type: e.target.value })}
                        label="File Type"
                        startAdornment={
                          <InputAdornment position="start">
                            <Business color="action" />
                          </InputAdornment>
                        }
                      >
                        {fileTypes.map((fileType) => (
                          <MenuItem key={fileType.id} value={fileType.id}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Box
                                sx={{
                                  width: 16,
                                  height: 16,
                                  borderRadius: '50%',
                                  bgcolor: fileType.color || '#2196f3',
                                  border: '1px solid #ccc',
                                }}
                              />
                              <Chip label={fileType.code} size="small" />
                              {fileType.name}
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                      {formErrors.file_type && (
                        <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 2 }}>
                          {formErrors.file_type}
                        </Typography>
                      )}
                    </FormControl>
                  </Box>

                  {/* Business Information */}
                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                    <TextField
                      label="Business Name"
                      value={formData.business_name}
                      onChange={(e) => setFormData({ ...formData, business_name: e.target.value })}
                      error={!!formErrors.business_name}
                      helperText={formErrors.business_name || 'Official business name'}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Business color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />

                    <TextField
                      label="TIN Number"
                      value={formData.tin_number}
                      onChange={(e) => setFormData({ ...formData, tin_number: e.target.value })}
                      error={!!formErrors.tin_number}
                      helperText={formErrors.tin_number || 'Tax Identification Number'}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Description color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>

                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                    <TextField
                      label="Owner Name"
                      value={formData.owner_name}
                      onChange={(e) => setFormData({ ...formData, owner_name: e.target.value })}
                      error={!!formErrors.owner_name}
                      helperText={formErrors.owner_name || 'Owner or contact person'}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Business color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />

                    <TextField
                      label="Contact Phone"
                      value={formData.contact_phone}
                      onChange={(e) => setFormData({ ...formData, contact_phone: e.target.value })}
                      error={!!formErrors.contact_phone}
                      helperText={formErrors.contact_phone || 'Contact phone number'}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Description color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>

                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                    <TextField
                      label="Contact Email"
                      type="email"
                      value={formData.contact_email}
                      onChange={(e) => setFormData({ ...formData, contact_email: e.target.value })}
                      error={!!formErrors.contact_email}
                      helperText={formErrors.contact_email || 'Contact email address'}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Description color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />

                    <TextField
                      label="Address"
                      value={formData.address}
                      onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                      error={!!formErrors.address}
                      helperText={formErrors.address || 'Business or property address'}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Description color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>

                  <TextField
                    label="Description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    error={!!formErrors.description}
                    helperText={formErrors.description || 'Optional description for this file'}
                    multiline
                    rows={3}
                    fullWidth
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Description color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />

                  <Box sx={{ display: 'flex', gap: 2, pt: 2 }}>
                    <Button
                      type="submit"
                      variant="contained"
                      disabled={submitting}
                      startIcon={submitting ? <CircularProgress size={20} /> : <Add />}
                      size="large"
                    >
                      {submitting ? 'Saving...' : editingFile ? 'Update File' : 'Create File'}
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={resetForm}
                      size="large"
                    >
                      Cancel
                    </Button>
                  </Box>
                </Stack>
              </form>
            </CardContent>
          </Card>
        </Fade>
      )}

      {/* Business Files Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
            Business Files List
          </Typography>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : files.length === 0 ? (
            <Alert severity="info">
              No business files found. Click "Add Business File" to create your first file.
            </Alert>
          ) : (
            <>
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Business File</TableCell>
                      <TableCell>Kent Location</TableCell>
                      <TableCell>File Type</TableCell>
                      <TableCell>Documents</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Created</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {files.map((file) => (
                      <TableRow key={file.id} hover>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <Avatar sx={{ bgcolor: 'info.main', width: 32, height: 32 }}>
                              <Folder fontSize="small" />
                            </Avatar>
                            <Box>
                              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                {file.name}
                              </Typography>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Chip label={file.file_number} size="small" color="primary" />
                                <Typography variant="caption" color="text.secondary">
                                  {file.description || 'No description'}
                                </Typography>
                              </Box>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box>
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {file.kent_location || `Kent ${file.kent}`}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {file.location_path || 'Location path not available'}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Box
                              sx={{
                                width: 12,
                                height: 12,
                                borderRadius: '50%',
                                bgcolor: file.file_type_color || '#2196f3',
                                border: '1px solid #ccc',
                              }}
                            />
                            <Chip
                              label={file.file_type_name || 'Unknown'}
                              size="small"
                              color="primary"
                              variant="outlined"
                            />
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {file.document_count || 0} documents
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={file.status || 'active'}
                            size="small"
                            color={file.status === 'active' ? 'success' : 'default'}
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {new Date(file.created_at).toLocaleDateString()}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                            <IconButton
                              size="small"
                              onClick={() => handleViewDetails(file)}
                              color="info"
                              title="View Details"
                            >
                              <Visibility fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleEdit(file)}
                              color="primary"
                              title="Edit"
                            >
                              <Edit fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleDelete(file)}
                              color="error"
                              title="Delete"
                            >
                              <Delete fontSize="small" />
                            </IconButton>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              <TablePagination
                component="div"
                count={totalCount}
                page={page}
                onPageChange={handleChangePage}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                rowsPerPageOptions={[5, 10, 25, 50]}
              />
            </>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        title="Delete Business File"
        itemName={fileToDelete?.name}
        itemType="Business File"
        message={`Are you sure you want to delete "${fileToDelete?.name}"? This will also delete all documents within this file. This action cannot be undone.`}
        confirmText="Delete File"
        severity="error"
        loading={deleting}
      />
    </Container>
  );
};

export default BusinessFilesPage;
