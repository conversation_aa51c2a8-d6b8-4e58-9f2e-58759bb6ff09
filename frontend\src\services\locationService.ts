import apiClient from './api';

export interface Building {
  id: number;
  organization: number;
  organization_name: string;
  name: string;
  code: string;
  description?: string;
  address?: string;
  barcode_image?: string;
  qr_code_image?: string;
  is_active: boolean;
  shelf_count: number;
  kent_count: number;
  created_at: string;
  updated_at: string;
}

export interface BuildingCreate {
  organization: number;
  name: string;
  code: string;
  description?: string;
  address?: string;
}

export interface BuildingUpdate extends Partial<BuildingCreate> {
  is_active?: boolean;
}

export interface Shelf {
  id: number;
  building: number;
  building_name: string;
  organization_name: string;
  name: string;
  code: string;
  full_code: string;
  description?: string;
  rows: number;
  columns: number;
  capacity?: number;
  barcode_image?: string;
  qr_code_image?: string;
  is_active: boolean;
  box_count: number;
  kent_count: number;
  utilization?: number;
  created_at: string;
  updated_at: string;
}

export interface ShelfCreate {
  building: number;
  name: string;
  code: string;
  description?: string;
  rows: number;
  columns: number;
}

export interface ShelfUpdate extends Partial<ShelfCreate> {
  is_active?: boolean;
}

export interface Box {
  id: number;
  shelf: number;
  shelf_name: string;
  building_name: string;
  organization_name: string;
  row: number;
  column: number;
  position_code: string;
  full_code: string;
  name?: string;
  description?: string;
  color?: string;
  material?: string;
  barcode_image?: string;
  qr_code_image?: string;
  is_active: boolean;
  kent_count: number;
  file_count: number;
  document_count: number;
  created_at: string;
  updated_at: string;
}

export interface BoxCreate {
  shelf: number;
  row: number;
  column: number;
  name?: string;
  description?: string;
  color?: string;
  material?: string;
}

export interface BoxUpdate extends Partial<BoxCreate> {
  is_active?: boolean;
}

export interface FileType {
  id: number;
  name: string;
  code: string;
  description?: string;
  color: string;
  icon: string;
  requires_business_name: boolean;
  requires_tin_number: boolean;
  requires_license_number: boolean;
  requires_owner_name: boolean;
  default_document_types?: string;
  default_document_types_list: string[];
  file_count: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface FileTypeCreate {
  name: string;
  code: string;
  description?: string;
  color?: string;
  icon?: string;
  requires_business_name?: boolean;
  requires_tin_number?: boolean;
  requires_license_number?: boolean;
  requires_owner_name?: boolean;
  default_document_types?: string;
}

export interface FileTypeUpdate extends Partial<FileTypeCreate> {
  is_active?: boolean;
}

export interface Kent {
  id: number;
  box: number;
  box_position: string;
  shelf_name: string;
  building_name: string;
  organization_name: string;
  name: string;
  code: string;
  full_code: string;
  location_path: string;
  description?: string;
  position_code: string;
  capacity?: number;
  color?: string;
  material?: string;
  barcode_image?: string;
  qr_code_image?: string;
  is_active: boolean;
  file_count: number;
  document_count: number;
  utilization?: number;
  is_full: boolean;
  created_at: string;
  updated_at: string;
}

export interface KentCreate {
  box: number;
  name: string;
  code: string;
  description?: string;
  capacity?: number;
  color?: string;
  material?: string;
}

export interface KentUpdate extends Partial<KentCreate> {
  is_active?: boolean;
}

export interface LocationHierarchy {
  id: number;
  name: string;
  code: string;
  description?: string;
  shelves: ShelfWithKents[];
}

export interface ShelfWithKents {
  id: number;
  name: string;
  code: string;
  full_code: string;
  description?: string;
  capacity?: number;
  kents: Kent[];
}

export interface LocationSummary {
  total_buildings: number;
  total_shelves: number;
  total_kents: number;
  total_capacity: number;
  used_capacity: number;
  utilization_percentage: number;
}

export interface LocationListResponse<T> {
  count: number;
  next?: string;
  previous?: string;
  results: T[];
}



class LocationService {
  private baseUrl = '/locations';

  // Building methods
  async getBuildings(params?: {
    page?: number;
    page_size?: number;
    search?: string;
    organization?: number;
    is_active?: boolean;
  }): Promise<LocationListResponse<Building>> {
    const response = await apiClient.get(`${this.baseUrl}/buildings/`, { params });
    return response.data;
  }

  async getBuilding(id: number): Promise<Building> {
    const response = await apiClient.get(`${this.baseUrl}/buildings/${id}/`);
    return response.data;
  }

  async createBuilding(data: BuildingCreate): Promise<Building> {
    const response = await apiClient.post(`${this.baseUrl}/buildings/`, data);
    return response.data;
  }

  async updateBuilding(id: number, data: BuildingUpdate): Promise<Building> {
    const response = await apiClient.patch(`${this.baseUrl}/buildings/${id}/`, data);
    return response.data;
  }

  async deleteBuilding(id: number): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/buildings/${id}/`);
  }

  // Shelf methods
  async getShelves(params?: {
    page?: number;
    page_size?: number;
    search?: string;
    building?: number;
    is_active?: boolean;
  }): Promise<LocationListResponse<Shelf>> {
    const response = await apiClient.get(`${this.baseUrl}/shelves/`, { params });
    return response.data;
  }

  async getBuildingShelves(buildingId: number): Promise<Shelf[]> {
    const response = await apiClient.get(`${this.baseUrl}/buildings/${buildingId}/shelves/`);
    return response.data.results || response.data;
  }

  async getShelf(id: number): Promise<Shelf> {
    const response = await apiClient.get(`${this.baseUrl}/shelves/${id}/`);
    return response.data;
  }

  async createShelf(data: ShelfCreate): Promise<Shelf> {
    const response = await apiClient.post(`${this.baseUrl}/shelves/`, data);
    return response.data;
  }

  async updateShelf(id: number, data: ShelfUpdate): Promise<Shelf> {
    const response = await apiClient.patch(`${this.baseUrl}/shelves/${id}/`, data);
    return response.data;
  }

  async deleteShelf(id: number): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/shelves/${id}/`);
  }

  // Box methods
  async getBoxes(params?: {
    page?: number;
    page_size?: number;
    search?: string;
    shelf?: number;
    is_active?: boolean;
  }): Promise<LocationListResponse<Box>> {
    const response = await apiClient.get(`${this.baseUrl}/boxes/`, { params });
    return response.data;
  }

  async getShelfBoxes(shelfId: number): Promise<Box[]> {
    const response = await apiClient.get(`${this.baseUrl}/shelves/${shelfId}/boxes/`);
    return response.data.results || response.data;
  }

  async getBox(id: number): Promise<Box> {
    const response = await apiClient.get(`${this.baseUrl}/boxes/${id}/`);
    return response.data;
  }

  async createBox(data: BoxCreate): Promise<Box> {
    const response = await apiClient.post(`${this.baseUrl}/boxes/`, data);
    return response.data;
  }

  async updateBox(id: number, data: BoxUpdate): Promise<Box> {
    const response = await apiClient.patch(`${this.baseUrl}/boxes/${id}/`, data);
    return response.data;
  }

  async deleteBox(id: number): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/boxes/${id}/`);
  }

  // Kent methods
  async getKents(params?: {
    page?: number;
    page_size?: number;
    search?: string;
    box?: number;
    shelf?: number;
    building?: number;
    is_active?: boolean;
    is_full?: boolean;
  }): Promise<LocationListResponse<Kent>> {
    const response = await apiClient.get(`${this.baseUrl}/kents/`, { params });
    return response.data;
  }

  async getBoxKents(boxId: number): Promise<Kent[]> {
    const response = await apiClient.get(`${this.baseUrl}/boxes/${boxId}/kents/`);
    return response.data.results || response.data;
  }

  async getShelfKents(shelfId: number): Promise<Kent[]> {
    const response = await apiClient.get(`${this.baseUrl}/shelves/${shelfId}/kents/`);
    return response.data.results || response.data;
  }

  async getKent(id: number): Promise<Kent> {
    const response = await apiClient.get(`${this.baseUrl}/kents/${id}/`);
    return response.data;
  }

  async createKent(data: KentCreate): Promise<Kent> {
    const response = await apiClient.post(`${this.baseUrl}/kents/`, data);
    return response.data;
  }

  async updateKent(id: number, data: KentUpdate): Promise<Kent> {
    const response = await apiClient.patch(`${this.baseUrl}/kents/${id}/`, data);
    return response.data;
  }

  async deleteKent(id: number): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/kents/${id}/`);
  }

  // Hierarchy and summary methods
  async getLocationHierarchy(organizationId?: number): Promise<LocationHierarchy[]> {
    const params = organizationId ? { organization: organizationId } : {};
    const response = await apiClient.get(`${this.baseUrl}/hierarchy/`, { params });
    return response.data.results || response.data;
  }

  async getLocationSummary(organizationId?: number): Promise<LocationSummary> {
    const params = organizationId ? { organization: organizationId } : {};
    const response = await apiClient.get(`${this.baseUrl}/summary/`, { params });
    return response.data;
  }

  async searchLocations(query: string, type?: 'building' | 'shelf' | 'kent'): Promise<any[]> {
    const params = { q: query, type };
    const response = await apiClient.get(`${this.baseUrl}/search/`, { params });
    return response.data.results || response.data;
  }

  // FileType methods
  async getFileTypes(params?: {
    page?: number;
    page_size?: number;
    search?: string;
    is_active?: boolean;
  }): Promise<LocationListResponse<FileType>> {
    const response = await apiClient.get(`${this.baseUrl}/file-types/`, { params });
    return response.data;
  }

  async getFileType(id: number): Promise<FileType> {
    const response = await apiClient.get(`${this.baseUrl}/file-types/${id}/`);
    return response.data;
  }

  async createFileType(data: FileTypeCreate): Promise<FileType> {
    const response = await apiClient.post(`${this.baseUrl}/file-types/`, data);
    return response.data;
  }

  async updateFileType(id: number, data: FileTypeUpdate): Promise<FileType> {
    const response = await apiClient.patch(`${this.baseUrl}/file-types/${id}/`, data);
    return response.data;
  }

  async deleteFileType(id: number): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/file-types/${id}/`);
  }



  // Utility methods
  getColorOptions() {
    return [
      { value: 'Red', label: 'Red', color: '#f44336' },
      { value: 'Blue', label: 'Blue', color: '#2196f3' },
      { value: 'Green', label: 'Green', color: '#4caf50' },
      { value: 'Yellow', label: 'Yellow', color: '#ffeb3b' },
      { value: 'Orange', label: 'Orange', color: '#ff9800' },
      { value: 'Purple', label: 'Purple', color: '#9c27b0' },
      { value: 'Brown', label: 'Brown', color: '#795548' },
      { value: 'Gray', label: 'Gray', color: '#9e9e9e' },
      { value: 'Black', label: 'Black', color: '#000000' },
      { value: 'White', label: 'White', color: '#ffffff' },
    ];
  }

  getMaterialOptions() {
    return [
      { value: 'Cardboard', label: 'Cardboard' },
      { value: 'Plastic', label: 'Plastic' },
      { value: 'Metal', label: 'Metal' },
      { value: 'Wood', label: 'Wood' },
      { value: 'Fabric', label: 'Fabric' },
    ];
  }

  validateCode(code: string): { isValid: boolean; error?: string } {
    if (!code) return { isValid: false, error: 'Code is required' };
    if (code.length < 1 || code.length > 10) {
      return { isValid: false, error: 'Code must be 1-10 characters' };
    }
    if (!/^[A-Z0-9]+$/i.test(code)) {
      return { isValid: false, error: 'Code can only contain letters and numbers' };
    }
    return { isValid: true };
  }

  formatUtilization(utilization?: number): string {
    if (utilization === undefined || utilization === null) return 'N/A';
    return `${utilization.toFixed(1)}%`;
  }

  getUtilizationColor(utilization?: number): 'success' | 'warning' | 'error' | 'default' {
    if (!utilization) return 'default';
    if (utilization < 70) return 'success';
    if (utilization < 90) return 'warning';
    return 'error';
  }

  getStatusColor(isActive: boolean): 'success' | 'default' {
    return isActive ? 'success' : 'default';
  }

  getStatusText(isActive: boolean): string {
    return isActive ? 'Active' : 'Inactive';
  }

  parseLocationPath(path: string): { building: string; shelf: string; kent: string } {
    const parts = path.split(' → ');
    return {
      building: parts[0] || '',
      shelf: parts[1] || '',
      kent: parts[2] || '',
    };
  }

  generateQRCodeData(item: Building | Shelf | Kent): string {
    if ('shelf' in item) {
      // Kent
      return JSON.stringify({
        type: 'kent',
        id: item.id,
        code: item.full_code,
        path: item.location_path,
      });
    } else if ('building' in item) {
      // Shelf
      return JSON.stringify({
        type: 'shelf',
        id: item.id,
        code: item.full_code,
        building: item.building_name,
      });
    } else {
      // Building
      return JSON.stringify({
        type: 'building',
        id: item.id,
        code: item.code,
        name: item.name,
      });
    }
  }
}

export default new LocationService();
