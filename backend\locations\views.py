from rest_framework import generics, permissions, status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.decorators import action
from django.db.models import Q
from .models import Building, Shelf, Box, Kent, FileType, File
from .serializers import (
    BuildingSerializer, ShelfSerializer, BoxSerializer, BoxCreateSerializer,
    KentSerializer, KentCreateSerializer, FileTypeSerializer, FileTypeCreateSerializer,
    FileSerializer, FileCreateSerializer, FileWithDocumentsSerializer,
    ShelfWithBoxesSerializer, BoxWithKentsSerializer
)


# Placeholder views - will be implemented in next phase
class BuildingListCreateView(generics.ListCreateAPIView):
    queryset = Building.objects.all()
    serializer_class = BuildingSerializer
    permission_classes = [permissions.IsAuthenticated]

class BuildingDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Building.objects.all()
    serializer_class = BuildingSerializer
    permission_classes = [permissions.IsAuthenticated]

class ShelfListCreateView(generics.ListCreateAPIView):
    serializer_class = ShelfSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = Shelf.objects.select_related(
            'building__organization'
        ).filter(is_active=True)

        # Filter by building if provided
        query_params = getattr(self.request, 'query_params', self.request.GET)
        building_id = query_params.get('building')
        if building_id:
            queryset = queryset.filter(building_id=building_id)

        return queryset.order_by('building', 'name')

class ShelfDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Shelf.objects.all()
    serializer_class = ShelfSerializer
    permission_classes = [permissions.IsAuthenticated]

class BoxListCreateView(generics.ListCreateAPIView):
    serializer_class = BoxSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = Box.objects.select_related(
            'shelf__building__organization'
        ).filter(is_active=True)

        # Filter by shelf if provided
        query_params = getattr(self.request, 'query_params', self.request.GET)
        shelf_id = query_params.get('shelf')
        if shelf_id:
            queryset = queryset.filter(shelf_id=shelf_id)

        # Filter by building if provided (through shelf)
        building_id = query_params.get('building')
        if building_id:
            queryset = queryset.filter(shelf__building_id=building_id)

        return queryset.order_by('shelf__building', 'shelf', 'row', 'column')

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return BoxCreateSerializer
        return BoxSerializer

class BoxDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Box.objects.all()
    serializer_class = BoxSerializer
    permission_classes = [permissions.IsAuthenticated]

class KentListCreateView(generics.ListCreateAPIView):
    serializer_class = KentSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = Kent.objects.select_related(
            'box__shelf__building__organization'
        ).filter(is_active=True)

        # Filter by box if provided
        query_params = getattr(self.request, 'query_params', self.request.GET)
        box_id = query_params.get('box')
        if box_id:
            queryset = queryset.filter(box_id=box_id)

        # Filter by shelf if provided (through box)
        shelf_id = query_params.get('shelf')
        if shelf_id:
            queryset = queryset.filter(box__shelf_id=shelf_id)

        # Filter by building if provided (through box->shelf)
        building_id = query_params.get('building')
        if building_id:
            queryset = queryset.filter(box__shelf__building_id=building_id)

        return queryset.order_by('box__shelf__building', 'box__shelf', 'box', 'code')

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return KentCreateSerializer
        return KentSerializer

class KentDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Kent.objects.all()
    serializer_class = KentSerializer
    permission_classes = [permissions.IsAuthenticated]

class BuildingShelfListView(generics.ListAPIView):
    serializer_class = ShelfSerializer
    permission_classes = [permissions.IsAuthenticated]
    def get_queryset(self):
        return Shelf.objects.filter(building=self.kwargs['building_pk'])

class ShelfBoxListView(generics.ListAPIView):
    serializer_class = BoxSerializer
    permission_classes = [permissions.IsAuthenticated]
    def get_queryset(self):
        return Box.objects.filter(shelf=self.kwargs['shelf_pk'])

class BoxKentListView(generics.ListAPIView):
    serializer_class = KentSerializer
    permission_classes = [permissions.IsAuthenticated]
    def get_queryset(self):
        return Kent.objects.filter(box=self.kwargs['box_pk'])

class ShelfKentListView(generics.ListAPIView):
    """Get all kents in a shelf (through boxes)"""
    serializer_class = KentSerializer
    permission_classes = [permissions.IsAuthenticated]
    def get_queryset(self):
        return Kent.objects.filter(box__shelf=self.kwargs['shelf_pk'])

class LocationHierarchyView(APIView):
    """Get complete location hierarchy with buildings, shelves, and kents"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        query_params = getattr(request, 'query_params', request.GET)
        organization_id = query_params.get('organization')

        # Filter buildings by organization if specified
        buildings_queryset = Building.objects.filter(is_active=True)
        if organization_id:
            buildings_queryset = buildings_queryset.filter(organization_id=organization_id)

        # Prefetch related data for efficiency
        buildings = buildings_queryset.prefetch_related(
            'shelves__kents'
        ).order_by('organization', 'code')

        # Build hierarchy data
        hierarchy_data = []
        for building in buildings:
            building_data = {
                'id': building.id,
                'name': building.name,
                'code': building.code,
                'description': building.description,
                'organization_name': building.organization.name,
                'shelves': []
            }

            for shelf in building.shelves.filter(is_active=True).order_by('code'):
                shelf_data = {
                    'id': shelf.id,
                    'name': shelf.name,
                    'code': shelf.code,
                    'full_code': shelf.full_code,
                    'description': shelf.description,
                    'capacity': shelf.capacity,
                    'rows': shelf.rows,
                    'columns': shelf.columns,
                    'kents': []
                }

                # Get kents through boxes
                shelf_kents = Kent.objects.filter(box__shelf=shelf, is_active=True).order_by('box__row', 'box__column', 'code')
                for kent in shelf_kents:
                    kent_data = {
                        'id': kent.id,
                        'name': kent.name,
                        'code': kent.code,
                        'full_code': kent.full_code,
                        'position_code': kent.position_code,
                        'row': kent.row,
                        'column': kent.column,
                        'capacity': kent.capacity,
                        'file_count': kent.get_file_count(),
                        'document_count': kent.get_document_count(),
                        'utilization': kent.utilization,
                        'is_full': kent.is_full
                    }
                    shelf_data['kents'].append(kent_data)

                building_data['shelves'].append(shelf_data)

            hierarchy_data.append(building_data)

        return Response({
            'results': hierarchy_data,
            'count': len(hierarchy_data)
        })

class LocationSearchView(APIView):
    """Search across all location types (buildings, shelves, kents)"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        query_params = getattr(request, 'query_params', request.GET)
        query = query_params.get('q', '').strip()
        location_type = query_params.get('type')  # 'building', 'shelf', 'kent', or None for all
        organization_id = query_params.get('organization')
        limit = int(query_params.get('limit', 50))

        if not query:
            return Response({
                'results': [],
                'count': 0,
                'message': 'Search query is required'
            })

        results = []

        # Search buildings
        if not location_type or location_type == 'building':
            buildings_queryset = Building.objects.filter(
                Q(name__icontains=query) | Q(code__icontains=query) | Q(description__icontains=query),
                is_active=True
            ).select_related('organization')

            if organization_id:
                buildings_queryset = buildings_queryset.filter(organization_id=organization_id)

            for building in buildings_queryset[:limit]:
                results.append({
                    'type': 'building',
                    'id': building.id,
                    'name': building.name,
                    'code': building.code,
                    'full_code': building.code,
                    'description': building.description,
                    'organization_name': building.organization.name,
                    'location_path': building.name,
                    'shelf_count': building.shelves.filter(is_active=True).count(),
                    'kent_count': Kent.objects.filter(
                        box__shelf__building=building,
                        is_active=True
                    ).count()
                })

        # Search shelves
        if not location_type or location_type == 'shelf':
            shelves_queryset = Shelf.objects.filter(
                Q(name__icontains=query) | Q(code__icontains=query) | Q(description__icontains=query),
                is_active=True
            ).select_related('building__organization')

            if organization_id:
                shelves_queryset = shelves_queryset.filter(building__organization_id=organization_id)

            for shelf in shelves_queryset[:limit]:
                results.append({
                    'type': 'shelf',
                    'id': shelf.id,
                    'name': shelf.name,
                    'code': shelf.code,
                    'full_code': shelf.full_code,
                    'description': shelf.description,
                    'building_name': shelf.building.name,
                    'organization_name': shelf.building.organization.name,
                    'location_path': f"{shelf.building.name} → {shelf.name}",
                    'kent_count': Kent.objects.filter(box__shelf=shelf, is_active=True).count(),
                    'capacity': shelf.capacity,
                    'utilization': shelf.utilization
                })

        # Search kents
        if not location_type or location_type == 'kent':
            kents_queryset = Kent.objects.filter(
                Q(name__icontains=query) | Q(code__icontains=query) | Q(description__icontains=query),
                is_active=True
            ).select_related('shelf__building__organization')

            if organization_id:
                kents_queryset = kents_queryset.filter(shelf__building__organization_id=organization_id)

            for kent in kents_queryset[:limit]:
                results.append({
                    'type': 'kent',
                    'id': kent.id,
                    'name': kent.name,
                    'code': kent.code,
                    'full_code': kent.full_code,
                    'description': kent.description,
                    'shelf_name': kent.shelf.name,
                    'building_name': kent.shelf.building.name,
                    'organization_name': kent.shelf.building.organization.name,
                    'location_path': kent.location_path,
                    'position_code': kent.position_code,
                    'file_count': kent.get_file_count(),
                    'document_count': kent.get_document_count(),
                    'capacity': kent.capacity,
                    'utilization': kent.utilization,
                    'is_full': kent.is_full
                })

        # Sort results by relevance (exact matches first, then partial matches)
        def sort_key(item):
            name_exact = item['name'].lower() == query.lower()
            code_exact = item['code'].lower() == query.lower()
            name_starts = item['name'].lower().startswith(query.lower())
            code_starts = item['code'].lower().startswith(query.lower())

            return (not name_exact, not code_exact, not name_starts, not code_starts, item['name'])

        results.sort(key=sort_key)

        return Response({
            'results': results[:limit],
            'count': len(results),
            'query': query,
            'type_filter': location_type
        })

class LocationSummaryView(APIView):
    """Get location summary statistics for dashboard"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            # Use request.GET for regular Django views, query_params for DRF views
            query_params = getattr(request, 'query_params', request.GET)
            organization_id = query_params.get('organization')

            # Base querysets
            buildings_queryset = Building.objects.filter(is_active=True)
            shelves_queryset = Shelf.objects.filter(is_active=True)
            kents_queryset = Kent.objects.filter(is_active=True)
            files_queryset = File.objects.filter(is_active=True)

            # Filter by organization if specified
            if organization_id:
                buildings_queryset = buildings_queryset.filter(organization_id=organization_id)
                shelves_queryset = shelves_queryset.filter(building__organization_id=organization_id)
                kents_queryset = kents_queryset.filter(box__shelf__building__organization_id=organization_id)
                files_queryset = files_queryset.filter(kent__box__shelf__building__organization_id=organization_id)

            # Basic counts
            total_buildings = buildings_queryset.count()
            total_shelves = shelves_queryset.count()
            total_kents = kents_queryset.count()
            total_files = files_queryset.count()

            # Capacity calculations
            total_kent_capacity = sum(kent.capacity or 0 for kent in kents_queryset)

            # Document count with error handling
            total_documents = 0
            try:
                # Try to get document count, but fallback to file count if documents app not available
                from django.apps import apps
                if apps.is_installed('documents'):
                    total_documents = sum(kent.get_document_count() for kent in kents_queryset)
                else:
                    total_documents = total_files
            except Exception as e:
                print(f"Error calculating document count: {e}")
                # Fallback: count files instead of documents
                total_documents = total_files

            # Utilization calculations
            utilization_percentage = 0
            if total_kent_capacity > 0:
                utilization_percentage = (total_documents / total_kent_capacity) * 100

            # Building-wise breakdown
            building_stats = []
            for building in buildings_queryset.prefetch_related('shelves__boxes__kents'):
                building_shelves = building.shelves.filter(is_active=True)
                building_kents = Kent.objects.filter(box__shelf__in=building_shelves, is_active=True)
                building_files = File.objects.filter(kent__in=building_kents, is_active=True)

                building_capacity = sum(kent.capacity or 0 for kent in building_kents)

                # Document count with error handling
                building_documents = 0
                try:
                    # Try to get document count, but fallback to file count if documents app not available
                    from django.apps import apps
                    if apps.is_installed('documents'):
                        building_documents = sum(kent.get_document_count() for kent in building_kents)
                    else:
                        building_documents = building_files.count()
                except Exception as e:
                    print(f"Error calculating building document count: {e}")
                    # Fallback: count files instead
                    building_documents = building_files.count()

                building_utilization = 0
                if building_capacity > 0:
                    building_utilization = (building_documents / building_capacity) * 100

                building_stats.append({
                    'id': building.id,
                    'name': building.name,
                    'code': building.code,
                    'shelves_count': building_shelves.count(),
                    'kents_count': building_kents.count(),
                    'files_count': building_files.count(),
                    'capacity': building_capacity,
                    'documents_count': building_documents,
                    'utilization_percentage': round(building_utilization, 2)
                })

            # Shelf utilization distribution
            shelf_utilization_ranges = {
                'empty': 0,      # 0%
                'low': 0,        # 1-30%
                'medium': 0,     # 31-70%
                'high': 0,       # 71-90%
                'full': 0        # 91-100%
            }

            for shelf in shelves_queryset:
                utilization = shelf.utilization or 0
                if utilization == 0:
                    shelf_utilization_ranges['empty'] += 1
                elif utilization <= 30:
                    shelf_utilization_ranges['low'] += 1
                elif utilization <= 70:
                    shelf_utilization_ranges['medium'] += 1
                elif utilization <= 90:
                    shelf_utilization_ranges['high'] += 1
                else:
                    shelf_utilization_ranges['full'] += 1

            # Recent activity (files created in last 30 days)
            from datetime import datetime, timedelta
            thirty_days_ago = datetime.now().date() - timedelta(days=30)
            recent_files = files_queryset.filter(created_at__date__gte=thirty_days_ago).count()

            summary_data = {
                'total_buildings': total_buildings,
                'total_shelves': total_shelves,
                'total_kents': total_kents,
                'total_files': total_files,
                'total_capacity': total_kent_capacity,
                'used_capacity': total_documents,
                'utilization_percentage': round(utilization_percentage, 2),
                'recent_files_count': recent_files,
                'building_stats': building_stats,
                'shelf_utilization_distribution': shelf_utilization_ranges,
                'organization_id': organization_id
            }

            return Response(summary_data)
        except Exception as e:
            return Response({
                'error': f'An error occurred while fetching location summary: {str(e)}'
            }, status=500)


# FileType Views
class FileTypeListCreateView(generics.ListCreateAPIView):
    """List and create file types"""
    queryset = FileType.objects.all()
    serializer_class = FileTypeSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return FileTypeCreateSerializer
        return FileTypeSerializer

class FileTypeDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, and delete file types"""
    queryset = FileType.objects.all()
    serializer_class = FileTypeSerializer
    permission_classes = [permissions.IsAuthenticated]


# File Views
class FileListCreateView(generics.ListCreateAPIView):
    """List and create files"""
    queryset = File.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return FileCreateSerializer
        return FileSerializer

    def get_queryset(self):
        try:
            queryset = File.objects.select_related(
                'kent__box__shelf__building', 'file_type', 'created_by'
            ).filter(is_active=True)

            # Get query parameters (works for both DRF and regular Django requests)
            query_params = getattr(self.request, 'query_params', self.request.GET)

            # Filter by kent
            kent_id = query_params.get('kent')
            if kent_id:
                queryset = queryset.filter(kent_id=kent_id)

            # Filter by file type
            file_type = query_params.get('file_type')
            if file_type:
                queryset = queryset.filter(file_type=file_type)

            # Filter by status
            status = query_params.get('status')
            if status:
                queryset = queryset.filter(status=status)

            # Search
            search = query_params.get('search')
            if search:
                queryset = queryset.filter(
                    Q(name__icontains=search) |
                    Q(file_number__icontains=search) |
                    Q(business_name__icontains=search) |
                    Q(tin_number__icontains=search) |
                    Q(vat_number__icontains=search) |
                    Q(owner_name__icontains=search)
                )

            return queryset.order_by('kent__box__shelf__building', 'kent__box__shelf', 'kent__box', 'kent', 'file_number')
        except Exception as e:
            # Return empty queryset on error to prevent 500
            return File.objects.none()

    def perform_create(self, serializer):
        # Handle taxpayer linking
        taxpayer_id = self.request.data.get('taxpayer_id')
        taxpayer_type = self.request.data.get('taxpayer_type')

        file_instance = serializer.save(created_by=self.request.user)

        # Link taxpayer if provided
        if taxpayer_id and taxpayer_type:
            self._link_taxpayer_to_file(file_instance, taxpayer_id, taxpayer_type)

    def _link_taxpayer_to_file(self, file_instance, taxpayer_id, taxpayer_type):
        """Link a taxpayer to a file and create bidirectional relationship"""
        try:
            if taxpayer_type == 'individual':
                from taxpayers.models import IndividualTaxPayer
                taxpayer = IndividualTaxPayer.objects.get(id=taxpayer_id)
                file_instance.linked_individual_taxpayer = taxpayer
                taxpayer.tax_file = file_instance
                taxpayer.save()
            elif taxpayer_type == 'organization':
                from taxpayers.models import OrganizationTaxPayer
                taxpayer = OrganizationTaxPayer.objects.get(id=taxpayer_id)
                file_instance.linked_organization_taxpayer = taxpayer
                taxpayer.tax_file = file_instance
                taxpayer.save()

            file_instance.save()
        except Exception as e:
            # Log error but don't fail file creation
            print(f"Error linking taxpayer to file: {e}")


class FileDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, or delete a file"""
    queryset = File.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return FileCreateSerializer
        return FileSerializer

    def get_queryset(self):
        return File.objects.select_related(
            'kent__box__shelf__building', 'file_type', 'created_by'
        ).filter(is_active=True)


class FileWithDocumentsView(generics.RetrieveAPIView):
    """Get file with all its documents"""
    queryset = File.objects.all()
    serializer_class = FileWithDocumentsSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return File.objects.select_related(
            'kent__box__shelf__building', 'file_type'
        ).prefetch_related('documents').filter(is_active=True)


class KentFileListView(generics.ListAPIView):
    """List files in a specific kent"""
    serializer_class = FileSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return File.objects.filter(
            kent=self.kwargs['kent_pk'],
            is_active=True
        ).select_related('kent__box__shelf__building', 'file_type', 'created_by')


class ShelfGridView(APIView):
    """Get shelf grid layout with kent positions"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, shelf_pk):
        try:
            shelf = Shelf.objects.get(pk=shelf_pk)

            # Create grid matrix
            grid = []
            for row in range(1, shelf.rows + 1):
                grid_row = []
                for col in range(1, shelf.columns + 1):
                    # Find or create box at this position
                    box, created = Box.objects.get_or_create(
                        shelf=shelf,
                        row=row,
                        column=col,
                        defaults={
                            'name': f"Box R{row:02d}C{col:02d}",
                            'is_active': True
                        }
                    )

                    # Get kents in this box
                    kents = Kent.objects.filter(
                        box=box, is_active=True
                    ).select_related('box')

                    position_data = {
                        'row': row,
                        'column': col,
                        'position_code': f"R{row:02d}C{col:02d}",
                        'box': {
                            'id': box.id,
                            'name': box.name,
                            'position_code': box.position_code
                        },
                        'kents': [KentSerializer(kent).data for kent in kents],
                        'kent_count': kents.count(),
                        'is_occupied': kents.exists()
                    }
                    grid_row.append(position_data)
                grid.append(grid_row)

            return Response({
                'shelf': ShelfSerializer(shelf).data,
                'grid': grid,
                'available_positions': shelf.get_available_positions()
            })

        except Shelf.DoesNotExist:
            return Response(
                {'error': 'Shelf not found'},
                status=status.HTTP_404_NOT_FOUND
            )
