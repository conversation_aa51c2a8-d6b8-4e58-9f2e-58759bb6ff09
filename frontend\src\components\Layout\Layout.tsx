import React, { useState } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  Divider,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Menu,
  MenuItem,
  Badge,
  Tooltip,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard,
  Description,
  Assignment,
  LocationOn,
  People,
  Business,
  AccountCircle,
  Logout,
  Notifications,
  Settings,
  Category,
  Folder,
  Receipt,
  AccountBalance,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useBranding, useBrandingInfo, useThemeColors } from '../../contexts/BrandingContext';
import { useNotification } from '../../contexts/NotificationContext';
import Footer from './Footer';
import LanguageSwitcher from '../LanguageSwitcher';

const drawerWidth = 240;

interface NavigationItem {
  text: string;
  icon: React.ReactElement;
  path: string;
  roles?: string[];
}

const navigationItems: NavigationItem[] = [
  {
    text: 'Dashboard',
    icon: <Dashboard />,
    path: '/dashboard',
  },
  {
    text: 'Requests',
    icon: <Assignment />,
    path: '/requests',
  },
  {
    text: 'Physical Locations',
    icon: <LocationOn />,
    path: '/locations',
  },
  {
    text: 'Location Hierarchy',
    icon: <LocationOn />,
    path: '/locations/hierarchy',
    roles: ['admin', 'manager'],
  },

  {
    text: 'Document Center',
    icon: <Description />,
    path: '/document-center',
  },
  {
    text: 'Tax Payers',
    icon: <Receipt />,
    path: '/taxpayers',
    roles: ['admin', 'manager'],
  },
  {
    text: 'Revenue Collection',
    icon: <AccountBalance />,
    path: '/revenue-collection',
    roles: ['admin', 'manager'],
  },
  {
    text: 'Organizations',
    icon: <Business />,
    path: '/organizations',
    roles: ['admin'],
  },
  {
    text: 'User Management',
    icon: <People />,
    path: '/users',
    roles: ['admin', 'manager'],
  },
];

const Layout: React.FC = () => {
  const [mobileOpen, setMobileOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const { user, logout } = useAuth();
  const { systemLogo, systemTitle } = useBranding();
  const brandingInfo = useBrandingInfo();
  const themeColors = useThemeColors();
  const { showSuccess } = useNotification();
  const navigate = useNavigate();
  const location = useLocation();

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = async () => {
    try {
      await logout();
      showSuccess('Logged out successfully');
      navigate('/login');
    } catch (error) {
      console.error('Logout error:', error);
    }
    handleProfileMenuClose();
  };

  const handleProfileClick = () => {
    navigate('/profile');
    handleProfileMenuClose();
  };

  const isMenuOpen = Boolean(anchorEl);

  // Filter navigation items based on user role
  const filteredNavigationItems = navigationItems.filter(item => {
    if (!item.roles) return true;
    return user && item.roles.includes(user.role);
  });

  const drawer = (
    <div>
      <Toolbar>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {brandingInfo.logo ? (
            <Avatar
              src={brandingInfo.logo}
              sx={{ width: 40, height: 40 }}
              alt={brandingInfo.name}
            />
          ) : (
            <Avatar sx={{ bgcolor: 'primary.main' }}>
              {brandingInfo.shortName?.charAt(0) || 'A'}
            </Avatar>
          )}
          <Box>
            <Typography variant="h6" noWrap component="div" sx={{ fontSize: '0.9rem' }}>
              {brandingInfo.shortName}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {brandingInfo.tagline || brandingInfo.motto || 'Document Management System'}
            </Typography>
          </Box>
        </Box>
      </Toolbar>
      <Divider />
      <List>
        {filteredNavigationItems.map((item) => (
          <ListItem key={item.text} disablePadding>
            <ListItemButton
              selected={location.pathname === item.path}
              onClick={() => navigate(item.path)}
              sx={{
                '&.Mui-selected': {
                  backgroundColor: 'primary.main',
                  color: 'white',
                  '&:hover': {
                    backgroundColor: 'primary.dark',
                  },
                  '& .MuiListItemIcon-root': {
                    color: 'white',
                  },
                },
              }}
            >
              <ListItemIcon>{item.icon}</ListItemIcon>
              <ListItemText primary={item.text} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </div>
  );

  const profileMenu = (
    <Menu
      anchorEl={anchorEl}
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'right',
      }}
      keepMounted
      transformOrigin={{
        vertical: 'top',
        horizontal: 'right',
      }}
      open={isMenuOpen}
      onClose={handleProfileMenuClose}
    >
      <MenuItem onClick={handleProfileClick}>
        <ListItemIcon>
          <AccountCircle fontSize="small" />
        </ListItemIcon>
        Profile
      </MenuItem>
      <MenuItem onClick={handleProfileMenuClose}>
        <ListItemIcon>
          <Settings fontSize="small" />
        </ListItemIcon>
        Settings
      </MenuItem>
      <Divider />
      <MenuItem onClick={handleLogout}>
        <ListItemIcon>
          <Logout fontSize="small" />
        </ListItemIcon>
        Logout
      </MenuItem>
    </Menu>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      <AppBar
        position="fixed"
        sx={{
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          ml: { sm: `${drawerWidth}px` },
          bgcolor: themeColors.primary,
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { sm: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexGrow: 1 }}>
            {brandingInfo.logo && (
              <Avatar
                src={brandingInfo.logo}
                sx={{ width: 40, height: 40, border: '2px solid rgba(255,255,255,0.2)' }}
                alt={brandingInfo.name}
              />
            )}
            <Box>
              <Typography variant="h6" noWrap component="div" fontWeight="bold">
                {brandingInfo.name}
              </Typography>
              {brandingInfo.tagline && (
                <Typography variant="caption" sx={{ opacity: 0.8, display: 'block', lineHeight: 1 }}>
                  {brandingInfo.tagline}
                </Typography>
              )}
            </Box>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <LanguageSwitcher variant="header" size="small" />

            <Tooltip title="Notifications">
              <IconButton color="inherit">
                <Badge badgeContent={0} color="error">
                  <Notifications />
                </Badge>
              </IconButton>
            </Tooltip>

            <Tooltip title="Account">
              <IconButton
                onClick={handleProfileMenuOpen}
                color="inherit"
                sx={{ ml: 1 }}
              >
                <Avatar sx={{ width: 32, height: 32 }}>
                  {user?.first_name?.charAt(0) || user?.username?.charAt(0) || 'U'}
                </Avatar>
              </IconButton>
            </Tooltip>
          </Box>
        </Toolbar>
      </AppBar>

      <Box
        component="nav"
        sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true,
          }}
          sx={{
            display: { xs: 'block', sm: 'none' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', sm: 'block' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>

      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          minHeight: '100vh',
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          mt: 8,
        }}
      >
        <Box
          component="main"
          sx={{
            flexGrow: 1,
            p: 3,
          }}
        >
          <Outlet />
        </Box>

        <Footer />
      </Box>

      {profileMenu}
    </Box>
  );
};

export default Layout;
