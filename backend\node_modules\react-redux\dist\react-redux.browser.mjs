import*as p from"react";var Re=p.version.startsWith("19"),Ee=Symbol.for(Re?"react.transitional.element":"react.element"),ke=Symbol.for("react.portal"),Fe=Symbol.for("react.fragment"),Ae=Symbol.for("react.strict_mode"),ve=Symbol.for("react.profiler"),Ne=Symbol.for("react.consumer"),_e=Symbol.for("react.context"),ue=Symbol.for("react.forward_ref"),Ie=Symbol.for("react.suspense"),Ve=Symbol.for("react.suspense_list"),te=Symbol.for("react.memo"),We=Symbol.for("react.lazy");var Pe=ue,de=te;function Ue(e){if(typeof e=="object"&&e!==null){let{$$typeof:t}=e;switch(t){case Ee:switch(e=e.type,e){case Fe:case ve:case Ae:case Ie:case Ve:return e;default:switch(e=e&&e.$$typeof,e){case _e:case ue:case We:case te:return e;case Ne:return e;default:return t}}case ke:return t}}}function le(e){return Ue(e)===te}function qe(e,t,o,n,{areStatesEqual:r,areOwnPropsEqual:s,areStatePropsEqual:i}){let u=!1,a,c,d,P,l;function x(f,m){return a=f,c=m,d=e(a,c),P=t(n,c),l=o(d,P,c),u=!0,l}function y(){return d=e(a,c),t.dependsOnOwnProps&&(P=t(n,c)),l=o(d,P,c),l}function M(){return e.dependsOnOwnProps&&(d=e(a,c)),t.dependsOnOwnProps&&(P=t(n,c)),l=o(d,P,c),l}function T(){let f=e(a,c),m=!i(f,d);return d=f,m&&(l=o(d,P,c)),l}function w(f,m){let C=!s(m,c),v=!r(f,a,m,c);return a=f,c=m,C&&v?y():C?M():v?T():l}return function(m,C){return u?w(m,C):x(m,C)}}function oe(e,{initMapStateToProps:t,initMapDispatchToProps:o,initMergeProps:n,...r}){let s=t(e,r),i=o(e,r),u=n(e,r);return qe(s,i,u,e,r)}function re(e,t){let o={};for(let n in e){let r=e[n];typeof r=="function"&&(o[n]=(...s)=>t(r(...s)))}return o}function U(e){return function(o){let n=e(o);function r(){return n}return r.dependsOnOwnProps=!1,r}}function Te(e){return e.dependsOnOwnProps?!!e.dependsOnOwnProps:e.length!==1}function $(e,t){return function(n,{displayName:r}){let s=function(u,a){return s.dependsOnOwnProps?s.mapToProps(u,a):s.mapToProps(u,void 0)};return s.dependsOnOwnProps=!0,s.mapToProps=function(u,a){s.mapToProps=e,s.dependsOnOwnProps=Te(e);let c=s(u,a);return typeof c=="function"&&(s.mapToProps=c,s.dependsOnOwnProps=Te(c),c=s(u,a)),c},s}}function k(e,t){return(o,n)=>{throw new Error(`Invalid value of type ${typeof e} for ${t} argument when connecting component ${n.wrappedComponentName}.`)}}function fe(e){return e&&typeof e=="object"?U(t=>re(e,t)):e?typeof e=="function"?$(e,"mapDispatchToProps"):k(e,"mapDispatchToProps"):U(t=>({dispatch:t}))}function Se(e){return e?typeof e=="function"?$(e,"mapStateToProps"):k(e,"mapStateToProps"):U(()=>({}))}function Le(e,t,o){return{...o,...e,...t}}function je(e){return function(o,{displayName:n,areMergedPropsEqual:r}){let s=!1,i;return function(a,c,d){let P=e(a,c,d);return s?r(P,i)||(i=P):(s=!0,i=P),i}}}function ye(e){return e?typeof e=="function"?je(e):k(e,"mergeProps"):()=>Le}function Y(e){e()}function $e(){let e=null,t=null;return{clear(){e=null,t=null},notify(){Y(()=>{let o=e;for(;o;)o.callback(),o=o.next})},get(){let o=[],n=e;for(;n;)o.push(n),n=n.next;return o},subscribe(o){let n=!0,r=t={callback:o,next:null,prev:t};return r.prev?r.prev.next=r:e=r,function(){!n||e===null||(n=!1,r.next?r.next.prev=r.prev:t=r.prev,r.prev?r.prev.next=r.next:e=r.next)}}}}var me={notify(){},get:()=>[]};function H(e,t){let o,n=me,r=0,s=!1;function i(M){d();let T=n.subscribe(M),w=!1;return()=>{w||(w=!0,T(),P())}}function u(){n.notify()}function a(){y.onStateChange&&y.onStateChange()}function c(){return s}function d(){r++,o||(o=t?t.addNestedSub(a):e.subscribe(a),n=$e())}function P(){r--,o&&r===0&&(o(),o=void 0,n.clear(),n=me)}function l(){s||(s=!0,d())}function x(){s&&(s=!1,P())}let y={addNestedSub:i,notifyNestedSubs:u,handleChangeWrapper:a,isSubscribed:c,trySubscribe:l,tryUnsubscribe:x,getListeners:()=>n};return y}var Ye=()=>typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",He=Ye(),Be=()=>typeof navigator<"u"&&navigator.product==="ReactNative",ze=Be(),Ke=()=>He||ze?p.useLayoutEffect:p.useEffect,F=Ke();function he(e,t){return e===t?e!==0||t!==0||1/e===1/t:e!==e&&t!==t}function A(e,t){if(he(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;let o=Object.keys(e),n=Object.keys(t);if(o.length!==n.length)return!1;for(let r=0;r<o.length;r++)if(!Object.prototype.hasOwnProperty.call(t,o[r])||!he(e[o[r]],t[o[r]]))return!1;return!0}var Ge={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},Je={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},Xe={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},Oe={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},Ze={[Pe]:Xe,[de]:Oe};function we(e){return le(e)?Oe:Ze[e.$$typeof]||Ge}var Qe=Object.defineProperty,et=Object.getOwnPropertyNames,xe=Object.getOwnPropertySymbols,tt=Object.getOwnPropertyDescriptor,ot=Object.getPrototypeOf,Ce=Object.prototype;function q(e,t){if(typeof t!="string"){if(Ce){let s=ot(t);s&&s!==Ce&&q(e,s)}let o=et(t);xe&&(o=o.concat(xe(t)));let n=we(e),r=we(t);for(let s=0;s<o.length;++s){let i=o[s];if(!Je[i]&&!(r&&r[i])&&!(n&&n[i])){let u=tt(t,i);try{Qe(e,i,u)}catch{}}}}return e}var rt=Symbol.for("react-redux-context"),nt=typeof globalThis<"u"?globalThis:{};function st(){if(!p.createContext)return{};let e=nt[rt]??=new Map,t=e.get(p.createContext);return t||(t=p.createContext(null),e.set(p.createContext,t)),t}var S=st();var pt=[null,null];function at(e,t,o){F(()=>e(...t),o)}function ct(e,t,o,n,r,s){e.current=n,o.current=!1,r.current&&(r.current=null,s())}function it(e,t,o,n,r,s,i,u,a,c,d){if(!e)return()=>{};let P=!1,l=null,x=()=>{if(P||!u.current)return;let M=t.getState(),T,w;try{T=n(M,r.current)}catch(f){w=f,l=f}w||(l=null),T===s.current?i.current||c():(s.current=T,a.current=T,i.current=!0,d())};return o.onStateChange=x,o.trySubscribe(),x(),()=>{if(P=!0,o.tryUnsubscribe(),o.onStateChange=null,l)throw l}}function ut(e,t){return e===t}function Pt(e,t,o,{pure:n,areStatesEqual:r=ut,areOwnPropsEqual:s=A,areStatePropsEqual:i=A,areMergedPropsEqual:u=A,forwardRef:a=!1,context:c=S}={}){let d=c,P=Se(e),l=fe(t),x=ye(o),y=!!e;return T=>{let w=T.displayName||T.name||"Component",f=`Connect(${w})`,m={shouldHandleStateChanges:y,displayName:f,wrappedComponentName:w,WrappedComponent:T,initMapStateToProps:P,initMapDispatchToProps:l,initMergeProps:x,areStatesEqual:r,areStatePropsEqual:i,areOwnPropsEqual:s,areMergedPropsEqual:u};function C(O){let[R,K,b]=p.useMemo(()=>{let{reactReduxForwardedRef:h,...E}=O;return[O.context,h,E]},[O]),_=p.useMemo(()=>{let h=d;return R?.Consumer,h},[R,d]),D=p.useContext(_),I=!!O.store&&!!O.store.getState&&!!O.store.dispatch,ge=!!D&&!!D.store,g=I?O.store:D.store,se=ge?D.getServerState:g.getState,G=p.useMemo(()=>oe(g.dispatch,m),[g]),[V,pe]=p.useMemo(()=>{if(!y)return pt;let h=H(g,I?void 0:D.subscription),E=h.notifyNestedSubs.bind(h);return[h,E]},[g,I,D]),ae=p.useMemo(()=>I?D:{...D,subscription:V},[I,D,V]),J=p.useRef(void 0),X=p.useRef(b),W=p.useRef(void 0),ce=p.useRef(!1),Z=p.useRef(!1),Q=p.useRef(void 0);F(()=>(Z.current=!0,()=>{Z.current=!1}),[]);let ie=p.useMemo(()=>()=>W.current&&b===X.current?W.current:G(g.getState(),b),[g,b]),Me=p.useMemo(()=>E=>V?it(y,g,V,G,X,J,ce,Z,W,pe,E):()=>{},[V]);at(ct,[X,J,ce,b,W,pe]);let j;try{j=p.useSyncExternalStore(Me,ie,se?()=>G(se(),b):ie)}catch(h){throw Q.current&&(h.message+=`
The error may be correlated with this previous error:
${Q.current.stack}

`),h}F(()=>{Q.current=void 0,W.current=void 0,J.current=j});let ee=p.useMemo(()=>p.createElement(T,{...j,ref:K}),[K,T,j]);return p.useMemo(()=>y?p.createElement(_.Provider,{value:ae},ee):ee,[_,ee,ae])}let N=p.memo(C);if(N.WrappedComponent=T,N.displayName=C.displayName=f,a){let R=p.forwardRef(function(b,_){return p.createElement(N,{...b,reactReduxForwardedRef:_})});return R.displayName=f,R.WrappedComponent=T,q(R,T)}return q(N,T)}}var dt=Pt;function lt(e){let{children:t,context:o,serverState:n,store:r}=e,s=p.useMemo(()=>{let a=H(r);return{store:r,subscription:a,getServerState:n?()=>n:void 0}},[r,n]),i=p.useMemo(()=>r.getState(),[r]);return F(()=>{let{subscription:a}=s;return a.onStateChange=a.notifyNestedSubs,a.trySubscribe(),i!==r.getState()&&a.notifyNestedSubs(),()=>{a.tryUnsubscribe(),a.onStateChange=void 0}},[s,i]),p.createElement((o||S).Provider,{value:s},t)}var Tt=lt;function L(e=S){return function(){return p.useContext(e)}}var B=L();function z(e=S){let t=e===S?B:L(e),o=()=>{let{store:n}=t();return n};return Object.assign(o,{withTypes:()=>o}),o}var ne=z();function De(e=S){let t=e===S?ne:z(e),o=()=>t().dispatch;return Object.assign(o,{withTypes:()=>o}),o}var ft=De();import{useSyncExternalStoreWithSelector as St}from"use-sync-external-store/with-selector.js";var yt=(e,t)=>e===t;function be(e=S){let t=e===S?B:L(e),o=(n,r={})=>{let{equalityFn:s=yt}=typeof r=="function"?{equalityFn:r}:r,i=t(),{store:u,subscription:a,getServerState:c}=i,d=p.useRef(!0),P=p.useCallback({[n.name](x){let y=n(x);if(0){if((m==="always"||m==="once"&&d.current)&&!s(y,C))try{}catch(N){}if((f==="always"||f==="once"&&d.current)&&y===x)try{}catch(v){}}return y}}[n.name],[n]),l=St(a.addNestedSub,u.getState,c||u.getState,P,s);return p.useDebugValue(l),l};return Object.assign(o,{withTypes:()=>o}),o}var mt=be();var Mo=Y;export{Tt as Provider,S as ReactReduxContext,Mo as batch,dt as connect,De as createDispatchHook,be as createSelectorHook,z as createStoreHook,A as shallowEqual,ft as useDispatch,mt as useSelector,ne as useStore};
//# sourceMappingURL=react-redux.browser.mjs.map