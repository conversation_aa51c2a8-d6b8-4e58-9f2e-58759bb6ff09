/**
 * Revenue Collection Dashboard
 * 
 * Main dashboard for revenue collection system with overview cards,
 * recent collections, and navigation to different modules.
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  AccountBalance,
  TrendingUp,
  Receipt,
  Category,
  Add,
  Visibility,
  Edit,
  Analytics,
  DateRange,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useNotification } from '../../contexts/NotificationContext';
import revenueCollectionService from '../../services/revenueCollectionService';
import StatisticsCard from '../../components/revenue-collection/StatisticsCard';
import ErrorBoundary from '../../components/revenue-collection/ErrorBoundary';
import type {
  RegionalRevenueCollection,
  CityServiceRevenueCollection,
  RevenuePeriod,
} from '../../services/revenueCollectionService';

interface DashboardStats {
  totalRegionalRevenue: number;
  totalCityServiceRevenue: number;
  totalCollections: number;
  currentPeriod: RevenuePeriod | null;
}

const RevenueCollectionDashboard: React.FC = () => {
  const navigate = useNavigate();
  const { showError } = useNotification();
  
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<DashboardStats>({
    totalRegionalRevenue: 0,
    totalCityServiceRevenue: 0,
    totalCollections: 0,
    currentPeriod: null,
  });
  const [recentCollections, setRecentCollections] = useState<(RegionalRevenueCollection | CityServiceRevenueCollection)[]>([]);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // Load current period
      let currentPeriod: RevenuePeriod | null = null;
      try {
        currentPeriod = await revenueCollectionService.getCurrentPeriod();
      } catch (error) {
        console.warn('No current period found');
      }

      // Load ALL regional collections for totals calculation
      const regionalTotalsResponse = await revenueCollectionService.getRegionalRevenueCollections({
        page_size: 1000, // Get all collections for totals
      });

      // Load ALL city service collections for totals calculation
      const cityServiceTotalsResponse = await revenueCollectionService.getCityServiceRevenueCollections({
        page_size: 1000, // Get all collections for totals
      });

      // Load recent collections for display (limited)
      const regionalRecentResponse = await revenueCollectionService.getRegionalRevenueCollections({
        ordering: '-collection_date',
        page_size: 5,
      });

      const cityServiceRecentResponse = await revenueCollectionService.getCityServiceRevenueCollections({
        ordering: '-collection_date',
        page_size: 5,
      });

      // Combine and sort recent collections for display
      const allCollections = [
        ...regionalRecentResponse.results.map(c => ({ ...c, type: 'regional' as const })),
        ...cityServiceRecentResponse.results.map(c => ({ ...c, type: 'city_service' as const })),
      ].sort((a, b) => new Date(b.collection_date).getTime() - new Date(a.collection_date).getTime())
        .slice(0, 10);

      // Calculate totals from ALL collections
      const totalRegionalRevenue = regionalTotalsResponse.results.reduce((sum, c) => sum + (Number(c.amount) || 0), 0);
      const totalCityServiceRevenue = cityServiceTotalsResponse.results.reduce((sum, c) => sum + (Number(c.amount) || 0), 0);
      const totalCollections = regionalTotalsResponse.count + cityServiceTotalsResponse.count;

      setStats({
        totalRegionalRevenue,
        totalCityServiceRevenue,
        totalCollections,
        currentPeriod,
      });
      setRecentCollections(allCollections);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      showError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ET', {
      style: 'currency',
      currency: 'ETB',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-ET', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <ErrorBoundary>
      <Box sx={{ flexGrow: 1, p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Revenue Collection Dashboard
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Overview of revenue collection activities and performance
        </Typography>
      </Box>

      {/* Current Period Alert */}
      {stats.currentPeriod && (
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="body2">
            Current Period: <strong>{stats.currentPeriod.name}</strong> 
            ({formatDate(stats.currentPeriod.start_date)} - {formatDate(stats.currentPeriod.end_date)})
          </Typography>
        </Alert>
      )}

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatisticsCard
            title="Regional Revenue"
            value={formatCurrency(stats.totalRegionalRevenue)}
            subtitle="Total collected"
            icon={<AccountBalance />}
            color="primary"
            loading={loading}
            onClick={() => navigate('/revenue-collection/collections')}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatisticsCard
            title="City Services"
            value={formatCurrency(stats.totalCityServiceRevenue)}
            subtitle="Total collected"
            icon={<TrendingUp />}
            color="secondary"
            loading={loading}
            onClick={() => navigate('/revenue-collection/collections')}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatisticsCard
            title="Total Collections"
            value={stats.totalCollections.toLocaleString()}
            subtitle="Number of records"
            icon={<Receipt />}
            color="success"
            loading={loading}
            onClick={() => navigate('/revenue-collection/collections')}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatisticsCard
            title="Grand Total"
            value={formatCurrency(stats.totalRegionalRevenue + stats.totalCityServiceRevenue)}
            subtitle="All revenue combined"
            icon={<Analytics />}
            color="warning"
            loading={loading}
            onClick={() => navigate('/revenue-collection/analytics')}
          />
        </Grid>
      </Grid>

      {/* Quick Actions */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Actions
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  onClick={() => navigate('/revenue-collection/collections/regional/create')}
                >
                  New Regional Collection
                </Button>
                <Button
                  variant="contained"
                  color="secondary"
                  startIcon={<Add />}
                  onClick={() => navigate('/revenue-collection/collections/city-service/create')}
                >
                  New City Service Collection
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<Category />}
                  onClick={() => navigate('/revenue-collection/categories')}
                >
                  Manage Categories
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<DateRange />}
                  onClick={() => navigate('/revenue-collection/periods')}
                >
                  Manage Periods
                </Button>
                <Button
                  variant="outlined"
                  onClick={() => navigate('/revenue-collection/sources')}
                >
                  Revenue Sources
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Navigation
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                <Button
                  variant="outlined"
                  startIcon={<Analytics />}
                  onClick={() => navigate('/revenue-collection/analytics')}
                >
                  Analytics & Reports
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<Receipt />}
                  onClick={() => navigate('/revenue-collection/collections')}
                >
                  All Collections
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<AccountBalance />}
                  onClick={() => navigate('/revenue-collection/summaries')}
                >
                  Revenue Summaries
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Recent Collections */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              Recent Collections
            </Typography>
            <Button
              variant="outlined"
              size="small"
              onClick={() => navigate('/revenue-collection/collections')}
            >
              View All
            </Button>
          </Box>

          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Date</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Taxpayer</TableCell>
                  <TableCell>Source</TableCell>
                  <TableCell align="right">Amount</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {recentCollections.map((collection) => (
                  <TableRow key={`${collection.id}-${(collection as any).type}`}>
                    <TableCell>{formatDate(collection.collection_date)}</TableCell>
                    <TableCell>
                      <Chip
                        label={(collection as any).type === 'regional' ? 'Regional' : 'City Service'}
                        color={(collection as any).type === 'regional' ? 'primary' : 'secondary'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {collection.taxpayer_name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        TIN: {collection.taxpayer_tin}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {collection.revenue_source_name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {collection.category_name}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      <Typography variant="body2" fontWeight="medium">
                        {formatCurrency(collection.amount)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Tooltip title="View Details">
                        <IconButton
                          size="small"
                          onClick={() => {
                            const type = (collection as any).type === 'regional' ? 'regional' : 'city-service';
                            navigate(`/revenue-collection/collections/${type}/${collection.id}`);
                          }}
                        >
                          <Visibility />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
                {recentCollections.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      <Typography variant="body2" color="text.secondary">
                        No recent collections found
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
    </ErrorBoundary>
  );
};

export default RevenueCollectionDashboard;
