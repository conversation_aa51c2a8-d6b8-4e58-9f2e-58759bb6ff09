#!/usr/bin/env python
"""
Script to check current file types in the database
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'arada_dms.settings')
django.setup()

from django.db import connection

def check_file_types():
    print("Checking current file types in database")
    print("=" * 60)
    
    with connection.cursor() as cursor:
        # Check the structure of the file table
        cursor.execute("""
            SELECT column_name, data_type, is_nullable 
            FROM information_schema.columns 
            WHERE table_name = 'locations_file' AND column_name LIKE '%file_type%'
            ORDER BY column_name
        """)
        
        columns = cursor.fetchall()
        print("File type columns:")
        for col in columns:
            print(f"  {col[0]}: {col[1]} (nullable: {col[2]})")
        
        print()
        
        # Check current file_type_id values
        cursor.execute("""
            SELECT ft.code, ft.name, COUNT(*)
            FROM locations_file f
            JOIN locations_filetype ft ON f.file_type_id = ft.id
            GROUP BY ft.code, ft.name
            ORDER BY ft.code
        """)
        file_types = cursor.fetchall()
        print("Current file_type assignments:")
        for ft in file_types:
            print(f"  {ft[0]} ({ft[1]}): {ft[2]} files")
        
        print()
        
        # Check FileType table
        cursor.execute("SELECT id, code, name FROM locations_filetype ORDER BY code")
        available_types = cursor.fetchall()
        print("Available FileTypes:")
        for ft in available_types:
            print(f"  {ft[0]}: {ft[1]} - {ft[2]}")

if __name__ == '__main__':
    check_file_types()
