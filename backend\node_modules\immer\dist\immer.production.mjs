var R=Symbol.for("immer-nothing"),z=Symbol.for("immer-draftable"),u=Symbol.for("immer-state");function h(e,...t){throw new Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var N=Object.getPrototypeOf;function O(e){return!!e&&!!e[u]}function I(e){return e?ue(e)||Array.isArray(e)||!!e[z]||!!e.constructor?.[z]||v(e)||k(e):!1}var me=Object.prototype.constructor.toString();function ue(e){if(!e||typeof e!="object")return!1;let t=N(e);if(t===null)return!0;let r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object?!0:typeof r=="function"&&Function.toString.call(r)===me}function Se(e){return O(e)||h(15,e),e[u].t}function _(e,t){j(e)===0?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,n)=>t(n,r,e))}function j(e){let t=e[u];return t?t.o:Array.isArray(e)?1:v(e)?2:k(e)?3:0}function C(e,t){return j(e)===2?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function J(e,t){return j(e)===2?e.get(t):e[t]}function X(e,t,r){let n=j(e);n===2?e.set(t,r):n===3?e.add(r):e[t]=r}function ye(e,t){return e===t?e!==0||1/e===1/t:e!==e&&t!==t}function v(e){return e instanceof Map}function k(e){return e instanceof Set}function T(e){return e.e||e.t}function L(e,t){if(v(e))return new Map(e);if(k(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);let r=ue(e);if(t===!0||t==="class_only"&&!r){let n=Object.getOwnPropertyDescriptors(e);delete n[u];let i=Reflect.ownKeys(n);for(let f=0;f<i.length;f++){let l=i[f],c=n[l];c.writable===!1&&(c.writable=!0,c.configurable=!0),(c.get||c.set)&&(n[l]={configurable:!0,writable:!0,enumerable:c.enumerable,value:e[l]})}return Object.create(N(e),n)}else{let n=N(e);if(n!==null&&r)return{...e};let i=Object.create(n);return Object.assign(i,e)}}function H(e,t=!1){return $(e)||O(e)||!I(e)||(j(e)>1&&(e.set=e.add=e.clear=e.delete=Pe),Object.freeze(e),t&&Object.entries(e).forEach(([r,n])=>H(n,!0))),e}function Pe(){h(2)}function $(e){return Object.isFrozen(e)}var re={};function w(e){let t=re[e];return t||h(0,e),t}function Q(e,t){re[e]||(re[e]=t)}var U;function K(){return U}function xe(e,t){return{a:[],i:e,p:t,P:!0,d:0}}function ne(e,t){t&&(w("Patches"),e.f=[],e.h=[],e.b=t)}function V(e){Y(e),e.a.forEach(ge),e.a=null}function Y(e){e===U&&(U=e.i)}function ae(e){return U=xe(U,e)}function ge(e){let t=e[u];t.o===0||t.o===1?t.x():t.m=!0}function oe(e,t){t.d=t.a.length;let r=t.a[0];return e!==void 0&&e!==r?(r[u].s&&(V(t),h(4)),I(e)&&(e=Z(t,e),t.i||ee(t,e)),t.f&&w("Patches").T(r[u].t,e,t.f,t.h)):e=Z(t,r,[]),V(t),t.f&&t.b(t.f,t.h),e!==R?e:void 0}function Z(e,t,r){if($(t))return t;let n=t[u];if(!n)return _(t,(i,f)=>le(e,n,t,i,f,r)),t;if(n.n!==e)return t;if(!n.s)return ee(e,n.t,!0),n.t;if(!n.c){n.c=!0,n.n.d--;let i=n.e,f=i,l=!1;n.o===3&&(f=new Set(i),i.clear(),l=!0),_(f,(c,b)=>le(e,n,i,c,b,r,l)),ee(e,i,!1),r&&e.f&&w("Patches").g(n,r,e.f,e.h)}return n.e}function le(e,t,r,n,i,f,l){if(O(i)){let c=f&&t&&t.o!==3&&!C(t.r,n)?f.concat(n):void 0,b=Z(e,i,c);if(X(r,n,b),O(b))e.P=!1;else return}else l&&r.add(i);if(I(i)&&!$(i)){if(!e.p.y&&e.d<1)return;Z(e,i),(!t||!t.n.i)&&typeof n!="symbol"&&Object.prototype.propertyIsEnumerable.call(r,n)&&ee(e,i)}}function ee(e,t,r=!1){!e.i&&e.p.y&&e.P&&H(t,r)}function pe(e,t){let r=Array.isArray(e),n={o:r?1:0,n:t?t.n:K(),s:!1,c:!1,r:{},i:t,t:e,u:null,e:null,x:null,l:!1},i=n,f=ce;r&&(i=[n],f=q);let{revoke:l,proxy:c}=Proxy.revocable(i,f);return n.u=c,n.x=l,c}var ce={get(e,t){if(t===u)return e;let r=T(e);if(!C(r,t))return be(e,r,t);let n=r[t];return e.c||!I(n)?n:n===ie(e.t,t)?(se(e),e.e[t]=B(n,e)):n},has(e,t){return t in T(e)},ownKeys(e){return Reflect.ownKeys(T(e))},set(e,t,r){let n=de(T(e),t);if(n?.set)return n.set.call(e.u,r),!0;if(!e.s){let i=ie(T(e),t),f=i?.[u];if(f&&f.t===r)return e.e[t]=r,e.r[t]=!1,!0;if(ye(r,i)&&(r!==void 0||C(e.t,t)))return!0;se(e),E(e)}return e.e[t]===r&&(r!==void 0||t in e.e)||Number.isNaN(r)&&Number.isNaN(e.e[t])||(e.e[t]=r,e.r[t]=!0),!0},deleteProperty(e,t){return ie(e.t,t)!==void 0||t in e.t?(e.r[t]=!1,se(e),E(e)):delete e.r[t],e.e&&delete e.e[t],!0},getOwnPropertyDescriptor(e,t){let r=T(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n&&{writable:!0,configurable:e.o!==1||t!=="length",enumerable:n.enumerable,value:r[t]}},defineProperty(){h(11)},getPrototypeOf(e){return N(e.t)},setPrototypeOf(){h(12)}},q={};_(ce,(e,t)=>{q[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}});q.deleteProperty=function(e,t){return q.set.call(this,e,t,void 0)};q.set=function(e,t,r){return ce.set.call(this,e[0],t,r,e[0])};function ie(e,t){let r=e[u];return(r?T(r):e)[t]}function be(e,t,r){let n=de(t,r);return n?"value"in n?n.value:n.get?.call(e.u):void 0}function de(e,t){if(!(t in e))return;let r=N(e);for(;r;){let n=Object.getOwnPropertyDescriptor(r,t);if(n)return n;r=N(r)}}function E(e){e.s||(e.s=!0,e.i&&E(e.i))}function se(e){e.e||(e.e=L(e.t,e.n.p.S))}var te=class{constructor(t){this.y=!0;this.S=!1;this.produce=(t,r,n)=>{if(typeof t=="function"&&typeof r!="function"){let f=r;r=t;let l=this;return function(b=f,...a){return l.produce(b,o=>r.call(this,o,...a))}}typeof r!="function"&&h(6),n!==void 0&&typeof n!="function"&&h(7);let i;if(I(t)){let f=ae(this),l=B(t,void 0),c=!0;try{i=r(l),c=!1}finally{c?V(f):Y(f)}return ne(f,n),oe(i,f)}else if(!t||typeof t!="object"){if(i=r(t),i===void 0&&(i=t),i===R&&(i=void 0),this.y&&H(i,!0),n){let f=[],l=[];w("Patches").T(t,i,f,l),n(f,l)}return i}else h(1,t)};this.produceWithPatches=(t,r)=>{if(typeof t=="function")return(l,...c)=>this.produceWithPatches(l,b=>t(b,...c));let n,i;return[this.produce(t,r,(l,c)=>{n=l,i=c}),n,i]};typeof t?.autoFreeze=="boolean"&&this.setAutoFreeze(t.autoFreeze),typeof t?.useStrictShallowCopy=="boolean"&&this.setUseStrictShallowCopy(t.useStrictShallowCopy)}createDraft(t){I(t)||h(8),O(t)&&(t=fe(t));let r=ae(this),n=B(t,void 0);return n[u].l=!0,Y(r),n}finishDraft(t,r){let n=t&&t[u];(!n||!n.l)&&h(9);let{n:i}=n;return ne(i,r),oe(void 0,i)}setAutoFreeze(t){this.y=t}setUseStrictShallowCopy(t){this.S=t}applyPatches(t,r){let n;for(n=r.length-1;n>=0;n--){let f=r[n];if(f.path.length===0&&f.op==="replace"){t=f.value;break}}n>-1&&(r=r.slice(n+1));let i=w("Patches").A;return O(t)?i(t,r):this.produce(t,f=>i(f,r))}};function B(e,t){let r=v(e)?w("MapSet").I(e,t):k(e)?w("MapSet").D(e,t):pe(e,t);return(t?t.n:K()).a.push(r),r}function fe(e){return O(e)||h(10,e),he(e)}function he(e){if(!I(e)||$(e))return e;let t=e[u],r;if(t){if(!t.s)return t.t;t.c=!0,r=L(e,t.n.p.S)}else r=L(e,!0);return _(r,(n,i)=>{X(r,n,he(i))}),t&&(t.c=!1),r}function Te(){let t="replace",r="add",n="remove";function i(s,S,m,x){switch(s.o){case 0:case 2:return l(s,S,m,x);case 1:return f(s,S,m,x);case 3:return c(s,S,m,x)}}function f(s,S,m,x){let{t:A,r:P}=s,g=s.e;g.length<A.length&&([A,g]=[g,A],[m,x]=[x,m]);for(let y=0;y<A.length;y++)if(P[y]&&g[y]!==A[y]){let d=S.concat([y]);m.push({op:t,path:d,value:p(g[y])}),x.push({op:t,path:d,value:p(A[y])})}for(let y=A.length;y<g.length;y++){let d=S.concat([y]);m.push({op:r,path:d,value:p(g[y])})}for(let y=g.length-1;A.length<=y;--y){let d=S.concat([y]);x.push({op:n,path:d})}}function l(s,S,m,x){let{t:A,e:P}=s;_(s.r,(g,y)=>{let d=J(A,g),W=J(P,g),F=y?C(A,g)?t:r:n;if(d===W&&F===t)return;let D=S.concat(g);m.push(F===n?{op:F,path:D}:{op:F,path:D,value:W}),x.push(F===r?{op:n,path:D}:F===n?{op:r,path:D,value:p(d)}:{op:t,path:D,value:p(d)})})}function c(s,S,m,x){let{t:A,e:P}=s,g=0;A.forEach(y=>{if(!P.has(y)){let d=S.concat([g]);m.push({op:n,path:d,value:y}),x.unshift({op:r,path:d,value:y})}g++}),g=0,P.forEach(y=>{if(!A.has(y)){let d=S.concat([g]);m.push({op:r,path:d,value:y}),x.unshift({op:n,path:d,value:y})}g++})}function b(s,S,m,x){m.push({op:t,path:[],value:S===R?void 0:S}),x.push({op:t,path:[],value:s})}function a(s,S){return S.forEach(m=>{let{path:x,op:A}=m,P=s;for(let W=0;W<x.length-1;W++){let F=j(P),D=x[W];typeof D!="string"&&typeof D!="number"&&(D=""+D),(F===0||F===1)&&(D==="__proto__"||D==="constructor")&&h(16+3),typeof P=="function"&&D==="prototype"&&h(16+3),P=J(P,D),typeof P!="object"&&h(16+2,x.join("/"))}let g=j(P),y=o(m.value),d=x[x.length-1];switch(A){case t:switch(g){case 2:return P.set(d,y);case 3:h(16);default:return P[d]=y}case r:switch(g){case 1:return d==="-"?P.push(y):P.splice(d,0,y);case 2:return P.set(d,y);case 3:return P.add(y);default:return P[d]=y}case n:switch(g){case 1:return P.splice(d,1);case 2:return P.delete(d);case 3:return P.delete(m.value);default:return delete P[d]}default:h(16+1,A)}}),s}function o(s){if(!I(s))return s;if(Array.isArray(s))return s.map(o);if(v(s))return new Map(Array.from(s.entries()).map(([m,x])=>[m,o(x)]));if(k(s))return new Set(Array.from(s).map(o));let S=Object.create(N(s));for(let m in s)S[m]=o(s[m]);return C(s,z)&&(S[z]=s[z]),S}function p(s){return O(s)?o(s):s}Q("Patches",{A:a,g:i,T:b})}function Ae(){class e extends Map{constructor(a,o){super();this[u]={o:2,i:o,n:o?o.n:K(),s:!1,c:!1,e:void 0,r:void 0,t:a,u:this,l:!1,m:!1}}get size(){return T(this[u]).size}has(a){return T(this[u]).has(a)}set(a,o){let p=this[u];return l(p),(!T(p).has(a)||T(p).get(a)!==o)&&(r(p),E(p),p.r.set(a,!0),p.e.set(a,o),p.r.set(a,!0)),this}delete(a){if(!this.has(a))return!1;let o=this[u];return l(o),r(o),E(o),o.t.has(a)?o.r.set(a,!1):o.r.delete(a),o.e.delete(a),!0}clear(){let a=this[u];l(a),T(a).size&&(r(a),E(a),a.r=new Map,_(a.t,o=>{a.r.set(o,!1)}),a.e.clear())}forEach(a,o){let p=this[u];T(p).forEach((s,S,m)=>{a.call(o,this.get(S),S,this)})}get(a){let o=this[u];l(o);let p=T(o).get(a);if(o.c||!I(p)||p!==o.t.get(a))return p;let s=B(p,o);return r(o),o.e.set(a,s),s}keys(){return T(this[u]).keys()}values(){let a=this.keys();return{[Symbol.iterator]:()=>this.values(),next:()=>{let o=a.next();return o.done?o:{done:!1,value:this.get(o.value)}}}}entries(){let a=this.keys();return{[Symbol.iterator]:()=>this.entries(),next:()=>{let o=a.next();if(o.done)return o;let p=this.get(o.value);return{done:!1,value:[o.value,p]}}}}[(u,Symbol.iterator)](){return this.entries()}}function t(c,b){return new e(c,b)}function r(c){c.e||(c.r=new Map,c.e=new Map(c.t))}class n extends Set{constructor(a,o){super();this[u]={o:3,i:o,n:o?o.n:K(),s:!1,c:!1,e:void 0,t:a,u:this,a:new Map,m:!1,l:!1}}get size(){return T(this[u]).size}has(a){let o=this[u];return l(o),o.e?!!(o.e.has(a)||o.a.has(a)&&o.e.has(o.a.get(a))):o.t.has(a)}add(a){let o=this[u];return l(o),this.has(a)||(f(o),E(o),o.e.add(a)),this}delete(a){if(!this.has(a))return!1;let o=this[u];return l(o),f(o),E(o),o.e.delete(a)||(o.a.has(a)?o.e.delete(o.a.get(a)):!1)}clear(){let a=this[u];l(a),T(a).size&&(f(a),E(a),a.e.clear())}values(){let a=this[u];return l(a),f(a),a.e.values()}entries(){let a=this[u];return l(a),f(a),a.e.entries()}keys(){return this.values()}[(u,Symbol.iterator)](){return this.values()}forEach(a,o){let p=this.values(),s=p.next();for(;!s.done;)a.call(o,s.value,s.value,this),s=p.next()}}function i(c,b){return new n(c,b)}function f(c){c.e||(c.e=new Set,c.t.forEach(b=>{if(I(b)){let a=B(b,c);c.a.set(b,a),c.e.add(a)}else c.e.add(b)}))}function l(c){c.m&&h(3,JSON.stringify(T(c)))}Q("MapSet",{I:t,D:i})}var M=new te,qt=M.produce,Jt=M.produceWithPatches.bind(M),Xt=M.setAutoFreeze.bind(M),Qt=M.setUseStrictShallowCopy.bind(M),Yt=M.applyPatches.bind(M),Zt=M.createDraft.bind(M),er=M.finishDraft.bind(M);function tr(e){return e}function rr(e){return e}export{te as Immer,Yt as applyPatches,tr as castDraft,rr as castImmutable,Zt as createDraft,fe as current,Ae as enableMapSet,Te as enablePatches,er as finishDraft,H as freeze,z as immerable,O as isDraft,I as isDraftable,R as nothing,Se as original,qt as produce,Jt as produceWithPatches,Xt as setAutoFreeze,Qt as setUseStrictShallowCopy};
//# sourceMappingURL=immer.production.mjs.map