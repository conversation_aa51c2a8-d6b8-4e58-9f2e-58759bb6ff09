# Generated by Django 5.2.3 on 2025-07-14 20:09

import django.core.validators
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Box",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "row",
                    models.PositiveIntegerField(
                        help_text="Row position in shelf (1-based)",
                        validators=[django.core.validators.MinValueValidator(1)],
                    ),
                ),
                (
                    "column",
                    models.PositiveIntegerField(
                        help_text="Column position in shelf (1-based)",
                        validators=[django.core.validators.MinValueValidator(1)],
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True,
                        help_text="Optional box name or identifier",
                        max_length=100,
                        null=True,
                    ),
                ),
                ("description", models.TextField(blank=True, null=True)),
                (
                    "color",
                    models.Char<PERSON>ield(
                        blank=True,
                        help_text="Physical color of the box for identification",
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "material",
                    models.CharField(
                        blank=True,
                        help_text="Material of the box (e.g., Cardboard, Metal, Plastic)",
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "barcode_image",
                    models.ImageField(
                        blank=True, null=True, upload_to="locations/barcodes/"
                    ),
                ),
                (
                    "qr_code_image",
                    models.ImageField(
                        blank=True, null=True, upload_to="locations/qrcodes/"
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Box (Position)",
                "verbose_name_plural": "Boxes (Positions)",
                "db_table": "locations_box",
                "ordering": ["shelf", "row", "column"],
            },
        ),
        migrations.CreateModel(
            name="Building",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Building name (e.g., Main Building, Annex)",
                        max_length=100,
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        help_text="Building code (e.g., B1, MAIN, ANNEX)",
                        max_length=10,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Building code must contain only uppercase letters and numbers",
                                regex="^[A-Z0-9]{1,10}$",
                            )
                        ],
                    ),
                ),
                ("description", models.TextField(blank=True, null=True)),
                ("address", models.TextField(blank=True, null=True)),
                (
                    "barcode_image",
                    models.ImageField(
                        blank=True, null=True, upload_to="locations/barcodes/"
                    ),
                ),
                (
                    "qr_code_image",
                    models.ImageField(
                        blank=True, null=True, upload_to="locations/qrcodes/"
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Building",
                "verbose_name_plural": "Buildings",
                "db_table": "locations_building",
                "ordering": ["organization", "code"],
            },
        ),
        migrations.CreateModel(
            name="City",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("code", models.CharField(max_length=20)),
                ("population", models.PositiveIntegerField(blank=True, null=True)),
                (
                    "area_km2",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                ("is_capital", models.BooleanField(default=False)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name_plural": "Cities",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="Country",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                (
                    "code",
                    models.CharField(
                        max_length=3,
                        unique=True,
                        validators=[
                            django.core.validators.RegexValidator(
                                "^[A-Z]{2,3}$",
                                "Country code must be 2-3 uppercase letters",
                            )
                        ],
                    ),
                ),
                ("phone_code", models.CharField(blank=True, max_length=10)),
                ("currency", models.CharField(blank=True, max_length=10)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name_plural": "Countries",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="File",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="File name (e.g., 'Abebe's Hotel', 'Kebede's Supermarket')",
                        max_length=255,
                    ),
                ),
                (
                    "file_number",
                    models.CharField(
                        help_text="Unique file number or identifier", max_length=50
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Description of the file contents",
                        null=True,
                    ),
                ),
                (
                    "business_name",
                    models.CharField(
                        blank=True,
                        help_text="Official business name (if business file)",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "tin_number",
                    models.CharField(
                        blank=True,
                        help_text="Tax Identification Number",
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "vat_number",
                    models.CharField(
                        blank=True,
                        help_text="VAT Registration Number",
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "owner_name",
                    models.CharField(
                        blank=True,
                        help_text="Owner or contact person name",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "contact_phone",
                    models.CharField(
                        blank=True,
                        help_text="Contact phone number",
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "contact_email",
                    models.EmailField(
                        blank=True,
                        help_text="Contact email address",
                        max_length=254,
                        null=True,
                    ),
                ),
                (
                    "address",
                    models.TextField(
                        blank=True, help_text="Business or property address", null=True
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("archived", "Archived"),
                            ("closed", "Closed"),
                            ("transferred", "Transferred"),
                        ],
                        default="active",
                        max_length=20,
                    ),
                ),
                (
                    "tags",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="Tags for categorization and search",
                    ),
                ),
                (
                    "created_date",
                    models.DateField(
                        blank=True,
                        help_text="Date when the file was created/opened",
                        null=True,
                    ),
                ),
                (
                    "last_activity_date",
                    models.DateField(
                        blank=True,
                        help_text="Date of last document activity in this file",
                        null=True,
                    ),
                ),
                (
                    "barcode_image",
                    models.ImageField(
                        blank=True, null=True, upload_to="files/barcodes/"
                    ),
                ),
                (
                    "qr_code_image",
                    models.ImageField(
                        blank=True, null=True, upload_to="files/qrcodes/"
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "File",
                "verbose_name_plural": "Files",
                "db_table": "locations_file",
                "ordering": ["kent", "file_number"],
            },
        ),
        migrations.CreateModel(
            name="FileType",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Name of the file type (e.g., Business File, Tax File)",
                        max_length=100,
                        unique=True,
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        help_text="Short code for the file type (e.g., BUS, TAX, LEG)",
                        max_length=10,
                        unique=True,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="File type code must contain only uppercase letters and numbers",
                                regex="^[A-Z0-9]{1,10}$",
                            )
                        ],
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Description of what this file type contains",
                        null=True,
                    ),
                ),
                (
                    "color",
                    models.CharField(
                        default="#2196F3",
                        help_text="Color code for visual identification (hex format)",
                        max_length=7,
                    ),
                ),
                (
                    "icon",
                    models.CharField(
                        default="folder",
                        help_text="Icon name for visual representation",
                        max_length=50,
                    ),
                ),
                (
                    "requires_business_name",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this file type requires a business name",
                    ),
                ),
                (
                    "requires_tin_number",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this file type requires a TIN number",
                    ),
                ),
                (
                    "requires_license_number",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this file type requires a license number",
                    ),
                ),
                (
                    "requires_owner_name",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this file type requires an owner name",
                    ),
                ),
                (
                    "default_document_types",
                    models.TextField(
                        blank=True,
                        help_text="Comma-separated list of default document types for this file type",
                        null=True,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "File Type",
                "verbose_name_plural": "File Types",
                "db_table": "locations_filetype",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="Kebele",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("code", models.CharField(max_length=20)),
                ("number", models.PositiveIntegerField(help_text="Kebele number")),
                ("population", models.PositiveIntegerField(blank=True, null=True)),
                (
                    "area_km2",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["number", "name"],
            },
        ),
        migrations.CreateModel(
            name="Kent",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Kent name or identifier", max_length=100
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        help_text="Kent code (e.g., K1, K2, KT01)",
                        max_length=10,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Kent code must contain only uppercase letters and numbers",
                                regex="^[A-Z0-9]{1,10}$",
                            )
                        ],
                    ),
                ),
                ("description", models.TextField(blank=True, null=True)),
                (
                    "capacity",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Maximum number of files this kent can hold",
                        null=True,
                    ),
                ),
                (
                    "color",
                    models.CharField(
                        blank=True,
                        help_text="Physical color of the kent for identification",
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "material",
                    models.CharField(
                        blank=True,
                        help_text="Material of the kent (e.g., Cardboard, Metal, Plastic)",
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "barcode_image",
                    models.ImageField(
                        blank=True, null=True, upload_to="locations/barcodes/"
                    ),
                ),
                (
                    "qr_code_image",
                    models.ImageField(
                        blank=True, null=True, upload_to="locations/qrcodes/"
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Kent",
                "verbose_name_plural": "Kents",
                "db_table": "locations_kent",
                "ordering": ["box", "code"],
            },
        ),
        migrations.CreateModel(
            name="Region",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("code", models.CharField(max_length=20)),
                ("population", models.PositiveIntegerField(blank=True, null=True)),
                (
                    "area_km2",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                ("capital_city", models.CharField(blank=True, max_length=100)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="Shelf",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Shelf name or identifier", max_length=100
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        help_text="Shelf code (e.g., S1, S2, SH01)",
                        max_length=10,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Shelf code must contain only uppercase letters and numbers",
                                regex="^[A-Z0-9]{1,10}$",
                            )
                        ],
                    ),
                ),
                ("description", models.TextField(blank=True, null=True)),
                (
                    "rows",
                    models.PositiveIntegerField(
                        default=5,
                        help_text="Number of rows in this shelf",
                        validators=[django.core.validators.MinValueValidator(1)],
                    ),
                ),
                (
                    "columns",
                    models.PositiveIntegerField(
                        default=10,
                        help_text="Number of columns in this shelf",
                        validators=[django.core.validators.MinValueValidator(1)],
                    ),
                ),
                (
                    "capacity",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Maximum number of kents this shelf can hold (auto-calculated from rows * columns)",
                        null=True,
                    ),
                ),
                (
                    "barcode_image",
                    models.ImageField(
                        blank=True, null=True, upload_to="locations/barcodes/"
                    ),
                ),
                (
                    "qr_code_image",
                    models.ImageField(
                        blank=True, null=True, upload_to="locations/qrcodes/"
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Shelf",
                "verbose_name_plural": "Shelves",
                "db_table": "locations_shelf",
                "ordering": ["building", "code"],
            },
        ),
        migrations.CreateModel(
            name="SpecialLocation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Name of the special location/place", max_length=100
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Special Location",
                "verbose_name_plural": "Special Locations",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="SubCity",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("code", models.CharField(max_length=20)),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("subcity", "Sub City"),
                            ("woreda", "Woreda"),
                            ("district", "District"),
                        ],
                        default="subcity",
                        max_length=20,
                    ),
                ),
                ("population", models.PositiveIntegerField(blank=True, null=True)),
                (
                    "area_km2",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name_plural": "SubCities/Woredas",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="Zone",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("code", models.CharField(max_length=20)),
                ("population", models.PositiveIntegerField(blank=True, null=True)),
                (
                    "area_km2",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["name"],
            },
        ),
    ]
