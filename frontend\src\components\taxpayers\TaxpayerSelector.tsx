import React, { useState, useEffect } from 'react';
import {
  Autocomplete,
  TextField,
  Box,
  Typography,
  Chip,
  Avatar,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Person,
  Business,
  Search,
  Clear,
} from '@mui/icons-material';
import taxpayerService from '../../services/taxpayerService';

interface TaxpayerOption {
  id: string;
  type: 'individual' | 'organization';
  tin: string;
  name: string;
  business_name?: string;
  display_name: string;
  full_name?: string;
  phone?: string;
  email?: string;
}

interface TaxpayerSelectorProps {
  value?: TaxpayerOption | null;
  onChange: (taxpayer: TaxpayerOption | null) => void;
  label?: string;
  placeholder?: string;
  required?: boolean;
  error?: boolean;
  helperText?: string;
  disabled?: boolean;
  filterType?: 'all' | 'individual' | 'organization';
}

const TaxpayerSelector: React.FC<TaxpayerSelectorProps> = ({
  value,
  onChange,
  label = "Select Taxpayer",
  placeholder = "Search by name, TIN, or business name...",
  required = false,
  error = false,
  helperText,
  disabled = false,
  filterType = 'all',
}) => {
  const [options, setOptions] = useState<TaxpayerOption[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState<'all' | 'individual' | 'organization'>(filterType);

  // Load taxpayers based on search query and type filter
  const loadTaxpayers = async (query: string = '', type: string = 'all') => {
    try {
      setLoading(true);
      
      const promises = [];
      
      if (type === 'all' || type === 'individual') {
        promises.push(
          taxpayerService.getIndividualTaxPayers({ 
            search: query, 
            page_size: 50 
          }).then(response => 
            response.results.map(taxpayer => ({
              id: taxpayer.id,
              type: 'individual' as const,
              tin: taxpayer.tin,
              name: taxpayer.full_name,
              display_name: taxpayer.full_name,
              full_name: taxpayer.full_name,
              phone: taxpayer.phone,
              email: taxpayer.email,
            }))
          )
        );
      }
      
      if (type === 'all' || type === 'organization') {
        promises.push(
          taxpayerService.getOrganizationTaxPayers({ 
            search: query, 
            page_size: 50 
          }).then(response => 
            response.results.map(taxpayer => ({
              id: taxpayer.id,
              type: 'organization' as const,
              tin: taxpayer.tin,
              name: taxpayer.business_name,
              business_name: taxpayer.business_name,
              display_name: taxpayer.business_name,
              phone: taxpayer.contact_phone,
              email: taxpayer.contact_email,
            }))
          )
        );
      }
      
      const results = await Promise.all(promises);
      const allTaxpayers = results.flat();
      
      // Sort by name
      allTaxpayers.sort((a, b) => a.display_name.localeCompare(b.display_name));
      
      setOptions(allTaxpayers);
    } catch (error) {
      console.error('Error loading taxpayers:', error);
      setOptions([]);
    } finally {
      setLoading(false);
    }
  };

  // Load initial data
  useEffect(() => {
    loadTaxpayers('', typeFilter);
  }, [typeFilter]);

  // Handle search with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchQuery.length >= 2 || searchQuery.length === 0) {
        loadTaxpayers(searchQuery, typeFilter);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery, typeFilter]);

  const handleInputChange = (event: any, newInputValue: string) => {
    setSearchQuery(newInputValue);
  };

  const getOptionLabel = (option: TaxpayerOption) => {
    return `${option.display_name} (TIN: ${option.tin})`;
  };

  const renderOption = (props: any, option: TaxpayerOption) => (
    <Box component="li" {...props} sx={{ display: 'flex', alignItems: 'center', gap: 2, p: 2 }}>
      <Avatar sx={{ bgcolor: option.type === 'individual' ? 'primary.main' : 'secondary.main' }}>
        {option.type === 'individual' ? <Person /> : <Business />}
      </Avatar>
      <Box sx={{ flex: 1 }}>
        <Typography variant="body1" fontWeight="medium">
          {option.display_name}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          TIN: {option.tin}
        </Typography>
        {option.phone && (
          <Typography variant="caption" color="text.secondary">
            Phone: {option.phone}
          </Typography>
        )}
      </Box>
      <Chip
        label={option.type === 'individual' ? 'Individual' : 'Organization'}
        color={option.type === 'individual' ? 'primary' : 'secondary'}
        size="small"
      />
    </Box>
  );

  return (
    <Box>
      {filterType === 'all' && (
        <FormControl fullWidth sx={{ mb: 2 }}>
          <InputLabel>Taxpayer Type Filter</InputLabel>
          <Select
            value={typeFilter}
            label="Taxpayer Type Filter"
            onChange={(e) => setTypeFilter(e.target.value as any)}
          >
            <MenuItem value="all">All Taxpayers</MenuItem>
            <MenuItem value="individual">Individuals Only</MenuItem>
            <MenuItem value="organization">Organizations Only</MenuItem>
          </Select>
        </FormControl>
      )}

      <Autocomplete
        options={options}
        value={value}
        onChange={(_, newValue) => onChange(newValue)}
        onInputChange={handleInputChange}
        getOptionLabel={getOptionLabel}
        renderOption={renderOption}
        loading={loading}
        disabled={disabled}
        filterOptions={(x) => x} // Disable client-side filtering since we do server-side
        renderInput={(params) => (
          <TextField
            {...params}
            label={label}
            placeholder={placeholder}
            required={required}
            error={error}
            helperText={helperText || (searchQuery.length > 0 && searchQuery.length < 2 ? 'Type at least 2 characters to search' : '')}
            InputProps={{
              ...params.InputProps,
              startAdornment: (
                <InputAdornment position="start">
                  <Search color="action" />
                </InputAdornment>
              ),
              endAdornment: (
                <>
                  {loading ? <CircularProgress color="inherit" size={20} /> : null}
                  {params.InputProps.endAdornment}
                </>
              ),
            }}
          />
        )}
        renderTags={(tagValue, getTagProps) =>
          tagValue.map((option, index) => (
            <Chip
              {...getTagProps({ index })}
              key={option.id}
              label={`${option.display_name} (${option.tin})`}
              color={option.type === 'individual' ? 'primary' : 'secondary'}
              avatar={
                <Avatar sx={{ bgcolor: 'transparent !important' }}>
                  {option.type === 'individual' ? <Person /> : <Business />}
                </Avatar>
              }
            />
          ))
        }
        noOptionsText={
          searchQuery.length < 2 ? 
            "Type at least 2 characters to search" : 
            loading ? "Searching..." : "No taxpayers found"
        }
      />

      {value && (
        <Alert severity="success" sx={{ mt: 2 }}>
          <Typography variant="body2">
            <strong>Selected:</strong> {value.display_name} ({value.type === 'individual' ? 'Individual' : 'Organization'})
            <br />
            <strong>TIN:</strong> {value.tin}
            {value.phone && (
              <>
                <br />
                <strong>Phone:</strong> {value.phone}
              </>
            )}
          </Typography>
        </Alert>
      )}
    </Box>
  );
};

export default TaxpayerSelector;
