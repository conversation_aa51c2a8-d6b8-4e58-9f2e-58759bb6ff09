# Generated by Django 5.2.3 on 2025-07-14 20:09

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="DocumentTag",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=50)),
                (
                    "color",
                    models.Char<PERSON>ield(
                        default="#007bff",
                        help_text="Hex color code for tag display",
                        max_length=7,
                    ),
                ),
                ("description", models.TextField(blank=True, null=True)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name": "Document Tag",
                "verbose_name_plural": "Document Tags",
                "db_table": "documents_document_tag",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="DocumentType",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Document type name (e.g., Tax Record, Business License)",
                        max_length=100,
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        help_text="Short code for document type (e.g., TAX, BLI, PRP)",
                        max_length=20,
                    ),
                ),
                ("description", models.TextField(blank=True, null=True)),
                (
                    "retention_days",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Document retention period in days (overrides organization default)",
                        null=True,
                    ),
                ),
                (
                    "confidentiality_level",
                    models.CharField(
                        choices=[
                            ("public", "Public"),
                            ("internal", "Internal"),
                            ("confidential", "Confidential"),
                            ("restricted", "Restricted"),
                        ],
                        default="internal",
                        max_length=20,
                    ),
                ),
                (
                    "requires_expiry_date",
                    models.BooleanField(
                        default=False,
                        help_text="Whether documents of this type require an expiry date",
                    ),
                ),
                (
                    "requires_approval",
                    models.BooleanField(
                        default=False,
                        help_text="Whether documents of this type require approval before access",
                    ),
                ),
                (
                    "allowed_file_extensions",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="Allowed file extensions for digital documents (e.g., ['pdf', 'jpg', 'png'])",
                    ),
                ),
                (
                    "max_file_size_mb",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Maximum file size in MB (overrides organization default)",
                        null=True,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Document Type",
                "verbose_name_plural": "Document Types",
                "db_table": "documents_document_type",
                "ordering": ["organization", "name"],
            },
        ),
        migrations.CreateModel(
            name="DocumentVersion",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("version_number", models.PositiveIntegerField()),
                (
                    "file",
                    models.FileField(
                        upload_to="documents/versions/",
                        validators=[
                            django.core.validators.FileExtensionValidator(
                                allowed_extensions=[
                                    "pdf",
                                    "jpg",
                                    "jpeg",
                                    "png",
                                    "doc",
                                    "docx",
                                    "xls",
                                    "xlsx",
                                ]
                            )
                        ],
                    ),
                ),
                ("file_size", models.PositiveIntegerField()),
                (
                    "change_summary",
                    models.TextField(
                        blank=True,
                        help_text="Summary of changes in this version",
                        null=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name": "Document Version",
                "verbose_name_plural": "Document Versions",
                "db_table": "documents_document_version",
                "ordering": ["-version_number"],
            },
        ),
        migrations.CreateModel(
            name="Document",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "title",
                    models.CharField(
                        help_text="Document title or name", max_length=255
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Detailed description of the document",
                        null=True,
                    ),
                ),
                (
                    "mode",
                    models.CharField(
                        choices=[
                            ("physical", "Physical"),
                            ("digital", "Digital"),
                            ("hybrid", "Hybrid (Both Physical and Digital)"),
                        ],
                        default="physical",
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("archived", "Archived"),
                            ("destroyed", "Destroyed"),
                            ("checked_out", "Checked Out"),
                            ("lost", "Lost"),
                        ],
                        default="active",
                        max_length=20,
                    ),
                ),
                (
                    "tags",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="Tags for categorization and search",
                    ),
                ),
                (
                    "reference_number",
                    models.CharField(
                        blank=True,
                        help_text="External reference number or identifier",
                        max_length=100,
                        null=True,
                    ),
                ),
                (
                    "document_date",
                    models.DateField(
                        blank=True,
                        help_text="Date when the document was created/issued",
                        null=True,
                    ),
                ),
                (
                    "expiry_date",
                    models.DateField(
                        blank=True,
                        help_text="Document expiry date (if applicable)",
                        null=True,
                    ),
                ),
                (
                    "file",
                    models.FileField(
                        blank=True,
                        help_text="Digital file (for digital or hybrid documents)",
                        null=True,
                        upload_to="documents/",
                        validators=[
                            django.core.validators.FileExtensionValidator(
                                allowed_extensions=[
                                    "pdf",
                                    "jpg",
                                    "jpeg",
                                    "png",
                                    "doc",
                                    "docx",
                                    "xls",
                                    "xlsx",
                                ]
                            )
                        ],
                    ),
                ),
                (
                    "file_size",
                    models.PositiveIntegerField(
                        blank=True, help_text="File size in bytes", null=True
                    ),
                ),
                (
                    "number_of_pages",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Total number of pages in this document (for audit and tracking purposes)",
                        null=True,
                    ),
                ),
                (
                    "barcode_image",
                    models.ImageField(
                        blank=True, null=True, upload_to="documents/barcodes/"
                    ),
                ),
                (
                    "qr_code_image",
                    models.ImageField(
                        blank=True, null=True, upload_to="documents/qrcodes/"
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_documents",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Document",
                "verbose_name_plural": "Documents",
                "db_table": "documents_document",
                "ordering": ["-created_at"],
            },
        ),
    ]
