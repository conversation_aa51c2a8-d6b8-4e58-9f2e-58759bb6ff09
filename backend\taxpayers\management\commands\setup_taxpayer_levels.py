from django.core.management.base import BaseCommand
from taxpayers.models import TaxPayerLevel


class Command(BaseCommand):
    help = 'Setup taxpayer levels with daily income ranges'

    def handle(self, *args, **options):
        self.stdout.write('Setting up taxpayer levels with income ranges...')
        
        # Define taxpayer levels with daily income ranges
        levels_data = [
            {
                'code': 'A',
                'name': 'Large Taxpayer',
                'description': 'High-income businesses with significant daily revenue',
                'priority': 1,
                'min_daily_income': 1000.00,
                'max_daily_income': None,  # Unlimited
                'tax_rate_percentage': 30.00,
                'requires_annual_assessment': True,
                'minimum_annual_turnover': 365000.00,  # Legacy field
            },
            {
                'code': 'B',
                'name': 'Medium Taxpayer',
                'description': 'Medium-income businesses with moderate daily revenue',
                'priority': 2,
                'min_daily_income': 300.00,
                'max_daily_income': 999.99,
                'tax_rate_percentage': 20.00,
                'requires_annual_assessment': True,
                'minimum_annual_turnover': 109500.00,  # Legacy field
                'maximum_annual_turnover': 364999.99,  # Legacy field
            },
            {
                'code': 'C',
                'name': 'Small Taxpayer',
                'description': 'Small-income businesses with lower daily revenue',
                'priority': 3,
                'min_daily_income': 0.00,
                'max_daily_income': 299.99,
                'tax_rate_percentage': 10.00,
                'requires_annual_assessment': True,
                'maximum_annual_turnover': 109499.99,  # Legacy field
            },
        ]
        
        created_count = 0
        updated_count = 0
        
        for level_data in levels_data:
            level, created = TaxPayerLevel.objects.update_or_create(
                code=level_data['code'],
                defaults=level_data
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created level: {level.code} - {level.name}')
                )
            else:
                updated_count += 1
                self.stdout.write(
                    self.style.WARNING(f'Updated level: {level.code} - {level.name}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'\nSetup complete! Created {created_count} new levels, updated {updated_count} existing levels.'
            )
        )
        
        # Display the levels
        self.stdout.write('\nTaxpayer Levels:')
        for level in TaxPayerLevel.objects.filter(is_active=True).order_by('priority'):
            income_range = f"${level.min_daily_income:,.2f}"
            if level.max_daily_income:
                income_range += f" - ${level.max_daily_income:,.2f}"
            else:
                income_range += "+"
            
            self.stdout.write(
                f'  {level.code}: {level.name} ({income_range}/day, {level.tax_rate_percentage}% tax)'
            )
