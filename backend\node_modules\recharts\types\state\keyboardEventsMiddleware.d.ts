export declare const keyDownAction: import("@reduxjs/toolkit").ActionCreatorWithOptionalPayload<string, string>;
export declare const focusAction: import("@reduxjs/toolkit").ActionCreatorWithoutPayload<"focus">;
export declare const keyboardEventsMiddleware: import("@reduxjs/toolkit").ListenerMiddlewareInstance<unknown, import("@reduxjs/toolkit").ThunkDispatch<unknown, unknown, import("redux").AnyAction>, unknown>;
