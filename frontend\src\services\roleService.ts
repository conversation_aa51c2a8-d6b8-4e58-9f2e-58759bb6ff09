import apiClient from './api';

export interface Role {
  id: number;
  name: string;
  description?: string;
  permissions: string[];
  is_active: boolean;
  is_system_role: boolean;
  user_count: number;
  created_at: string;
  updated_at: string;
}

export interface RoleCreate {
  name: string;
  description?: string;
  permissions: string[];
  is_active?: boolean;
}

export interface RoleUpdate extends Partial<RoleCreate> {}

export interface RoleListResponse {
  count: number;
  next?: string;
  previous?: string;
  results: Role[];
}

export interface Permission {
  id: string;
  name: string;
  content_type: string;
  codename: string;
  description?: string;
  category: string;
}

export interface PermissionCategory {
  name: string;
  permissions: Permission[];
}

class RoleService {
  private baseUrl = '/accounts/roles';

  async getRoles(params?: {
    page?: number;
    page_size?: number;
    search?: string;
    is_active?: boolean;
  }): Promise<RoleListResponse> {
    const response = await apiClient.get(this.baseUrl, { params });
    return response.data;
  }

  async getRole(id: number): Promise<Role> {
    const response = await apiClient.get(`${this.baseUrl}/${id}/`);
    return response.data;
  }

  async createRole(data: RoleCreate): Promise<Role> {
    const response = await apiClient.post(this.baseUrl + '/', data);
    return response.data;
  }

  async updateRole(id: number, data: RoleUpdate): Promise<Role> {
    const response = await apiClient.patch(`${this.baseUrl}/${id}/`, data);
    return response.data;
  }

  async deleteRole(id: number): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/${id}/`);
  }

  async toggleRoleStatus(id: number): Promise<Role> {
    const role = await this.getRole(id);
    return this.updateRole(id, { is_active: !role.is_active });
  }

  async getPermissions(): Promise<PermissionCategory[]> {
    const response = await apiClient.get('/accounts/permissions/');
    return response.data;
  }

  async getUsersByRole(roleId: number): Promise<any[]> {
    const response = await apiClient.get(`${this.baseUrl}/${roleId}/users/`);
    return response.data;
  }

  async assignRoleToUsers(roleId: number, userIds: number[]): Promise<void> {
    await apiClient.post(`${this.baseUrl}/${roleId}/assign-users/`, { user_ids: userIds });
  }

  async removeRoleFromUsers(roleId: number, userIds: number[]): Promise<void> {
    await apiClient.post(`${this.baseUrl}/${roleId}/remove-users/`, { user_ids: userIds });
  }

  // Utility methods
  getDefaultRoles() {
    return [
      {
        name: 'Administrator',
        description: 'Full system access with all permissions',
        permissions: ['*'], // All permissions
        color: 'error',
        icon: 'AdminPanelSettings',
      },
      {
        name: 'Manager',
        description: 'Manage documents, users, and organizational settings',
        permissions: [
          'documents.*',
          'users.view',
          'users.create',
          'users.update',
          'organizations.view',
          'organizations.update',
          'locations.*',
          'requests.*',
        ],
        color: 'warning',
        icon: 'ManageAccounts',
      },
      {
        name: 'Employee',
        description: 'Create and manage own documents, submit requests',
        permissions: [
          'documents.view',
          'documents.create',
          'documents.update_own',
          'documents.delete_own',
          'requests.view_own',
          'requests.create',
          'requests.update_own',
          'locations.view',
        ],
        color: 'info',
        icon: 'Person',
      },
      {
        name: 'Viewer',
        description: 'Read-only access to documents and basic information',
        permissions: [
          'documents.view',
          'locations.view',
          'requests.view_own',
        ],
        color: 'default',
        icon: 'Visibility',
      },
    ];
  }

  getPermissionCategories() {
    return [
      {
        name: 'Documents',
        description: 'Document management permissions',
        permissions: [
          'documents.view',
          'documents.create',
          'documents.update',
          'documents.delete',
          'documents.update_own',
          'documents.delete_own',
          'documents.approve',
          'documents.archive',
        ],
      },
      {
        name: 'Users',
        description: 'User management permissions',
        permissions: [
          'users.view',
          'users.create',
          'users.update',
          'users.delete',
          'users.activate',
          'users.deactivate',
          'users.assign_roles',
        ],
      },
      {
        name: 'Organizations',
        description: 'Organization management permissions',
        permissions: [
          'organizations.view',
          'organizations.create',
          'organizations.update',
          'organizations.delete',
          'organizations.manage_settings',
        ],
      },
      {
        name: 'Locations',
        description: 'Location management permissions',
        permissions: [
          'locations.view',
          'locations.create',
          'locations.update',
          'locations.delete',
          'locations.manage_hierarchy',
        ],
      },
      {
        name: 'Requests',
        description: 'Request management permissions',
        permissions: [
          'requests.view',
          'requests.view_own',
          'requests.create',
          'requests.update',
          'requests.update_own',
          'requests.delete',
          'requests.approve',
          'requests.reject',
        ],
      },
      {
        name: 'System',
        description: 'System administration permissions',
        permissions: [
          'system.view_logs',
          'system.manage_settings',
          'system.backup',
          'system.restore',
          'system.maintenance',
        ],
      },
    ];
  }

  formatPermissionName(permission: string): string {
    return permission
      .split('.')
      .map(part => part.charAt(0).toUpperCase() + part.slice(1))
      .join(' ');
  }

  getPermissionDescription(permission: string): string {
    const descriptions: Record<string, string> = {
      'documents.view': 'View documents',
      'documents.create': 'Create new documents',
      'documents.update': 'Edit any document',
      'documents.delete': 'Delete any document',
      'documents.update_own': 'Edit own documents',
      'documents.delete_own': 'Delete own documents',
      'documents.approve': 'Approve document submissions',
      'documents.archive': 'Archive documents',
      'users.view': 'View user profiles',
      'users.create': 'Create new users',
      'users.update': 'Edit user profiles',
      'users.delete': 'Delete users',
      'users.activate': 'Activate user accounts',
      'users.deactivate': 'Deactivate user accounts',
      'users.assign_roles': 'Assign roles to users',
      'organizations.view': 'View organization details',
      'organizations.create': 'Create new organizations',
      'organizations.update': 'Edit organization settings',
      'organizations.delete': 'Delete organizations',
      'organizations.manage_settings': 'Manage organization settings',
      'locations.view': 'View location hierarchy',
      'locations.create': 'Create new locations',
      'locations.update': 'Edit location details',
      'locations.delete': 'Delete locations',
      'locations.manage_hierarchy': 'Manage location hierarchy',
      'requests.view': 'View all requests',
      'requests.view_own': 'View own requests',
      'requests.create': 'Create new requests',
      'requests.update': 'Edit any request',
      'requests.update_own': 'Edit own requests',
      'requests.delete': 'Delete requests',
      'requests.approve': 'Approve requests',
      'requests.reject': 'Reject requests',
      'system.view_logs': 'View system logs',
      'system.manage_settings': 'Manage system settings',
      'system.backup': 'Create system backups',
      'system.restore': 'Restore from backups',
      'system.maintenance': 'Perform system maintenance',
    };

    return descriptions[permission] || this.formatPermissionName(permission);
  }

  getRoleColor(roleName: string): 'error' | 'warning' | 'info' | 'success' | 'default' {
    const colorMap: Record<string, any> = {
      'administrator': 'error',
      'admin': 'error',
      'manager': 'warning',
      'employee': 'info',
      'viewer': 'default',
      'guest': 'default',
    };

    return colorMap[roleName.toLowerCase()] || 'default';
  }

  validateRoleName(name: string): boolean {
    return /^[a-zA-Z0-9\s_-]{2,50}$/.test(name);
  }

  hasPermission(role: Role, permission: string): boolean {
    if (role.permissions.includes('*')) return true;
    return role.permissions.includes(permission);
  }

  getEffectivePermissions(roles: Role[]): string[] {
    const permissions = new Set<string>();
    
    for (const role of roles) {
      if (role.permissions.includes('*')) {
        // If any role has all permissions, return all
        return ['*'];
      }
      role.permissions.forEach(permission => permissions.add(permission));
    }
    
    return Array.from(permissions);
  }
}

export default new RoleService();
