{"version": 3, "sources": ["../../src/index.ts", "../../src/createDraftSafeSelector.ts", "../../src/configureStore.ts", "../../src/devtoolsExtension.ts", "../../src/getDefaultMiddleware.ts", "../../src/createAction.ts", "../../src/tsHelpers.ts", "../../src/actionCreatorInvariantMiddleware.ts", "../../src/utils.ts", "../../src/immutableStateInvariantMiddleware.ts", "../../src/serializableStateInvariantMiddleware.ts", "../../src/autoBatchEnhancer.ts", "../../src/getDefaultEnhancers.ts", "../../src/createReducer.ts", "../../src/mapBuilders.ts", "../../src/matchers.ts", "../../src/nanoid.ts", "../../src/createAsyncThunk.ts", "../../src/createSlice.ts", "../../src/entities/entity_state.ts", "../../src/entities/state_selectors.ts", "../../src/entities/state_adapter.ts", "../../src/entities/utils.ts", "../../src/entities/unsorted_state_adapter.ts", "../../src/entities/sorted_state_adapter.ts", "../../src/entities/create_adapter.ts", "../../src/listenerMiddleware/index.ts", "../../src/listenerMiddleware/exceptions.ts", "../../src/listenerMiddleware/utils.ts", "../../src/listenerMiddleware/task.ts", "../../src/dynamicMiddleware/index.ts", "../../src/combineSlices.ts", "../../src/formatProdErrorMessage.ts"], "sourcesContent": ["// This must remain here so that the `mangleErrors.cjs` build script\n// does not have to import this into each source file it rewrites.\nimport { formatProdErrorMessage } from './formatProdErrorMessage';\nexport * from 'redux';\nexport { produce as createNextState, current, freeze, original, isDraft } from 'immer';\nexport type { Draft } from 'immer';\nexport { createSelector, createSelectorCreator, lruMemoize, weakMapMemoize } from 'reselect';\nexport type { Selector, OutputSelector } from 'reselect';\nexport { createDraftSafeSelector, createDraftSafeSelectorCreator } from './createDraftSafeSelector';\nexport type { ThunkAction, ThunkDispatch, ThunkMiddleware } from 'redux-thunk';\nexport {\n// js\nconfigureStore } from './configureStore';\nexport type {\n// types\nConfigureStoreOptions, EnhancedStore } from './configureStore';\nexport type { DevToolsEnhancerOptions } from './devtoolsExtension';\nexport {\n// js\ncreateAction, isActionCreator, isFSA as isFluxStandardAction } from './createAction';\nexport type {\n// types\nPayloadAction, PayloadActionCreator, ActionCreatorWithNonInferrablePayload, ActionCreatorWithOptionalPayload, ActionCreatorWithPayload, ActionCreatorWithoutPayload, ActionCreatorWithPreparedPayload, PrepareAction } from './createAction';\nexport {\n// js\ncreateReducer } from './createReducer';\nexport type {\n// types\nActions, CaseReducer, CaseReducers } from './createReducer';\nexport {\n// js\ncreateSlice, buildCreateSlice, asyncThunkCreator, ReducerType } from './createSlice';\nexport type {\n// types\nCreateSliceOptions, Slice, CaseReducerActions, SliceCaseReducers, ValidateSliceCaseReducers, CaseReducerWithPrepare, ReducerCreators, SliceSelectors } from './createSlice';\nexport type { ActionCreatorInvariantMiddlewareOptions } from './actionCreatorInvariantMiddleware';\nexport { createActionCreatorInvariantMiddleware } from './actionCreatorInvariantMiddleware';\nexport {\n// js\ncreateImmutableStateInvariantMiddleware, isImmutableDefault } from './immutableStateInvariantMiddleware';\nexport type {\n// types\nImmutableStateInvariantMiddlewareOptions } from './immutableStateInvariantMiddleware';\nexport {\n// js\ncreateSerializableStateInvariantMiddleware, findNonSerializableValue, isPlain } from './serializableStateInvariantMiddleware';\nexport type {\n// types\nSerializableStateInvariantMiddlewareOptions } from './serializableStateInvariantMiddleware';\nexport type {\n// types\nActionReducerMapBuilder } from './mapBuilders';\nexport { Tuple } from './utils';\nexport { createEntityAdapter } from './entities/create_adapter';\nexport type { EntityState, EntityAdapter, EntitySelectors, EntityStateAdapter, EntityId, Update, IdSelector, Comparer } from './entities/models';\nexport { createAsyncThunk, unwrapResult, miniSerializeError } from './createAsyncThunk';\nexport type { AsyncThunk, AsyncThunkOptions, AsyncThunkAction, AsyncThunkPayloadCreatorReturnValue, AsyncThunkPayloadCreator, GetState, GetThunkAPI, SerializedError, CreateAsyncThunkFunction } from './createAsyncThunk';\nexport {\n// js\nisAllOf, isAnyOf, isPending, isRejected, isFulfilled, isAsyncThunkAction, isRejectedWithValue } from './matchers';\nexport type {\n// types\nActionMatchingAllOf, ActionMatchingAnyOf } from './matchers';\nexport { nanoid } from './nanoid';\nexport type { ListenerEffect, ListenerMiddleware, ListenerEffectAPI, ListenerMiddlewareInstance, CreateListenerMiddlewareOptions, ListenerErrorHandler, TypedStartListening, TypedAddListener, TypedStopListening, TypedRemoveListener, UnsubscribeListener, UnsubscribeListenerOptions, ForkedTaskExecutor, ForkedTask, ForkedTaskAPI, AsyncTaskExecutor, SyncTaskExecutor, TaskCancelled, TaskRejected, TaskResolved, TaskResult } from './listenerMiddleware/index';\nexport type { AnyListenerPredicate } from './listenerMiddleware/types';\nexport { createListenerMiddleware, addListener, removeListener, clearAllListeners, TaskAbortError } from './listenerMiddleware/index';\nexport type { AddMiddleware, DynamicDispatch, DynamicMiddlewareInstance, GetDispatchType as GetDispatch, MiddlewareApiConfig } from './dynamicMiddleware/types';\nexport { createDynamicMiddleware } from './dynamicMiddleware/index';\nexport { SHOULD_AUTOBATCH, prepareAutoBatched, autoBatchEnhancer } from './autoBatchEnhancer';\nexport type { AutoBatchOptions } from './autoBatchEnhancer';\nexport { combineSlices } from './combineSlices';\nexport type { CombinedSliceReducer, WithSlice } from './combineSlices';\nexport type { ExtractDispatchExtensions as TSHelpersExtractDispatchExtensions, SafePromise } from './tsHelpers';\nexport { formatProdErrorMessage } from './formatProdErrorMessage';", "import { current, isDraft } from 'immer';\nimport { createSelectorCreator, weakMapMemoize } from 'reselect';\nexport const createDraftSafeSelectorCreator: typeof createSelectorCreator = (...args: unknown[]) => {\n  const createSelector = (createSelectorCreator as any)(...args);\n  const createDraftSafeSelector = Object.assign((...args: unknown[]) => {\n    const selector = createSelector(...args);\n    const wrappedSelector = (value: unknown, ...rest: unknown[]) => selector(isDraft(value) ? current(value) : value, ...rest);\n    Object.assign(wrappedSelector, selector);\n    return wrappedSelector as any;\n  }, {\n    withTypes: () => createDraftSafeSelector\n  });\n  return createDraftSafeSelector;\n};\n\n/**\n * \"Draft-Safe\" version of `reselect`'s `createSelector`:\n * If an `immer`-drafted object is passed into the resulting selector's first argument,\n * the selector will act on the current draft value, instead of returning a cached value\n * that might be possibly outdated if the draft has been modified since.\n * @public\n */\nexport const createDraftSafeSelector = /* @__PURE__ */\ncreateDraftSafeSelectorCreator(weakMapMemoize);", "import { formatProdErrorMessage as _formatProdErrorMessage, formatProdErrorMessage as _formatProdErrorMessage2, formatProdErrorMessage as _formatProdErrorMessage3, formatProdErrorMessage as _formatProdErrorMessage4, formatProdErrorMessage as _formatProdErrorMessage5, formatProdErrorMessage as _formatProdErrorMessage6, formatProdErrorMessage as _formatProdErrorMessage7, formatProdErrorMessage as _formatProdErrorMessage8 } from \"@reduxjs/toolkit\";\nimport type { Reducer, ReducersMapObject, Middleware, Action, StoreEnhancer, Store, UnknownAction } from 'redux';\nimport { applyMiddleware, createStore, compose, combineReducers, isPlainObject } from 'redux';\nimport type { DevToolsEnhancerOptions as DevToolsOptions } from './devtoolsExtension';\nimport { composeWithDevTools } from './devtoolsExtension';\nimport type { ThunkMiddlewareFor, GetDefaultMiddleware } from './getDefaultMiddleware';\nimport { buildGetDefaultMiddleware } from './getDefaultMiddleware';\nimport type { ExtractDispatchExtensions, ExtractStoreExtensions, ExtractStateExtensions, UnknownIfNonSpecific } from './tsHelpers';\nimport type { Tuple } from './utils';\nimport type { GetDefaultEnhancers } from './getDefaultEnhancers';\nimport { buildGetDefaultEnhancers } from './getDefaultEnhancers';\n\n/**\n * Options for `configureStore()`.\n *\n * @public\n */\nexport interface ConfigureStoreOptions<S = any, A extends Action = UnknownAction, M extends Tuple<Middlewares<S>> = Tuple<Middlewares<S>>, E extends Tuple<Enhancers> = Tuple<Enhancers>, P = S> {\n  /**\n   * A single reducer function that will be used as the root reducer, or an\n   * object of slice reducers that will be passed to `combineReducers()`.\n   */\n  reducer: Reducer<S, A, P> | ReducersMapObject<S, A, P>;\n\n  /**\n   * An array of Redux middleware to install, or a callback receiving `getDefaultMiddleware` and returning a Tuple of middleware.\n   * If not supplied, defaults to the set of middleware returned by `getDefaultMiddleware()`.\n   *\n   * @example `middleware: (gDM) => gDM().concat(logger, apiMiddleware, yourCustomMiddleware)`\n   * @see https://redux-toolkit.js.org/api/getDefaultMiddleware#intended-usage\n   */\n  middleware?: (getDefaultMiddleware: GetDefaultMiddleware<S>) => M;\n\n  /**\n   * Whether to enable Redux DevTools integration. Defaults to `true`.\n   *\n   * Additional configuration can be done by passing Redux DevTools options\n   */\n  devTools?: boolean | DevToolsOptions;\n\n  /**\n   * Whether to check for duplicate middleware instances. Defaults to `true`.\n   */\n  duplicateMiddlewareCheck?: boolean;\n\n  /**\n   * The initial state, same as Redux's createStore.\n   * You may optionally specify it to hydrate the state\n   * from the server in universal apps, or to restore a previously serialized\n   * user session. If you use `combineReducers()` to produce the root reducer\n   * function (either directly or indirectly by passing an object as `reducer`),\n   * this must be an object with the same shape as the reducer map keys.\n   */\n  // we infer here, and instead complain if the reducer doesn't match\n  preloadedState?: P;\n\n  /**\n   * The store enhancers to apply. See Redux's `createStore()`.\n   * All enhancers will be included before the DevTools Extension enhancer.\n   * If you need to customize the order of enhancers, supply a callback\n   * function that will receive a `getDefaultEnhancers` function that returns a Tuple,\n   * and should return a Tuple of enhancers (such as `getDefaultEnhancers().concat(offline)`).\n   * If you only need to add middleware, you can use the `middleware` parameter instead.\n   */\n  enhancers?: (getDefaultEnhancers: GetDefaultEnhancers<M>) => E;\n}\nexport type Middlewares<S> = ReadonlyArray<Middleware<{}, S>>;\ntype Enhancers = ReadonlyArray<StoreEnhancer>;\n\n/**\n * A Redux store returned by `configureStore()`. Supports dispatching\n * side-effectful _thunks_ in addition to plain actions.\n *\n * @public\n */\nexport type EnhancedStore<S = any, A extends Action = UnknownAction, E extends Enhancers = Enhancers> = ExtractStoreExtensions<E> & Store<S, A, UnknownIfNonSpecific<ExtractStateExtensions<E>>>;\n\n/**\n * A friendly abstraction over the standard Redux `createStore()` function.\n *\n * @param options The store configuration.\n * @returns A configured Redux store.\n *\n * @public\n */\nexport function configureStore<S = any, A extends Action = UnknownAction, M extends Tuple<Middlewares<S>> = Tuple<[ThunkMiddlewareFor<S>]>, E extends Tuple<Enhancers> = Tuple<[StoreEnhancer<{\n  dispatch: ExtractDispatchExtensions<M>;\n}>, StoreEnhancer]>, P = S>(options: ConfigureStoreOptions<S, A, M, E, P>): EnhancedStore<S, A, E> {\n  const getDefaultMiddleware = buildGetDefaultMiddleware<S>();\n  const {\n    reducer = undefined,\n    middleware,\n    devTools = true,\n    duplicateMiddlewareCheck = true,\n    preloadedState = undefined,\n    enhancers = undefined\n  } = options || {};\n  let rootReducer: Reducer<S, A, P>;\n  if (typeof reducer === 'function') {\n    rootReducer = reducer;\n  } else if (isPlainObject(reducer)) {\n    rootReducer = combineReducers(reducer) as unknown as Reducer<S, A, P>;\n  } else {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(1) : '`reducer` is a required argument, and must be a function or an object of functions that can be passed to combineReducers');\n  }\n  if (process.env.NODE_ENV !== 'production' && middleware && typeof middleware !== 'function') {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(2) : '`middleware` field must be a callback');\n  }\n  let finalMiddleware: Tuple<Middlewares<S>>;\n  if (typeof middleware === 'function') {\n    finalMiddleware = middleware(getDefaultMiddleware);\n    if (process.env.NODE_ENV !== 'production' && !Array.isArray(finalMiddleware)) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage3(3) : 'when using a middleware builder function, an array of middleware must be returned');\n    }\n  } else {\n    finalMiddleware = getDefaultMiddleware();\n  }\n  if (process.env.NODE_ENV !== 'production' && finalMiddleware.some((item: any) => typeof item !== 'function')) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage4(4) : 'each middleware provided to configureStore must be a function');\n  }\n  if (process.env.NODE_ENV !== 'production' && duplicateMiddlewareCheck) {\n    let middlewareReferences = new Set<Middleware<any, S>>();\n    finalMiddleware.forEach(middleware => {\n      if (middlewareReferences.has(middleware)) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage5(42) : 'Duplicate middleware references found when creating the store. Ensure that each middleware is only included once.');\n      }\n      middlewareReferences.add(middleware);\n    });\n  }\n  let finalCompose = compose;\n  if (devTools) {\n    finalCompose = composeWithDevTools({\n      // Enable capture of stack traces for dispatched Redux actions\n      trace: process.env.NODE_ENV !== 'production',\n      ...(typeof devTools === 'object' && devTools)\n    });\n  }\n  const middlewareEnhancer = applyMiddleware(...finalMiddleware);\n  const getDefaultEnhancers = buildGetDefaultEnhancers<M>(middlewareEnhancer);\n  if (process.env.NODE_ENV !== 'production' && enhancers && typeof enhancers !== 'function') {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage6(5) : '`enhancers` field must be a callback');\n  }\n  let storeEnhancers = typeof enhancers === 'function' ? enhancers(getDefaultEnhancers) : getDefaultEnhancers();\n  if (process.env.NODE_ENV !== 'production' && !Array.isArray(storeEnhancers)) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage7(6) : '`enhancers` callback must return an array');\n  }\n  if (process.env.NODE_ENV !== 'production' && storeEnhancers.some((item: any) => typeof item !== 'function')) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage8(7) : 'each enhancer provided to configureStore must be a function');\n  }\n  if (process.env.NODE_ENV !== 'production' && finalMiddleware.length && !storeEnhancers.includes(middlewareEnhancer)) {\n    console.error('middlewares were provided, but middleware enhancer was not included in final enhancers - make sure to call `getDefaultEnhancers`');\n  }\n  const composedEnhancer: StoreEnhancer<any> = finalCompose(...storeEnhancers);\n  return createStore(rootReducer, preloadedState as P, composedEnhancer);\n}", "import type { Action, ActionCreator, StoreEnhancer } from 'redux';\nimport { compose } from 'redux';\n\n/**\r\n * @public\r\n */\nexport interface DevToolsEnhancerOptions {\n  /**\r\n   * the instance name to be showed on the monitor page. Default value is `document.title`.\r\n   * If not specified and there's no document title, it will consist of `tabId` and `instanceId`.\r\n   */\n  name?: string;\n  /**\r\n   * action creators functions to be available in the Dispatcher.\r\n   */\n  actionCreators?: ActionCreator<any>[] | {\n    [key: string]: ActionCreator<any>;\n  };\n  /**\r\n   * if more than one action is dispatched in the indicated interval, all new actions will be collected and sent at once.\r\n   * It is the joint between performance and speed. When set to `0`, all actions will be sent instantly.\r\n   * Set it to a higher value when experiencing perf issues (also `maxAge` to a lower value).\r\n   *\r\n   * @default 500 ms.\r\n   */\n  latency?: number;\n  /**\r\n   * (> 1) - maximum allowed actions to be stored in the history tree. The oldest actions are removed once maxAge is reached. It's critical for performance.\r\n   *\r\n   * @default 50\r\n   */\n  maxAge?: number;\n  /**\r\n   * Customizes how actions and state are serialized and deserialized. Can be a boolean or object. If given a boolean, the behavior is the same as if you\r\n   * were to pass an object and specify `options` as a boolean. Giving an object allows fine-grained customization using the `replacer` and `reviver`\r\n   * functions.\r\n   */\n  serialize?: boolean | {\n    /**\r\n     * - `undefined` - will use regular `JSON.stringify` to send data (it's the fast mode).\r\n     * - `false` - will handle also circular references.\r\n     * - `true` - will handle also date, regex, undefined, error objects, symbols, maps, sets and functions.\r\n     * - object, which contains `date`, `regex`, `undefined`, `error`, `symbol`, `map`, `set` and `function` keys.\r\n     *   For each of them you can indicate if to include (by setting as `true`).\r\n     *   For `function` key you can also specify a custom function which handles serialization.\r\n     *   See [`jsan`](https://github.com/kolodny/jsan) for more details.\r\n     */\n    options?: undefined | boolean | {\n      date?: true;\n      regex?: true;\n      undefined?: true;\n      error?: true;\n      symbol?: true;\n      map?: true;\n      set?: true;\n      function?: true | ((fn: (...args: any[]) => any) => string);\n    };\n    /**\r\n     * [JSON replacer function](https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify#The_replacer_parameter) used for both actions and states stringify.\r\n     * In addition, you can specify a data type by adding a [`__serializedType__`](https://github.com/zalmoxisus/remotedev-serialize/blob/master/helpers/index.js#L4)\r\n     * key. So you can deserialize it back while importing or persisting data.\r\n     * Moreover, it will also [show a nice preview showing the provided custom type](https://cloud.githubusercontent.com/assets/7957859/21814330/a17d556a-d761-11e6-85ef-159dd12f36c5.png):\r\n     */\n    replacer?: (key: string, value: unknown) => any;\n    /**\r\n     * [JSON `reviver` function](https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/JSON/parse#Using_the_reviver_parameter)\r\n     * used for parsing the imported actions and states. See [`remotedev-serialize`](https://github.com/zalmoxisus/remotedev-serialize/blob/master/immutable/serialize.js#L8-L41)\r\n     * as an example on how to serialize special data types and get them back.\r\n     */\n    reviver?: (key: string, value: unknown) => any;\n    /**\r\n     * Automatically serialize/deserialize immutablejs via [remotedev-serialize](https://github.com/zalmoxisus/remotedev-serialize).\r\n     * Just pass the Immutable library. It will support all ImmutableJS structures. You can even export them into a file and get them back.\r\n     * The only exception is `Record` class, for which you should pass this in addition the references to your classes in `refs`.\r\n     */\n    immutable?: any;\n    /**\r\n     * ImmutableJS `Record` classes used to make possible restore its instances back when importing, persisting...\r\n     */\n    refs?: any;\n  };\n  /**\r\n   * function which takes `action` object and id number as arguments, and should return `action` object back.\r\n   */\n  actionSanitizer?: <A extends Action>(action: A, id: number) => A;\n  /**\r\n   * function which takes `state` object and index as arguments, and should return `state` object back.\r\n   */\n  stateSanitizer?: <S>(state: S, index: number) => S;\n  /**\r\n   * *string or array of strings as regex* - actions types to be hidden / shown in the monitors (while passed to the reducers).\r\n   * If `actionsAllowlist` specified, `actionsDenylist` is ignored.\r\n   */\n  actionsDenylist?: string | string[];\n  /**\r\n   * *string or array of strings as regex* - actions types to be hidden / shown in the monitors (while passed to the reducers).\r\n   * If `actionsAllowlist` specified, `actionsDenylist` is ignored.\r\n   */\n  actionsAllowlist?: string | string[];\n  /**\r\n   * called for every action before sending, takes `state` and `action` object, and returns `true` in case it allows sending the current data to the monitor.\r\n   * Use it as a more advanced version of `actionsDenylist`/`actionsAllowlist` parameters.\r\n   */\n  predicate?: <S, A extends Action>(state: S, action: A) => boolean;\n  /**\r\n   * if specified as `false`, it will not record the changes till clicking on `Start recording` button.\r\n   * Available only for Redux enhancer, for others use `autoPause`.\r\n   *\r\n   * @default true\r\n   */\n  shouldRecordChanges?: boolean;\n  /**\r\n   * if specified, whenever clicking on `Pause recording` button and there are actions in the history log, will add this action type.\r\n   * If not specified, will commit when paused. Available only for Redux enhancer.\r\n   *\r\n   * @default \"@@PAUSED\"\"\r\n   */\n  pauseActionType?: string;\n  /**\r\n   * auto pauses when the extension’s window is not opened, and so has zero impact on your app when not in use.\r\n   * Not available for Redux enhancer (as it already does it but storing the data to be sent).\r\n   *\r\n   * @default false\r\n   */\n  autoPause?: boolean;\n  /**\r\n   * if specified as `true`, it will not allow any non-monitor actions to be dispatched till clicking on `Unlock changes` button.\r\n   * Available only for Redux enhancer.\r\n   *\r\n   * @default false\r\n   */\n  shouldStartLocked?: boolean;\n  /**\r\n   * if set to `false`, will not recompute the states on hot reloading (or on replacing the reducers). Available only for Redux enhancer.\r\n   *\r\n   * @default true\r\n   */\n  shouldHotReload?: boolean;\n  /**\r\n   * if specified as `true`, whenever there's an exception in reducers, the monitors will show the error message, and next actions will not be dispatched.\r\n   *\r\n   * @default false\r\n   */\n  shouldCatchErrors?: boolean;\n  /**\r\n   * If you want to restrict the extension, specify the features you allow.\r\n   * If not specified, all of the features are enabled. When set as an object, only those included as `true` will be allowed.\r\n   * Note that except `true`/`false`, `import` and `export` can be set as `custom` (which is by default for Redux enhancer), meaning that the importing/exporting occurs on the client side.\r\n   * Otherwise, you'll get/set the data right from the monitor part.\r\n   */\n  features?: {\n    /**\r\n     * start/pause recording of dispatched actions\r\n     */\n    pause?: boolean;\n    /**\r\n     * lock/unlock dispatching actions and side effects\r\n     */\n    lock?: boolean;\n    /**\r\n     * persist states on page reloading\r\n     */\n    persist?: boolean;\n    /**\r\n     * export history of actions in a file\r\n     */\n    export?: boolean | 'custom';\n    /**\r\n     * import history of actions from a file\r\n     */\n    import?: boolean | 'custom';\n    /**\r\n     * jump back and forth (time travelling)\r\n     */\n    jump?: boolean;\n    /**\r\n     * skip (cancel) actions\r\n     */\n    skip?: boolean;\n    /**\r\n     * drag and drop actions in the history list\r\n     */\n    reorder?: boolean;\n    /**\r\n     * dispatch custom actions or action creators\r\n     */\n    dispatch?: boolean;\n    /**\r\n     * generate tests for the selected actions\r\n     */\n    test?: boolean;\n  };\n  /**\r\n   * Set to true or a stacktrace-returning function to record call stack traces for dispatched actions.\r\n   * Defaults to false.\r\n   */\n  trace?: boolean | (<A extends Action>(action: A) => string);\n  /**\r\n   * The maximum number of stack trace entries to record per action. Defaults to 10.\r\n   */\n  traceLimit?: number;\n}\ntype Compose = typeof compose;\ninterface ComposeWithDevTools {\n  (options: DevToolsEnhancerOptions): Compose;\n  <StoreExt extends {}>(...funcs: StoreEnhancer<StoreExt>[]): StoreEnhancer<StoreExt>;\n}\n\n/**\r\n * @public\r\n */\nexport const composeWithDevTools: ComposeWithDevTools = typeof window !== 'undefined' && (window as any).__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ ? (window as any).__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ : function () {\n  if (arguments.length === 0) return undefined;\n  if (typeof arguments[0] === 'object') return compose;\n  return compose.apply(null, arguments as any as Function[]);\n};\n\n/**\r\n * @public\r\n */\nexport const devToolsEnhancer: {\n  (options: DevToolsEnhancerOptions): StoreEnhancer<any>;\n} = typeof window !== 'undefined' && (window as any).__REDUX_DEVTOOLS_EXTENSION__ ? (window as any).__REDUX_DEVTOOLS_EXTENSION__ : function () {\n  return function (noop) {\n    return noop;\n  };\n};", "import type { Middleware, UnknownAction } from 'redux';\nimport type { ThunkMiddleware } from 'redux-thunk';\nimport { thunk as thunkMiddleware, withExtraArgument } from 'redux-thunk';\nimport type { ActionCreatorInvariantMiddlewareOptions } from './actionCreatorInvariantMiddleware';\nimport { createActionCreatorInvariantMiddleware } from './actionCreatorInvariantMiddleware';\nimport type { ImmutableStateInvariantMiddlewareOptions } from './immutableStateInvariantMiddleware';\n/* PROD_START_REMOVE_UMD */\nimport { createImmutableStateInvariantMiddleware } from './immutableStateInvariantMiddleware';\n/* PROD_STOP_REMOVE_UMD */\n\nimport type { SerializableStateInvariantMiddlewareOptions } from './serializableStateInvariantMiddleware';\nimport { createSerializableStateInvariantMiddleware } from './serializableStateInvariantMiddleware';\nimport type { ExcludeFromTuple } from './tsHelpers';\nimport { Tuple } from './utils';\nfunction isBoolean(x: any): x is boolean {\n  return typeof x === 'boolean';\n}\ninterface ThunkOptions<E = any> {\n  extraArgument: E;\n}\ninterface GetDefaultMiddlewareOptions {\n  thunk?: boolean | ThunkOptions;\n  immutableCheck?: boolean | ImmutableStateInvariantMiddlewareOptions;\n  serializableCheck?: boolean | SerializableStateInvariantMiddlewareOptions;\n  actionCreatorCheck?: boolean | ActionCreatorInvariantMiddlewareOptions;\n}\nexport type ThunkMiddlewareFor<S, O extends GetDefaultMiddlewareOptions = {}> = O extends {\n  thunk: false;\n} ? never : O extends {\n  thunk: {\n    extraArgument: infer E;\n  };\n} ? ThunkMiddleware<S, UnknownAction, E> : ThunkMiddleware<S, UnknownAction>;\nexport type GetDefaultMiddleware<S = any> = <O extends GetDefaultMiddlewareOptions = {\n  thunk: true;\n  immutableCheck: true;\n  serializableCheck: true;\n  actionCreatorCheck: true;\n}>(options?: O) => Tuple<ExcludeFromTuple<[ThunkMiddlewareFor<S, O>], never>>;\nexport const buildGetDefaultMiddleware = <S = any,>(): GetDefaultMiddleware<S> => function getDefaultMiddleware(options) {\n  const {\n    thunk = true,\n    immutableCheck = true,\n    serializableCheck = true,\n    actionCreatorCheck = true\n  } = options ?? {};\n  let middlewareArray = new Tuple<Middleware[]>();\n  if (thunk) {\n    if (isBoolean(thunk)) {\n      middlewareArray.push(thunkMiddleware);\n    } else {\n      middlewareArray.push(withExtraArgument(thunk.extraArgument));\n    }\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    if (immutableCheck) {\n      /* PROD_START_REMOVE_UMD */\n      let immutableOptions: ImmutableStateInvariantMiddlewareOptions = {};\n      if (!isBoolean(immutableCheck)) {\n        immutableOptions = immutableCheck;\n      }\n      middlewareArray.unshift(createImmutableStateInvariantMiddleware(immutableOptions));\n      /* PROD_STOP_REMOVE_UMD */\n    }\n    if (serializableCheck) {\n      let serializableOptions: SerializableStateInvariantMiddlewareOptions = {};\n      if (!isBoolean(serializableCheck)) {\n        serializableOptions = serializableCheck;\n      }\n      middlewareArray.push(createSerializableStateInvariantMiddleware(serializableOptions));\n    }\n    if (actionCreatorCheck) {\n      let actionCreatorOptions: ActionCreatorInvariantMiddlewareOptions = {};\n      if (!isBoolean(actionCreatorCheck)) {\n        actionCreatorOptions = actionCreatorCheck;\n      }\n      middlewareArray.unshift(createActionCreatorInvariantMiddleware(actionCreatorOptions));\n    }\n  }\n  return middlewareArray as any;\n};", "import { formatProdErrorMessage as _formatProdErrorMessage } from \"@reduxjs/toolkit\";\nimport { isAction } from 'redux';\nimport type { IsUnknownOrNonInferrable, IfMaybeUndefined, IfVoid, IsAny } from './tsHelpers';\nimport { hasMatchFunction } from './tsHelpers';\n\n/**\n * An action with a string type and an associated payload. This is the\n * type of action returned by `createAction()` action creators.\n *\n * @template P The type of the action's payload.\n * @template T the type used for the action type.\n * @template M The type of the action's meta (optional)\n * @template E The type of the action's error (optional)\n *\n * @public\n */\nexport type PayloadAction<P = void, T extends string = string, M = never, E = never> = {\n  payload: P;\n  type: T;\n} & ([M] extends [never] ? {} : {\n  meta: M;\n}) & ([E] extends [never] ? {} : {\n  error: E;\n});\n\n/**\n * A \"prepare\" method to be used as the second parameter of `createAction`.\n * Takes any number of arguments and returns a Flux Standard Action without\n * type (will be added later) that *must* contain a payload (might be undefined).\n *\n * @public\n */\nexport type PrepareAction<P> = ((...args: any[]) => {\n  payload: P;\n}) | ((...args: any[]) => {\n  payload: P;\n  meta: any;\n}) | ((...args: any[]) => {\n  payload: P;\n  error: any;\n}) | ((...args: any[]) => {\n  payload: P;\n  meta: any;\n  error: any;\n});\n\n/**\n * Internal version of `ActionCreatorWithPreparedPayload`. Not to be used externally.\n *\n * @internal\n */\nexport type _ActionCreatorWithPreparedPayload<PA extends PrepareAction<any> | void, T extends string = string> = PA extends PrepareAction<infer P> ? ActionCreatorWithPreparedPayload<Parameters<PA>, P, T, ReturnType<PA> extends {\n  error: infer E;\n} ? E : never, ReturnType<PA> extends {\n  meta: infer M;\n} ? M : never> : void;\n\n/**\n * Basic type for all action creators.\n *\n * @inheritdoc {redux#ActionCreator}\n */\nexport type BaseActionCreator<P, T extends string, M = never, E = never> = {\n  type: T;\n  match: (action: unknown) => action is PayloadAction<P, T, M, E>;\n};\n\n/**\n * An action creator that takes multiple arguments that are passed\n * to a `PrepareAction` method to create the final Action.\n * @typeParam Args arguments for the action creator function\n * @typeParam P `payload` type\n * @typeParam T `type` name\n * @typeParam E optional `error` type\n * @typeParam M optional `meta` type\n *\n * @inheritdoc {redux#ActionCreator}\n *\n * @public\n */\nexport interface ActionCreatorWithPreparedPayload<Args extends unknown[], P, T extends string = string, E = never, M = never> extends BaseActionCreator<P, T, M, E> {\n  /**\n   * Calling this {@link redux#ActionCreator} with `Args` will return\n   * an Action with a payload of type `P` and (depending on the `PrepareAction`\n   * method used) a `meta`- and `error` property of types `M` and `E` respectively.\n   */\n  (...args: Args): PayloadAction<P, T, M, E>;\n}\n\n/**\n * An action creator of type `T` that takes an optional payload of type `P`.\n *\n * @inheritdoc {redux#ActionCreator}\n *\n * @public\n */\nexport interface ActionCreatorWithOptionalPayload<P, T extends string = string> extends BaseActionCreator<P, T> {\n  /**\n   * Calling this {@link redux#ActionCreator} with an argument will\n   * return a {@link PayloadAction} of type `T` with a payload of `P`.\n   * Calling it without an argument will return a PayloadAction with a payload of `undefined`.\n   */\n  (payload?: P): PayloadAction<P, T>;\n}\n\n/**\n * An action creator of type `T` that takes no payload.\n *\n * @inheritdoc {redux#ActionCreator}\n *\n * @public\n */\nexport interface ActionCreatorWithoutPayload<T extends string = string> extends BaseActionCreator<undefined, T> {\n  /**\n   * Calling this {@link redux#ActionCreator} will\n   * return a {@link PayloadAction} of type `T` with a payload of `undefined`\n   */\n  (noArgument: void): PayloadAction<undefined, T>;\n}\n\n/**\n * An action creator of type `T` that requires a payload of type P.\n *\n * @inheritdoc {redux#ActionCreator}\n *\n * @public\n */\nexport interface ActionCreatorWithPayload<P, T extends string = string> extends BaseActionCreator<P, T> {\n  /**\n   * Calling this {@link redux#ActionCreator} with an argument will\n   * return a {@link PayloadAction} of type `T` with a payload of `P`\n   */\n  (payload: P): PayloadAction<P, T>;\n}\n\n/**\n * An action creator of type `T` whose `payload` type could not be inferred. Accepts everything as `payload`.\n *\n * @inheritdoc {redux#ActionCreator}\n *\n * @public\n */\nexport interface ActionCreatorWithNonInferrablePayload<T extends string = string> extends BaseActionCreator<unknown, T> {\n  /**\n   * Calling this {@link redux#ActionCreator} with an argument will\n   * return a {@link PayloadAction} of type `T` with a payload\n   * of exactly the type of the argument.\n   */\n  <PT extends unknown>(payload: PT): PayloadAction<PT, T>;\n}\n\n/**\n * An action creator that produces actions with a `payload` attribute.\n *\n * @typeParam P the `payload` type\n * @typeParam T the `type` of the resulting action\n * @typeParam PA if the resulting action is preprocessed by a `prepare` method, the signature of said method.\n *\n * @public\n */\nexport type PayloadActionCreator<P = void, T extends string = string, PA extends PrepareAction<P> | void = void> = IfPrepareActionMethodProvided<PA, _ActionCreatorWithPreparedPayload<PA, T>,\n// else\nIsAny<P, ActionCreatorWithPayload<any, T>, IsUnknownOrNonInferrable<P, ActionCreatorWithNonInferrablePayload<T>,\n// else\nIfVoid<P, ActionCreatorWithoutPayload<T>,\n// else\nIfMaybeUndefined<P, ActionCreatorWithOptionalPayload<P, T>,\n// else\nActionCreatorWithPayload<P, T>>>>>>;\n\n/**\n * A utility function to create an action creator for the given action type\n * string. The action creator accepts a single argument, which will be included\n * in the action object as a field called payload. The action creator function\n * will also have its toString() overridden so that it returns the action type.\n *\n * @param type The action type to use for created actions.\n * @param prepare (optional) a method that takes any number of arguments and returns { payload } or { payload, meta }.\n *                If this is given, the resulting action creator will pass its arguments to this method to calculate payload & meta.\n *\n * @public\n */\nexport function createAction<P = void, T extends string = string>(type: T): PayloadActionCreator<P, T>;\n\n/**\n * A utility function to create an action creator for the given action type\n * string. The action creator accepts a single argument, which will be included\n * in the action object as a field called payload. The action creator function\n * will also have its toString() overridden so that it returns the action type.\n *\n * @param type The action type to use for created actions.\n * @param prepare (optional) a method that takes any number of arguments and returns { payload } or { payload, meta }.\n *                If this is given, the resulting action creator will pass its arguments to this method to calculate payload & meta.\n *\n * @public\n */\nexport function createAction<PA extends PrepareAction<any>, T extends string = string>(type: T, prepareAction: PA): PayloadActionCreator<ReturnType<PA>['payload'], T, PA>;\nexport function createAction(type: string, prepareAction?: Function): any {\n  function actionCreator(...args: any[]) {\n    if (prepareAction) {\n      let prepared = prepareAction(...args);\n      if (!prepared) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(0) : 'prepareAction did not return an object');\n      }\n      return {\n        type,\n        payload: prepared.payload,\n        ...('meta' in prepared && {\n          meta: prepared.meta\n        }),\n        ...('error' in prepared && {\n          error: prepared.error\n        })\n      };\n    }\n    return {\n      type,\n      payload: args[0]\n    };\n  }\n  actionCreator.toString = () => `${type}`;\n  actionCreator.type = type;\n  actionCreator.match = (action: unknown): action is PayloadAction => isAction(action) && action.type === type;\n  return actionCreator;\n}\n\n/**\n * Returns true if value is an RTK-like action creator, with a static type property and match method.\n */\nexport function isActionCreator(action: unknown): action is BaseActionCreator<unknown, string> & Function {\n  return typeof action === 'function' && 'type' in action &&\n  // hasMatchFunction only wants Matchers but I don't see the point in rewriting it\n  hasMatchFunction(action as any);\n}\n\n/**\n * Returns true if value is an action with a string type and valid Flux Standard Action keys.\n */\nexport function isFSA(action: unknown): action is {\n  type: string;\n  payload?: unknown;\n  error?: unknown;\n  meta?: unknown;\n} {\n  return isAction(action) && Object.keys(action).every(isValidKey);\n}\nfunction isValidKey(key: string) {\n  return ['type', 'payload', 'error', 'meta'].indexOf(key) > -1;\n}\n\n// helper types for more readable typings\n\ntype IfPrepareActionMethodProvided<PA extends PrepareAction<any> | void, True, False> = PA extends ((...args: any[]) => any) ? True : False;", "import type { Middleware, StoreEnhancer } from 'redux';\nimport type { Tuple } from './utils';\nexport function safeAssign<T extends object>(target: T, ...args: Array<Partial<NoInfer<T>>>) {\n  Object.assign(target, ...args);\n}\n\n/**\n * return True if T is `any`, otherwise return False\n * taken from https://github.com/joonhocho/tsdef\n *\n * @internal\n */\nexport type IsAny<T, True, False = never> =\n// test if we are going the left AND right path in the condition\ntrue | false extends (T extends never ? true : false) ? True : False;\nexport type CastAny<T, CastTo> = IsAny<T, CastTo, T>;\n\n/**\n * return True if T is `unknown`, otherwise return False\n * taken from https://github.com/joonhocho/tsdef\n *\n * @internal\n */\nexport type IsUnknown<T, True, False = never> = unknown extends T ? IsAny<T, False, True> : False;\nexport type FallbackIfUnknown<T, Fallback> = IsUnknown<T, Fallback, T>;\n\n/**\n * @internal\n */\nexport type IfMaybeUndefined<P, True, False> = [undefined] extends [P] ? True : False;\n\n/**\n * @internal\n */\nexport type IfVoid<P, True, False> = [void] extends [P] ? True : False;\n\n/**\n * @internal\n */\nexport type IsEmptyObj<T, True, False = never> = T extends any ? keyof T extends never ? IsUnknown<T, False, IfMaybeUndefined<T, False, IfVoid<T, False, True>>> : False : never;\n\n/**\n * returns True if TS version is above 3.5, False if below.\n * uses feature detection to detect TS version >= 3.5\n * * versions below 3.5 will return `{}` for unresolvable interference\n * * versions above will return `unknown`\n *\n * @internal\n */\nexport type AtLeastTS35<True, False> = [True, False][IsUnknown<ReturnType<<T>() => T>, 0, 1>];\n\n/**\n * @internal\n */\nexport type IsUnknownOrNonInferrable<T, True, False> = AtLeastTS35<IsUnknown<T, True, False>, IsEmptyObj<T, True, IsUnknown<T, True, False>>>;\n\n/**\n * Convert a Union type `(A|B)` to an intersection type `(A&B)`\n */\nexport type UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends ((k: infer I) => void) ? I : never;\n\n// Appears to have a convenient side effect of ignoring `never` even if that's not what you specified\nexport type ExcludeFromTuple<T, E, Acc extends unknown[] = []> = T extends [infer Head, ...infer Tail] ? ExcludeFromTuple<Tail, E, [...Acc, ...([Head] extends [E] ? [] : [Head])]> : Acc;\ntype ExtractDispatchFromMiddlewareTuple<MiddlewareTuple extends readonly any[], Acc extends {}> = MiddlewareTuple extends [infer Head, ...infer Tail] ? ExtractDispatchFromMiddlewareTuple<Tail, Acc & (Head extends Middleware<infer D> ? IsAny<D, {}, D> : {})> : Acc;\nexport type ExtractDispatchExtensions<M> = M extends Tuple<infer MiddlewareTuple> ? ExtractDispatchFromMiddlewareTuple<MiddlewareTuple, {}> : M extends ReadonlyArray<Middleware> ? ExtractDispatchFromMiddlewareTuple<[...M], {}> : never;\ntype ExtractStoreExtensionsFromEnhancerTuple<EnhancerTuple extends readonly any[], Acc extends {}> = EnhancerTuple extends [infer Head, ...infer Tail] ? ExtractStoreExtensionsFromEnhancerTuple<Tail, Acc & (Head extends StoreEnhancer<infer Ext> ? IsAny<Ext, {}, Ext> : {})> : Acc;\nexport type ExtractStoreExtensions<E> = E extends Tuple<infer EnhancerTuple> ? ExtractStoreExtensionsFromEnhancerTuple<EnhancerTuple, {}> : E extends ReadonlyArray<StoreEnhancer> ? UnionToIntersection<E[number] extends StoreEnhancer<infer Ext> ? Ext extends {} ? IsAny<Ext, {}, Ext> : {} : {}> : never;\ntype ExtractStateExtensionsFromEnhancerTuple<EnhancerTuple extends readonly any[], Acc extends {}> = EnhancerTuple extends [infer Head, ...infer Tail] ? ExtractStateExtensionsFromEnhancerTuple<Tail, Acc & (Head extends StoreEnhancer<any, infer StateExt> ? IsAny<StateExt, {}, StateExt> : {})> : Acc;\nexport type ExtractStateExtensions<E> = E extends Tuple<infer EnhancerTuple> ? ExtractStateExtensionsFromEnhancerTuple<EnhancerTuple, {}> : E extends ReadonlyArray<StoreEnhancer> ? UnionToIntersection<E[number] extends StoreEnhancer<any, infer StateExt> ? StateExt extends {} ? IsAny<StateExt, {}, StateExt> : {} : {}> : never;\n\n/**\n * Helper type. Passes T out again, but boxes it in a way that it cannot\n * \"widen\" the type by accident if it is a generic that should be inferred\n * from elsewhere.\n *\n * @internal\n */\nexport type NoInfer<T> = [T][T extends any ? 0 : never];\nexport type NonUndefined<T> = T extends undefined ? never : T;\nexport type WithRequiredProp<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>;\nexport type WithOptionalProp<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;\nexport interface TypeGuard<T> {\n  (value: any): value is T;\n}\nexport interface HasMatchFunction<T> {\n  match: TypeGuard<T>;\n}\nexport const hasMatchFunction = <T,>(v: Matcher<T>): v is HasMatchFunction<T> => {\n  return v && typeof (v as HasMatchFunction<T>).match === 'function';\n};\n\n/** @public */\nexport type Matcher<T> = HasMatchFunction<T> | TypeGuard<T>;\n\n/** @public */\nexport type ActionFromMatcher<M extends Matcher<any>> = M extends Matcher<infer T> ? T : never;\nexport type Id<T> = { [K in keyof T]: T[K] } & {};\nexport type Tail<T extends any[]> = T extends [any, ...infer Tail] ? Tail : never;\nexport type UnknownIfNonSpecific<T> = {} extends T ? unknown : T;\n\n/**\n * A Promise that will never reject.\n * @see https://github.com/reduxjs/redux-toolkit/issues/4101\n */\nexport type SafePromise<T> = Promise<T> & {\n  __linterBrands: 'SafePromise';\n};\n\n/**\n * Properly wraps a Promise as a {@link SafePromise} with .catch(fallback).\n */\nexport function asSafePromise<Resolved, Rejected>(promise: Promise<Resolved>, fallback: (error: unknown) => Rejected) {\n  return promise.catch(fallback) as SafePromise<Resolved | Rejected>;\n}", "import type { Middleware } from 'redux';\nimport { isActionCreator as isRTKAction } from './createAction';\nexport interface ActionCreatorInvariantMiddlewareOptions {\n  /**\n   * The function to identify whether a value is an action creator.\n   * The default checks for a function with a static type property and match method.\n   */\n  isActionCreator?: (action: unknown) => action is Function & {\n    type?: unknown;\n  };\n}\nexport function getMessage(type?: unknown) {\n  const splitType = type ? `${type}`.split('/') : [];\n  const actionName = splitType[splitType.length - 1] || 'actionCreator';\n  return `Detected an action creator with type \"${type || 'unknown'}\" being dispatched. \nMake sure you're calling the action creator before dispatching, i.e. \\`dispatch(${actionName}())\\` instead of \\`dispatch(${actionName})\\`. This is necessary even if the action has no payload.`;\n}\nexport function createActionCreatorInvariantMiddleware(options: ActionCreatorInvariantMiddlewareOptions = {}): Middleware {\n  if (process.env.NODE_ENV === 'production') {\n    return () => next => action => next(action);\n  }\n  const {\n    isActionCreator = isRTKAction\n  } = options;\n  return () => next => action => {\n    if (isActionCreator(action)) {\n      console.warn(getMessage(action.type));\n    }\n    return next(action);\n  };\n}", "import { produce as createNextState, isDraftable } from 'immer';\nexport function getTimeMeasureUtils(maxDelay: number, fnName: string) {\n  let elapsed = 0;\n  return {\n    measureTime<T>(fn: () => T): T {\n      const started = Date.now();\n      try {\n        return fn();\n      } finally {\n        const finished = Date.now();\n        elapsed += finished - started;\n      }\n    },\n    warnIfExceeded() {\n      if (elapsed > maxDelay) {\n        console.warn(`${fnName} took ${elapsed}ms, which is more than the warning threshold of ${maxDelay}ms. \nIf your state or actions are very large, you may want to disable the middleware as it might cause too much of a slowdown in development mode. See https://redux-toolkit.js.org/api/getDefaultMiddleware for instructions.\nIt is disabled in production builds, so you don't need to worry about that.`);\n      }\n    }\n  };\n}\nexport function delay(ms: number) {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\nexport class Tuple<Items extends ReadonlyArray<unknown> = []> extends Array<Items[number]> {\n  constructor(length: number);\n  constructor(...items: Items);\n  constructor(...items: any[]) {\n    super(...items);\n    Object.setPrototypeOf(this, Tuple.prototype);\n  }\n  static override get [Symbol.species]() {\n    return Tuple as any;\n  }\n  override concat<AdditionalItems extends ReadonlyArray<unknown>>(items: Tuple<AdditionalItems>): Tuple<[...Items, ...AdditionalItems]>;\n  override concat<AdditionalItems extends ReadonlyArray<unknown>>(items: AdditionalItems): Tuple<[...Items, ...AdditionalItems]>;\n  override concat<AdditionalItems extends ReadonlyArray<unknown>>(...items: AdditionalItems): Tuple<[...Items, ...AdditionalItems]>;\n  override concat(...arr: any[]) {\n    return super.concat.apply(this, arr);\n  }\n  prepend<AdditionalItems extends ReadonlyArray<unknown>>(items: Tuple<AdditionalItems>): Tuple<[...AdditionalItems, ...Items]>;\n  prepend<AdditionalItems extends ReadonlyArray<unknown>>(items: AdditionalItems): Tuple<[...AdditionalItems, ...Items]>;\n  prepend<AdditionalItems extends ReadonlyArray<unknown>>(...items: AdditionalItems): Tuple<[...AdditionalItems, ...Items]>;\n  prepend(...arr: any[]) {\n    if (arr.length === 1 && Array.isArray(arr[0])) {\n      return new Tuple(...arr[0].concat(this));\n    }\n    return new Tuple(...arr.concat(this));\n  }\n}\nexport function freezeDraftable<T>(val: T) {\n  return isDraftable(val) ? createNextState(val, () => {}) : val;\n}\nexport function getOrInsert<K extends object, V>(map: WeakMap<K, V>, key: K, value: V): V;\nexport function getOrInsert<K, V>(map: Map<K, V>, key: K, value: V): V;\nexport function getOrInsert<K extends object, V>(map: Map<K, V> | WeakMap<K, V>, key: K, value: V): V {\n  if (map.has(key)) return map.get(key) as V;\n  return map.set(key, value).get(key) as V;\n}\nexport function getOrInsertComputed<K extends object, V>(map: WeakMap<K, V>, key: K, compute: (key: K) => V): V;\nexport function getOrInsertComputed<K, V>(map: Map<K, V>, key: K, compute: (key: K) => V): V;\nexport function getOrInsertComputed<K extends object, V>(map: Map<K, V> | WeakMap<K, V>, key: K, compute: (key: K) => V): V {\n  if (map.has(key)) return map.get(key) as V;\n  return map.set(key, compute(key)).get(key) as V;\n}\nexport function promiseWithResolvers<T>(): {\n  promise: Promise<T>;\n  resolve: (value: T | PromiseLike<T>) => void;\n  reject: (reason?: any) => void;\n} {\n  let resolve: any;\n  let reject: any;\n  const promise = new Promise<T>((res, rej) => {\n    resolve = res;\n    reject = rej;\n  });\n  return {\n    promise,\n    resolve,\n    reject\n  };\n}", "import { formatProdErrorMessage as _formatProdErrorMessage, formatProdErrorMessage as _formatProdErrorMessage2 } from \"@reduxjs/toolkit\";\nimport type { Middleware } from 'redux';\nimport type { IgnorePaths } from './serializableStateInvariantMiddleware';\nimport { getTimeMeasureUtils } from './utils';\ntype EntryProcessor = (key: string, value: any) => any;\n\n/**\n * The default `isImmutable` function.\n *\n * @public\n */\nexport function isImmutableDefault(value: unknown): boolean {\n  return typeof value !== 'object' || value == null || Object.isFrozen(value);\n}\nexport function trackForMutations(isImmutable: IsImmutableFunc, ignorePaths: IgnorePaths | undefined, obj: any) {\n  const trackedProperties = trackProperties(isImmutable, ignorePaths, obj);\n  return {\n    detectMutations() {\n      return detectMutations(isImmutable, ignorePaths, trackedProperties, obj);\n    }\n  };\n}\ninterface TrackedProperty {\n  value: any;\n  children: Record<string, any>;\n}\nfunction trackProperties(isImmutable: IsImmutableFunc, ignorePaths: IgnorePaths = [], obj: Record<string, any>, path: string = '', checkedObjects: Set<Record<string, any>> = new Set()) {\n  const tracked: Partial<TrackedProperty> = {\n    value: obj\n  };\n  if (!isImmutable(obj) && !checkedObjects.has(obj)) {\n    checkedObjects.add(obj);\n    tracked.children = {};\n    for (const key in obj) {\n      const childPath = path ? path + '.' + key : key;\n      if (ignorePaths.length && ignorePaths.indexOf(childPath) !== -1) {\n        continue;\n      }\n      tracked.children[key] = trackProperties(isImmutable, ignorePaths, obj[key], childPath);\n    }\n  }\n  return tracked as TrackedProperty;\n}\nfunction detectMutations(isImmutable: IsImmutableFunc, ignoredPaths: IgnorePaths = [], trackedProperty: TrackedProperty, obj: any, sameParentRef: boolean = false, path: string = ''): {\n  wasMutated: boolean;\n  path?: string;\n} {\n  const prevObj = trackedProperty ? trackedProperty.value : undefined;\n  const sameRef = prevObj === obj;\n  if (sameParentRef && !sameRef && !Number.isNaN(obj)) {\n    return {\n      wasMutated: true,\n      path\n    };\n  }\n  if (isImmutable(prevObj) || isImmutable(obj)) {\n    return {\n      wasMutated: false\n    };\n  }\n\n  // Gather all keys from prev (tracked) and after objs\n  const keysToDetect: Record<string, boolean> = {};\n  for (let key in trackedProperty.children) {\n    keysToDetect[key] = true;\n  }\n  for (let key in obj) {\n    keysToDetect[key] = true;\n  }\n  const hasIgnoredPaths = ignoredPaths.length > 0;\n  for (let key in keysToDetect) {\n    const nestedPath = path ? path + '.' + key : key;\n    if (hasIgnoredPaths) {\n      const hasMatches = ignoredPaths.some(ignored => {\n        if (ignored instanceof RegExp) {\n          return ignored.test(nestedPath);\n        }\n        return nestedPath === ignored;\n      });\n      if (hasMatches) {\n        continue;\n      }\n    }\n    const result = detectMutations(isImmutable, ignoredPaths, trackedProperty.children[key], obj[key], sameRef, nestedPath);\n    if (result.wasMutated) {\n      return result;\n    }\n  }\n  return {\n    wasMutated: false\n  };\n}\ntype IsImmutableFunc = (value: any) => boolean;\n\n/**\n * Options for `createImmutableStateInvariantMiddleware()`.\n *\n * @public\n */\nexport interface ImmutableStateInvariantMiddlewareOptions {\n  /**\n    Callback function to check if a value is considered to be immutable.\n    This function is applied recursively to every value contained in the state.\n    The default implementation will return true for primitive types\n    (like numbers, strings, booleans, null and undefined).\n   */\n  isImmutable?: IsImmutableFunc;\n  /**\n    An array of dot-separated path strings that match named nodes from\n    the root state to ignore when checking for immutability.\n    Defaults to undefined\n   */\n  ignoredPaths?: IgnorePaths;\n  /** Print a warning if checks take longer than N ms. Default: 32ms */\n  warnAfter?: number;\n}\n\n/**\n * Creates a middleware that checks whether any state was mutated in between\n * dispatches or during a dispatch. If any mutations are detected, an error is\n * thrown.\n *\n * @param options Middleware options.\n *\n * @public\n */\nexport function createImmutableStateInvariantMiddleware(options: ImmutableStateInvariantMiddlewareOptions = {}): Middleware {\n  if (process.env.NODE_ENV === 'production') {\n    return () => next => action => next(action);\n  } else {\n    function stringify(obj: any, serializer?: EntryProcessor, indent?: string | number, decycler?: EntryProcessor): string {\n      return JSON.stringify(obj, getSerialize(serializer, decycler), indent);\n    }\n    function getSerialize(serializer?: EntryProcessor, decycler?: EntryProcessor): EntryProcessor {\n      let stack: any[] = [],\n        keys: any[] = [];\n      if (!decycler) decycler = function (_: string, value: any) {\n        if (stack[0] === value) return '[Circular ~]';\n        return '[Circular ~.' + keys.slice(0, stack.indexOf(value)).join('.') + ']';\n      };\n      return function (this: any, key: string, value: any) {\n        if (stack.length > 0) {\n          var thisPos = stack.indexOf(this);\n          ~thisPos ? stack.splice(thisPos + 1) : stack.push(this);\n          ~thisPos ? keys.splice(thisPos, Infinity, key) : keys.push(key);\n          if (~stack.indexOf(value)) value = decycler!.call(this, key, value);\n        } else stack.push(value);\n        return serializer == null ? value : serializer.call(this, key, value);\n      };\n    }\n    let {\n      isImmutable = isImmutableDefault,\n      ignoredPaths,\n      warnAfter = 32\n    } = options;\n    const track = trackForMutations.bind(null, isImmutable, ignoredPaths);\n    return ({\n      getState\n    }) => {\n      let state = getState();\n      let tracker = track(state);\n      let result;\n      return next => action => {\n        const measureUtils = getTimeMeasureUtils(warnAfter, 'ImmutableStateInvariantMiddleware');\n        measureUtils.measureTime(() => {\n          state = getState();\n          result = tracker.detectMutations();\n          // Track before potentially not meeting the invariant\n          tracker = track(state);\n          if (result.wasMutated) {\n            throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(19) : `A state mutation was detected between dispatches, in the path '${result.path || ''}'.  This may cause incorrect behavior. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)`);\n          }\n        });\n        const dispatchedAction = next(action);\n        measureUtils.measureTime(() => {\n          state = getState();\n          result = tracker.detectMutations();\n          // Track before potentially not meeting the invariant\n          tracker = track(state);\n          if (result.wasMutated) {\n            throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(20) : `A state mutation was detected inside a dispatch, in the path: ${result.path || ''}. Take a look at the reducer(s) handling the action ${stringify(action)}. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)`);\n          }\n        });\n        measureUtils.warnIfExceeded();\n        return dispatchedAction;\n      };\n    };\n  }\n}", "import type { Middleware } from 'redux';\nimport { isAction, isPlainObject } from 'redux';\nimport { getTimeMeasureUtils } from './utils';\n\n/**\n * Returns true if the passed value is \"plain\", i.e. a value that is either\n * directly JSON-serializable (boolean, number, string, array, plain object)\n * or `undefined`.\n *\n * @param val The value to check.\n *\n * @public\n */\nexport function isPlain(val: any) {\n  const type = typeof val;\n  return val == null || type === 'string' || type === 'boolean' || type === 'number' || Array.isArray(val) || isPlainObject(val);\n}\ninterface NonSerializableValue {\n  keyPath: string;\n  value: unknown;\n}\nexport type IgnorePaths = readonly (string | RegExp)[];\n\n/**\n * @public\n */\nexport function findNonSerializableValue(value: unknown, path: string = '', isSerializable: (value: unknown) => boolean = isPlain, getEntries?: (value: unknown) => [string, any][], ignoredPaths: IgnorePaths = [], cache?: WeakSet<object>): NonSerializableValue | false {\n  let foundNestedSerializable: NonSerializableValue | false;\n  if (!isSerializable(value)) {\n    return {\n      keyPath: path || '<root>',\n      value: value\n    };\n  }\n  if (typeof value !== 'object' || value === null) {\n    return false;\n  }\n  if (cache?.has(value)) return false;\n  const entries = getEntries != null ? getEntries(value) : Object.entries(value);\n  const hasIgnoredPaths = ignoredPaths.length > 0;\n  for (const [key, nestedValue] of entries) {\n    const nestedPath = path ? path + '.' + key : key;\n    if (hasIgnoredPaths) {\n      const hasMatches = ignoredPaths.some(ignored => {\n        if (ignored instanceof RegExp) {\n          return ignored.test(nestedPath);\n        }\n        return nestedPath === ignored;\n      });\n      if (hasMatches) {\n        continue;\n      }\n    }\n    if (!isSerializable(nestedValue)) {\n      return {\n        keyPath: nestedPath,\n        value: nestedValue\n      };\n    }\n    if (typeof nestedValue === 'object') {\n      foundNestedSerializable = findNonSerializableValue(nestedValue, nestedPath, isSerializable, getEntries, ignoredPaths, cache);\n      if (foundNestedSerializable) {\n        return foundNestedSerializable;\n      }\n    }\n  }\n  if (cache && isNestedFrozen(value)) cache.add(value);\n  return false;\n}\nexport function isNestedFrozen(value: object) {\n  if (!Object.isFrozen(value)) return false;\n  for (const nestedValue of Object.values(value)) {\n    if (typeof nestedValue !== 'object' || nestedValue === null) continue;\n    if (!isNestedFrozen(nestedValue)) return false;\n  }\n  return true;\n}\n\n/**\n * Options for `createSerializableStateInvariantMiddleware()`.\n *\n * @public\n */\nexport interface SerializableStateInvariantMiddlewareOptions {\n  /**\n   * The function to check if a value is considered serializable. This\n   * function is applied recursively to every value contained in the\n   * state. Defaults to `isPlain()`.\n   */\n  isSerializable?: (value: any) => boolean;\n  /**\n   * The function that will be used to retrieve entries from each\n   * value.  If unspecified, `Object.entries` will be used. Defaults\n   * to `undefined`.\n   */\n  getEntries?: (value: any) => [string, any][];\n\n  /**\n   * An array of action types to ignore when checking for serializability.\n   * Defaults to []\n   */\n  ignoredActions?: string[];\n\n  /**\n   * An array of dot-separated path strings or regular expressions to ignore\n   * when checking for serializability, Defaults to\n   * ['meta.arg', 'meta.baseQueryMeta']\n   */\n  ignoredActionPaths?: (string | RegExp)[];\n\n  /**\n   * An array of dot-separated path strings or regular expressions to ignore\n   * when checking for serializability, Defaults to []\n   */\n  ignoredPaths?: (string | RegExp)[];\n  /**\n   * Execution time warning threshold. If the middleware takes longer\n   * than `warnAfter` ms, a warning will be displayed in the console.\n   * Defaults to 32ms.\n   */\n  warnAfter?: number;\n\n  /**\n   * Opt out of checking state. When set to `true`, other state-related params will be ignored.\n   */\n  ignoreState?: boolean;\n\n  /**\n   * Opt out of checking actions. When set to `true`, other action-related params will be ignored.\n   */\n  ignoreActions?: boolean;\n\n  /**\n   * Opt out of caching the results. The cache uses a WeakSet and speeds up repeated checking processes.\n   * The cache is automatically disabled if no browser support for WeakSet is present.\n   */\n  disableCache?: boolean;\n}\n\n/**\n * Creates a middleware that, after every state change, checks if the new\n * state is serializable. If a non-serializable value is found within the\n * state, an error is printed to the console.\n *\n * @param options Middleware options.\n *\n * @public\n */\nexport function createSerializableStateInvariantMiddleware(options: SerializableStateInvariantMiddlewareOptions = {}): Middleware {\n  if (process.env.NODE_ENV === 'production') {\n    return () => next => action => next(action);\n  } else {\n    const {\n      isSerializable = isPlain,\n      getEntries,\n      ignoredActions = [],\n      ignoredActionPaths = ['meta.arg', 'meta.baseQueryMeta'],\n      ignoredPaths = [],\n      warnAfter = 32,\n      ignoreState = false,\n      ignoreActions = false,\n      disableCache = false\n    } = options;\n    const cache: WeakSet<object> | undefined = !disableCache && WeakSet ? new WeakSet() : undefined;\n    return storeAPI => next => action => {\n      if (!isAction(action)) {\n        return next(action);\n      }\n      const result = next(action);\n      const measureUtils = getTimeMeasureUtils(warnAfter, 'SerializableStateInvariantMiddleware');\n      if (!ignoreActions && !(ignoredActions.length && ignoredActions.indexOf(action.type as any) !== -1)) {\n        measureUtils.measureTime(() => {\n          const foundActionNonSerializableValue = findNonSerializableValue(action, '', isSerializable, getEntries, ignoredActionPaths, cache);\n          if (foundActionNonSerializableValue) {\n            const {\n              keyPath,\n              value\n            } = foundActionNonSerializableValue;\n            console.error(`A non-serializable value was detected in an action, in the path: \\`${keyPath}\\`. Value:`, value, '\\nTake a look at the logic that dispatched this action: ', action, '\\n(See https://redux.js.org/faq/actions#why-should-type-be-a-string-or-at-least-serializable-why-should-my-action-types-be-constants)', '\\n(To allow non-serializable values see: https://redux-toolkit.js.org/usage/usage-guide#working-with-non-serializable-data)');\n          }\n        });\n      }\n      if (!ignoreState) {\n        measureUtils.measureTime(() => {\n          const state = storeAPI.getState();\n          const foundStateNonSerializableValue = findNonSerializableValue(state, '', isSerializable, getEntries, ignoredPaths, cache);\n          if (foundStateNonSerializableValue) {\n            const {\n              keyPath,\n              value\n            } = foundStateNonSerializableValue;\n            console.error(`A non-serializable value was detected in the state, in the path: \\`${keyPath}\\`. Value:`, value, `\nTake a look at the reducer(s) handling this action type: ${action.type}.\n(See https://redux.js.org/faq/organizing-state#can-i-put-functions-promises-or-other-non-serializable-items-in-my-store-state)`);\n          }\n        });\n        measureUtils.warnIfExceeded();\n      }\n      return result;\n    };\n  }\n}", "import type { StoreEnhancer } from 'redux';\nexport const SHOULD_AUTOBATCH = 'RTK_autoBatch';\nexport const prepareAutoBatched = <T,>() => (payload: T): {\n  payload: T;\n  meta: unknown;\n} => ({\n  payload,\n  meta: {\n    [SHOULD_AUTOBATCH]: true\n  }\n});\nconst createQueueWithTimer = (timeout: number) => {\n  return (notify: () => void) => {\n    setTimeout(notify, timeout);\n  };\n};\nexport type AutoBatchOptions = {\n  type: 'tick';\n} | {\n  type: 'timer';\n  timeout: number;\n} | {\n  type: 'raf';\n} | {\n  type: 'callback';\n  queueNotification: (notify: () => void) => void;\n};\n\n/**\n * A Redux store enhancer that watches for \"low-priority\" actions, and delays\n * notifying subscribers until either the queued callback executes or the\n * next \"standard-priority\" action is dispatched.\n *\n * This allows dispatching multiple \"low-priority\" actions in a row with only\n * a single subscriber notification to the UI after the sequence of actions\n * is finished, thus improving UI re-render performance.\n *\n * Watches for actions with the `action.meta[SHOULD_AUTOBATCH]` attribute.\n * This can be added to `action.meta` manually, or by using the\n * `prepareAutoBatched` helper.\n *\n * By default, it will queue a notification for the end of the event loop tick.\n * However, you can pass several other options to configure the behavior:\n * - `{type: 'tick'}`: queues using `queueMicrotask`\n * - `{type: 'timer', timeout: number}`: queues using `setTimeout`\n * - `{type: 'raf'}`: queues using `requestAnimationFrame` (default)\n * - `{type: 'callback', queueNotification: (notify: () => void) => void}`: lets you provide your own callback\n *\n *\n */\nexport const autoBatchEnhancer = (options: AutoBatchOptions = {\n  type: 'raf'\n}): StoreEnhancer => next => (...args) => {\n  const store = next(...args);\n  let notifying = true;\n  let shouldNotifyAtEndOfTick = false;\n  let notificationQueued = false;\n  const listeners = new Set<() => void>();\n  const queueCallback = options.type === 'tick' ? queueMicrotask : options.type === 'raf' ?\n  // requestAnimationFrame won't exist in SSR environments. Fall back to a vague approximation just to keep from erroring.\n  typeof window !== 'undefined' && window.requestAnimationFrame ? window.requestAnimationFrame : createQueueWithTimer(10) : options.type === 'callback' ? options.queueNotification : createQueueWithTimer(options.timeout);\n  const notifyListeners = () => {\n    // We're running at the end of the event loop tick.\n    // Run the real listener callbacks to actually update the UI.\n    notificationQueued = false;\n    if (shouldNotifyAtEndOfTick) {\n      shouldNotifyAtEndOfTick = false;\n      listeners.forEach(l => l());\n    }\n  };\n  return Object.assign({}, store, {\n    // Override the base `store.subscribe` method to keep original listeners\n    // from running if we're delaying notifications\n    subscribe(listener: () => void) {\n      // Each wrapped listener will only call the real listener if\n      // the `notifying` flag is currently active when it's called.\n      // This lets the base store work as normal, while the actual UI\n      // update becomes controlled by this enhancer.\n      const wrappedListener: typeof listener = () => notifying && listener();\n      const unsubscribe = store.subscribe(wrappedListener);\n      listeners.add(listener);\n      return () => {\n        unsubscribe();\n        listeners.delete(listener);\n      };\n    },\n    // Override the base `store.dispatch` method so that we can check actions\n    // for the `shouldAutoBatch` flag and determine if batching is active\n    dispatch(action: any) {\n      try {\n        // If the action does _not_ have the `shouldAutoBatch` flag,\n        // we resume/continue normal notify-after-each-dispatch behavior\n        notifying = !action?.meta?.[SHOULD_AUTOBATCH];\n        // If a `notifyListeners` microtask was queued, you can't cancel it.\n        // Instead, we set a flag so that it's a no-op when it does run\n        shouldNotifyAtEndOfTick = !notifying;\n        if (shouldNotifyAtEndOfTick) {\n          // We've seen at least 1 action with `SHOULD_AUTOBATCH`. Try to queue\n          // a microtask to notify listeners at the end of the event loop tick.\n          // Make sure we only enqueue this _once_ per tick.\n          if (!notificationQueued) {\n            notificationQueued = true;\n            queueCallback(notifyListeners);\n          }\n        }\n        // Go ahead and process the action as usual, including reducers.\n        // If normal notification behavior is enabled, the store will notify\n        // all of its own listeners, and the wrapper callbacks above will\n        // see `notifying` is true and pass on to the real listener callbacks.\n        // If we're \"batching\" behavior, then the wrapped callbacks will\n        // bail out, causing the base store notification behavior to be no-ops.\n        return store.dispatch(action);\n      } finally {\n        // Assume we're back to normal behavior after each action\n        notifying = true;\n      }\n    }\n  });\n};", "import type { StoreEnhancer } from 'redux';\nimport type { AutoBatchOptions } from './autoBatchEnhancer';\nimport { autoBatchEnhancer } from './autoBatchEnhancer';\nimport { Tuple } from './utils';\nimport type { Middlewares } from './configureStore';\nimport type { ExtractDispatchExtensions } from './tsHelpers';\ntype GetDefaultEnhancersOptions = {\n  autoBatch?: boolean | AutoBatchOptions;\n};\nexport type GetDefaultEnhancers<M extends Middlewares<any>> = (options?: GetDefaultEnhancersOptions) => Tuple<[StoreEnhancer<{\n  dispatch: ExtractDispatchExtensions<M>;\n}>]>;\nexport const buildGetDefaultEnhancers = <M extends Middlewares<any>,>(middlewareEnhancer: StoreEnhancer<{\n  dispatch: ExtractDispatchExtensions<M>;\n}>): GetDefaultEnhancers<M> => function getDefaultEnhancers(options) {\n  const {\n    autoBatch = true\n  } = options ?? {};\n  let enhancerArray = new Tuple<StoreEnhancer[]>(middlewareEnhancer);\n  if (autoBatch) {\n    enhancerArray.push(autoBatchEnhancer(typeof autoBatch === 'object' ? autoBatch : undefined));\n  }\n  return enhancerArray as any;\n};", "import { formatProdErrorMessage as _formatProdErrorMessage } from \"@reduxjs/toolkit\";\nimport type { Draft } from 'immer';\nimport { produce as createNextState, isDraft, isDraftable } from 'immer';\nimport type { Action, Reducer, UnknownAction } from 'redux';\nimport type { ActionReducerMapBuilder } from './mapBuilders';\nimport { executeReducerBuilderCallback } from './mapBuilders';\nimport type { NoInfer, TypeGuard } from './tsHelpers';\nimport { freezeDraftable } from './utils';\n\n/**\n * Defines a mapping from action types to corresponding action object shapes.\n *\n * @deprecated This should not be used manually - it is only used for internal\n *             inference purposes and should not have any further value.\n *             It might be removed in the future.\n * @public\n */\nexport type Actions<T extends keyof any = string> = Record<T, Action>;\nexport type ActionMatcherDescription<S, A extends Action> = {\n  matcher: TypeGuard<A>;\n  reducer: CaseReducer<S, NoInfer<A>>;\n};\nexport type ReadonlyActionMatcherDescriptionCollection<S> = ReadonlyArray<ActionMatcherDescription<S, any>>;\nexport type ActionMatcherDescriptionCollection<S> = Array<ActionMatcherDescription<S, any>>;\n\n/**\n * A *case reducer* is a reducer function for a specific action type. Case\n * reducers can be composed to full reducers using `createReducer()`.\n *\n * Unlike a normal Redux reducer, a case reducer is never called with an\n * `undefined` state to determine the initial state. Instead, the initial\n * state is explicitly specified as an argument to `createReducer()`.\n *\n * In addition, a case reducer can choose to mutate the passed-in `state`\n * value directly instead of returning a new state. This does not actually\n * cause the store state to be mutated directly; instead, thanks to\n * [immer](https://github.com/mweststrate/immer), the mutations are\n * translated to copy operations that result in a new state.\n *\n * @public\n */\nexport type CaseReducer<S = any, A extends Action = UnknownAction> = (state: Draft<S>, action: A) => NoInfer<S> | void | Draft<NoInfer<S>>;\n\n/**\n * A mapping from action types to case reducers for `createReducer()`.\n *\n * @deprecated This should not be used manually - it is only used\n *             for internal inference purposes and using it manually\n *             would lead to type erasure.\n *             It might be removed in the future.\n * @public\n */\nexport type CaseReducers<S, AS extends Actions> = { [T in keyof AS]: AS[T] extends Action ? CaseReducer<S, AS[T]> : void };\nexport type NotFunction<T> = T extends Function ? never : T;\nfunction isStateFunction<S>(x: unknown): x is () => S {\n  return typeof x === 'function';\n}\nexport type ReducerWithInitialState<S extends NotFunction<any>> = Reducer<S> & {\n  getInitialState: () => S;\n};\n\n/**\n * A utility function that allows defining a reducer as a mapping from action\n * type to *case reducer* functions that handle these action types. The\n * reducer's initial state is passed as the first argument.\n *\n * @remarks\n * The body of every case reducer is implicitly wrapped with a call to\n * `produce()` from the [immer](https://github.com/mweststrate/immer) library.\n * This means that rather than returning a new state object, you can also\n * mutate the passed-in state object directly; these mutations will then be\n * automatically and efficiently translated into copies, giving you both\n * convenience and immutability.\n *\n * @overloadSummary\n * This function accepts a callback that receives a `builder` object as its argument.\n * That builder provides `addCase`, `addMatcher` and `addDefaultCase` functions that may be\n * called to define what actions this reducer will handle.\n *\n * @param initialState - `State | (() => State)`: The initial state that should be used when the reducer is called the first time. This may also be a \"lazy initializer\" function, which should return an initial state value when called. This will be used whenever the reducer is called with `undefined` as its state value, and is primarily useful for cases like reading initial state from `localStorage`.\n * @param builderCallback - `(builder: Builder) => void` A callback that receives a *builder* object to define\n *   case reducers via calls to `builder.addCase(actionCreatorOrType, reducer)`.\n * @example\n```ts\nimport {\n  createAction,\n  createReducer,\n  UnknownAction,\n  PayloadAction,\n} from \"@reduxjs/toolkit\";\n\nconst increment = createAction<number>(\"increment\");\nconst decrement = createAction<number>(\"decrement\");\n\nfunction isActionWithNumberPayload(\n  action: UnknownAction\n): action is PayloadAction<number> {\n  return typeof action.payload === \"number\";\n}\n\nconst reducer = createReducer(\n  {\n    counter: 0,\n    sumOfNumberPayloads: 0,\n    unhandledActions: 0,\n  },\n  (builder) => {\n    builder\n      .addCase(increment, (state, action) => {\n        // action is inferred correctly here\n        state.counter += action.payload;\n      })\n      // You can chain calls, or have separate `builder.addCase()` lines each time\n      .addCase(decrement, (state, action) => {\n        state.counter -= action.payload;\n      })\n      // You can apply a \"matcher function\" to incoming actions\n      .addMatcher(isActionWithNumberPayload, (state, action) => {})\n      // and provide a default case if no other handlers matched\n      .addDefaultCase((state, action) => {});\n  }\n);\n```\n * @public\n */\nexport function createReducer<S extends NotFunction<any>>(initialState: S | (() => S), mapOrBuilderCallback: (builder: ActionReducerMapBuilder<S>) => void): ReducerWithInitialState<S> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof mapOrBuilderCallback === 'object') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(8) : \"The object notation for `createReducer` has been removed. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createReducer\");\n    }\n  }\n  let [actionsMap, finalActionMatchers, finalDefaultCaseReducer] = executeReducerBuilderCallback(mapOrBuilderCallback);\n\n  // Ensure the initial state gets frozen either way (if draftable)\n  let getInitialState: () => S;\n  if (isStateFunction(initialState)) {\n    getInitialState = () => freezeDraftable(initialState());\n  } else {\n    const frozenInitialState = freezeDraftable(initialState);\n    getInitialState = () => frozenInitialState;\n  }\n  function reducer(state = getInitialState(), action: any): S {\n    let caseReducers = [actionsMap[action.type], ...finalActionMatchers.filter(({\n      matcher\n    }) => matcher(action)).map(({\n      reducer\n    }) => reducer)];\n    if (caseReducers.filter(cr => !!cr).length === 0) {\n      caseReducers = [finalDefaultCaseReducer];\n    }\n    return caseReducers.reduce((previousState, caseReducer): S => {\n      if (caseReducer) {\n        if (isDraft(previousState)) {\n          // If it's already a draft, we must already be inside a `createNextState` call,\n          // likely because this is being wrapped in `createReducer`, `createSlice`, or nested\n          // inside an existing draft. It's safe to just pass the draft to the mutator.\n          const draft = previousState as Draft<S>; // We can assume this is already a draft\n          const result = caseReducer(draft, action);\n          if (result === undefined) {\n            return previousState;\n          }\n          return result as S;\n        } else if (!isDraftable(previousState)) {\n          // If state is not draftable (ex: a primitive, such as 0), we want to directly\n          // return the caseReducer func and not wrap it with produce.\n          const result = caseReducer(previousState as any, action);\n          if (result === undefined) {\n            if (previousState === null) {\n              return previousState;\n            }\n            throw Error('A case reducer on a non-draftable value must not return undefined');\n          }\n          return result as S;\n        } else {\n          // @ts-ignore createNextState() produces an Immutable<Draft<S>> rather\n          // than an Immutable<S>, and TypeScript cannot find out how to reconcile\n          // these two types.\n          return createNextState(previousState, (draft: Draft<S>) => {\n            return caseReducer(draft, action);\n          });\n        }\n      }\n      return previousState;\n    }, state);\n  }\n  reducer.getInitialState = getInitialState;\n  return reducer as ReducerWithInitialState<S>;\n}", "import { formatProdErrorMessage as _formatProdErrorMessage, formatProdErrorMessage as _formatProdErrorMessage2, formatProdErrorMessage as _formatProdErrorMessage3, formatProdErrorMessage as _formatProdErrorMessage4, formatProdErrorMessage as _formatProdErrorMessage5, formatProdErrorMessage as _formatProdErrorMessage6 } from \"@reduxjs/toolkit\";\nimport type { Action } from 'redux';\nimport type { CaseReducer, CaseReducers, ActionMatcherDescriptionCollection } from './createReducer';\nimport type { TypeGuard } from './tsHelpers';\nexport type TypedActionCreator<Type extends string> = {\n  (...args: any[]): Action<Type>;\n  type: Type;\n};\n\n/**\n * A builder for an action <-> reducer map.\n *\n * @public\n */\nexport interface ActionReducerMapBuilder<State> {\n  /**\n   * Adds a case reducer to handle a single exact action type.\n   * @remarks\n   * All calls to `builder.addCase` must come before any calls to `builder.addMatcher` or `builder.addDefaultCase`.\n   * @param actionCreator - Either a plain action type string, or an action creator generated by [`createAction`](./createAction) that can be used to determine the action type.\n   * @param reducer - The actual case reducer function.\n   */\n  addCase<ActionCreator extends TypedActionCreator<string>>(actionCreator: ActionCreator, reducer: CaseReducer<State, ReturnType<ActionCreator>>): ActionReducerMapBuilder<State>;\n  /**\n   * Adds a case reducer to handle a single exact action type.\n   * @remarks\n   * All calls to `builder.addCase` must come before any calls to `builder.addMatcher` or `builder.addDefaultCase`.\n   * @param actionCreator - Either a plain action type string, or an action creator generated by [`createAction`](./createAction) that can be used to determine the action type.\n   * @param reducer - The actual case reducer function.\n   */\n  addCase<Type extends string, A extends Action<Type>>(type: Type, reducer: CaseReducer<State, A>): ActionReducerMapBuilder<State>;\n\n  /**\n   * Allows you to match your incoming actions against your own filter function instead of only the `action.type` property.\n   * @remarks\n   * If multiple matcher reducers match, all of them will be executed in the order\n   * they were defined in - even if a case reducer already matched.\n   * All calls to `builder.addMatcher` must come after any calls to `builder.addCase` and before any calls to `builder.addDefaultCase`.\n   * @param matcher - A matcher function. In TypeScript, this should be a [type predicate](https://www.typescriptlang.org/docs/handbook/2/narrowing.html#using-type-predicates)\n   *   function\n   * @param reducer - The actual case reducer function.\n   *\n   * @example\n  ```ts\n  import {\n  createAction,\n  createReducer,\n  AsyncThunk,\n  UnknownAction,\n  } from \"@reduxjs/toolkit\";\n  type GenericAsyncThunk = AsyncThunk<unknown, unknown, any>;\n  type PendingAction = ReturnType<GenericAsyncThunk[\"pending\"]>;\n  type RejectedAction = ReturnType<GenericAsyncThunk[\"rejected\"]>;\n  type FulfilledAction = ReturnType<GenericAsyncThunk[\"fulfilled\"]>;\n  const initialState: Record<string, string> = {};\n  const resetAction = createAction(\"reset-tracked-loading-state\");\n  function isPendingAction(action: UnknownAction): action is PendingAction {\n  return typeof action.type === \"string\" && action.type.endsWith(\"/pending\");\n  }\n  const reducer = createReducer(initialState, (builder) => {\n  builder\n    .addCase(resetAction, () => initialState)\n    // matcher can be defined outside as a type predicate function\n    .addMatcher(isPendingAction, (state, action) => {\n      state[action.meta.requestId] = \"pending\";\n    })\n    .addMatcher(\n      // matcher can be defined inline as a type predicate function\n      (action): action is RejectedAction => action.type.endsWith(\"/rejected\"),\n      (state, action) => {\n        state[action.meta.requestId] = \"rejected\";\n      }\n    )\n    // matcher can just return boolean and the matcher can receive a generic argument\n    .addMatcher<FulfilledAction>(\n      (action) => action.type.endsWith(\"/fulfilled\"),\n      (state, action) => {\n        state[action.meta.requestId] = \"fulfilled\";\n      }\n    );\n  });\n  ```\n   */\n  addMatcher<A>(matcher: TypeGuard<A> | ((action: any) => boolean), reducer: CaseReducer<State, A extends Action ? A : A & Action>): Omit<ActionReducerMapBuilder<State>, 'addCase'>;\n\n  /**\n   * Adds a \"default case\" reducer that is executed if no case reducer and no matcher\n   * reducer was executed for this action.\n   * @param reducer - The fallback \"default case\" reducer function.\n   *\n   * @example\n  ```ts\n  import { createReducer } from '@reduxjs/toolkit'\n  const initialState = { otherActions: 0 }\n  const reducer = createReducer(initialState, builder => {\n  builder\n    // .addCase(...)\n    // .addMatcher(...)\n    .addDefaultCase((state, action) => {\n      state.otherActions++\n    })\n  })\n  ```\n   */\n  addDefaultCase(reducer: CaseReducer<State, Action>): {};\n}\nexport function executeReducerBuilderCallback<S>(builderCallback: (builder: ActionReducerMapBuilder<S>) => void): [CaseReducers<S, any>, ActionMatcherDescriptionCollection<S>, CaseReducer<S, Action> | undefined] {\n  const actionsMap: CaseReducers<S, any> = {};\n  const actionMatchers: ActionMatcherDescriptionCollection<S> = [];\n  let defaultCaseReducer: CaseReducer<S, Action> | undefined;\n  const builder = {\n    addCase(typeOrActionCreator: string | TypedActionCreator<any>, reducer: CaseReducer<S>) {\n      if (process.env.NODE_ENV !== 'production') {\n        /*\n         to keep the definition by the user in line with actual behavior,\n         we enforce `addCase` to always be called before calling `addMatcher`\n         as matching cases take precedence over matchers\n         */\n        if (actionMatchers.length > 0) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(26) : '`builder.addCase` should only be called before calling `builder.addMatcher`');\n        }\n        if (defaultCaseReducer) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(27) : '`builder.addCase` should only be called before calling `builder.addDefaultCase`');\n        }\n      }\n      const type = typeof typeOrActionCreator === 'string' ? typeOrActionCreator : typeOrActionCreator.type;\n      if (!type) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage3(28) : '`builder.addCase` cannot be called with an empty action type');\n      }\n      if (type in actionsMap) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage4(29) : '`builder.addCase` cannot be called with two reducers for the same action type ' + `'${type}'`);\n      }\n      actionsMap[type] = reducer;\n      return builder;\n    },\n    addMatcher<A>(matcher: TypeGuard<A>, reducer: CaseReducer<S, A extends Action ? A : A & Action>) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (defaultCaseReducer) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage5(30) : '`builder.addMatcher` should only be called before calling `builder.addDefaultCase`');\n        }\n      }\n      actionMatchers.push({\n        matcher,\n        reducer\n      });\n      return builder;\n    },\n    addDefaultCase(reducer: CaseReducer<S, Action>) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (defaultCaseReducer) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage6(31) : '`builder.addDefaultCase` can only be called once');\n        }\n      }\n      defaultCaseReducer = reducer;\n      return builder;\n    }\n  };\n  builderCallback(builder);\n  return [actionsMap, actionMatchers, defaultCaseReducer];\n}", "import type { Action<PERSON>romMatcher, Matcher, UnionToIntersection } from './tsHelpers';\nimport { hasMatchFunction } from './tsHelpers';\nimport type { AsyncThunk, AsyncThunkFulfilledActionCreator, AsyncThunkPendingActionCreator, AsyncThunkRejectedActionCreator } from './createAsyncThunk';\n\n/** @public */\nexport type ActionMatchingAnyOf<Matchers extends Matcher<any>[]> = ActionFromMatcher<Matchers[number]>;\n\n/** @public */\nexport type ActionMatchingAllOf<Matchers extends Matcher<any>[]> = UnionToIntersection<ActionMatchingAnyOf<Matchers>>;\nconst matches = (matcher: Matcher<any>, action: any) => {\n  if (hasMatchFunction(matcher)) {\n    return matcher.match(action);\n  } else {\n    return matcher(action);\n  }\n};\n\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action matches any one of the supplied type guards or action\n * creators.\n *\n * @param matchers The type guards or action creators to match against.\n *\n * @public\n */\nexport function isAnyOf<Matchers extends Matcher<any>[]>(...matchers: Matchers) {\n  return (action: any): action is ActionMatchingAnyOf<Matchers> => {\n    return matchers.some(matcher => matches(matcher, action));\n  };\n}\n\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action matches all of the supplied type guards or action\n * creators.\n *\n * @param matchers The type guards or action creators to match against.\n *\n * @public\n */\nexport function isAllOf<Matchers extends Matcher<any>[]>(...matchers: Matchers) {\n  return (action: any): action is ActionMatchingAllOf<Matchers> => {\n    return matchers.every(matcher => matches(matcher, action));\n  };\n}\n\n/**\n * @param action A redux action\n * @param validStatus An array of valid meta.requestStatus values\n *\n * @internal\n */\nexport function hasExpectedRequestMetadata(action: any, validStatus: readonly string[]) {\n  if (!action || !action.meta) return false;\n  const hasValidRequestId = typeof action.meta.requestId === 'string';\n  const hasValidRequestStatus = validStatus.indexOf(action.meta.requestStatus) > -1;\n  return hasValidRequestId && hasValidRequestStatus;\n}\nfunction isAsyncThunkArray(a: [any] | AnyAsyncThunk[]): a is AnyAsyncThunk[] {\n  return typeof a[0] === 'function' && 'pending' in a[0] && 'fulfilled' in a[0] && 'rejected' in a[0];\n}\nexport type UnknownAsyncThunkPendingAction = ReturnType<AsyncThunkPendingActionCreator<unknown>>;\nexport type PendingActionFromAsyncThunk<T extends AnyAsyncThunk> = ActionFromMatcher<T['pending']>;\n\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action was created by an async thunk action creator, and that\n * the action is pending.\n *\n * @public\n */\nexport function isPending(): (action: any) => action is UnknownAsyncThunkPendingAction;\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action belongs to one of the provided async thunk action creators,\n * and that the action is pending.\n *\n * @param asyncThunks (optional) The async thunk action creators to match against.\n *\n * @public\n */\nexport function isPending<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks): (action: any) => action is PendingActionFromAsyncThunk<AsyncThunks[number]>;\n/**\n * Tests if `action` is a pending thunk action\n * @public\n */\nexport function isPending(action: any): action is UnknownAsyncThunkPendingAction;\nexport function isPending<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks | [any]) {\n  if (asyncThunks.length === 0) {\n    return (action: any) => hasExpectedRequestMetadata(action, ['pending']);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isPending()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map(asyncThunk => asyncThunk.pending));\n}\nexport type UnknownAsyncThunkRejectedAction = ReturnType<AsyncThunkRejectedActionCreator<unknown, unknown>>;\nexport type RejectedActionFromAsyncThunk<T extends AnyAsyncThunk> = ActionFromMatcher<T['rejected']>;\n\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action was created by an async thunk action creator, and that\n * the action is rejected.\n *\n * @public\n */\nexport function isRejected(): (action: any) => action is UnknownAsyncThunkRejectedAction;\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action belongs to one of the provided async thunk action creators,\n * and that the action is rejected.\n *\n * @param asyncThunks (optional) The async thunk action creators to match against.\n *\n * @public\n */\nexport function isRejected<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks): (action: any) => action is RejectedActionFromAsyncThunk<AsyncThunks[number]>;\n/**\n * Tests if `action` is a rejected thunk action\n * @public\n */\nexport function isRejected(action: any): action is UnknownAsyncThunkRejectedAction;\nexport function isRejected<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks | [any]) {\n  if (asyncThunks.length === 0) {\n    return (action: any) => hasExpectedRequestMetadata(action, ['rejected']);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isRejected()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map(asyncThunk => asyncThunk.rejected));\n}\nexport type UnknownAsyncThunkRejectedWithValueAction = ReturnType<AsyncThunkRejectedActionCreator<unknown, unknown>>;\nexport type RejectedWithValueActionFromAsyncThunk<T extends AnyAsyncThunk> = ActionFromMatcher<T['rejected']> & (T extends AsyncThunk<any, any, {\n  rejectValue: infer RejectedValue;\n}> ? {\n  payload: RejectedValue;\n} : unknown);\n\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action was created by an async thunk action creator, and that\n * the action is rejected with value.\n *\n * @public\n */\nexport function isRejectedWithValue(): (action: any) => action is UnknownAsyncThunkRejectedAction;\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action belongs to one of the provided async thunk action creators,\n * and that the action is rejected with value.\n *\n * @param asyncThunks (optional) The async thunk action creators to match against.\n *\n * @public\n */\nexport function isRejectedWithValue<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks): (action: any) => action is RejectedWithValueActionFromAsyncThunk<AsyncThunks[number]>;\n/**\n * Tests if `action` is a rejected thunk action with value\n * @public\n */\nexport function isRejectedWithValue(action: any): action is UnknownAsyncThunkRejectedAction;\nexport function isRejectedWithValue<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks | [any]) {\n  const hasFlag = (action: any): action is any => {\n    return action && action.meta && action.meta.rejectedWithValue;\n  };\n  if (asyncThunks.length === 0) {\n    return isAllOf(isRejected(...asyncThunks), hasFlag);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isRejectedWithValue()(asyncThunks[0]);\n  }\n  return isAllOf(isRejected(...asyncThunks), hasFlag);\n}\nexport type UnknownAsyncThunkFulfilledAction = ReturnType<AsyncThunkFulfilledActionCreator<unknown, unknown>>;\nexport type FulfilledActionFromAsyncThunk<T extends AnyAsyncThunk> = ActionFromMatcher<T['fulfilled']>;\n\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action was created by an async thunk action creator, and that\n * the action is fulfilled.\n *\n * @public\n */\nexport function isFulfilled(): (action: any) => action is UnknownAsyncThunkFulfilledAction;\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action belongs to one of the provided async thunk action creators,\n * and that the action is fulfilled.\n *\n * @param asyncThunks (optional) The async thunk action creators to match against.\n *\n * @public\n */\nexport function isFulfilled<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks): (action: any) => action is FulfilledActionFromAsyncThunk<AsyncThunks[number]>;\n/**\n * Tests if `action` is a fulfilled thunk action\n * @public\n */\nexport function isFulfilled(action: any): action is UnknownAsyncThunkFulfilledAction;\nexport function isFulfilled<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks | [any]) {\n  if (asyncThunks.length === 0) {\n    return (action: any) => hasExpectedRequestMetadata(action, ['fulfilled']);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isFulfilled()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map(asyncThunk => asyncThunk.fulfilled));\n}\nexport type UnknownAsyncThunkAction = UnknownAsyncThunkPendingAction | UnknownAsyncThunkRejectedAction | UnknownAsyncThunkFulfilledAction;\nexport type AnyAsyncThunk = {\n  pending: {\n    match: (action: any) => action is any;\n  };\n  fulfilled: {\n    match: (action: any) => action is any;\n  };\n  rejected: {\n    match: (action: any) => action is any;\n  };\n};\nexport type ActionsFromAsyncThunk<T extends AnyAsyncThunk> = ActionFromMatcher<T['pending']> | ActionFromMatcher<T['fulfilled']> | ActionFromMatcher<T['rejected']>;\n\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action was created by an async thunk action creator.\n *\n * @public\n */\nexport function isAsyncThunkAction(): (action: any) => action is UnknownAsyncThunkAction;\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action belongs to one of the provided async thunk action creators.\n *\n * @param asyncThunks (optional) The async thunk action creators to match against.\n *\n * @public\n */\nexport function isAsyncThunkAction<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks): (action: any) => action is ActionsFromAsyncThunk<AsyncThunks[number]>;\n/**\n * Tests if `action` is a thunk action\n * @public\n */\nexport function isAsyncThunkAction(action: any): action is UnknownAsyncThunkAction;\nexport function isAsyncThunkAction<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks | [any]) {\n  if (asyncThunks.length === 0) {\n    return (action: any) => hasExpectedRequestMetadata(action, ['pending', 'fulfilled', 'rejected']);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isAsyncThunkAction()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.flatMap(asyncThunk => [asyncThunk.pending, asyncThunk.rejected, asyncThunk.fulfilled]));\n}", "// Borrowed from https://github.com/ai/nanoid/blob/3.0.2/non-secure/index.js\n// This alphabet uses `A-Za-z0-9_-` symbols. A genetic algorithm helped\n// optimize the gzip compression for this alphabet.\nlet urlAlphabet = 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';\n\n/**\r\n *\r\n * @public\r\n */\nexport let nanoid = (size = 21) => {\n  let id = '';\n  // A compact alternative for `for (var i = 0; i < step; i++)`.\n  let i = size;\n  while (i--) {\n    // `| 0` is more compact and faster than `Math.floor()`.\n    id += urlAlphabet[Math.random() * 64 | 0];\n  }\n  return id;\n};", "import type { Dispatch, UnknownAction } from 'redux';\nimport type { ThunkDispatch } from 'redux-thunk';\nimport type { ActionCreatorWithPreparedPayload } from './createAction';\nimport { createAction } from './createAction';\nimport { isAnyOf } from './matchers';\nimport { nanoid } from './nanoid';\nimport type { FallbackIfUnknown, Id, IsAny, IsUnknown, SafePromise } from './tsHelpers';\nexport type BaseThunkAPI<S, E, D extends Dispatch = Dispatch, RejectedValue = unknown, RejectedMeta = unknown, FulfilledMeta = unknown> = {\n  dispatch: D;\n  getState: () => S;\n  extra: E;\n  requestId: string;\n  signal: AbortSignal;\n  abort: (reason?: string) => void;\n  rejectWithValue: IsUnknown<RejectedMeta, (value: RejectedValue) => RejectWithValue<RejectedValue, RejectedMeta>, (value: RejectedValue, meta: RejectedMeta) => RejectWithValue<RejectedValue, RejectedMeta>>;\n  fulfillWithValue: IsUnknown<FulfilledMeta, <FulfilledValue>(value: FulfilledValue) => FulfilledValue, <FulfilledValue>(value: FulfilledValue, meta: FulfilledMeta) => FulfillWithMeta<FulfilledValue, FulfilledMeta>>;\n};\n\n/**\n * @public\n */\nexport interface SerializedError {\n  name?: string;\n  message?: string;\n  stack?: string;\n  code?: string;\n}\nconst commonProperties: Array<keyof SerializedError> = ['name', 'message', 'stack', 'code'];\nclass RejectWithValue<Payload, RejectedMeta> {\n  /*\n  type-only property to distinguish between RejectWithValue and FulfillWithMeta\n  does not exist at runtime\n  */\n  private readonly _type!: 'RejectWithValue';\n  constructor(public readonly payload: Payload, public readonly meta: RejectedMeta) {}\n}\nclass FulfillWithMeta<Payload, FulfilledMeta> {\n  /*\n  type-only property to distinguish between RejectWithValue and FulfillWithMeta\n  does not exist at runtime\n  */\n  private readonly _type!: 'FulfillWithMeta';\n  constructor(public readonly payload: Payload, public readonly meta: FulfilledMeta) {}\n}\n\n/**\n * Serializes an error into a plain object.\n * Reworked from https://github.com/sindresorhus/serialize-error\n *\n * @public\n */\nexport const miniSerializeError = (value: any): SerializedError => {\n  if (typeof value === 'object' && value !== null) {\n    const simpleError: SerializedError = {};\n    for (const property of commonProperties) {\n      if (typeof value[property] === 'string') {\n        simpleError[property] = value[property];\n      }\n    }\n    return simpleError;\n  }\n  return {\n    message: String(value)\n  };\n};\nexport type AsyncThunkConfig = {\n  state?: unknown;\n  dispatch?: ThunkDispatch<unknown, unknown, UnknownAction>;\n  extra?: unknown;\n  rejectValue?: unknown;\n  serializedErrorType?: unknown;\n  pendingMeta?: unknown;\n  fulfilledMeta?: unknown;\n  rejectedMeta?: unknown;\n};\nexport type GetState<ThunkApiConfig> = ThunkApiConfig extends {\n  state: infer State;\n} ? State : unknown;\ntype GetExtra<ThunkApiConfig> = ThunkApiConfig extends {\n  extra: infer Extra;\n} ? Extra : unknown;\ntype GetDispatch<ThunkApiConfig> = ThunkApiConfig extends {\n  dispatch: infer Dispatch;\n} ? FallbackIfUnknown<Dispatch, ThunkDispatch<GetState<ThunkApiConfig>, GetExtra<ThunkApiConfig>, UnknownAction>> : ThunkDispatch<GetState<ThunkApiConfig>, GetExtra<ThunkApiConfig>, UnknownAction>;\nexport type GetThunkAPI<ThunkApiConfig> = BaseThunkAPI<GetState<ThunkApiConfig>, GetExtra<ThunkApiConfig>, GetDispatch<ThunkApiConfig>, GetRejectValue<ThunkApiConfig>, GetRejectedMeta<ThunkApiConfig>, GetFulfilledMeta<ThunkApiConfig>>;\ntype GetRejectValue<ThunkApiConfig> = ThunkApiConfig extends {\n  rejectValue: infer RejectValue;\n} ? RejectValue : unknown;\ntype GetPendingMeta<ThunkApiConfig> = ThunkApiConfig extends {\n  pendingMeta: infer PendingMeta;\n} ? PendingMeta : unknown;\ntype GetFulfilledMeta<ThunkApiConfig> = ThunkApiConfig extends {\n  fulfilledMeta: infer FulfilledMeta;\n} ? FulfilledMeta : unknown;\ntype GetRejectedMeta<ThunkApiConfig> = ThunkApiConfig extends {\n  rejectedMeta: infer RejectedMeta;\n} ? RejectedMeta : unknown;\ntype GetSerializedErrorType<ThunkApiConfig> = ThunkApiConfig extends {\n  serializedErrorType: infer GetSerializedErrorType;\n} ? GetSerializedErrorType : SerializedError;\ntype MaybePromise<T> = T | Promise<T> | (T extends any ? Promise<T> : never);\n\n/**\n * A type describing the return value of the `payloadCreator` argument to `createAsyncThunk`.\n * Might be useful for wrapping `createAsyncThunk` in custom abstractions.\n *\n * @public\n */\nexport type AsyncThunkPayloadCreatorReturnValue<Returned, ThunkApiConfig extends AsyncThunkConfig> = MaybePromise<IsUnknown<GetFulfilledMeta<ThunkApiConfig>, Returned, FulfillWithMeta<Returned, GetFulfilledMeta<ThunkApiConfig>>> | RejectWithValue<GetRejectValue<ThunkApiConfig>, GetRejectedMeta<ThunkApiConfig>>>;\n/**\n * A type describing the `payloadCreator` argument to `createAsyncThunk`.\n * Might be useful for wrapping `createAsyncThunk` in custom abstractions.\n *\n * @public\n */\nexport type AsyncThunkPayloadCreator<Returned, ThunkArg = void, ThunkApiConfig extends AsyncThunkConfig = {}> = (arg: ThunkArg, thunkAPI: GetThunkAPI<ThunkApiConfig>) => AsyncThunkPayloadCreatorReturnValue<Returned, ThunkApiConfig>;\n\n/**\n * A ThunkAction created by `createAsyncThunk`.\n * Dispatching it returns a Promise for either a\n * fulfilled or rejected action.\n * Also, the returned value contains an `abort()` method\n * that allows the asyncAction to be cancelled from the outside.\n *\n * @public\n */\nexport type AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig extends AsyncThunkConfig> = (dispatch: NonNullable<GetDispatch<ThunkApiConfig>>, getState: () => GetState<ThunkApiConfig>, extra: GetExtra<ThunkApiConfig>) => SafePromise<ReturnType<AsyncThunkFulfilledActionCreator<Returned, ThunkArg>> | ReturnType<AsyncThunkRejectedActionCreator<ThunkArg, ThunkApiConfig>>> & {\n  abort: (reason?: string) => void;\n  requestId: string;\n  arg: ThunkArg;\n  unwrap: () => Promise<Returned>;\n};\n\n/**\n * Config provided when calling the async thunk action creator.\n */\nexport interface AsyncThunkDispatchConfig {\n  /**\n   * An external `AbortSignal` that will be tracked by the internal `AbortSignal`.\n   */\n  signal?: AbortSignal;\n}\ntype AsyncThunkActionCreator<Returned, ThunkArg, ThunkApiConfig extends AsyncThunkConfig> = IsAny<ThunkArg,\n// any handling\n(arg: ThunkArg, config?: AsyncThunkDispatchConfig) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig>,\n// unknown handling\nunknown extends ThunkArg ? (arg: ThunkArg, config?: AsyncThunkDispatchConfig) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig> // argument not specified or specified as void or undefined\n: [ThunkArg] extends [void] | [undefined] ? (arg?: undefined, config?: AsyncThunkDispatchConfig) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig> // argument contains void\n: [void] extends [ThunkArg] // make optional\n? (arg?: ThunkArg, config?: AsyncThunkDispatchConfig) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig> // argument contains undefined\n: [undefined] extends [ThunkArg] ? WithStrictNullChecks<\n// with strict nullChecks: make optional\n(arg?: ThunkArg, config?: AsyncThunkDispatchConfig) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig>,\n// without strict null checks this will match everything, so don't make it optional\n(arg: ThunkArg, config?: AsyncThunkDispatchConfig) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig>> // default case: normal argument\n: (arg: ThunkArg, config?: AsyncThunkDispatchConfig) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig>>;\n\n/**\n * Options object for `createAsyncThunk`.\n *\n * @public\n */\nexport type AsyncThunkOptions<ThunkArg = void, ThunkApiConfig extends AsyncThunkConfig = {}> = {\n  /**\n   * A method to control whether the asyncThunk should be executed. Has access to the\n   * `arg`, `api.getState()` and `api.extra` arguments.\n   *\n   * @returns `false` if it should be skipped\n   */\n  condition?(arg: ThunkArg, api: Pick<GetThunkAPI<ThunkApiConfig>, 'getState' | 'extra'>): MaybePromise<boolean | undefined>;\n  /**\n   * If `condition` returns `false`, the asyncThunk will be skipped.\n   * This option allows you to control whether a `rejected` action with `meta.condition == false`\n   * will be dispatched or not.\n   *\n   * @default `false`\n   */\n  dispatchConditionRejection?: boolean;\n  serializeError?: (x: unknown) => GetSerializedErrorType<ThunkApiConfig>;\n\n  /**\n   * A function to use when generating the `requestId` for the request sequence.\n   *\n   * @default `nanoid`\n   */\n  idGenerator?: (arg: ThunkArg) => string;\n} & IsUnknown<GetPendingMeta<ThunkApiConfig>, {\n  /**\n   * A method to generate additional properties to be added to `meta` of the pending action.\n   *\n   * Using this optional overload will not modify the types correctly, this overload is only in place to support JavaScript users.\n   * Please use the `ThunkApiConfig` parameter `pendingMeta` to get access to a correctly typed overload\n   */\n  getPendingMeta?(base: {\n    arg: ThunkArg;\n    requestId: string;\n  }, api: Pick<GetThunkAPI<ThunkApiConfig>, 'getState' | 'extra'>): GetPendingMeta<ThunkApiConfig>;\n}, {\n  /**\n   * A method to generate additional properties to be added to `meta` of the pending action.\n   */\n  getPendingMeta(base: {\n    arg: ThunkArg;\n    requestId: string;\n  }, api: Pick<GetThunkAPI<ThunkApiConfig>, 'getState' | 'extra'>): GetPendingMeta<ThunkApiConfig>;\n}>;\nexport type AsyncThunkPendingActionCreator<ThunkArg, ThunkApiConfig = {}> = ActionCreatorWithPreparedPayload<[string, ThunkArg, GetPendingMeta<ThunkApiConfig>?], undefined, string, never, {\n  arg: ThunkArg;\n  requestId: string;\n  requestStatus: 'pending';\n} & GetPendingMeta<ThunkApiConfig>>;\nexport type AsyncThunkRejectedActionCreator<ThunkArg, ThunkApiConfig = {}> = ActionCreatorWithPreparedPayload<[Error | null, string, ThunkArg, GetRejectValue<ThunkApiConfig>?, GetRejectedMeta<ThunkApiConfig>?], GetRejectValue<ThunkApiConfig> | undefined, string, GetSerializedErrorType<ThunkApiConfig>, {\n  arg: ThunkArg;\n  requestId: string;\n  requestStatus: 'rejected';\n  aborted: boolean;\n  condition: boolean;\n} & (({\n  rejectedWithValue: false;\n} & { [K in keyof GetRejectedMeta<ThunkApiConfig>]?: undefined }) | ({\n  rejectedWithValue: true;\n} & GetRejectedMeta<ThunkApiConfig>))>;\nexport type AsyncThunkFulfilledActionCreator<Returned, ThunkArg, ThunkApiConfig = {}> = ActionCreatorWithPreparedPayload<[Returned, string, ThunkArg, GetFulfilledMeta<ThunkApiConfig>?], Returned, string, never, {\n  arg: ThunkArg;\n  requestId: string;\n  requestStatus: 'fulfilled';\n} & GetFulfilledMeta<ThunkApiConfig>>;\n\n/**\n * A type describing the return value of `createAsyncThunk`.\n * Might be useful for wrapping `createAsyncThunk` in custom abstractions.\n *\n * @public\n */\nexport type AsyncThunk<Returned, ThunkArg, ThunkApiConfig extends AsyncThunkConfig> = AsyncThunkActionCreator<Returned, ThunkArg, ThunkApiConfig> & {\n  pending: AsyncThunkPendingActionCreator<ThunkArg, ThunkApiConfig>;\n  rejected: AsyncThunkRejectedActionCreator<ThunkArg, ThunkApiConfig>;\n  fulfilled: AsyncThunkFulfilledActionCreator<Returned, ThunkArg, ThunkApiConfig>;\n  // matchSettled?\n  settled: (action: any) => action is ReturnType<AsyncThunkRejectedActionCreator<ThunkArg, ThunkApiConfig> | AsyncThunkFulfilledActionCreator<Returned, ThunkArg, ThunkApiConfig>>;\n  typePrefix: string;\n};\nexport type OverrideThunkApiConfigs<OldConfig, NewConfig> = Id<NewConfig & Omit<OldConfig, keyof NewConfig>>;\nexport type CreateAsyncThunkFunction<CurriedThunkApiConfig extends AsyncThunkConfig> = {\n  /**\n   *\n   * @param typePrefix\n   * @param payloadCreator\n   * @param options\n   *\n   * @public\n   */\n  // separate signature without `AsyncThunkConfig` for better inference\n  <Returned, ThunkArg = void>(typePrefix: string, payloadCreator: AsyncThunkPayloadCreator<Returned, ThunkArg, CurriedThunkApiConfig>, options?: AsyncThunkOptions<ThunkArg, CurriedThunkApiConfig>): AsyncThunk<Returned, ThunkArg, CurriedThunkApiConfig>;\n\n  /**\n   *\n   * @param typePrefix\n   * @param payloadCreator\n   * @param options\n   *\n   * @public\n   */\n  <Returned, ThunkArg, ThunkApiConfig extends AsyncThunkConfig>(typePrefix: string, payloadCreator: AsyncThunkPayloadCreator<Returned, ThunkArg, OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>>, options?: AsyncThunkOptions<ThunkArg, OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>>): AsyncThunk<Returned, ThunkArg, OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>>;\n};\ntype CreateAsyncThunk<CurriedThunkApiConfig extends AsyncThunkConfig> = CreateAsyncThunkFunction<CurriedThunkApiConfig> & {\n  withTypes<ThunkApiConfig extends AsyncThunkConfig>(): CreateAsyncThunk<OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>>;\n};\nconst externalAbortMessage = 'External signal was aborted';\nexport const createAsyncThunk = /* @__PURE__ */(() => {\n  function createAsyncThunk<Returned, ThunkArg, ThunkApiConfig extends AsyncThunkConfig>(typePrefix: string, payloadCreator: AsyncThunkPayloadCreator<Returned, ThunkArg, ThunkApiConfig>, options?: AsyncThunkOptions<ThunkArg, ThunkApiConfig>): AsyncThunk<Returned, ThunkArg, ThunkApiConfig> {\n    type RejectedValue = GetRejectValue<ThunkApiConfig>;\n    type PendingMeta = GetPendingMeta<ThunkApiConfig>;\n    type FulfilledMeta = GetFulfilledMeta<ThunkApiConfig>;\n    type RejectedMeta = GetRejectedMeta<ThunkApiConfig>;\n    const fulfilled: AsyncThunkFulfilledActionCreator<Returned, ThunkArg, ThunkApiConfig> = createAction(typePrefix + '/fulfilled', (payload: Returned, requestId: string, arg: ThunkArg, meta?: FulfilledMeta) => ({\n      payload,\n      meta: {\n        ...(meta as any || {}),\n        arg,\n        requestId,\n        requestStatus: 'fulfilled' as const\n      }\n    }));\n    const pending: AsyncThunkPendingActionCreator<ThunkArg, ThunkApiConfig> = createAction(typePrefix + '/pending', (requestId: string, arg: ThunkArg, meta?: PendingMeta) => ({\n      payload: undefined,\n      meta: {\n        ...(meta as any || {}),\n        arg,\n        requestId,\n        requestStatus: 'pending' as const\n      }\n    }));\n    const rejected: AsyncThunkRejectedActionCreator<ThunkArg, ThunkApiConfig> = createAction(typePrefix + '/rejected', (error: Error | null, requestId: string, arg: ThunkArg, payload?: RejectedValue, meta?: RejectedMeta) => ({\n      payload,\n      error: (options && options.serializeError || miniSerializeError)(error || 'Rejected') as GetSerializedErrorType<ThunkApiConfig>,\n      meta: {\n        ...(meta as any || {}),\n        arg,\n        requestId,\n        rejectedWithValue: !!payload,\n        requestStatus: 'rejected' as const,\n        aborted: error?.name === 'AbortError',\n        condition: error?.name === 'ConditionError'\n      }\n    }));\n    function actionCreator(arg: ThunkArg, {\n      signal\n    }: AsyncThunkDispatchConfig = {}): AsyncThunkAction<Returned, ThunkArg, Required<ThunkApiConfig>> {\n      return (dispatch, getState, extra) => {\n        const requestId = options?.idGenerator ? options.idGenerator(arg) : nanoid();\n        const abortController = new AbortController();\n        let abortHandler: (() => void) | undefined;\n        let abortReason: string | undefined;\n        function abort(reason?: string) {\n          abortReason = reason;\n          abortController.abort();\n        }\n        if (signal) {\n          if (signal.aborted) {\n            abort(externalAbortMessage);\n          } else {\n            signal.addEventListener('abort', () => abort(externalAbortMessage), {\n              once: true\n            });\n          }\n        }\n        const promise = async function () {\n          let finalAction: ReturnType<typeof fulfilled | typeof rejected>;\n          try {\n            let conditionResult = options?.condition?.(arg, {\n              getState,\n              extra\n            });\n            if (isThenable(conditionResult)) {\n              conditionResult = await conditionResult;\n            }\n            if (conditionResult === false || abortController.signal.aborted) {\n              // eslint-disable-next-line no-throw-literal\n              throw {\n                name: 'ConditionError',\n                message: 'Aborted due to condition callback returning false.'\n              };\n            }\n            const abortedPromise = new Promise<never>((_, reject) => {\n              abortHandler = () => {\n                reject({\n                  name: 'AbortError',\n                  message: abortReason || 'Aborted'\n                });\n              };\n              abortController.signal.addEventListener('abort', abortHandler);\n            });\n            dispatch(pending(requestId, arg, options?.getPendingMeta?.({\n              requestId,\n              arg\n            }, {\n              getState,\n              extra\n            })) as any);\n            finalAction = await Promise.race([abortedPromise, Promise.resolve(payloadCreator(arg, {\n              dispatch,\n              getState,\n              extra,\n              requestId,\n              signal: abortController.signal,\n              abort,\n              rejectWithValue: ((value: RejectedValue, meta?: RejectedMeta) => {\n                return new RejectWithValue(value, meta);\n              }) as any,\n              fulfillWithValue: ((value: unknown, meta?: FulfilledMeta) => {\n                return new FulfillWithMeta(value, meta);\n              }) as any\n            })).then(result => {\n              if (result instanceof RejectWithValue) {\n                throw result;\n              }\n              if (result instanceof FulfillWithMeta) {\n                return fulfilled(result.payload, requestId, arg, result.meta);\n              }\n              return fulfilled(result as any, requestId, arg);\n            })]);\n          } catch (err) {\n            finalAction = err instanceof RejectWithValue ? rejected(null, requestId, arg, err.payload, err.meta) : rejected(err as any, requestId, arg);\n          } finally {\n            if (abortHandler) {\n              abortController.signal.removeEventListener('abort', abortHandler);\n            }\n          }\n          // We dispatch the result action _after_ the catch, to avoid having any errors\n          // here get swallowed by the try/catch block,\n          // per https://twitter.com/dan_abramov/status/770914221638942720\n          // and https://github.com/reduxjs/redux-toolkit/blob/e85eb17b39a2118d859f7b7746e0f3fee523e089/docs/tutorials/advanced-tutorial.md#async-error-handling-logic-in-thunks\n\n          const skipDispatch = options && !options.dispatchConditionRejection && rejected.match(finalAction) && (finalAction as any).meta.condition;\n          if (!skipDispatch) {\n            dispatch(finalAction as any);\n          }\n          return finalAction;\n        }();\n        return Object.assign(promise as SafePromise<any>, {\n          abort,\n          requestId,\n          arg,\n          unwrap() {\n            return promise.then<any>(unwrapResult);\n          }\n        });\n      };\n    }\n    return Object.assign(actionCreator as AsyncThunkActionCreator<Returned, ThunkArg, ThunkApiConfig>, {\n      pending,\n      rejected,\n      fulfilled,\n      settled: isAnyOf(rejected, fulfilled),\n      typePrefix\n    });\n  }\n  createAsyncThunk.withTypes = () => createAsyncThunk;\n  return createAsyncThunk as CreateAsyncThunk<AsyncThunkConfig>;\n})();\ninterface UnwrappableAction {\n  payload: any;\n  meta?: any;\n  error?: any;\n}\ntype UnwrappedActionPayload<T extends UnwrappableAction> = Exclude<T, {\n  error: any;\n}>['payload'];\n\n/**\n * @public\n */\nexport function unwrapResult<R extends UnwrappableAction>(action: R): UnwrappedActionPayload<R> {\n  if (action.meta && action.meta.rejectedWithValue) {\n    throw action.payload;\n  }\n  if (action.error) {\n    throw action.error;\n  }\n  return action.payload;\n}\ntype WithStrictNullChecks<True, False> = undefined extends boolean ? False : True;\nfunction isThenable(value: any): value is PromiseLike<any> {\n  return value !== null && typeof value === 'object' && typeof value.then === 'function';\n}", "import { formatProdErrorMessage as _formatProdErrorMessage, formatProdErrorMessage as _formatProdErrorMessage2, formatProdErrorMessage as _formatProdErrorMessage3, formatProdErrorMessage as _formatProdErrorMessage4, formatProdErrorMessage as _formatProdErrorMessage5, formatProdErrorMessage as _formatProdErrorMessage6, formatProdErrorMessage as _formatProdErrorMessage7, formatProdErrorMessage as _formatProdErrorMessage8 } from \"@reduxjs/toolkit\";\nimport type { Action, Reducer, UnknownAction } from 'redux';\nimport type { Selector } from 'reselect';\nimport type { InjectConfig } from './combineSlices';\nimport type { ActionCreatorWithoutPayload, PayloadAction, PayloadActionCreator, PrepareAction, _ActionCreatorWithPreparedPayload } from './createAction';\nimport { createAction } from './createAction';\nimport type { AsyncThunk, AsyncThunkConfig, AsyncThunkOptions, AsyncThunkPayloadCreator, OverrideThunkApiConfigs } from './createAsyncThunk';\nimport { createAsyncThunk as _createAsyncThunk } from './createAsyncThunk';\nimport type { ActionMatcherDescriptionCollection, CaseReducer, ReducerWithInitialState } from './createReducer';\nimport { createReducer } from './createReducer';\nimport type { ActionReducerMapBuilder, TypedActionCreator } from './mapBuilders';\nimport { executeReducerBuilderCallback } from './mapBuilders';\nimport type { Id, TypeGuard } from './tsHelpers';\nimport { getOrInsertComputed } from './utils';\nconst asyncThunkSymbol = /* @__PURE__ */Symbol.for('rtk-slice-createasyncthunk');\n// type is annotated because it's too long to infer\nexport const asyncThunkCreator: {\n  [asyncThunkSymbol]: typeof _createAsyncThunk;\n} = {\n  [asyncThunkSymbol]: _createAsyncThunk\n};\ntype InjectIntoConfig<NewReducerPath extends string> = InjectConfig & {\n  reducerPath?: NewReducerPath;\n};\n\n/**\n * The return value of `createSlice`\n *\n * @public\n */\nexport interface Slice<State = any, CaseReducers extends SliceCaseReducers<State> = SliceCaseReducers<State>, Name extends string = string, ReducerPath extends string = Name, Selectors extends SliceSelectors<State> = SliceSelectors<State>> {\n  /**\n   * The slice name.\n   */\n  name: Name;\n\n  /**\n   *  The slice reducer path.\n   */\n  reducerPath: ReducerPath;\n\n  /**\n   * The slice's reducer.\n   */\n  reducer: Reducer<State>;\n\n  /**\n   * Action creators for the types of actions that are handled by the slice\n   * reducer.\n   */\n  actions: CaseReducerActions<CaseReducers, Name>;\n\n  /**\n   * The individual case reducer functions that were passed in the `reducers` parameter.\n   * This enables reuse and testing if they were defined inline when calling `createSlice`.\n   */\n  caseReducers: SliceDefinedCaseReducers<CaseReducers>;\n\n  /**\n   * Provides access to the initial state value given to the slice.\n   * If a lazy state initializer was provided, it will be called and a fresh value returned.\n   */\n  getInitialState: () => State;\n\n  /**\n   * Get localised slice selectors (expects to be called with *just* the slice's state as the first parameter)\n   */\n  getSelectors(): Id<SliceDefinedSelectors<State, Selectors, State>>;\n\n  /**\n   * Get globalised slice selectors (`selectState` callback is expected to receive first parameter and return slice state)\n   */\n  getSelectors<RootState>(selectState: (rootState: RootState) => State): Id<SliceDefinedSelectors<State, Selectors, RootState>>;\n\n  /**\n   * Selectors that assume the slice's state is `rootState[slice.reducerPath]` (which is usually the case)\n   *\n   * Equivalent to `slice.getSelectors((state: RootState) => state[slice.reducerPath])`.\n   */\n  get selectors(): Id<SliceDefinedSelectors<State, Selectors, { [K in ReducerPath]: State }>>;\n\n  /**\n   * Inject slice into provided reducer (return value from `combineSlices`), and return injected slice.\n   */\n  injectInto<NewReducerPath extends string = ReducerPath>(this: this, injectable: {\n    inject: (slice: {\n      reducerPath: string;\n      reducer: Reducer;\n    }, config?: InjectConfig) => void;\n  }, config?: InjectIntoConfig<NewReducerPath>): InjectedSlice<State, CaseReducers, Name, NewReducerPath, Selectors>;\n\n  /**\n   * Select the slice state, using the slice's current reducerPath.\n   *\n   * Will throw an error if slice is not found.\n   */\n  selectSlice(state: { [K in ReducerPath]: State }): State;\n}\n\n/**\n * A slice after being called with `injectInto(reducer)`.\n *\n * Selectors can now be called with an `undefined` value, in which case they use the slice's initial state.\n */\ntype InjectedSlice<State = any, CaseReducers extends SliceCaseReducers<State> = SliceCaseReducers<State>, Name extends string = string, ReducerPath extends string = Name, Selectors extends SliceSelectors<State> = SliceSelectors<State>> = Omit<Slice<State, CaseReducers, Name, ReducerPath, Selectors>, 'getSelectors' | 'selectors'> & {\n  /**\n   * Get localised slice selectors (expects to be called with *just* the slice's state as the first parameter)\n   */\n  getSelectors(): Id<SliceDefinedSelectors<State, Selectors, State | undefined>>;\n\n  /**\n   * Get globalised slice selectors (`selectState` callback is expected to receive first parameter and return slice state)\n   */\n  getSelectors<RootState>(selectState: (rootState: RootState) => State | undefined): Id<SliceDefinedSelectors<State, Selectors, RootState>>;\n\n  /**\n   * Selectors that assume the slice's state is `rootState[slice.name]` (which is usually the case)\n   *\n   * Equivalent to `slice.getSelectors((state: RootState) => state[slice.name])`.\n   */\n  get selectors(): Id<SliceDefinedSelectors<State, Selectors, { [K in ReducerPath]?: State | undefined }>>;\n\n  /**\n   * Select the slice state, using the slice's current reducerPath.\n   *\n   * Returns initial state if slice is not found.\n   */\n  selectSlice(state: { [K in ReducerPath]?: State | undefined }): State;\n};\n\n/**\n * Options for `createSlice()`.\n *\n * @public\n */\nexport interface CreateSliceOptions<State = any, CR extends SliceCaseReducers<State> = SliceCaseReducers<State>, Name extends string = string, ReducerPath extends string = Name, Selectors extends SliceSelectors<State> = SliceSelectors<State>> {\n  /**\n   * The slice's name. Used to namespace the generated action types.\n   */\n  name: Name;\n\n  /**\n   * The slice's reducer path. Used when injecting into a combined slice reducer.\n   */\n  reducerPath?: ReducerPath;\n\n  /**\n   * The initial state that should be used when the reducer is called the first time. This may also be a \"lazy initializer\" function, which should return an initial state value when called. This will be used whenever the reducer is called with `undefined` as its state value, and is primarily useful for cases like reading initial state from `localStorage`.\n   */\n  initialState: State | (() => State);\n\n  /**\n   * A mapping from action types to action-type-specific *case reducer*\n   * functions. For every action type, a matching action creator will be\n   * generated using `createAction()`.\n   */\n  reducers: ValidateSliceCaseReducers<State, CR> | ((creators: ReducerCreators<State>) => CR);\n\n  /**\n   * A callback that receives a *builder* object to define\n   * case reducers via calls to `builder.addCase(actionCreatorOrType, reducer)`.\n   *\n   *\n   * @example\n  ```ts\n  import { createAction, createSlice, Action } from '@reduxjs/toolkit'\n  const incrementBy = createAction<number>('incrementBy')\n  const decrement = createAction('decrement')\n  interface RejectedAction extends Action {\n  error: Error\n  }\n  function isRejectedAction(action: Action): action is RejectedAction {\n  return action.type.endsWith('rejected')\n  }\n  createSlice({\n  name: 'counter',\n  initialState: 0,\n  reducers: {},\n  extraReducers: builder => {\n    builder\n      .addCase(incrementBy, (state, action) => {\n        // action is inferred correctly here if using TS\n      })\n      // You can chain calls, or have separate `builder.addCase()` lines each time\n      .addCase(decrement, (state, action) => {})\n      // You can match a range of action types\n      .addMatcher(\n        isRejectedAction,\n        // `action` will be inferred as a RejectedAction due to isRejectedAction being defined as a type guard\n        (state, action) => {}\n      )\n      // and provide a default case if no other handlers matched\n      .addDefaultCase((state, action) => {})\n    }\n  })\n  ```\n   */\n  extraReducers?: (builder: ActionReducerMapBuilder<State>) => void;\n\n  /**\n   * A map of selectors that receive the slice's state and any additional arguments, and return a result.\n   */\n  selectors?: Selectors;\n}\nexport enum ReducerType {\n  reducer = 'reducer',\n  reducerWithPrepare = 'reducerWithPrepare',\n  asyncThunk = 'asyncThunk',\n}\ntype ReducerDefinition<T extends ReducerType = ReducerType> = {\n  _reducerDefinitionType: T;\n};\nexport type CaseReducerDefinition<S = any, A extends Action = UnknownAction> = CaseReducer<S, A> & ReducerDefinition<ReducerType.reducer>;\n\n/**\n * A CaseReducer with a `prepare` method.\n *\n * @public\n */\nexport type CaseReducerWithPrepare<State, Action extends PayloadAction> = {\n  reducer: CaseReducer<State, Action>;\n  prepare: PrepareAction<Action['payload']>;\n};\nexport interface CaseReducerWithPrepareDefinition<State, Action extends PayloadAction> extends CaseReducerWithPrepare<State, Action>, ReducerDefinition<ReducerType.reducerWithPrepare> {}\ntype AsyncThunkSliceReducerConfig<State, ThunkArg extends any, Returned = unknown, ThunkApiConfig extends AsyncThunkConfig = {}> = {\n  pending?: CaseReducer<State, ReturnType<AsyncThunk<Returned, ThunkArg, ThunkApiConfig>['pending']>>;\n  rejected?: CaseReducer<State, ReturnType<AsyncThunk<Returned, ThunkArg, ThunkApiConfig>['rejected']>>;\n  fulfilled?: CaseReducer<State, ReturnType<AsyncThunk<Returned, ThunkArg, ThunkApiConfig>['fulfilled']>>;\n  settled?: CaseReducer<State, ReturnType<AsyncThunk<Returned, ThunkArg, ThunkApiConfig>['rejected' | 'fulfilled']>>;\n  options?: AsyncThunkOptions<ThunkArg, ThunkApiConfig>;\n};\ntype AsyncThunkSliceReducerDefinition<State, ThunkArg extends any, Returned = unknown, ThunkApiConfig extends AsyncThunkConfig = {}> = AsyncThunkSliceReducerConfig<State, ThunkArg, Returned, ThunkApiConfig> & ReducerDefinition<ReducerType.asyncThunk> & {\n  payloadCreator: AsyncThunkPayloadCreator<Returned, ThunkArg, ThunkApiConfig>;\n};\n\n/**\n * Providing these as part of the config would cause circular types, so we disallow passing them\n */\ntype PreventCircular<ThunkApiConfig> = { [K in keyof ThunkApiConfig]: K extends 'state' | 'dispatch' ? never : ThunkApiConfig[K] };\ninterface AsyncThunkCreator<State, CurriedThunkApiConfig extends PreventCircular<AsyncThunkConfig> = PreventCircular<AsyncThunkConfig>> {\n  <Returned, ThunkArg = void>(payloadCreator: AsyncThunkPayloadCreator<Returned, ThunkArg, CurriedThunkApiConfig>, config?: AsyncThunkSliceReducerConfig<State, ThunkArg, Returned, CurriedThunkApiConfig>): AsyncThunkSliceReducerDefinition<State, ThunkArg, Returned, CurriedThunkApiConfig>;\n  <Returned, ThunkArg, ThunkApiConfig extends PreventCircular<AsyncThunkConfig> = {}>(payloadCreator: AsyncThunkPayloadCreator<Returned, ThunkArg, ThunkApiConfig>, config?: AsyncThunkSliceReducerConfig<State, ThunkArg, Returned, ThunkApiConfig>): AsyncThunkSliceReducerDefinition<State, ThunkArg, Returned, ThunkApiConfig>;\n  withTypes<ThunkApiConfig extends PreventCircular<AsyncThunkConfig>>(): AsyncThunkCreator<State, OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>>;\n}\nexport interface ReducerCreators<State> {\n  reducer(caseReducer: CaseReducer<State, PayloadAction>): CaseReducerDefinition<State, PayloadAction>;\n  reducer<Payload>(caseReducer: CaseReducer<State, PayloadAction<Payload>>): CaseReducerDefinition<State, PayloadAction<Payload>>;\n  asyncThunk: AsyncThunkCreator<State>;\n  preparedReducer<Prepare extends PrepareAction<any>>(prepare: Prepare, reducer: CaseReducer<State, ReturnType<_ActionCreatorWithPreparedPayload<Prepare>>>): {\n    _reducerDefinitionType: ReducerType.reducerWithPrepare;\n    prepare: Prepare;\n    reducer: CaseReducer<State, ReturnType<_ActionCreatorWithPreparedPayload<Prepare>>>;\n  };\n}\n\n/**\n * The type describing a slice's `reducers` option.\n *\n * @public\n */\nexport type SliceCaseReducers<State> = Record<string, ReducerDefinition> | Record<string, CaseReducer<State, PayloadAction<any>> | CaseReducerWithPrepare<State, PayloadAction<any, string, any, any>>>;\n\n/**\n * The type describing a slice's `selectors` option.\n */\nexport type SliceSelectors<State> = {\n  [K: string]: (sliceState: State, ...args: any[]) => any;\n};\ntype SliceActionType<SliceName extends string, ActionName extends keyof any> = ActionName extends string | number ? `${SliceName}/${ActionName}` : string;\n\n/**\n * Derives the slice's `actions` property from the `reducers` options\n *\n * @public\n */\nexport type CaseReducerActions<CaseReducers extends SliceCaseReducers<any>, SliceName extends string> = { [Type in keyof CaseReducers]: CaseReducers[Type] extends infer Definition ? Definition extends {\n  prepare: any;\n} ? ActionCreatorForCaseReducerWithPrepare<Definition, SliceActionType<SliceName, Type>> : Definition extends AsyncThunkSliceReducerDefinition<any, infer ThunkArg, infer Returned, infer ThunkApiConfig> ? AsyncThunk<Returned, ThunkArg, ThunkApiConfig> : Definition extends {\n  reducer: any;\n} ? ActionCreatorForCaseReducer<Definition['reducer'], SliceActionType<SliceName, Type>> : ActionCreatorForCaseReducer<Definition, SliceActionType<SliceName, Type>> : never };\n\n/**\n * Get a `PayloadActionCreator` type for a passed `CaseReducerWithPrepare`\n *\n * @internal\n */\ntype ActionCreatorForCaseReducerWithPrepare<CR extends {\n  prepare: any;\n}, Type extends string> = _ActionCreatorWithPreparedPayload<CR['prepare'], Type>;\n\n/**\n * Get a `PayloadActionCreator` type for a passed `CaseReducer`\n *\n * @internal\n */\ntype ActionCreatorForCaseReducer<CR, Type extends string> = CR extends ((state: any, action: infer Action) => any) ? Action extends {\n  payload: infer P;\n} ? PayloadActionCreator<P, Type> : ActionCreatorWithoutPayload<Type> : ActionCreatorWithoutPayload<Type>;\n\n/**\n * Extracts the CaseReducers out of a `reducers` object, even if they are\n * tested into a `CaseReducerWithPrepare`.\n *\n * @internal\n */\ntype SliceDefinedCaseReducers<CaseReducers extends SliceCaseReducers<any>> = { [Type in keyof CaseReducers]: CaseReducers[Type] extends infer Definition ? Definition extends AsyncThunkSliceReducerDefinition<any, any, any> ? Id<Pick<Required<Definition>, 'fulfilled' | 'rejected' | 'pending' | 'settled'>> : Definition extends {\n  reducer: infer Reducer;\n} ? Reducer : Definition : never };\ntype RemappedSelector<S extends Selector, NewState> = S extends Selector<any, infer R, infer P> ? Selector<NewState, R, P> & {\n  unwrapped: S;\n} : never;\n\n/**\n * Extracts the final selector type from the `selectors` object.\n *\n * Removes the `string` index signature from the default value.\n */\ntype SliceDefinedSelectors<State, Selectors extends SliceSelectors<State>, RootState> = { [K in keyof Selectors as string extends K ? never : K]: RemappedSelector<Selectors[K], RootState> };\n\n/**\n * Used on a SliceCaseReducers object.\n * Ensures that if a CaseReducer is a `CaseReducerWithPrepare`, that\n * the `reducer` and the `prepare` function use the same type of `payload`.\n *\n * Might do additional such checks in the future.\n *\n * This type is only ever useful if you want to write your own wrapper around\n * `createSlice`. Please don't use it otherwise!\n *\n * @public\n */\nexport type ValidateSliceCaseReducers<S, ACR extends SliceCaseReducers<S>> = ACR & { [T in keyof ACR]: ACR[T] extends {\n  reducer(s: S, action?: infer A): any;\n} ? {\n  prepare(...a: never[]): Omit<A, 'type'>;\n} : {} };\nfunction getType(slice: string, actionKey: string): string {\n  return `${slice}/${actionKey}`;\n}\ninterface BuildCreateSliceConfig {\n  creators?: {\n    asyncThunk?: typeof asyncThunkCreator;\n  };\n}\nexport function buildCreateSlice({\n  creators\n}: BuildCreateSliceConfig = {}) {\n  const cAT = creators?.asyncThunk?.[asyncThunkSymbol];\n  return function createSlice<State, CaseReducers extends SliceCaseReducers<State>, Name extends string, Selectors extends SliceSelectors<State>, ReducerPath extends string = Name>(options: CreateSliceOptions<State, CaseReducers, Name, ReducerPath, Selectors>): Slice<State, CaseReducers, Name, ReducerPath, Selectors> {\n    const {\n      name,\n      reducerPath = name as unknown as ReducerPath\n    } = options;\n    if (!name) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(11) : '`name` is a required option for createSlice');\n    }\n    if (typeof process !== 'undefined' && process.env.NODE_ENV === 'development') {\n      if (options.initialState === undefined) {\n        console.error('You must provide an `initialState` value that is not `undefined`. You may have misspelled `initialState`');\n      }\n    }\n    const reducers = (typeof options.reducers === 'function' ? options.reducers(buildReducerCreators<State>()) : options.reducers) || {};\n    const reducerNames = Object.keys(reducers);\n    const context: ReducerHandlingContext<State> = {\n      sliceCaseReducersByName: {},\n      sliceCaseReducersByType: {},\n      actionCreators: {},\n      sliceMatchers: []\n    };\n    const contextMethods: ReducerHandlingContextMethods<State> = {\n      addCase(typeOrActionCreator: string | TypedActionCreator<any>, reducer: CaseReducer<State>) {\n        const type = typeof typeOrActionCreator === 'string' ? typeOrActionCreator : typeOrActionCreator.type;\n        if (!type) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(12) : '`context.addCase` cannot be called with an empty action type');\n        }\n        if (type in context.sliceCaseReducersByType) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage3(13) : '`context.addCase` cannot be called with two reducers for the same action type: ' + type);\n        }\n        context.sliceCaseReducersByType[type] = reducer;\n        return contextMethods;\n      },\n      addMatcher(matcher, reducer) {\n        context.sliceMatchers.push({\n          matcher,\n          reducer\n        });\n        return contextMethods;\n      },\n      exposeAction(name, actionCreator) {\n        context.actionCreators[name] = actionCreator;\n        return contextMethods;\n      },\n      exposeCaseReducer(name, reducer) {\n        context.sliceCaseReducersByName[name] = reducer;\n        return contextMethods;\n      }\n    };\n    reducerNames.forEach(reducerName => {\n      const reducerDefinition = reducers[reducerName];\n      const reducerDetails: ReducerDetails = {\n        reducerName,\n        type: getType(name, reducerName),\n        createNotation: typeof options.reducers === 'function'\n      };\n      if (isAsyncThunkSliceReducerDefinition<State>(reducerDefinition)) {\n        handleThunkCaseReducerDefinition(reducerDetails, reducerDefinition, contextMethods, cAT);\n      } else {\n        handleNormalReducerDefinition<State>(reducerDetails, reducerDefinition as any, contextMethods);\n      }\n    });\n    function buildReducer() {\n      if (process.env.NODE_ENV !== 'production') {\n        if (typeof options.extraReducers === 'object') {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage4(14) : \"The object notation for `createSlice.extraReducers` has been removed. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createSlice\");\n        }\n      }\n      const [extraReducers = {}, actionMatchers = [], defaultCaseReducer = undefined] = typeof options.extraReducers === 'function' ? executeReducerBuilderCallback(options.extraReducers) : [options.extraReducers];\n      const finalCaseReducers = {\n        ...extraReducers,\n        ...context.sliceCaseReducersByType\n      };\n      return createReducer(options.initialState, builder => {\n        for (let key in finalCaseReducers) {\n          builder.addCase(key, finalCaseReducers[key] as CaseReducer<any>);\n        }\n        for (let sM of context.sliceMatchers) {\n          builder.addMatcher(sM.matcher, sM.reducer);\n        }\n        for (let m of actionMatchers) {\n          builder.addMatcher(m.matcher, m.reducer);\n        }\n        if (defaultCaseReducer) {\n          builder.addDefaultCase(defaultCaseReducer);\n        }\n      });\n    }\n    const selectSelf = (state: State) => state;\n    const injectedSelectorCache = new Map<boolean, WeakMap<(rootState: any) => State | undefined, Record<string, (rootState: any) => any>>>();\n    const injectedStateCache = new WeakMap<(rootState: any) => State, State>();\n    let _reducer: ReducerWithInitialState<State>;\n    function reducer(state: State | undefined, action: UnknownAction) {\n      if (!_reducer) _reducer = buildReducer();\n      return _reducer(state, action);\n    }\n    function getInitialState() {\n      if (!_reducer) _reducer = buildReducer();\n      return _reducer.getInitialState();\n    }\n    function makeSelectorProps<CurrentReducerPath extends string = ReducerPath>(reducerPath: CurrentReducerPath, injected = false): Pick<Slice<State, CaseReducers, Name, CurrentReducerPath, Selectors>, 'getSelectors' | 'selectors' | 'selectSlice' | 'reducerPath'> {\n      function selectSlice(state: { [K in CurrentReducerPath]: State }) {\n        let sliceState = state[reducerPath];\n        if (typeof sliceState === 'undefined') {\n          if (injected) {\n            sliceState = getOrInsertComputed(injectedStateCache, selectSlice, getInitialState);\n          } else if (process.env.NODE_ENV !== 'production') {\n            throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage5(15) : 'selectSlice returned undefined for an uninjected slice reducer');\n          }\n        }\n        return sliceState;\n      }\n      function getSelectors(selectState: (rootState: any) => State = selectSelf) {\n        const selectorCache = getOrInsertComputed(injectedSelectorCache, injected, () => new WeakMap());\n        return getOrInsertComputed(selectorCache, selectState, () => {\n          const map: Record<string, Selector<any, any>> = {};\n          for (const [name, selector] of Object.entries(options.selectors ?? {})) {\n            map[name] = wrapSelector(selector, selectState, () => getOrInsertComputed(injectedStateCache, selectState, getInitialState), injected);\n          }\n          return map;\n        }) as any;\n      }\n      return {\n        reducerPath,\n        getSelectors,\n        get selectors() {\n          return getSelectors(selectSlice);\n        },\n        selectSlice\n      };\n    }\n    const slice: Slice<State, CaseReducers, Name, ReducerPath, Selectors> = {\n      name,\n      reducer,\n      actions: context.actionCreators as any,\n      caseReducers: context.sliceCaseReducersByName as any,\n      getInitialState,\n      ...makeSelectorProps(reducerPath),\n      injectInto(injectable, {\n        reducerPath: pathOpt,\n        ...config\n      } = {}) {\n        const newReducerPath = pathOpt ?? reducerPath;\n        injectable.inject({\n          reducerPath: newReducerPath,\n          reducer\n        }, config);\n        return {\n          ...slice,\n          ...makeSelectorProps(newReducerPath, true)\n        } as any;\n      }\n    };\n    return slice;\n  };\n}\nfunction wrapSelector<State, NewState, S extends Selector<State>>(selector: S, selectState: Selector<NewState, State>, getInitialState: () => State, injected?: boolean) {\n  function wrapper(rootState: NewState, ...args: any[]) {\n    let sliceState = selectState(rootState);\n    if (typeof sliceState === 'undefined') {\n      if (injected) {\n        sliceState = getInitialState();\n      } else if (process.env.NODE_ENV !== 'production') {\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage6(16) : 'selectState returned undefined for an uninjected slice reducer');\n      }\n    }\n    return selector(sliceState, ...args);\n  }\n  wrapper.unwrapped = selector;\n  return wrapper as RemappedSelector<S, NewState>;\n}\n\n/**\n * A function that accepts an initial state, an object full of reducer\n * functions, and a \"slice name\", and automatically generates\n * action creators and action types that correspond to the\n * reducers and state.\n *\n * @public\n */\nexport const createSlice = /* @__PURE__ */buildCreateSlice();\ninterface ReducerHandlingContext<State> {\n  sliceCaseReducersByName: Record<string, CaseReducer<State, any> | Pick<AsyncThunkSliceReducerDefinition<State, any, any, any>, 'fulfilled' | 'rejected' | 'pending' | 'settled'>>;\n  sliceCaseReducersByType: Record<string, CaseReducer<State, any>>;\n  sliceMatchers: ActionMatcherDescriptionCollection<State>;\n  actionCreators: Record<string, Function>;\n}\ninterface ReducerHandlingContextMethods<State> {\n  /**\n   * Adds a case reducer to handle a single action type.\n   * @param actionCreator - Either a plain action type string, or an action creator generated by [`createAction`](./createAction) that can be used to determine the action type.\n   * @param reducer - The actual case reducer function.\n   */\n  addCase<ActionCreator extends TypedActionCreator<string>>(actionCreator: ActionCreator, reducer: CaseReducer<State, ReturnType<ActionCreator>>): ReducerHandlingContextMethods<State>;\n  /**\n   * Adds a case reducer to handle a single action type.\n   * @param actionCreator - Either a plain action type string, or an action creator generated by [`createAction`](./createAction) that can be used to determine the action type.\n   * @param reducer - The actual case reducer function.\n   */\n  addCase<Type extends string, A extends Action<Type>>(type: Type, reducer: CaseReducer<State, A>): ReducerHandlingContextMethods<State>;\n\n  /**\n   * Allows you to match incoming actions against your own filter function instead of only the `action.type` property.\n   * @remarks\n   * If multiple matcher reducers match, all of them will be executed in the order\n   * they were defined in - even if a case reducer already matched.\n   * All calls to `builder.addMatcher` must come after any calls to `builder.addCase` and before any calls to `builder.addDefaultCase`.\n   * @param matcher - A matcher function. In TypeScript, this should be a [type predicate](https://www.typescriptlang.org/docs/handbook/2/narrowing.html#using-type-predicates)\n   *   function\n   * @param reducer - The actual case reducer function.\n   *\n   */\n  addMatcher<A>(matcher: TypeGuard<A>, reducer: CaseReducer<State, A extends Action ? A : A & Action>): ReducerHandlingContextMethods<State>;\n  /**\n   * Add an action to be exposed under the final `slice.actions` key.\n   * @param name The key to be exposed as.\n   * @param actionCreator The action to expose.\n   * @example\n   * context.exposeAction(\"addPost\", createAction<Post>(\"addPost\"));\n   *\n   * export const { addPost } = slice.actions\n   *\n   * dispatch(addPost(post))\n   */\n  exposeAction(name: string, actionCreator: Function): ReducerHandlingContextMethods<State>;\n  /**\n   * Add a case reducer to be exposed under the final `slice.caseReducers` key.\n   * @param name The key to be exposed as.\n   * @param reducer The reducer to expose.\n   * @example\n   * context.exposeCaseReducer(\"addPost\", (state, action: PayloadAction<Post>) => {\n   *   state.push(action.payload)\n   * })\n   *\n   * slice.caseReducers.addPost([], addPost(post))\n   */\n  exposeCaseReducer(name: string, reducer: CaseReducer<State, any> | Pick<AsyncThunkSliceReducerDefinition<State, any, any, any>, 'fulfilled' | 'rejected' | 'pending' | 'settled'>): ReducerHandlingContextMethods<State>;\n}\ninterface ReducerDetails {\n  /** The key the reducer was defined under */\n  reducerName: string;\n  /** The predefined action type, i.e. `${slice.name}/${reducerName}` */\n  type: string;\n  /** Whether create. notation was used when defining reducers */\n  createNotation: boolean;\n}\nfunction buildReducerCreators<State>(): ReducerCreators<State> {\n  function asyncThunk(payloadCreator: AsyncThunkPayloadCreator<any, any>, config: AsyncThunkSliceReducerConfig<State, any>): AsyncThunkSliceReducerDefinition<State, any> {\n    return {\n      _reducerDefinitionType: ReducerType.asyncThunk,\n      payloadCreator,\n      ...config\n    };\n  }\n  asyncThunk.withTypes = () => asyncThunk;\n  return {\n    reducer(caseReducer: CaseReducer<State, any>) {\n      return Object.assign({\n        // hack so the wrapping function has the same name as the original\n        // we need to create a wrapper so the `reducerDefinitionType` is not assigned to the original\n        [caseReducer.name](...args: Parameters<typeof caseReducer>) {\n          return caseReducer(...args);\n        }\n      }[caseReducer.name], {\n        _reducerDefinitionType: ReducerType.reducer\n      } as const);\n    },\n    preparedReducer(prepare, reducer) {\n      return {\n        _reducerDefinitionType: ReducerType.reducerWithPrepare,\n        prepare,\n        reducer\n      };\n    },\n    asyncThunk: asyncThunk as any\n  };\n}\nfunction handleNormalReducerDefinition<State>({\n  type,\n  reducerName,\n  createNotation\n}: ReducerDetails, maybeReducerWithPrepare: CaseReducer<State, {\n  payload: any;\n  type: string;\n}> | CaseReducerWithPrepare<State, PayloadAction<any, string, any, any>>, context: ReducerHandlingContextMethods<State>) {\n  let caseReducer: CaseReducer<State, any>;\n  let prepareCallback: PrepareAction<any> | undefined;\n  if ('reducer' in maybeReducerWithPrepare) {\n    if (createNotation && !isCaseReducerWithPrepareDefinition(maybeReducerWithPrepare)) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage7(17) : 'Please use the `create.preparedReducer` notation for prepared action creators with the `create` notation.');\n    }\n    caseReducer = maybeReducerWithPrepare.reducer;\n    prepareCallback = maybeReducerWithPrepare.prepare;\n  } else {\n    caseReducer = maybeReducerWithPrepare;\n  }\n  context.addCase(type, caseReducer).exposeCaseReducer(reducerName, caseReducer).exposeAction(reducerName, prepareCallback ? createAction(type, prepareCallback) : createAction(type));\n}\nfunction isAsyncThunkSliceReducerDefinition<State>(reducerDefinition: any): reducerDefinition is AsyncThunkSliceReducerDefinition<State, any, any, any> {\n  return reducerDefinition._reducerDefinitionType === ReducerType.asyncThunk;\n}\nfunction isCaseReducerWithPrepareDefinition<State>(reducerDefinition: any): reducerDefinition is CaseReducerWithPrepareDefinition<State, any> {\n  return reducerDefinition._reducerDefinitionType === ReducerType.reducerWithPrepare;\n}\nfunction handleThunkCaseReducerDefinition<State>({\n  type,\n  reducerName\n}: ReducerDetails, reducerDefinition: AsyncThunkSliceReducerDefinition<State, any, any, any>, context: ReducerHandlingContextMethods<State>, cAT: typeof _createAsyncThunk | undefined) {\n  if (!cAT) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage8(18) : 'Cannot use `create.asyncThunk` in the built-in `createSlice`. ' + 'Use `buildCreateSlice({ creators: { asyncThunk: asyncThunkCreator } })` to create a customised version of `createSlice`.');\n  }\n  const {\n    payloadCreator,\n    fulfilled,\n    pending,\n    rejected,\n    settled,\n    options\n  } = reducerDefinition;\n  const thunk = cAT(type, payloadCreator, options as any);\n  context.exposeAction(reducerName, thunk);\n  if (fulfilled) {\n    context.addCase(thunk.fulfilled, fulfilled);\n  }\n  if (pending) {\n    context.addCase(thunk.pending, pending);\n  }\n  if (rejected) {\n    context.addCase(thunk.rejected, rejected);\n  }\n  if (settled) {\n    context.addMatcher(thunk.settled, settled);\n  }\n  context.exposeCaseReducer(reducerName, {\n    fulfilled: fulfilled || noop,\n    pending: pending || noop,\n    rejected: rejected || noop,\n    settled: settled || noop\n  });\n}\nfunction noop() {}", "import type { EntityId, EntityState, EntityStateAdapter, EntityStateFactory } from './models';\nexport function getInitialEntityState<T, Id extends EntityId>(): EntityState<T, Id> {\n  return {\n    ids: [],\n    entities: {} as Record<Id, T>\n  };\n}\nexport function createInitialStateFactory<T, Id extends EntityId>(stateAdapter: EntityStateAdapter<T, Id>): EntityStateFactory<T, Id> {\n  function getInitialState(state?: undefined, entities?: readonly T[] | Record<Id, T>): EntityState<T, Id>;\n  function getInitialState<S extends object>(additionalState: S, entities?: readonly T[] | Record<Id, T>): EntityState<T, Id> & S;\n  function getInitialState(additionalState: any = {}, entities?: readonly T[] | Record<Id, T>): any {\n    const state = Object.assign(getInitialEntityState(), additionalState);\n    return entities ? stateAdapter.setAll(state, entities) : state;\n  }\n  return {\n    getInitialState\n  };\n}", "import type { CreateSelectorFunction, Selector } from 'reselect';\nimport { createDraftSafeSelector } from '../createDraftSafeSelector';\nimport type { EntityId, EntitySelectors, EntityState } from './models';\ntype AnyFunction = (...args: any) => any;\ntype AnyCreateSelectorFunction = CreateSelectorFunction<<F extends AnyFunction>(f: F) => F, <F extends AnyFunction>(f: F) => F>;\nexport type GetSelectorsOptions = {\n  createSelector?: AnyCreateSelectorFunction;\n};\nexport function createSelectorsFactory<T, Id extends EntityId>() {\n  function getSelectors(selectState?: undefined, options?: GetSelectorsOptions): EntitySelectors<T, EntityState<T, Id>, Id>;\n  function getSelectors<V>(selectState: (state: V) => EntityState<T, Id>, options?: GetSelectorsOptions): EntitySelectors<T, V, Id>;\n  function getSelectors<V>(selectState?: (state: V) => EntityState<T, Id>, options: GetSelectorsOptions = {}): EntitySelectors<T, any, Id> {\n    const {\n      createSelector = createDraftSafeSelector as AnyCreateSelectorFunction\n    } = options;\n    const selectIds = (state: EntityState<T, Id>) => state.ids;\n    const selectEntities = (state: EntityState<T, Id>) => state.entities;\n    const selectAll = createSelector(selectIds, selectEntities, (ids, entities): T[] => ids.map(id => entities[id]!));\n    const selectId = (_: unknown, id: Id) => id;\n    const selectById = (entities: Record<Id, T>, id: Id) => entities[id];\n    const selectTotal = createSelector(selectIds, ids => ids.length);\n    if (!selectState) {\n      return {\n        selectIds,\n        selectEntities,\n        selectAll,\n        selectTotal,\n        selectById: createSelector(selectEntities, selectId, selectById)\n      };\n    }\n    const selectGlobalizedEntities = createSelector(selectState as Selector<V, EntityState<T, Id>>, selectEntities);\n    return {\n      selectIds: createSelector(selectState, selectIds),\n      selectEntities: selectGlobalizedEntities,\n      selectAll: createSelector(selectState, selectAll),\n      selectTotal: createSelector(selectState, selectTotal),\n      selectById: createSelector(selectGlobalizedEntities, selectId, selectById)\n    };\n  }\n  return {\n    getSelectors\n  };\n}", "import { produce as createNextState, isDraft } from 'immer';\nimport type { Draft } from 'immer';\nimport type { EntityId, DraftableEntityState, PreventAny } from './models';\nimport type { PayloadAction } from '../createAction';\nimport { isFSA } from '../createAction';\nexport const isDraftTyped = isDraft as <T>(value: T | Draft<T>) => value is Draft<T>;\nexport function createSingleArgumentStateOperator<T, Id extends EntityId>(mutator: (state: DraftableEntityState<T, Id>) => void) {\n  const operator = createStateOperator((_: undefined, state: DraftableEntityState<T, Id>) => mutator(state));\n  return function operation<S extends DraftableEntityState<T, Id>>(state: PreventAny<S, T, Id>): S {\n    return operator(state as S, undefined);\n  };\n}\nexport function createStateOperator<T, Id extends EntityId, R>(mutator: (arg: R, state: DraftableEntityState<T, Id>) => void) {\n  return function operation<S extends DraftableEntityState<T, Id>>(state: S, arg: R | PayloadAction<R>): S {\n    function isPayloadActionArgument(arg: R | PayloadAction<R>): arg is PayloadAction<R> {\n      return isFSA(arg);\n    }\n    const runMutator = (draft: DraftableEntityState<T, Id>) => {\n      if (isPayloadActionArgument(arg)) {\n        mutator(arg.payload, draft);\n      } else {\n        mutator(arg, draft);\n      }\n    };\n    if (isDraftTyped<DraftableEntityState<T, Id>>(state)) {\n      // we must already be inside a `createNextState` call, likely because\n      // this is being wrapped in `createReducer` or `createSlice`.\n      // It's safe to just pass the draft to the mutator.\n      runMutator(state);\n\n      // since it's a draft, we'll just return it\n      return state;\n    }\n    return createNextState(state, runMutator);\n  };\n}", "import type { Draft } from 'immer';\nimport { current, isDraft } from 'immer';\nimport type { DraftableEntityState, EntityId, IdSelector, Update } from './models';\nexport function selectIdValue<T, Id extends EntityId>(entity: T, selectId: IdSelector<T, Id>) {\n  const key = selectId(entity);\n  if (process.env.NODE_ENV !== 'production' && key === undefined) {\n    console.warn('The entity passed to the `selectId` implementation returned undefined.', 'You should probably provide your own `selectId` implementation.', 'The entity that was passed:', entity, 'The `selectId` implementation:', selectId.toString());\n  }\n  return key;\n}\nexport function ensureEntitiesArray<T, Id extends EntityId>(entities: readonly T[] | Record<Id, T>): readonly T[] {\n  if (!Array.isArray(entities)) {\n    entities = Object.values(entities);\n  }\n  return entities;\n}\nexport function getCurrent<T>(value: T | Draft<T>): T {\n  return (isDraft(value) ? current(value) : value) as T;\n}\nexport function splitAddedUpdatedEntities<T, Id extends EntityId>(newEntities: readonly T[] | Record<Id, T>, selectId: IdSelector<T, Id>, state: DraftableEntityState<T, Id>): [T[], Update<T, Id>[], Id[]] {\n  newEntities = ensureEntitiesArray(newEntities);\n  const existingIdsArray = getCurrent(state.ids);\n  const existingIds = new Set<Id>(existingIdsArray);\n  const added: T[] = [];\n  const addedIds = new Set<Id>([]);\n  const updated: Update<T, Id>[] = [];\n  for (const entity of newEntities) {\n    const id = selectIdValue(entity, selectId);\n    if (existingIds.has(id) || addedIds.has(id)) {\n      updated.push({\n        id,\n        changes: entity\n      });\n    } else {\n      addedIds.add(id);\n      added.push(entity);\n    }\n  }\n  return [added, updated, existingIdsArray];\n}", "import type { Draft } from 'immer';\nimport type { EntityStateAdapter, IdSelector, Update, EntityId, DraftableEntityState } from './models';\nimport { createStateOperator, createSingleArgumentStateOperator } from './state_adapter';\nimport { selectIdValue, ensureEntitiesArray, splitAddedUpdatedEntities } from './utils';\nexport function createUnsortedStateAdapter<T, Id extends EntityId>(selectId: IdSelector<T, Id>): EntityStateAdapter<T, Id> {\n  type R = DraftableEntityState<T, Id>;\n  function addOneMutably(entity: T, state: R): void {\n    const key = selectIdValue(entity, selectId);\n    if (key in state.entities) {\n      return;\n    }\n    state.ids.push(key as Id & Draft<Id>);\n    (state.entities as Record<Id, T>)[key] = entity;\n  }\n  function addManyMutably(newEntities: readonly T[] | Record<Id, T>, state: R): void {\n    newEntities = ensureEntitiesArray(newEntities);\n    for (const entity of newEntities) {\n      addOneMutably(entity, state);\n    }\n  }\n  function setOneMutably(entity: T, state: R): void {\n    const key = selectIdValue(entity, selectId);\n    if (!(key in state.entities)) {\n      state.ids.push(key as Id & Draft<Id>);\n    }\n    ;\n    (state.entities as Record<Id, T>)[key] = entity;\n  }\n  function setManyMutably(newEntities: readonly T[] | Record<Id, T>, state: R): void {\n    newEntities = ensureEntitiesArray(newEntities);\n    for (const entity of newEntities) {\n      setOneMutably(entity, state);\n    }\n  }\n  function setAllMutably(newEntities: readonly T[] | Record<Id, T>, state: R): void {\n    newEntities = ensureEntitiesArray(newEntities);\n    state.ids = [];\n    state.entities = {} as Record<Id, T>;\n    addManyMutably(newEntities, state);\n  }\n  function removeOneMutably(key: Id, state: R): void {\n    return removeManyMutably([key], state);\n  }\n  function removeManyMutably(keys: readonly Id[], state: R): void {\n    let didMutate = false;\n    keys.forEach(key => {\n      if (key in state.entities) {\n        delete (state.entities as Record<Id, T>)[key];\n        didMutate = true;\n      }\n    });\n    if (didMutate) {\n      state.ids = (state.ids as Id[]).filter(id => id in state.entities) as Id[] | Draft<Id[]>;\n    }\n  }\n  function removeAllMutably(state: R): void {\n    Object.assign(state, {\n      ids: [],\n      entities: {}\n    });\n  }\n  function takeNewKey(keys: {\n    [id: string]: Id;\n  }, update: Update<T, Id>, state: R): boolean {\n    const original: T | undefined = (state.entities as Record<Id, T>)[update.id];\n    if (original === undefined) {\n      return false;\n    }\n    const updated: T = Object.assign({}, original, update.changes);\n    const newKey = selectIdValue(updated, selectId);\n    const hasNewKey = newKey !== update.id;\n    if (hasNewKey) {\n      keys[update.id] = newKey;\n      delete (state.entities as Record<Id, T>)[update.id];\n    }\n    ;\n    (state.entities as Record<Id, T>)[newKey] = updated;\n    return hasNewKey;\n  }\n  function updateOneMutably(update: Update<T, Id>, state: R): void {\n    return updateManyMutably([update], state);\n  }\n  function updateManyMutably(updates: ReadonlyArray<Update<T, Id>>, state: R): void {\n    const newKeys: {\n      [id: string]: Id;\n    } = {};\n    const updatesPerEntity: {\n      [id: string]: Update<T, Id>;\n    } = {};\n    updates.forEach(update => {\n      // Only apply updates to entities that currently exist\n      if (update.id in state.entities) {\n        // If there are multiple updates to one entity, merge them together\n        updatesPerEntity[update.id] = {\n          id: update.id,\n          // Spreads ignore falsy values, so this works even if there isn't\n          // an existing update already at this key\n          changes: {\n            ...updatesPerEntity[update.id]?.changes,\n            ...update.changes\n          }\n        };\n      }\n    });\n    updates = Object.values(updatesPerEntity);\n    const didMutateEntities = updates.length > 0;\n    if (didMutateEntities) {\n      const didMutateIds = updates.filter(update => takeNewKey(newKeys, update, state)).length > 0;\n      if (didMutateIds) {\n        state.ids = Object.values(state.entities).map(e => selectIdValue(e as T, selectId));\n      }\n    }\n  }\n  function upsertOneMutably(entity: T, state: R): void {\n    return upsertManyMutably([entity], state);\n  }\n  function upsertManyMutably(newEntities: readonly T[] | Record<Id, T>, state: R): void {\n    const [added, updated] = splitAddedUpdatedEntities<T, Id>(newEntities, selectId, state);\n    addManyMutably(added, state);\n    updateManyMutably(updated, state);\n  }\n  return {\n    removeAll: createSingleArgumentStateOperator(removeAllMutably),\n    addOne: createStateOperator(addOneMutably),\n    addMany: createStateOperator(addManyMutably),\n    setOne: createStateOperator(setOneMutably),\n    setMany: createStateOperator(setManyMutably),\n    setAll: createStateOperator(setAllMutably),\n    updateOne: createStateOperator(updateOneMutably),\n    updateMany: createStateOperator(updateManyMutably),\n    upsertOne: createStateOperator(upsertOneMutably),\n    upsertMany: createStateOperator(upsertManyMutably),\n    removeOne: createStateOperator(removeOneMutably),\n    removeMany: createStateOperator(removeManyMutably)\n  };\n}", "import type { IdSelector, Comparer, EntityStateAdapter, Update, EntityId, DraftableEntityState } from './models';\nimport { createStateOperator } from './state_adapter';\nimport { createUnsortedStateAdapter } from './unsorted_state_adapter';\nimport { selectIdValue, ensureEntitiesArray, splitAddedUpdatedEntities, getCurrent } from './utils';\n\n// Borrowed from Replay\nexport function findInsertIndex<T>(sortedItems: T[], item: T, comparisonFunction: Comparer<T>): number {\n  let lowIndex = 0;\n  let highIndex = sortedItems.length;\n  while (lowIndex < highIndex) {\n    let middleIndex = lowIndex + highIndex >>> 1;\n    const currentItem = sortedItems[middleIndex];\n    const res = comparisonFunction(item, currentItem);\n    if (res >= 0) {\n      lowIndex = middleIndex + 1;\n    } else {\n      highIndex = middleIndex;\n    }\n  }\n  return lowIndex;\n}\nexport function insert<T>(sortedItems: T[], item: T, comparisonFunction: Comparer<T>): T[] {\n  const insertAtIndex = findInsertIndex(sortedItems, item, comparisonFunction);\n  sortedItems.splice(insertAtIndex, 0, item);\n  return sortedItems;\n}\nexport function createSortedStateAdapter<T, Id extends EntityId>(selectId: IdSelector<T, Id>, comparer: Comparer<T>): EntityStateAdapter<T, Id> {\n  type R = DraftableEntityState<T, Id>;\n  const {\n    removeOne,\n    removeMany,\n    removeAll\n  } = createUnsortedStateAdapter(selectId);\n  function addOneMutably(entity: T, state: R): void {\n    return addManyMutably([entity], state);\n  }\n  function addManyMutably(newEntities: readonly T[] | Record<Id, T>, state: R, existingIds?: Id[]): void {\n    newEntities = ensureEntitiesArray(newEntities);\n    const existingKeys = new Set<Id>(existingIds ?? getCurrent(state.ids));\n    const models = newEntities.filter(model => !existingKeys.has(selectIdValue(model, selectId)));\n    if (models.length !== 0) {\n      mergeFunction(state, models);\n    }\n  }\n  function setOneMutably(entity: T, state: R): void {\n    return setManyMutably([entity], state);\n  }\n  function setManyMutably(newEntities: readonly T[] | Record<Id, T>, state: R): void {\n    newEntities = ensureEntitiesArray(newEntities);\n    if (newEntities.length !== 0) {\n      for (const item of newEntities) {\n        delete (state.entities as Record<Id, T>)[selectId(item)];\n      }\n      mergeFunction(state, newEntities);\n    }\n  }\n  function setAllMutably(newEntities: readonly T[] | Record<Id, T>, state: R): void {\n    newEntities = ensureEntitiesArray(newEntities);\n    state.entities = {} as Record<Id, T>;\n    state.ids = [];\n    addManyMutably(newEntities, state, []);\n  }\n  function updateOneMutably(update: Update<T, Id>, state: R): void {\n    return updateManyMutably([update], state);\n  }\n  function updateManyMutably(updates: ReadonlyArray<Update<T, Id>>, state: R): void {\n    let appliedUpdates = false;\n    let replacedIds = false;\n    for (let update of updates) {\n      const entity: T | undefined = (state.entities as Record<Id, T>)[update.id];\n      if (!entity) {\n        continue;\n      }\n      appliedUpdates = true;\n      Object.assign(entity, update.changes);\n      const newId = selectId(entity);\n      if (update.id !== newId) {\n        // We do support the case where updates can change an item's ID.\n        // This makes things trickier - go ahead and swap the IDs in state now.\n        replacedIds = true;\n        delete (state.entities as Record<Id, T>)[update.id];\n        const oldIndex = (state.ids as Id[]).indexOf(update.id);\n        state.ids[oldIndex] = newId;\n        (state.entities as Record<Id, T>)[newId] = entity;\n      }\n    }\n    if (appliedUpdates) {\n      mergeFunction(state, [], appliedUpdates, replacedIds);\n    }\n  }\n  function upsertOneMutably(entity: T, state: R): void {\n    return upsertManyMutably([entity], state);\n  }\n  function upsertManyMutably(newEntities: readonly T[] | Record<Id, T>, state: R): void {\n    const [added, updated, existingIdsArray] = splitAddedUpdatedEntities<T, Id>(newEntities, selectId, state);\n    if (added.length) {\n      addManyMutably(added, state, existingIdsArray);\n    }\n    if (updated.length) {\n      updateManyMutably(updated, state);\n    }\n  }\n  function areArraysEqual(a: readonly unknown[], b: readonly unknown[]) {\n    if (a.length !== b.length) {\n      return false;\n    }\n    for (let i = 0; i < a.length; i++) {\n      if (a[i] === b[i]) {\n        continue;\n      }\n      return false;\n    }\n    return true;\n  }\n  type MergeFunction = (state: R, addedItems: readonly T[], appliedUpdates?: boolean, replacedIds?: boolean) => void;\n  const mergeFunction: MergeFunction = (state, addedItems, appliedUpdates, replacedIds) => {\n    const currentEntities = getCurrent(state.entities);\n    const currentIds = getCurrent(state.ids);\n    const stateEntities = state.entities as Record<Id, T>;\n    let ids: Iterable<Id> = currentIds;\n    if (replacedIds) {\n      ids = new Set(currentIds);\n    }\n    let sortedEntities: T[] = [];\n    for (const id of ids) {\n      const entity = currentEntities[id];\n      if (entity) {\n        sortedEntities.push(entity);\n      }\n    }\n    const wasPreviouslyEmpty = sortedEntities.length === 0;\n\n    // Insert/overwrite all new/updated\n    for (const item of addedItems) {\n      stateEntities[selectId(item)] = item;\n      if (!wasPreviouslyEmpty) {\n        // Binary search insertion generally requires fewer comparisons\n        insert(sortedEntities, item, comparer);\n      }\n    }\n    if (wasPreviouslyEmpty) {\n      // All we have is the incoming values, sort them\n      sortedEntities = addedItems.slice().sort(comparer);\n    } else if (appliedUpdates) {\n      // We should have a _mostly_-sorted array already\n      sortedEntities.sort(comparer);\n    }\n    const newSortedIds = sortedEntities.map(selectId);\n    if (!areArraysEqual(currentIds, newSortedIds)) {\n      state.ids = newSortedIds;\n    }\n  };\n  return {\n    removeOne,\n    removeMany,\n    removeAll,\n    addOne: createStateOperator(addOneMutably),\n    updateOne: createStateOperator(updateOneMutably),\n    upsertOne: createStateOperator(upsertOneMutably),\n    setOne: createStateOperator(setOneMutably),\n    setMany: createStateOperator(setManyMutably),\n    setAll: createStateOperator(setAllMutably),\n    addMany: createStateOperator(addManyMutably),\n    updateMany: createStateOperator(updateManyMutably),\n    upsertMany: createStateOperator(upsertManyMutably)\n  };\n}", "import type { EntityAdapter, EntityId, EntityAdapterOptions } from './models';\nimport { createInitialStateFactory } from './entity_state';\nimport { createSelectorsFactory } from './state_selectors';\nimport { createSortedStateAdapter } from './sorted_state_adapter';\nimport { createUnsortedStateAdapter } from './unsorted_state_adapter';\nimport type { WithRequiredProp } from '../tsHelpers';\nexport function createEntityAdapter<T, Id extends EntityId>(options: WithRequiredProp<EntityAdapterOptions<T, Id>, 'selectId'>): EntityAdapter<T, Id>;\nexport function createEntityAdapter<T extends {\n  id: EntityId;\n}>(options?: Omit<EntityAdapterOptions<T, T['id']>, 'selectId'>): EntityAdapter<T, T['id']>;\n\n/**\n *\n * @param options\n *\n * @public\n */\nexport function createEntityAdapter<T>(options: EntityAdapterOptions<T, EntityId> = {}): EntityAdapter<T, EntityId> {\n  const {\n    selectId,\n    sortComparer\n  }: Required<EntityAdapterOptions<T, EntityId>> = {\n    sortComparer: false,\n    selectId: (instance: any) => instance.id,\n    ...options\n  };\n  const stateAdapter = sortComparer ? createSortedStateAdapter(selectId, sortComparer) : createUnsortedStateAdapter(selectId);\n  const stateFactory = createInitialStateFactory(stateAdapter);\n  const selectorsFactory = createSelectorsFactory<T, EntityId>();\n  return {\n    selectId,\n    sortComparer,\n    ...stateFactory,\n    ...selectorsFactory,\n    ...stateAdapter\n  };\n}", "import { formatProdErrorMessage as _formatProdErrorMessage, formatProdErrorMessage as _formatProdErrorMessage2, formatProdErrorMessage as _formatProdErrorMessage3 } from \"@reduxjs/toolkit\";\nimport type { Action, Dispatch, MiddlewareAPI, UnknownAction } from 'redux';\nimport { isAction } from 'redux';\nimport type { ThunkDispatch } from 'redux-thunk';\nimport { createAction } from '../createAction';\nimport { nanoid } from '../nanoid';\nimport { TaskAbortError, listenerCancelled, listenerCompleted, taskCancelled, taskCompleted } from './exceptions';\nimport { createDelay, createPause, raceWithSignal, runTask, validateActive } from './task';\nimport type { AbortSignalWithReason, AddListenerOverloads, AnyListenerPredicate, CreateListenerMiddlewareOptions, FallbackAddListenerOptions, ForkOptions, ForkedTask, ForkedTaskExecutor, ListenerEntry, ListenerErrorHandler, ListenerErrorInfo, ListenerMiddleware, ListenerMiddlewareInstance, TakePattern, TaskResult, TypedAddListener, TypedCreateListenerEntry, TypedRemoveListener, UnsubscribeListener, UnsubscribeListenerOptions } from './types';\nimport { abortControllerWithReason, addAbortSignalListener, assertFunction, catchRejection, noop } from './utils';\nexport { TaskAbortError } from './exceptions';\nexport type { AsyncTaskExecutor, CreateListenerMiddlewareOptions, ForkedTask, ForkedTaskAPI, ForkedTaskExecutor, ListenerEffect, ListenerEffectAPI, ListenerErrorHandler, ListenerMiddleware, ListenerMiddlewareInstance, SyncTaskExecutor, TaskCancelled, TaskRejected, TaskResolved, TaskResult, TypedAddListener, TypedRemoveListener, TypedStartListening, TypedStopListening, UnsubscribeListener, UnsubscribeListenerOptions } from './types';\n\n//Overly-aggressive byte-shaving\nconst {\n  assign\n} = Object;\n/**\n * @internal\n */\nconst INTERNAL_NIL_TOKEN = {} as const;\nconst alm = 'listenerMiddleware' as const;\nconst createFork = (parentAbortSignal: AbortSignalWithReason<unknown>, parentBlockingPromises: Promise<any>[]) => {\n  const linkControllers = (controller: AbortController) => addAbortSignalListener(parentAbortSignal, () => abortControllerWithReason(controller, parentAbortSignal.reason));\n  return <T,>(taskExecutor: ForkedTaskExecutor<T>, opts?: ForkOptions): ForkedTask<T> => {\n    assertFunction(taskExecutor, 'taskExecutor');\n    const childAbortController = new AbortController();\n    linkControllers(childAbortController);\n    const result = runTask<T>(async (): Promise<T> => {\n      validateActive(parentAbortSignal);\n      validateActive(childAbortController.signal);\n      const result = (await taskExecutor({\n        pause: createPause(childAbortController.signal),\n        delay: createDelay(childAbortController.signal),\n        signal: childAbortController.signal\n      })) as T;\n      validateActive(childAbortController.signal);\n      return result;\n    }, () => abortControllerWithReason(childAbortController, taskCompleted));\n    if (opts?.autoJoin) {\n      parentBlockingPromises.push(result.catch(noop));\n    }\n    return {\n      result: createPause<TaskResult<T>>(parentAbortSignal)(result),\n      cancel() {\n        abortControllerWithReason(childAbortController, taskCancelled);\n      }\n    };\n  };\n};\nconst createTakePattern = <S,>(startListening: AddListenerOverloads<UnsubscribeListener, S, Dispatch>, signal: AbortSignal): TakePattern<S> => {\n  /**\n   * A function that takes a ListenerPredicate and an optional timeout,\n   * and resolves when either the predicate returns `true` based on an action\n   * state combination or when the timeout expires.\n   * If the parent listener is canceled while waiting, this will throw a\n   * TaskAbortError.\n   */\n  const take = async <P extends AnyListenerPredicate<S>,>(predicate: P, timeout: number | undefined) => {\n    validateActive(signal);\n\n    // Placeholder unsubscribe function until the listener is added\n    let unsubscribe: UnsubscribeListener = () => {};\n    const tuplePromise = new Promise<[Action, S, S]>((resolve, reject) => {\n      // Inside the Promise, we synchronously add the listener.\n      let stopListening = startListening({\n        predicate: predicate as any,\n        effect: (action, listenerApi): void => {\n          // One-shot listener that cleans up as soon as the predicate passes\n          listenerApi.unsubscribe();\n          // Resolve the promise with the same arguments the predicate saw\n          resolve([action, listenerApi.getState(), listenerApi.getOriginalState()]);\n        }\n      });\n      unsubscribe = () => {\n        stopListening();\n        reject();\n      };\n    });\n    const promises: (Promise<null> | Promise<[Action, S, S]>)[] = [tuplePromise];\n    if (timeout != null) {\n      promises.push(new Promise<null>(resolve => setTimeout(resolve, timeout, null)));\n    }\n    try {\n      const output = await raceWithSignal(signal, Promise.race(promises));\n      validateActive(signal);\n      return output;\n    } finally {\n      // Always clean up the listener\n      unsubscribe();\n    }\n  };\n  return ((predicate: AnyListenerPredicate<S>, timeout: number | undefined) => catchRejection(take(predicate, timeout))) as TakePattern<S>;\n};\nconst getListenerEntryPropsFrom = (options: FallbackAddListenerOptions) => {\n  let {\n    type,\n    actionCreator,\n    matcher,\n    predicate,\n    effect\n  } = options;\n  if (type) {\n    predicate = createAction(type).match;\n  } else if (actionCreator) {\n    type = actionCreator!.type;\n    predicate = actionCreator.match;\n  } else if (matcher) {\n    predicate = matcher;\n  } else if (predicate) {\n    // pass\n  } else {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(21) : 'Creating or removing a listener requires one of the known fields for matching an action');\n  }\n  assertFunction(effect, 'options.listener');\n  return {\n    predicate,\n    type,\n    effect\n  };\n};\n\n/** Accepts the possible options for creating a listener, and returns a formatted listener entry */\nexport const createListenerEntry: TypedCreateListenerEntry<unknown> = /* @__PURE__ */assign((options: FallbackAddListenerOptions) => {\n  const {\n    type,\n    predicate,\n    effect\n  } = getListenerEntryPropsFrom(options);\n  const entry: ListenerEntry<unknown> = {\n    id: nanoid(),\n    effect,\n    type,\n    predicate,\n    pending: new Set<AbortController>(),\n    unsubscribe: () => {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(22) : 'Unsubscribe not initialized');\n    }\n  };\n  return entry;\n}, {\n  withTypes: () => createListenerEntry\n}) as unknown as TypedCreateListenerEntry<unknown>;\nconst findListenerEntry = (listenerMap: Map<string, ListenerEntry>, options: FallbackAddListenerOptions) => {\n  const {\n    type,\n    effect,\n    predicate\n  } = getListenerEntryPropsFrom(options);\n  return Array.from(listenerMap.values()).find(entry => {\n    const matchPredicateOrType = typeof type === 'string' ? entry.type === type : entry.predicate === predicate;\n    return matchPredicateOrType && entry.effect === effect;\n  });\n};\nconst cancelActiveListeners = (entry: ListenerEntry<unknown, Dispatch<UnknownAction>>) => {\n  entry.pending.forEach(controller => {\n    abortControllerWithReason(controller, listenerCancelled);\n  });\n};\nconst createClearListenerMiddleware = (listenerMap: Map<string, ListenerEntry>) => {\n  return () => {\n    listenerMap.forEach(cancelActiveListeners);\n    listenerMap.clear();\n  };\n};\n\n/**\n * Safely reports errors to the `errorHandler` provided.\n * Errors that occur inside `errorHandler` are notified in a new task.\n * Inspired by [rxjs reportUnhandledError](https://github.com/ReactiveX/rxjs/blob/6fafcf53dc9e557439b25debaeadfd224b245a66/src/internal/util/reportUnhandledError.ts)\n * @param errorHandler\n * @param errorToNotify\n */\nconst safelyNotifyError = (errorHandler: ListenerErrorHandler, errorToNotify: unknown, errorInfo: ListenerErrorInfo): void => {\n  try {\n    errorHandler(errorToNotify, errorInfo);\n  } catch (errorHandlerError) {\n    // We cannot let an error raised here block the listener queue.\n    // The error raised here will be picked up by `window.onerror`, `process.on('error')` etc...\n    setTimeout(() => {\n      throw errorHandlerError;\n    }, 0);\n  }\n};\n\n/**\n * @public\n */\nexport const addListener = /* @__PURE__ */assign(/* @__PURE__ */createAction(`${alm}/add`), {\n  withTypes: () => addListener\n}) as unknown as TypedAddListener<unknown>;\n\n/**\n * @public\n */\nexport const clearAllListeners = /* @__PURE__ */createAction(`${alm}/removeAll`);\n\n/**\n * @public\n */\nexport const removeListener = /* @__PURE__ */assign(/* @__PURE__ */createAction(`${alm}/remove`), {\n  withTypes: () => removeListener\n}) as unknown as TypedRemoveListener<unknown>;\nconst defaultErrorHandler: ListenerErrorHandler = (...args: unknown[]) => {\n  console.error(`${alm}/error`, ...args);\n};\n\n/**\n * @public\n */\nexport const createListenerMiddleware = <StateType = unknown, DispatchType extends Dispatch<Action> = ThunkDispatch<StateType, unknown, UnknownAction>, ExtraArgument = unknown>(middlewareOptions: CreateListenerMiddlewareOptions<ExtraArgument> = {}) => {\n  const listenerMap = new Map<string, ListenerEntry>();\n  const {\n    extra,\n    onError = defaultErrorHandler\n  } = middlewareOptions;\n  assertFunction(onError, 'onError');\n  const insertEntry = (entry: ListenerEntry) => {\n    entry.unsubscribe = () => listenerMap.delete(entry.id);\n    listenerMap.set(entry.id, entry);\n    return (cancelOptions?: UnsubscribeListenerOptions) => {\n      entry.unsubscribe();\n      if (cancelOptions?.cancelActive) {\n        cancelActiveListeners(entry);\n      }\n    };\n  };\n  const startListening = ((options: FallbackAddListenerOptions) => {\n    const entry = findListenerEntry(listenerMap, options) ?? createListenerEntry(options as any);\n    return insertEntry(entry);\n  }) as AddListenerOverloads<any>;\n  assign(startListening, {\n    withTypes: () => startListening\n  });\n  const stopListening = (options: FallbackAddListenerOptions & UnsubscribeListenerOptions): boolean => {\n    const entry = findListenerEntry(listenerMap, options);\n    if (entry) {\n      entry.unsubscribe();\n      if (options.cancelActive) {\n        cancelActiveListeners(entry);\n      }\n    }\n    return !!entry;\n  };\n  assign(stopListening, {\n    withTypes: () => stopListening\n  });\n  const notifyListener = async (entry: ListenerEntry<unknown, Dispatch<UnknownAction>>, action: unknown, api: MiddlewareAPI, getOriginalState: () => StateType) => {\n    const internalTaskController = new AbortController();\n    const take = createTakePattern(startListening as AddListenerOverloads<any>, internalTaskController.signal);\n    const autoJoinPromises: Promise<any>[] = [];\n    try {\n      entry.pending.add(internalTaskController);\n      await Promise.resolve(entry.effect(action,\n      // Use assign() rather than ... to avoid extra helper functions added to bundle\n      assign({}, api, {\n        getOriginalState,\n        condition: (predicate: AnyListenerPredicate<any>, timeout?: number) => take(predicate, timeout).then(Boolean),\n        take,\n        delay: createDelay(internalTaskController.signal),\n        pause: createPause<any>(internalTaskController.signal),\n        extra,\n        signal: internalTaskController.signal,\n        fork: createFork(internalTaskController.signal, autoJoinPromises),\n        unsubscribe: entry.unsubscribe,\n        subscribe: () => {\n          listenerMap.set(entry.id, entry);\n        },\n        cancelActiveListeners: () => {\n          entry.pending.forEach((controller, _, set) => {\n            if (controller !== internalTaskController) {\n              abortControllerWithReason(controller, listenerCancelled);\n              set.delete(controller);\n            }\n          });\n        },\n        cancel: () => {\n          abortControllerWithReason(internalTaskController, listenerCancelled);\n          entry.pending.delete(internalTaskController);\n        },\n        throwIfCancelled: () => {\n          validateActive(internalTaskController.signal);\n        }\n      })));\n    } catch (listenerError) {\n      if (!(listenerError instanceof TaskAbortError)) {\n        safelyNotifyError(onError, listenerError, {\n          raisedBy: 'effect'\n        });\n      }\n    } finally {\n      await Promise.all(autoJoinPromises);\n      abortControllerWithReason(internalTaskController, listenerCompleted); // Notify that the task has completed\n      entry.pending.delete(internalTaskController);\n    }\n  };\n  const clearListenerMiddleware = createClearListenerMiddleware(listenerMap);\n  const middleware: ListenerMiddleware<StateType, DispatchType, ExtraArgument> = api => next => action => {\n    if (!isAction(action)) {\n      // we only want to notify listeners for action objects\n      return next(action);\n    }\n    if (addListener.match(action)) {\n      return startListening(action.payload as any);\n    }\n    if (clearAllListeners.match(action)) {\n      clearListenerMiddleware();\n      return;\n    }\n    if (removeListener.match(action)) {\n      return stopListening(action.payload);\n    }\n\n    // Need to get this state _before_ the reducer processes the action\n    let originalState: StateType | typeof INTERNAL_NIL_TOKEN = api.getState();\n\n    // `getOriginalState` can only be called synchronously.\n    // @see https://github.com/reduxjs/redux-toolkit/discussions/1648#discussioncomment-1932820\n    const getOriginalState = (): StateType => {\n      if (originalState === INTERNAL_NIL_TOKEN) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage3(23) : `${alm}: getOriginalState can only be called synchronously`);\n      }\n      return originalState as StateType;\n    };\n    let result: unknown;\n    try {\n      // Actually forward the action to the reducer before we handle listeners\n      result = next(action);\n      if (listenerMap.size > 0) {\n        const currentState = api.getState();\n        // Work around ESBuild+TS transpilation issue\n        const listenerEntries = Array.from(listenerMap.values());\n        for (const entry of listenerEntries) {\n          let runListener = false;\n          try {\n            runListener = entry.predicate(action, currentState, originalState);\n          } catch (predicateError) {\n            runListener = false;\n            safelyNotifyError(onError, predicateError, {\n              raisedBy: 'predicate'\n            });\n          }\n          if (!runListener) {\n            continue;\n          }\n          notifyListener(entry, action, api, getOriginalState);\n        }\n      }\n    } finally {\n      // Remove `originalState` store from this scope.\n      originalState = INTERNAL_NIL_TOKEN;\n    }\n    return result;\n  };\n  return {\n    middleware,\n    startListening,\n    stopListening,\n    clearListeners: clearListenerMiddleware\n  } as ListenerMiddlewareInstance<StateType, DispatchType, ExtraArgument>;\n};", "import type { SerializedError } from '@reduxjs/toolkit';\nconst task = 'task';\nconst listener = 'listener';\nconst completed = 'completed';\nconst cancelled = 'cancelled';\n\n/* TaskAbortError error codes  */\nexport const taskCancelled = `task-${cancelled}` as const;\nexport const taskCompleted = `task-${completed}` as const;\nexport const listenerCancelled = `${listener}-${cancelled}` as const;\nexport const listenerCompleted = `${listener}-${completed}` as const;\nexport class TaskAbortError implements SerializedError {\n  name = 'TaskAbortError';\n  message: string;\n  constructor(public code: string | undefined) {\n    this.message = `${task} ${cancelled} (reason: ${code})`;\n  }\n}", "import { formatProdErrorMessage as _formatProdErrorMessage } from \"@reduxjs/toolkit\";\nimport type { AbortSignalWithReason } from './types';\nexport const assertFunction: (func: unknown, expected: string) => asserts func is (...args: unknown[]) => unknown = (func: unknown, expected: string) => {\n  if (typeof func !== 'function') {\n    throw new TypeError(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(32) : `${expected} is not a function`);\n  }\n};\nexport const noop = () => {};\nexport const catchRejection = <T,>(promise: Promise<T>, onError = noop): Promise<T> => {\n  promise.catch(onError);\n  return promise;\n};\nexport const addAbortSignalListener = (abortSignal: AbortSignal, callback: (evt: Event) => void) => {\n  abortSignal.addEventListener('abort', callback, {\n    once: true\n  });\n  return () => abortSignal.removeEventListener('abort', callback);\n};\n\n/**\n * Calls `abortController.abort(reason)` and patches `signal.reason`.\n * if it is not supported.\n *\n * At the time of writing `signal.reason` is available in FF chrome, edge node 17 and deno.\n * @param abortController\n * @param reason\n * @returns\n * @see https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal/reason\n */\nexport const abortControllerWithReason = <T,>(abortController: AbortController, reason: T): void => {\n  type Consumer<T> = (val: T) => void;\n  const signal = abortController.signal as AbortSignalWithReason<T>;\n  if (signal.aborted) {\n    return;\n  }\n\n  // Patch `reason` if necessary.\n  // - We use defineProperty here because reason is a getter of `AbortSignal.__proto__`.\n  // - We need to patch 'reason' before calling `.abort()` because listeners to the 'abort'\n  // event are are notified immediately.\n  if (!('reason' in signal)) {\n    Object.defineProperty(signal, 'reason', {\n      enumerable: true,\n      value: reason,\n      configurable: true,\n      writable: true\n    });\n  }\n  ;\n  (abortController.abort as Consumer<typeof reason>)(reason);\n};", "import { TaskAbortError } from './exceptions';\nimport type { AbortSignalWithReason, TaskResult } from './types';\nimport { addAbortSignalListener, catchRejection, noop } from './utils';\n\n/**\n * Synchronously raises {@link TaskAbortError} if the task tied to the input `signal` has been cancelled.\n * @param signal\n * @param reason\n * @see {TaskAbortError}\n */\nexport const validateActive = (signal: AbortSignal): void => {\n  if (signal.aborted) {\n    const {\n      reason\n    } = signal as AbortSignalWithReason<string>;\n    throw new TaskAbortError(reason);\n  }\n};\n\n/**\n * Generates a race between the promise(s) and the AbortSignal\n * This avoids `Promise.race()`-related memory leaks:\n * https://github.com/nodejs/node/issues/17469#issuecomment-349794909\n */\nexport function raceWithSignal<T>(signal: AbortSignalWithReason<string>, promise: Promise<T>): Promise<T> {\n  let cleanup = noop;\n  return new Promise<T>((resolve, reject) => {\n    const notifyRejection = () => reject(new TaskAbortError(signal.reason));\n    if (signal.aborted) {\n      notifyRejection();\n      return;\n    }\n    cleanup = addAbortSignalListener(signal, notifyRejection);\n    promise.finally(() => cleanup()).then(resolve, reject);\n  }).finally(() => {\n    // after this point, replace `cleanup` with a noop, so there is no reference to `signal` any more\n    cleanup = noop;\n  });\n}\n\n/**\n * Runs a task and returns promise that resolves to {@link TaskResult}.\n * Second argument is an optional `cleanUp` function that always runs after task.\n *\n * **Note:** `runTask` runs the executor in the next microtask.\n * @returns\n */\nexport const runTask = async <T,>(task: () => Promise<T>, cleanUp?: () => void): Promise<TaskResult<T>> => {\n  try {\n    await Promise.resolve();\n    const value = await task();\n    return {\n      status: 'ok',\n      value\n    };\n  } catch (error: any) {\n    return {\n      status: error instanceof TaskAbortError ? 'cancelled' : 'rejected',\n      error\n    };\n  } finally {\n    cleanUp?.();\n  }\n};\n\n/**\n * Given an input `AbortSignal` and a promise returns another promise that resolves\n * as soon the input promise is provided or rejects as soon as\n * `AbortSignal.abort` is `true`.\n * @param signal\n * @returns\n */\nexport const createPause = <T,>(signal: AbortSignal) => {\n  return (promise: Promise<T>): Promise<T> => {\n    return catchRejection(raceWithSignal(signal, promise).then(output => {\n      validateActive(signal);\n      return output;\n    }));\n  };\n};\n\n/**\n * Given an input `AbortSignal` and `timeoutMs` returns a promise that resolves\n * after `timeoutMs` or rejects as soon as `AbortSignal.abort` is `true`.\n * @param signal\n * @returns\n */\nexport const createDelay = (signal: AbortSignal) => {\n  const pause = createPause<void>(signal);\n  return (timeoutMs: number): Promise<void> => {\n    return pause(new Promise<void>(resolve => setTimeout(resolve, timeoutMs)));\n  };\n};", "import type { Dispatch, Middleware, UnknownAction } from 'redux';\nimport { compose } from 'redux';\nimport { createAction } from '../createAction';\nimport { isAllOf } from '../matchers';\nimport { nanoid } from '../nanoid';\nimport { getOrInsertComputed } from '../utils';\nimport type { AddMiddleware, DynamicMiddleware, DynamicMiddlewareInstance, MiddlewareEntry, WithMiddleware } from './types';\nexport type { DynamicMiddlewareInstance, GetDispatchType as GetDispatch, MiddlewareApiConfig } from './types';\nconst createMiddlewareEntry = <State = any, DispatchType extends Dispatch<UnknownAction> = Dispatch<UnknownAction>>(middleware: Middleware<any, State, DispatchType>): MiddlewareEntry<State, DispatchType> => ({\n  middleware,\n  applied: new Map()\n});\nconst matchInstance = (instanceId: string) => (action: any): action is {\n  meta: {\n    instanceId: string;\n  };\n} => action?.meta?.instanceId === instanceId;\nexport const createDynamicMiddleware = <State = any, DispatchType extends Dispatch<UnknownAction> = Dispatch<UnknownAction>>(): DynamicMiddlewareInstance<State, DispatchType> => {\n  const instanceId = nanoid();\n  const middlewareMap = new Map<Middleware<any, State, DispatchType>, MiddlewareEntry<State, DispatchType>>();\n  const withMiddleware = Object.assign(createAction('dynamicMiddleware/add', (...middlewares: Middleware<any, State, DispatchType>[]) => ({\n    payload: middlewares,\n    meta: {\n      instanceId\n    }\n  })), {\n    withTypes: () => withMiddleware\n  }) as WithMiddleware<State, DispatchType>;\n  const addMiddleware = Object.assign(function addMiddleware(...middlewares: Middleware<any, State, DispatchType>[]) {\n    middlewares.forEach(middleware => {\n      getOrInsertComputed(middlewareMap, middleware, createMiddlewareEntry);\n    });\n  }, {\n    withTypes: () => addMiddleware\n  }) as AddMiddleware<State, DispatchType>;\n  const getFinalMiddleware: Middleware<{}, State, DispatchType> = api => {\n    const appliedMiddleware = Array.from(middlewareMap.values()).map(entry => getOrInsertComputed(entry.applied, api, entry.middleware));\n    return compose(...appliedMiddleware);\n  };\n  const isWithMiddleware = isAllOf(withMiddleware, matchInstance(instanceId));\n  const middleware: DynamicMiddleware<State, DispatchType> = api => next => action => {\n    if (isWithMiddleware(action)) {\n      addMiddleware(...action.payload);\n      return api.dispatch;\n    }\n    return getFinalMiddleware(api)(next)(action);\n  };\n  return {\n    middleware,\n    addMiddleware,\n    withMiddleware,\n    instanceId\n  };\n};", "import { formatProdErrorMessage as _formatProdErrorMessage, formatProdErrorMessage as _formatProdErrorMessage2 } from \"@reduxjs/toolkit\";\nimport type { Reducer, StateFromReducersMapObject, UnknownAction } from 'redux';\nimport { combineReducers } from 'redux';\nimport { nanoid } from './nanoid';\nimport type { Id, NonUndefined, Tail, UnionToIntersection, WithOptionalProp } from './tsHelpers';\nimport { getOrInsertComputed } from './utils';\ntype SliceLike<ReducerPath extends string, State> = {\n  reducerPath: ReducerPath;\n  reducer: Reducer<State>;\n};\ntype AnySliceLike = SliceLike<string, any>;\ntype SliceLikeReducerPath<A extends AnySliceLike> = A extends SliceLike<infer ReducerPath, any> ? ReducerPath : never;\ntype SliceLikeState<A extends AnySliceLike> = A extends SliceLike<any, infer State> ? State : never;\nexport type WithSlice<A extends AnySliceLike> = { [Path in SliceLikeReducerPath<A>]: SliceLikeState<A> };\ntype ReducerMap = Record<string, Reducer>;\ntype ExistingSliceLike<DeclaredState> = { [ReducerPath in keyof DeclaredState]: SliceLike<ReducerPath & string, NonUndefined<DeclaredState[ReducerPath]>> }[keyof DeclaredState];\nexport type InjectConfig = {\n  /**\n   * Allow replacing reducer with a different reference. Normally, an error will be thrown if a different reducer instance to the one already injected is used.\n   */\n  overrideExisting?: boolean;\n};\n\n/**\n * A reducer that allows for slices/reducers to be injected after initialisation.\n */\nexport interface CombinedSliceReducer<InitialState, DeclaredState = InitialState> extends Reducer<DeclaredState, UnknownAction, Partial<DeclaredState>> {\n  /**\n   * Provide a type for slices that will be injected lazily.\n   *\n   * One way to do this would be with interface merging:\n   * ```ts\n   *\n   * export interface LazyLoadedSlices {}\n   *\n   * export const rootReducer = combineSlices(stringSlice).withLazyLoadedSlices<LazyLoadedSlices>();\n   *\n   * // elsewhere\n   *\n   * declare module './reducer' {\n   *   export interface LazyLoadedSlices extends WithSlice<typeof booleanSlice> {}\n   * }\n   *\n   * const withBoolean = rootReducer.inject(booleanSlice);\n   *\n   * // elsewhere again\n   *\n   * declare module './reducer' {\n   *   export interface LazyLoadedSlices {\n   *     customName: CustomState\n   *   }\n   * }\n   *\n   * const withCustom = rootReducer.inject({ reducerPath: \"customName\", reducer: customSlice.reducer })\n   * ```\n   */\n  withLazyLoadedSlices<Lazy = {}>(): CombinedSliceReducer<InitialState, Id<DeclaredState & Partial<Lazy>>>;\n\n  /**\n   * Inject a slice.\n   *\n   * Accepts an individual slice, RTKQ API instance, or a \"slice-like\" { reducerPath, reducer } object.\n   *\n   * ```ts\n   * rootReducer.inject(booleanSlice)\n   * rootReducer.inject(baseApi)\n   * rootReducer.inject({ reducerPath: 'boolean' as const, reducer: newReducer }, { overrideExisting: true })\n   * ```\n   *\n   */\n  inject<Sl extends Id<ExistingSliceLike<DeclaredState>>>(slice: Sl, config?: InjectConfig): CombinedSliceReducer<InitialState, Id<DeclaredState & WithSlice<Sl>>>;\n\n  /**\n   * Inject a slice.\n   *\n   * Accepts an individual slice, RTKQ API instance, or a \"slice-like\" { reducerPath, reducer } object.\n   *\n   * ```ts\n   * rootReducer.inject(booleanSlice)\n   * rootReducer.inject(baseApi)\n   * rootReducer.inject({ reducerPath: 'boolean' as const, reducer: newReducer }, { overrideExisting: true })\n   * ```\n   *\n   */\n  inject<ReducerPath extends string, State>(slice: SliceLike<ReducerPath, State & (ReducerPath extends keyof DeclaredState ? never : State)>, config?: InjectConfig): CombinedSliceReducer<InitialState, Id<DeclaredState & WithSlice<SliceLike<ReducerPath, State>>>>;\n\n  /**\n   * Create a selector that guarantees that the slices injected will have a defined value when selector is run.\n   *\n   * ```ts\n   * const selectBooleanWithoutInjection = (state: RootState) => state.boolean;\n   * //                                                                ^? boolean | undefined\n   *\n   * const selectBoolean = rootReducer.inject(booleanSlice).selector((state) => {\n   *   // if action hasn't been dispatched since slice was injected, this would usually be undefined\n   *   // however selector() uses a Proxy around the first parameter to ensure that it evaluates to the initial state instead, if undefined\n   *   return state.boolean;\n   *   //           ^? boolean\n   * })\n   * ```\n   *\n   * If the reducer is nested inside the root state, a selectState callback can be passed to retrieve the reducer's state.\n   *\n   * ```ts\n   *\n   * export interface LazyLoadedSlices {};\n   *\n   * export const innerReducer = combineSlices(stringSlice).withLazyLoadedSlices<LazyLoadedSlices>();\n   *\n   * export const rootReducer = combineSlices({ inner: innerReducer });\n   *\n   * export type RootState = ReturnType<typeof rootReducer>;\n   *\n   * // elsewhere\n   *\n   * declare module \"./reducer.ts\" {\n   *  export interface LazyLoadedSlices extends WithSlice<typeof booleanSlice> {}\n   * }\n   *\n   * const withBool = innerReducer.inject(booleanSlice);\n   *\n   * const selectBoolean = withBool.selector(\n   *   (state) => state.boolean,\n   *   (rootState: RootState) => state.inner\n   * );\n   * //    now expects to be passed RootState instead of innerReducer state\n   *\n   * ```\n   *\n   * Value passed to selectorFn will be a Proxy - use selector.original(proxy) to get original state value (useful for debugging)\n   *\n   * ```ts\n   * const injectedReducer = rootReducer.inject(booleanSlice);\n   * const selectBoolean = injectedReducer.selector((state) => {\n   *   console.log(injectedReducer.selector.original(state).boolean) // possibly undefined\n   *   return state.boolean\n   * })\n   * ```\n   */\n  selector: {\n    /**\n     * Create a selector that guarantees that the slices injected will have a defined value when selector is run.\n     *\n     * ```ts\n     * const selectBooleanWithoutInjection = (state: RootState) => state.boolean;\n     * //                                                                ^? boolean | undefined\n     *\n     * const selectBoolean = rootReducer.inject(booleanSlice).selector((state) => {\n     *   // if action hasn't been dispatched since slice was injected, this would usually be undefined\n     *   // however selector() uses a Proxy around the first parameter to ensure that it evaluates to the initial state instead, if undefined\n     *   return state.boolean;\n     *   //           ^? boolean\n     * })\n     * ```\n     *\n     * Value passed to selectorFn will be a Proxy - use selector.original(proxy) to get original state value (useful for debugging)\n     *\n     * ```ts\n     * const injectedReducer = rootReducer.inject(booleanSlice);\n     * const selectBoolean = injectedReducer.selector((state) => {\n     *   console.log(injectedReducer.selector.original(state).boolean) // undefined\n     *   return state.boolean\n     * })\n     * ```\n     */\n    <Selector extends (state: DeclaredState, ...args: any[]) => unknown>(selectorFn: Selector): (state: WithOptionalProp<Parameters<Selector>[0], Exclude<keyof DeclaredState, keyof InitialState>>, ...args: Tail<Parameters<Selector>>) => ReturnType<Selector>;\n\n    /**\n     * Create a selector that guarantees that the slices injected will have a defined value when selector is run.\n     *\n     * ```ts\n     * const selectBooleanWithoutInjection = (state: RootState) => state.boolean;\n     * //                                                                ^? boolean | undefined\n     *\n     * const selectBoolean = rootReducer.inject(booleanSlice).selector((state) => {\n     *   // if action hasn't been dispatched since slice was injected, this would usually be undefined\n     *   // however selector() uses a Proxy around the first parameter to ensure that it evaluates to the initial state instead, if undefined\n     *   return state.boolean;\n     *   //           ^? boolean\n     * })\n     * ```\n     *\n     * If the reducer is nested inside the root state, a selectState callback can be passed to retrieve the reducer's state.\n     *\n     * ```ts\n     *\n     * interface LazyLoadedSlices {};\n     *\n     * const innerReducer = combineSlices(stringSlice).withLazyLoadedSlices<LazyLoadedSlices>();\n     *\n     * const rootReducer = combineSlices({ inner: innerReducer });\n     *\n     * type RootState = ReturnType<typeof rootReducer>;\n     *\n     * // elsewhere\n     *\n     * declare module \"./reducer.ts\" {\n     *  interface LazyLoadedSlices extends WithSlice<typeof booleanSlice> {}\n     * }\n     *\n     * const withBool = innerReducer.inject(booleanSlice);\n     *\n     * const selectBoolean = withBool.selector(\n     *   (state) => state.boolean,\n     *   (rootState: RootState) => state.inner\n     * );\n     * //    now expects to be passed RootState instead of innerReducer state\n     *\n     * ```\n     *\n     * Value passed to selectorFn will be a Proxy - use selector.original(proxy) to get original state value (useful for debugging)\n     *\n     * ```ts\n     * const injectedReducer = rootReducer.inject(booleanSlice);\n     * const selectBoolean = injectedReducer.selector((state) => {\n     *   console.log(injectedReducer.selector.original(state).boolean) // possibly undefined\n     *   return state.boolean\n     * })\n     * ```\n     */\n    <Selector extends (state: DeclaredState, ...args: any[]) => unknown, RootState>(selectorFn: Selector, selectState: (rootState: RootState, ...args: Tail<Parameters<Selector>>) => WithOptionalProp<Parameters<Selector>[0], Exclude<keyof DeclaredState, keyof InitialState>>): (state: RootState, ...args: Tail<Parameters<Selector>>) => ReturnType<Selector>;\n    /**\n     * Returns the unproxied state. Useful for debugging.\n     * @param state state Proxy, that ensures injected reducers have value\n     * @returns original, unproxied state\n     * @throws if value passed is not a state Proxy\n     */\n    original: (state: DeclaredState) => InitialState & Partial<DeclaredState>;\n  };\n}\ntype InitialState<Slices extends Array<AnySliceLike | ReducerMap>> = UnionToIntersection<Slices[number] extends infer Slice ? Slice extends AnySliceLike ? WithSlice<Slice> : StateFromReducersMapObject<Slice> : never>;\nconst isSliceLike = (maybeSliceLike: AnySliceLike | ReducerMap): maybeSliceLike is AnySliceLike => 'reducerPath' in maybeSliceLike && typeof maybeSliceLike.reducerPath === 'string';\nconst getReducers = (slices: Array<AnySliceLike | ReducerMap>) => slices.flatMap(sliceOrMap => isSliceLike(sliceOrMap) ? [[sliceOrMap.reducerPath, sliceOrMap.reducer] as const] : Object.entries(sliceOrMap));\nconst ORIGINAL_STATE = Symbol.for('rtk-state-proxy-original');\nconst isStateProxy = (value: any) => !!value && !!value[ORIGINAL_STATE];\nconst stateProxyMap = new WeakMap<object, object>();\nconst createStateProxy = <State extends object,>(state: State, reducerMap: Partial<Record<PropertyKey, Reducer>>, initialStateCache: Record<PropertyKey, unknown>) => getOrInsertComputed(stateProxyMap, state, () => new Proxy(state, {\n  get: (target, prop, receiver) => {\n    if (prop === ORIGINAL_STATE) return target;\n    const result = Reflect.get(target, prop, receiver);\n    if (typeof result === 'undefined') {\n      const cached = initialStateCache[prop];\n      if (typeof cached !== 'undefined') return cached;\n      const reducer = reducerMap[prop];\n      if (reducer) {\n        // ensure action type is random, to prevent reducer treating it differently\n        const reducerResult = reducer(undefined, {\n          type: nanoid()\n        });\n        if (typeof reducerResult === 'undefined') {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(24) : `The slice reducer for key \"${prop.toString()}\" returned undefined when called for selector(). ` + `If the state passed to the reducer is undefined, you must ` + `explicitly return the initial state. The initial state may ` + `not be undefined. If you don't want to set a value for this reducer, ` + `you can use null instead of undefined.`);\n        }\n        initialStateCache[prop] = reducerResult;\n        return reducerResult;\n      }\n    }\n    return result;\n  }\n})) as State;\nconst original = (state: any) => {\n  if (!isStateProxy(state)) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(25) : 'original must be used on state Proxy');\n  }\n  return state[ORIGINAL_STATE];\n};\nconst emptyObject = {};\nconst noopReducer: Reducer<Record<string, any>> = (state = emptyObject) => state;\nexport function combineSlices<Slices extends Array<AnySliceLike | ReducerMap>>(...slices: Slices): CombinedSliceReducer<Id<InitialState<Slices>>> {\n  const reducerMap = Object.fromEntries<Reducer>(getReducers(slices));\n  const getReducer = () => Object.keys(reducerMap).length ? combineReducers(reducerMap) : noopReducer;\n  let reducer = getReducer();\n  function combinedReducer(state: Record<string, unknown>, action: UnknownAction) {\n    return reducer(state, action);\n  }\n  combinedReducer.withLazyLoadedSlices = () => combinedReducer;\n  const initialStateCache: Record<PropertyKey, unknown> = {};\n  const inject = (slice: AnySliceLike, config: InjectConfig = {}): typeof combinedReducer => {\n    const {\n      reducerPath,\n      reducer: reducerToInject\n    } = slice;\n    const currentReducer = reducerMap[reducerPath];\n    if (!config.overrideExisting && currentReducer && currentReducer !== reducerToInject) {\n      if (typeof process !== 'undefined' && process.env.NODE_ENV === 'development') {\n        console.error(`called \\`inject\\` to override already-existing reducer ${reducerPath} without specifying \\`overrideExisting: true\\``);\n      }\n      return combinedReducer;\n    }\n    if (config.overrideExisting && currentReducer !== reducerToInject) {\n      delete initialStateCache[reducerPath];\n    }\n    reducerMap[reducerPath] = reducerToInject;\n    reducer = getReducer();\n    return combinedReducer;\n  };\n  const selector = Object.assign(function makeSelector<State extends object, RootState, Args extends any[]>(selectorFn: (state: State, ...args: Args) => any, selectState?: (rootState: RootState, ...args: Args) => State) {\n    return function selector(state: State, ...args: Args) {\n      return selectorFn(createStateProxy(selectState ? selectState(state as any, ...args) : state, reducerMap, initialStateCache), ...args);\n    };\n  }, {\n    original\n  });\n  return Object.assign(combinedReducer, {\n    inject,\n    selector\n  }) as any;\n}", "/**\r\n * Adapted from React: https://github.com/facebook/react/blob/master/packages/shared/formatProdErrorMessage.js\r\n *\r\n * Do not require this module directly! Use normal throw error calls. These messages will be replaced with error codes\r\n * during build.\r\n * @param {number} code\r\n */\nexport function formatProdErrorMessage(code: number) {\n  return `Minified Redux Toolkit error #${code}; visit https://redux-toolkit.js.org/Errors?code=${code} for the full message or ` + 'use the non-minified dev environment for full errors. ';\n}"], "mappings": "2eAAA,IAAAA,EAAA,GAAAC,GAAAD,EAAA,iBAAAE,GAAA,qBAAAC,GAAA,mBAAAC,EAAA,UAAAC,EAAA,gBAAAC,GAAA,sBAAAC,GAAA,sBAAAC,GAAA,qBAAAC,GAAA,sBAAAC,GAAA,kBAAAC,GAAA,mBAAAC,GAAA,iBAAAC,EAAA,2CAAAC,GAAA,qBAAAC,GAAA,4BAAAC,GAAA,mCAAAC,GAAA,4BAAAC,GAAA,wBAAAC,GAAA,4CAAAC,GAAA,6BAAAC,GAAA,gDAAAC,GAAA,qIAAAC,GAAA,gBAAAC,GAAA,mDAAAC,GAAA,2BAAAC,EAAA,wCAAAC,GAAA,YAAAC,EAAA,YAAAC,EAAA,uBAAAC,GAAA,+CAAAC,GAAA,gBAAAC,GAAA,uBAAAC,GAAA,cAAAC,GAAA,YAAAC,GAAA,eAAAC,EAAA,wBAAAC,GAAA,mDAAAC,GAAA,WAAAC,EAAA,+CAAAC,GAAA,mBAAAC,GAAA,iBAAAC,GAAA,qDAAAC,GAAA3C,GAGA4C,EAAA5C,EAAc,iBAHd,gBAIA,IAAA6C,EAA+E,iBAE/EC,EAAkF,oBCNlF,IAAAC,GAAiC,iBACjCC,GAAsD,oBACzCC,GAA+D,IAAIC,IAAoB,CAClG,IAAMC,KAAkB,0BAA8B,GAAGD,CAAI,EACvDE,EAA0B,OAAO,OAAO,IAAIF,IAAoB,CACpE,IAAMG,EAAWF,EAAe,GAAGD,CAAI,EACjCI,EAAkB,CAACC,KAAmBC,IAAoBH,KAAS,YAAQE,CAAK,KAAI,YAAQA,CAAK,EAAIA,EAAO,GAAGC,CAAI,EACzH,cAAO,OAAOF,EAAiBD,CAAQ,EAChCC,CACT,EAAG,CACD,UAAW,IAAMF,CACnB,CAAC,EACD,OAAOA,CACT,EASaA,GACbH,GAA+B,iBAAc,ECrB7C,IAAAQ,EAAsF,iBCDtF,IAAAC,GAAwB,iBAkNXC,GAA2C,OAAO,OAAW,KAAgB,OAAe,qCAAwC,OAAe,qCAAuC,UAAY,CACjN,GAAI,UAAU,SAAW,EACzB,OAAI,OAAO,UAAU,CAAC,GAAM,SAAiB,WACtC,WAAQ,MAAM,KAAM,SAA8B,CAC3D,EAKaC,GAET,OAAO,OAAW,KAAgB,OAAe,6BAAgC,OAAe,6BAA+B,UAAY,CAC7I,OAAO,SAAUC,EAAM,CACrB,OAAOA,CACT,CACF,EChOA,IAAAC,GAA4D,uBCD5D,IAAAC,GAAyB,iBCsFlB,IAAMC,GAAwBC,GAC5BA,GAAK,OAAQA,EAA0B,OAAU,WD6GnD,SAASC,EAAaC,EAAcC,EAA+B,CACxE,SAASC,KAAiBC,EAAa,CACrC,GAAIF,EAAe,CACjB,IAAIG,EAAWH,EAAc,GAAGE,CAAI,EACpC,GAAI,CAACC,EACH,MAAM,IAAI,MAA8CC,EAAwB,CAAC,CAA4C,EAE/H,MAAO,CACL,KAAAL,EACA,QAASI,EAAS,QAClB,GAAI,SAAUA,GAAY,CACxB,KAAMA,EAAS,IACjB,EACA,GAAI,UAAWA,GAAY,CACzB,MAAOA,EAAS,KAClB,CACF,CACF,CACA,MAAO,CACL,KAAAJ,EACA,QAASG,EAAK,CAAC,CACjB,CACF,CACA,OAAAD,EAAc,SAAW,IAAM,GAAGF,CAAI,GACtCE,EAAc,KAAOF,EACrBE,EAAc,MAASI,MAA6C,aAASA,CAAM,GAAKA,EAAO,OAASN,EACjGE,CACT,CAKO,SAASK,GAAgBD,EAA0E,CACxG,OAAO,OAAOA,GAAW,YAAc,SAAUA,GAEjDE,GAAiBF,CAAa,CAChC,CAKO,SAASG,GAAMH,EAKpB,CACA,SAAO,aAASA,CAAM,GAAK,OAAO,KAAKA,CAAM,EAAE,MAAMI,EAAU,CACjE,CACA,SAASA,GAAWC,EAAa,CAC/B,MAAO,CAAC,OAAQ,UAAW,QAAS,MAAM,EAAE,QAAQA,CAAG,EAAI,EAC7D,CE7OO,SAASC,GAAWC,EAAgB,CACzC,IAAMC,EAAYD,EAAO,GAAGA,CAAI,GAAG,MAAM,GAAG,EAAI,CAAC,EAC3CE,EAAaD,EAAUA,EAAU,OAAS,CAAC,GAAK,gBACtD,MAAO,yCAAyCD,GAAQ,SAAS;AAAA,kFACeE,CAAU,+BAA+BA,CAAU,2DACrI,CACO,SAASC,GAAuCC,EAAmD,CAAC,EAAe,CAEtH,MAAO,IAAMC,GAAQC,GAAUD,EAAKC,CAAM,CAW9C,CC9BA,IAAAC,GAAwD,iBAyBjD,IAAMC,EAAN,MAAMC,UAAyD,KAAqB,CAGzF,eAAeC,EAAc,CAC3B,MAAM,GAAGA,CAAK,EACd,OAAO,eAAe,KAAMD,EAAM,SAAS,CAC7C,CACA,WAAqB,OAAO,OAAO,GAAI,CACrC,OAAOA,CACT,CAIS,UAAUE,EAAY,CAC7B,OAAO,MAAM,OAAO,MAAM,KAAMA,CAAG,CACrC,CAIA,WAAWA,EAAY,CACrB,OAAIA,EAAI,SAAW,GAAK,MAAM,QAAQA,EAAI,CAAC,CAAC,EACnC,IAAIF,EAAM,GAAGE,EAAI,CAAC,EAAE,OAAO,IAAI,CAAC,EAElC,IAAIF,EAAM,GAAGE,EAAI,OAAO,IAAI,CAAC,CACtC,CACF,EACO,SAASC,GAAmBC,EAAQ,CACzC,SAAO,gBAAYA,CAAG,KAAI,GAAAC,SAAgBD,EAAK,IAAM,CAAC,CAAC,EAAIA,CAC7D,CASO,SAASE,EAAyCC,EAAgCC,EAAQC,EAA2B,CAC1H,OAAIF,EAAI,IAAIC,CAAG,EAAUD,EAAI,IAAIC,CAAG,EAC7BD,EAAI,IAAIC,EAAKC,EAAQD,CAAG,CAAC,EAAE,IAAIA,CAAG,CAC3C,CCtDO,SAASE,GAAmBC,EAAyB,CAC1D,OAAO,OAAOA,GAAU,UAAYA,GAAS,MAAQ,OAAO,SAASA,CAAK,CAC5E,CAiHO,SAASC,GAAwCC,EAAoD,CAAC,EAAe,CAC1H,GAAI,EACF,MAAO,IAAMC,GAAQC,GAAUD,EAAKC,CAAM,EAEjC,IAAAC,EAGAC,CAuDb,CC3LA,IAAAC,GAAwC,iBAYjC,SAASC,GAAQC,EAAU,CAChC,IAAMC,EAAO,OAAOD,EACpB,OAAOA,GAAO,MAAQC,IAAS,UAAYA,IAAS,WAAaA,IAAS,UAAY,MAAM,QAAQD,CAAG,MAAK,kBAAcA,CAAG,CAC/H,CAUO,SAASE,GAAyBC,EAAgBC,EAAe,GAAIC,EAA8CN,GAASO,EAAkDC,EAA4B,CAAC,EAAGC,EAAuD,CAC1Q,IAAIC,EACJ,GAAI,CAACJ,EAAeF,CAAK,EACvB,MAAO,CACL,QAASC,GAAQ,SACjB,MAAOD,CACT,EAKF,GAHI,OAAOA,GAAU,UAAYA,IAAU,MAGvCK,GAAO,IAAIL,CAAK,EAAG,MAAO,GAC9B,IAAMO,EAAUJ,GAAc,KAAOA,EAAWH,CAAK,EAAI,OAAO,QAAQA,CAAK,EACvEQ,EAAkBJ,EAAa,OAAS,EAC9C,OAAW,CAACK,EAAKC,CAAW,IAAKH,EAAS,CACxC,IAAMI,EAAaV,EAAOA,EAAO,IAAMQ,EAAMA,EAC7C,GAAI,EAAAD,GACiBJ,EAAa,KAAKQ,GAC/BA,aAAmB,OACdA,EAAQ,KAAKD,CAAU,EAEzBA,IAAeC,CACvB,GAKH,IAAI,CAACV,EAAeQ,CAAW,EAC7B,MAAO,CACL,QAASC,EACT,MAAOD,CACT,EAEF,GAAI,OAAOA,GAAgB,WACzBJ,EAA0BP,GAAyBW,EAAaC,EAAYT,EAAgBC,EAAYC,EAAcC,CAAK,EACvHC,GACF,OAAOA,EAGb,CACA,OAAID,GAASQ,GAAeb,CAAK,GAAGK,EAAM,IAAIL,CAAK,EAC5C,EACT,CACO,SAASa,GAAeb,EAAe,CAC5C,GAAI,CAAC,OAAO,SAASA,CAAK,EAAG,MAAO,GACpC,QAAWU,KAAe,OAAO,OAAOV,CAAK,EAC3C,GAAI,SAAOU,GAAgB,UAAYA,IAAgB,OACnD,CAACG,GAAeH,CAAW,EAAG,MAAO,GAE3C,MAAO,EACT,CAwEO,SAASI,GAA2CC,EAAuD,CAAC,EAAe,CAE9H,MAAO,IAAMC,GAAQC,GAAUD,EAAKC,CAAM,CAmD9C,CN3LA,SAASC,GAAUC,EAAsB,CACvC,OAAO,OAAOA,GAAM,SACtB,CAuBO,IAAMC,GAA4B,IAAyC,SAA8BC,EAAS,CACvH,GAAM,CACJ,MAAAC,EAAQ,GACR,eAAAC,EAAiB,GACjB,kBAAAC,EAAoB,GACpB,mBAAAC,EAAqB,EACvB,EAAIJ,GAAW,CAAC,EACZK,EAAkB,IAAIC,EAC1B,OAAIL,IACEJ,GAAUI,CAAK,EACjBI,EAAgB,KAAK,GAAAE,KAAe,EAEpCF,EAAgB,QAAK,sBAAkBJ,EAAM,aAAa,CAAC,GA4BxDI,CACT,EO/EO,IAAMG,GAAmB,gBACnBC,GAAqB,IAAWC,IAGvC,CACJ,QAAAA,EACA,KAAM,CACJ,CAACF,EAAgB,EAAG,EACtB,CACF,GACMG,GAAwBC,GACpBC,GAAuB,CAC7B,WAAWA,EAAQD,CAAO,CAC5B,EAoCWE,GAAoB,CAACC,EAA4B,CAC5D,KAAM,KACR,IAAqBC,GAAQ,IAAIC,IAAS,CACxC,IAAMC,EAAQF,EAAK,GAAGC,CAAI,EACtBE,EAAY,GACZC,EAA0B,GAC1BC,EAAqB,GACnBC,EAAY,IAAI,IAChBC,EAAgBR,EAAQ,OAAS,OAAS,eAAiBA,EAAQ,OAAS,MAElF,OAAO,OAAW,KAAe,OAAO,sBAAwB,OAAO,sBAAwBJ,GAAqB,EAAE,EAAII,EAAQ,OAAS,WAAaA,EAAQ,kBAAoBJ,GAAqBI,EAAQ,OAAO,EAClNS,EAAkB,IAAM,CAG5BH,EAAqB,GACjBD,IACFA,EAA0B,GAC1BE,EAAU,QAAQG,GAAKA,EAAE,CAAC,EAE9B,EACA,OAAO,OAAO,OAAO,CAAC,EAAGP,EAAO,CAG9B,UAAUQ,EAAsB,CAK9B,IAAMC,EAAmC,IAAMR,GAAaO,EAAS,EAC/DE,EAAcV,EAAM,UAAUS,CAAe,EACnD,OAAAL,EAAU,IAAII,CAAQ,EACf,IAAM,CACXE,EAAY,EACZN,EAAU,OAAOI,CAAQ,CAC3B,CACF,EAGA,SAASG,EAAa,CACpB,GAAI,CAGF,OAAAV,EAAY,CAACU,GAAQ,OAAOrB,EAAgB,EAG5CY,EAA0B,CAACD,EACvBC,IAIGC,IACHA,EAAqB,GACrBE,EAAcC,CAAe,IAS1BN,EAAM,SAASW,CAAM,CAC9B,QAAE,CAEAV,EAAY,EACd,CACF,CACF,CAAC,CACH,EC1GO,IAAMW,GAAyDC,GAEvC,SAA6BC,EAAS,CACnE,GAAM,CACJ,UAAAC,EAAY,EACd,EAAID,GAAW,CAAC,EACZE,EAAgB,IAAIC,EAAuBJ,CAAkB,EACjE,OAAIE,GACFC,EAAc,KAAKE,GAAkB,OAAOH,GAAc,SAAWA,EAAY,MAAS,CAAC,EAEtFC,CACT,EV8DO,SAASG,GAEYC,EAAuE,CACjG,IAAMC,EAAuBC,GAA6B,EACpD,CACJ,QAAAC,EAAU,OACV,WAAAC,EACA,SAAAC,EAAW,GACX,yBAAAC,EAA2B,GAC3B,eAAAC,EAAiB,OACjB,UAAAC,EAAY,MACd,EAAIR,GAAW,CAAC,EACZS,EACJ,GAAI,OAAON,GAAY,WACrBM,EAAcN,aACL,iBAAcA,CAAO,EAC9BM,KAAc,mBAAgBN,CAAO,MAErC,OAAM,IAAI,MAA8CO,EAAwB,CAAC,CAA8H,EAKjN,IAAIC,EACA,OAAOP,GAAe,WACxBO,EAAkBP,EAAWH,CAAoB,EAKjDU,EAAkBV,EAAqB,EAczC,IAAIW,EAAe,UACfP,IACFO,EAAeC,GAAoB,CAEjC,MAAO,GACP,GAAI,OAAOR,GAAa,UAAYA,CACtC,CAAC,GAEH,IAAMS,KAAqB,mBAAgB,GAAGH,CAAe,EACvDI,EAAsBC,GAA4BF,CAAkB,EAItEG,EAAiB,OAAOT,GAAc,WAAaA,EAAUO,CAAmB,EAAIA,EAAoB,EAUtGG,EAAuCN,EAAa,GAAGK,CAAc,EAC3E,SAAO,eAAYR,EAAaF,EAAqBW,CAAgB,CACvE,CWxJA,IAAAC,EAAiE,iBCwG1D,SAASC,GAAiCC,EAAmK,CAClN,IAAMC,EAAmC,CAAC,EACpCC,EAAwD,CAAC,EAC3DC,EACEC,EAAU,CACd,QAAQC,EAAuDC,EAAyB,CActF,IAAMC,EAAO,OAAOF,GAAwB,SAAWA,EAAsBA,EAAoB,KACjG,GAAI,CAACE,EACH,MAAM,IAAI,MAA8CC,EAAyB,EAAE,CAAkE,EAEvJ,GAAID,KAAQN,EACV,MAAM,IAAI,MAA8CO,EAAyB,EAAE,CAAkG,EAEvL,OAAAP,EAAWM,CAAI,EAAID,EACZF,CACT,EACA,WAAcK,EAAuBH,EAA4D,CAM/F,OAAAJ,EAAe,KAAK,CAClB,QAAAO,EACA,QAAAH,CACF,CAAC,EACMF,CACT,EACA,eAAeE,EAAiC,CAM9C,OAAAH,EAAqBG,EACdF,CACT,CACF,EACA,OAAAJ,EAAgBI,CAAO,EAChB,CAACH,EAAYC,EAAgBC,CAAkB,CACxD,CDzGA,SAASO,GAAmBC,EAA0B,CACpD,OAAO,OAAOA,GAAM,UACtB,CAqEO,SAASC,GAA0CC,EAA6BC,EAAiG,CAMtL,GAAI,CAACC,EAAYC,EAAqBC,CAAuB,EAAIC,GAA8BJ,CAAoB,EAG/GK,EACJ,GAAIT,GAAgBG,CAAY,EAC9BM,EAAkB,IAAMC,GAAgBP,EAAa,CAAC,MACjD,CACL,IAAMQ,EAAqBD,GAAgBP,CAAY,EACvDM,EAAkB,IAAME,CAC1B,CACA,SAASC,EAAQC,EAAQJ,EAAgB,EAAGK,EAAgB,CAC1D,IAAIC,EAAe,CAACV,EAAWS,EAAO,IAAI,EAAG,GAAGR,EAAoB,OAAO,CAAC,CAC1E,QAAAU,CACF,IAAMA,EAAQF,CAAM,CAAC,EAAE,IAAI,CAAC,CAC1B,QAAAF,CACF,IAAMA,CAAO,CAAC,EACd,OAAIG,EAAa,OAAOE,GAAM,CAAC,CAACA,CAAE,EAAE,SAAW,IAC7CF,EAAe,CAACR,CAAuB,GAElCQ,EAAa,OAAO,CAACG,EAAeC,IAAmB,CAC5D,GAAIA,EACF,MAAI,WAAQD,CAAa,EAAG,CAK1B,IAAME,EAASD,EADDD,EACoBJ,CAAM,EACxC,OAAIM,IAAW,OACNF,EAEFE,CACT,KAAO,OAAK,eAAYF,CAAa,EAenC,SAAO,EAAAG,SAAgBH,EAAgBI,GAC9BH,EAAYG,EAAOR,CAAM,CACjC,EAjBqC,CAGtC,IAAMM,EAASD,EAAYD,EAAsBJ,CAAM,EACvD,GAAIM,IAAW,OAAW,CACxB,GAAIF,IAAkB,KACpB,OAAOA,EAET,MAAM,MAAM,mEAAmE,CACjF,CACA,OAAOE,CACT,EASF,OAAOF,CACT,EAAGL,CAAK,CACV,CACA,OAAAD,EAAQ,gBAAkBH,EACnBG,CACT,CElLA,IAAMW,GAAU,CAACC,EAAuBC,IAClCC,GAAiBF,CAAO,EACnBA,EAAQ,MAAMC,CAAM,EAEpBD,EAAQC,CAAM,EAalB,SAASE,KAA4CC,EAAoB,CAC9E,OAAQH,GACCG,EAAS,KAAKJ,GAAWD,GAAQC,EAASC,CAAM,CAAC,CAE5D,CAWO,SAASI,KAA4CD,EAAoB,CAC9E,OAAQH,GACCG,EAAS,MAAMJ,GAAWD,GAAQC,EAASC,CAAM,CAAC,CAE7D,CAQO,SAASK,GAA2BL,EAAaM,EAAgC,CACtF,GAAI,CAACN,GAAU,CAACA,EAAO,KAAM,MAAO,GACpC,IAAMO,EAAoB,OAAOP,EAAO,KAAK,WAAc,SACrDQ,EAAwBF,EAAY,QAAQN,EAAO,KAAK,aAAa,EAAI,GAC/E,OAAOO,GAAqBC,CAC9B,CACA,SAASC,EAAkBC,EAAkD,CAC3E,OAAO,OAAOA,EAAE,CAAC,GAAM,YAAc,YAAaA,EAAE,CAAC,GAAK,cAAeA,EAAE,CAAC,GAAK,aAAcA,EAAE,CAAC,CACpG,CA2BO,SAASC,MAAsEC,EAAkC,CACtH,OAAIA,EAAY,SAAW,EACjBZ,GAAgBK,GAA2BL,EAAQ,CAAC,SAAS,CAAC,EAEnES,EAAkBG,CAAW,EAG3BV,EAAQ,GAAGU,EAAY,IAAIC,GAAcA,EAAW,OAAO,CAAC,EAF1DF,GAAU,EAAEC,EAAY,CAAC,CAAC,CAGrC,CA2BO,SAASE,KAAuEF,EAAkC,CACvH,OAAIA,EAAY,SAAW,EACjBZ,GAAgBK,GAA2BL,EAAQ,CAAC,UAAU,CAAC,EAEpES,EAAkBG,CAAW,EAG3BV,EAAQ,GAAGU,EAAY,IAAIC,GAAcA,EAAW,QAAQ,CAAC,EAF3DC,EAAW,EAAEF,EAAY,CAAC,CAAC,CAGtC,CA+BO,SAASG,MAAgFH,EAAkC,CAChI,IAAMI,EAAWhB,GACRA,GAAUA,EAAO,MAAQA,EAAO,KAAK,kBAE9C,OAAIY,EAAY,SAAW,EAClBR,EAAQU,EAAW,GAAGF,CAAW,EAAGI,CAAO,EAE/CP,EAAkBG,CAAW,EAG3BR,EAAQU,EAAW,GAAGF,CAAW,EAAGI,CAAO,EAFzCD,GAAoB,EAAEH,EAAY,CAAC,CAAC,CAG/C,CA2BO,SAASK,MAAwEL,EAAkC,CACxH,OAAIA,EAAY,SAAW,EACjBZ,GAAgBK,GAA2BL,EAAQ,CAAC,WAAW,CAAC,EAErES,EAAkBG,CAAW,EAG3BV,EAAQ,GAAGU,EAAY,IAAIC,GAAcA,EAAW,SAAS,CAAC,EAF5DI,GAAY,EAAEL,EAAY,CAAC,CAAC,CAGvC,CAoCO,SAASM,MAA+EN,EAAkC,CAC/H,OAAIA,EAAY,SAAW,EACjBZ,GAAgBK,GAA2BL,EAAQ,CAAC,UAAW,YAAa,UAAU,CAAC,EAE5FS,EAAkBG,CAAW,EAG3BV,EAAQ,GAAGU,EAAY,QAAQC,GAAc,CAACA,EAAW,QAASA,EAAW,SAAUA,EAAW,SAAS,CAAC,CAAC,EAF3GK,GAAmB,EAAEN,EAAY,CAAC,CAAC,CAG9C,CCzPA,IAAIO,GAAc,mEAMPC,EAAS,CAACC,EAAO,KAAO,CACjC,IAAIC,EAAK,GAELC,EAAIF,EACR,KAAOE,KAELD,GAAMH,GAAY,KAAK,OAAO,EAAI,GAAK,CAAC,EAE1C,OAAOG,CACT,ECSA,IAAME,GAAiD,CAAC,OAAQ,UAAW,QAAS,MAAM,EACpFC,EAAN,KAA6C,CAM3C,YAA4BC,EAAkCC,EAAoB,CAAtD,aAAAD,EAAkC,UAAAC,CAAqB,CADlE,KAEnB,EACMC,GAAN,KAA8C,CAM5C,YAA4BF,EAAkCC,EAAqB,CAAvD,aAAAD,EAAkC,UAAAC,CAAsB,CADnE,KAEnB,EAQaE,GAAsBC,GAAgC,CACjE,GAAI,OAAOA,GAAU,UAAYA,IAAU,KAAM,CAC/C,IAAMC,EAA+B,CAAC,EACtC,QAAWC,KAAYR,GACjB,OAAOM,EAAME,CAAQ,GAAM,WAC7BD,EAAYC,CAAQ,EAAIF,EAAME,CAAQ,GAG1C,OAAOD,CACT,CACA,MAAO,CACL,QAAS,OAAOD,CAAK,CACvB,CACF,EA4MMG,GAAuB,8BAChBC,IAAmC,IAAM,CACpD,SAASA,EAA8EC,EAAoBC,EAA8EC,EAAuG,CAK9R,IAAMC,EAAkFC,EAAaJ,EAAa,aAAc,CAACT,EAAmBc,EAAmBC,EAAed,KAA0B,CAC9M,QAAAD,EACA,KAAM,CACJ,GAAIC,GAAe,CAAC,EACpB,IAAAc,EACA,UAAAD,EACA,cAAe,WACjB,CACF,EAAE,EACIE,EAAoEH,EAAaJ,EAAa,WAAY,CAACK,EAAmBC,EAAed,KAAwB,CACzK,QAAS,OACT,KAAM,CACJ,GAAIA,GAAe,CAAC,EACpB,IAAAc,EACA,UAAAD,EACA,cAAe,SACjB,CACF,EAAE,EACIG,EAAsEJ,EAAaJ,EAAa,YAAa,CAACS,EAAqBJ,EAAmBC,EAAef,EAAyBC,KAAyB,CAC3N,QAAAD,EACA,OAAQW,GAAWA,EAAQ,gBAAkBR,IAAoBe,GAAS,UAAU,EACpF,KAAM,CACJ,GAAIjB,GAAe,CAAC,EACpB,IAAAc,EACA,UAAAD,EACA,kBAAmB,CAAC,CAACd,EACrB,cAAe,WACf,QAASkB,GAAO,OAAS,aACzB,UAAWA,GAAO,OAAS,gBAC7B,CACF,EAAE,EACF,SAASC,EAAcJ,EAAe,CACpC,OAAAK,CACF,EAA8B,CAAC,EAAmE,CAChG,MAAO,CAACC,EAAUC,EAAUC,IAAU,CACpC,IAAMT,EAAYH,GAAS,YAAcA,EAAQ,YAAYI,CAAG,EAAIS,EAAO,EACrEC,EAAkB,IAAI,gBACxBC,EACAC,EACJ,SAASC,EAAMC,EAAiB,CAC9BF,EAAcE,EACdJ,EAAgB,MAAM,CACxB,CACIL,IACEA,EAAO,QACTQ,EAAMrB,EAAoB,EAE1Ba,EAAO,iBAAiB,QAAS,IAAMQ,EAAMrB,EAAoB,EAAG,CAClE,KAAM,EACR,CAAC,GAGL,IAAMuB,EAAU,gBAAkB,CAChC,IAAIC,EACJ,GAAI,CACF,IAAIC,EAAkBrB,GAAS,YAAYI,EAAK,CAC9C,SAAAO,EACA,MAAAC,CACF,CAAC,EAID,GAHIU,GAAWD,CAAe,IAC5BA,EAAkB,MAAMA,GAEtBA,IAAoB,IAASP,EAAgB,OAAO,QAEtD,KAAM,CACJ,KAAM,iBACN,QAAS,oDACX,EAEF,IAAMS,EAAiB,IAAI,QAAe,CAACC,EAAGC,IAAW,CACvDV,EAAe,IAAM,CACnBU,EAAO,CACL,KAAM,aACN,QAAST,GAAe,SAC1B,CAAC,CACH,EACAF,EAAgB,OAAO,iBAAiB,QAASC,CAAY,CAC/D,CAAC,EACDL,EAASL,EAAQF,EAAWC,EAAKJ,GAAS,iBAAiB,CACzD,UAAAG,EACA,IAAAC,CACF,EAAG,CACD,SAAAO,EACA,MAAAC,CACF,CAAC,CAAC,CAAQ,EACVQ,EAAc,MAAM,QAAQ,KAAK,CAACG,EAAgB,QAAQ,QAAQxB,EAAeK,EAAK,CACpF,SAAAM,EACA,SAAAC,EACA,MAAAC,EACA,UAAAT,EACA,OAAQW,EAAgB,OACxB,MAAAG,EACA,gBAAkB,CAACxB,EAAsBH,IAChC,IAAIF,EAAgBK,EAAOH,CAAI,EAExC,iBAAmB,CAACG,EAAgBH,IAC3B,IAAIC,GAAgBE,EAAOH,CAAI,CAE1C,CAAC,CAAC,EAAE,KAAKoC,GAAU,CACjB,GAAIA,aAAkBtC,EACpB,MAAMsC,EAER,OAAIA,aAAkBnC,GACbU,EAAUyB,EAAO,QAASvB,EAAWC,EAAKsB,EAAO,IAAI,EAEvDzB,EAAUyB,EAAevB,EAAWC,CAAG,CAChD,CAAC,CAAC,CAAC,CACL,OAASuB,EAAK,CACZP,EAAcO,aAAevC,EAAkBkB,EAAS,KAAMH,EAAWC,EAAKuB,EAAI,QAASA,EAAI,IAAI,EAAIrB,EAASqB,EAAYxB,EAAWC,CAAG,CAC5I,QAAE,CACIW,GACFD,EAAgB,OAAO,oBAAoB,QAASC,CAAY,CAEpE,CAOA,OADqBf,GAAW,CAACA,EAAQ,4BAA8BM,EAAS,MAAMc,CAAW,GAAMA,EAAoB,KAAK,WAE9HV,EAASU,CAAkB,EAEtBA,CACT,EAAE,EACF,OAAO,OAAO,OAAOD,EAA6B,CAChD,MAAAF,EACA,UAAAd,EACA,IAAAC,EACA,QAAS,CACP,OAAOe,EAAQ,KAAUS,EAAY,CACvC,CACF,CAAC,CACH,CACF,CACA,OAAO,OAAO,OAAOpB,EAA8E,CACjG,QAAAH,EACA,SAAAC,EACA,UAAAL,EACA,QAAS4B,EAAQvB,EAAUL,CAAS,EACpC,WAAAH,CACF,CAAC,CACH,CACA,OAAAD,EAAiB,UAAY,IAAMA,EAC5BA,CACT,GAAG,EAaI,SAAS+B,GAA0CE,EAAsC,CAC9F,GAAIA,EAAO,MAAQA,EAAO,KAAK,kBAC7B,MAAMA,EAAO,QAEf,GAAIA,EAAO,MACT,MAAMA,EAAO,MAEf,OAAOA,EAAO,OAChB,CAEA,SAASR,GAAW7B,EAAuC,CACzD,OAAOA,IAAU,MAAQ,OAAOA,GAAU,UAAY,OAAOA,EAAM,MAAS,UAC9E,CC/aA,IAAMsC,GAAkC,OAAO,IAAI,4BAA4B,EAElEC,GAET,CACF,CAACD,EAAgB,EAAGE,EACtB,EAwLYC,QACVA,EAAA,QAAU,UACVA,EAAA,mBAAqB,qBACrBA,EAAA,WAAa,aAHHA,QAAA,IAoIZ,SAASC,GAAQC,EAAeC,EAA2B,CACzD,MAAO,GAAGD,CAAK,IAAIC,CAAS,EAC9B,CAMO,SAASC,GAAiB,CAC/B,SAAAC,CACF,EAA4B,CAAC,EAAG,CAC9B,IAAMC,EAAMD,GAAU,aAAaR,EAAgB,EACnD,OAAO,SAA4KU,EAA0I,CAC3T,GAAM,CACJ,KAAAC,EACA,YAAAC,EAAcD,CAChB,EAAID,EACJ,GAAI,CAACC,EACH,MAAM,IAAI,MAA8CE,EAAwB,EAAE,CAAiD,EAEjI,OAAO,QAAY,IAKvB,IAAMC,GAAY,OAAOJ,EAAQ,UAAa,WAAaA,EAAQ,SAASK,GAA4B,CAAC,EAAIL,EAAQ,WAAa,CAAC,EAC7HM,EAAe,OAAO,KAAKF,CAAQ,EACnCG,EAAyC,CAC7C,wBAAyB,CAAC,EAC1B,wBAAyB,CAAC,EAC1B,eAAgB,CAAC,EACjB,cAAe,CAAC,CAClB,EACMC,EAAuD,CAC3D,QAAQC,EAAuDC,EAA6B,CAC1F,IAAMC,EAAO,OAAOF,GAAwB,SAAWA,EAAsBA,EAAoB,KACjG,GAAI,CAACE,EACH,MAAM,IAAI,MAA8CR,EAAyB,EAAE,CAAkE,EAEvJ,GAAIQ,KAAQJ,EAAQ,wBAClB,MAAM,IAAI,MAA8CJ,EAAyB,EAAE,CAA4F,EAEjL,OAAAI,EAAQ,wBAAwBI,CAAI,EAAID,EACjCF,CACT,EACA,WAAWI,EAASF,EAAS,CAC3B,OAAAH,EAAQ,cAAc,KAAK,CACzB,QAAAK,EACA,QAAAF,CACF,CAAC,EACMF,CACT,EACA,aAAaP,EAAMY,EAAe,CAChC,OAAAN,EAAQ,eAAeN,CAAI,EAAIY,EACxBL,CACT,EACA,kBAAkBP,EAAMS,EAAS,CAC/B,OAAAH,EAAQ,wBAAwBN,CAAI,EAAIS,EACjCF,CACT,CACF,EACAF,EAAa,QAAQQ,GAAe,CAClC,IAAMC,EAAoBX,EAASU,CAAW,EACxCE,EAAiC,CACrC,YAAAF,EACA,KAAMpB,GAAQO,EAAMa,CAAW,EAC/B,eAAgB,OAAOd,EAAQ,UAAa,UAC9C,EACIiB,GAA0CF,CAAiB,EAC7DG,GAAiCF,EAAgBD,EAAmBP,EAAgBT,CAAG,EAEvFoB,GAAqCH,EAAgBD,EAA0BP,CAAc,CAEjG,CAAC,EACD,SAASY,GAAe,CAMtB,GAAM,CAACC,EAAgB,CAAC,EAAGC,EAAiB,CAAC,EAAGC,EAAqB,MAAS,EAAI,OAAOvB,EAAQ,eAAkB,WAAawB,GAA8BxB,EAAQ,aAAa,EAAI,CAACA,EAAQ,aAAa,EACvMyB,EAAoB,CACxB,GAAGJ,EACH,GAAGd,EAAQ,uBACb,EACA,OAAOmB,GAAc1B,EAAQ,aAAc2B,GAAW,CACpD,QAASC,KAAOH,EACdE,EAAQ,QAAQC,EAAKH,EAAkBG,CAAG,CAAqB,EAEjE,QAASC,KAAMtB,EAAQ,cACrBoB,EAAQ,WAAWE,EAAG,QAASA,EAAG,OAAO,EAE3C,QAASC,KAAKR,EACZK,EAAQ,WAAWG,EAAE,QAASA,EAAE,OAAO,EAErCP,GACFI,EAAQ,eAAeJ,CAAkB,CAE7C,CAAC,CACH,CACA,IAAMQ,EAAcC,GAAiBA,EAC/BC,EAAwB,IAAI,IAC5BC,EAAqB,IAAI,QAC3BC,EACJ,SAASzB,EAAQsB,EAA0BI,EAAuB,CAChE,OAAKD,IAAUA,EAAWf,EAAa,GAChCe,EAASH,EAAOI,CAAM,CAC/B,CACA,SAASC,GAAkB,CACzB,OAAKF,IAAUA,EAAWf,EAAa,GAChCe,EAAS,gBAAgB,CAClC,CACA,SAASG,EAAmEpC,EAAiCqC,EAAW,GAA4I,CAClQ,SAASC,EAAYR,EAA6C,CAChE,IAAIS,EAAaT,EAAM9B,CAAW,EAClC,OAAI,OAAOuC,EAAe,KACpBF,IACFE,EAAaC,EAAoBR,EAAoBM,EAAaH,CAAe,GAK9EI,CACT,CACA,SAASE,EAAaC,EAAyCb,EAAY,CACzE,IAAMc,EAAgBH,EAAoBT,EAAuBM,EAAU,IAAM,IAAI,OAAS,EAC9F,OAAOG,EAAoBG,EAAeD,EAAa,IAAM,CAC3D,IAAME,EAA0C,CAAC,EACjD,OAAW,CAAC7C,EAAM8C,CAAQ,IAAK,OAAO,QAAQ/C,EAAQ,WAAa,CAAC,CAAC,EACnE8C,EAAI7C,CAAI,EAAI+C,GAAaD,EAAUH,EAAa,IAAMF,EAAoBR,EAAoBU,EAAaP,CAAe,EAAGE,CAAQ,EAEvI,OAAOO,CACT,CAAC,CACH,CACA,MAAO,CACL,YAAA5C,EACA,aAAAyC,EACA,IAAI,WAAY,CACd,OAAOA,EAAaH,CAAW,CACjC,EACA,YAAAA,CACF,CACF,CACA,IAAM7C,EAAkE,CACtE,KAAAM,EACA,QAAAS,EACA,QAASH,EAAQ,eACjB,aAAcA,EAAQ,wBACtB,gBAAA8B,EACA,GAAGC,EAAkBpC,CAAW,EAChC,WAAW+C,EAAY,CACrB,YAAaC,EACb,GAAGC,CACL,EAAI,CAAC,EAAG,CACN,IAAMC,EAAiBF,GAAWhD,EAClC,OAAA+C,EAAW,OAAO,CAChB,YAAaG,EACb,QAAA1C,CACF,EAAGyC,CAAM,EACF,CACL,GAAGxD,EACH,GAAG2C,EAAkBc,EAAgB,EAAI,CAC3C,CACF,CACF,EACA,OAAOzD,CACT,CACF,CACA,SAASqD,GAAyDD,EAAaH,EAAwCP,EAA8BE,EAAoB,CACvK,SAASc,EAAQC,KAAwBC,EAAa,CACpD,IAAId,EAAaG,EAAYU,CAAS,EACtC,OAAI,OAAOb,EAAe,KACpBF,IACFE,EAAaJ,EAAgB,GAK1BU,EAASN,EAAY,GAAGc,CAAI,CACrC,CACA,OAAAF,EAAQ,UAAYN,EACbM,CACT,CAUO,IAAMG,GAA6B3D,GAAiB,EAkE3D,SAASQ,IAAsD,CAC7D,SAASoD,EAAWC,EAAoDP,EAAgG,CACtK,MAAO,CACL,uBAAwB,aACxB,eAAAO,EACA,GAAGP,CACL,CACF,CACA,OAAAM,EAAW,UAAY,IAAMA,EACtB,CACL,QAAQE,EAAsC,CAC5C,OAAO,OAAO,OAAO,CAGnB,CAACA,EAAY,IAAI,KAAKJ,EAAsC,CAC1D,OAAOI,EAAY,GAAGJ,CAAI,CAC5B,CACF,EAAEI,EAAY,IAAI,EAAG,CACnB,uBAAwB,SAC1B,CAAU,CACZ,EACA,gBAAgBC,EAASlD,EAAS,CAChC,MAAO,CACL,uBAAwB,qBACxB,QAAAkD,EACA,QAAAlD,CACF,CACF,EACA,WAAY+C,CACd,CACF,CACA,SAAStC,GAAqC,CAC5C,KAAAR,EACA,YAAAG,EACA,eAAA+C,CACF,EAAmBC,EAGuDvD,EAA+C,CACvH,IAAIoD,EACAI,EACJ,GAAI,YAAaD,EAAyB,CACxC,GAAID,GAAkB,CAACG,GAAmCF,CAAuB,EAC/E,MAAM,IAAI,MAA8C3D,EAAyB,EAAE,CAA+G,EAEpMwD,EAAcG,EAAwB,QACtCC,EAAkBD,EAAwB,OAC5C,MACEH,EAAcG,EAEhBvD,EAAQ,QAAQI,EAAMgD,CAAW,EAAE,kBAAkB7C,EAAa6C,CAAW,EAAE,aAAa7C,EAAaiD,EAAkBE,EAAatD,EAAMoD,CAAe,EAAIE,EAAatD,CAAI,CAAC,CACrL,CACA,SAASM,GAA0CF,EAAqG,CACtJ,OAAOA,EAAkB,yBAA2B,YACtD,CACA,SAASiD,GAA0CjD,EAA2F,CAC5I,OAAOA,EAAkB,yBAA2B,oBACtD,CACA,SAASG,GAAwC,CAC/C,KAAAP,EACA,YAAAG,CACF,EAAmBC,EAA2ER,EAA+CR,EAA2C,CACtL,GAAI,CAACA,EACH,MAAM,IAAI,MAA8CI,EAAyB,EAAE,CAAiM,EAEtR,GAAM,CACJ,eAAAuD,EACA,UAAAQ,EACA,QAAAC,EACA,SAAAC,EACA,QAAAC,EACA,QAAArE,CACF,EAAIe,EACEuD,EAAQvE,EAAIY,EAAM+C,EAAgB1D,CAAc,EACtDO,EAAQ,aAAaO,EAAawD,CAAK,EACnCJ,GACF3D,EAAQ,QAAQ+D,EAAM,UAAWJ,CAAS,EAExCC,GACF5D,EAAQ,QAAQ+D,EAAM,QAASH,CAAO,EAEpCC,GACF7D,EAAQ,QAAQ+D,EAAM,SAAUF,CAAQ,EAEtCC,GACF9D,EAAQ,WAAW+D,EAAM,QAASD,CAAO,EAE3C9D,EAAQ,kBAAkBO,EAAa,CACrC,UAAWoD,GAAaK,GACxB,QAASJ,GAAWI,GACpB,SAAUH,GAAYG,GACtB,QAASF,GAAWE,EACtB,CAAC,CACH,CACA,SAASA,IAAO,CAAC,CC/qBV,SAASC,IAAoE,CAClF,MAAO,CACL,IAAK,CAAC,EACN,SAAU,CAAC,CACb,CACF,CACO,SAASC,GAAkDC,EAAoE,CAGpI,SAASC,EAAgBC,EAAuB,CAAC,EAAGC,EAA8C,CAChG,IAAMC,EAAQ,OAAO,OAAON,GAAsB,EAAGI,CAAe,EACpE,OAAOC,EAAWH,EAAa,OAAOI,EAAOD,CAAQ,EAAIC,CAC3D,CACA,MAAO,CACL,gBAAAH,CACF,CACF,CCTO,SAASI,IAAiD,CAG/D,SAASC,EAAgBC,EAAgDC,EAA+B,CAAC,EAAgC,CACvI,GAAM,CACJ,eAAAC,EAAiBC,EACnB,EAAIF,EACEG,EAAaC,GAA8BA,EAAM,IACjDC,EAAkBD,GAA8BA,EAAM,SACtDE,EAAYL,EAAeE,EAAWE,EAAgB,CAACE,EAAKC,IAAkBD,EAAI,IAAIE,GAAMD,EAASC,CAAE,CAAE,CAAC,EAC1GC,EAAW,CAACC,EAAYF,IAAWA,EACnCG,EAAa,CAACJ,EAAyBC,IAAWD,EAASC,CAAE,EAC7DI,EAAcZ,EAAeE,EAAWI,GAAOA,EAAI,MAAM,EAC/D,GAAI,CAACR,EACH,MAAO,CACL,UAAAI,EACA,eAAAE,EACA,UAAAC,EACA,YAAAO,EACA,WAAYZ,EAAeI,EAAgBK,EAAUE,CAAU,CACjE,EAEF,IAAME,EAA2Bb,EAAeF,EAAgDM,CAAc,EAC9G,MAAO,CACL,UAAWJ,EAAeF,EAAaI,CAAS,EAChD,eAAgBW,EAChB,UAAWb,EAAeF,EAAaO,CAAS,EAChD,YAAaL,EAAeF,EAAac,CAAW,EACpD,WAAYZ,EAAea,EAA0BJ,EAAUE,CAAU,CAC3E,CACF,CACA,MAAO,CACL,aAAAd,CACF,CACF,CC1CA,IAAAiB,GAAoD,iBAK7C,IAAMC,GAAe,WACrB,SAASC,GAA0DC,EAAuD,CAC/H,IAAMC,EAAWC,EAAoB,CAACC,EAAcC,IAAuCJ,EAAQI,CAAK,CAAC,EACzG,OAAO,SAA0DA,EAAgC,CAC/F,OAAOH,EAASG,EAAY,MAAS,CACvC,CACF,CACO,SAASF,EAA+CF,EAA+D,CAC5H,OAAO,SAA0DI,EAAUC,EAA8B,CACvG,SAASC,EAAwBD,EAAoD,CACnF,OAAOE,GAAMF,CAAG,CAClB,CACA,IAAMG,EAAcC,GAAuC,CACrDH,EAAwBD,CAAG,EAC7BL,EAAQK,EAAI,QAASI,CAAK,EAE1BT,EAAQK,EAAKI,CAAK,CAEtB,EACA,OAAIX,GAA0CM,CAAK,GAIjDI,EAAWJ,CAAK,EAGTA,MAEF,GAAAM,SAAgBN,EAAOI,CAAU,CAC1C,CACF,CClCA,IAAAG,GAAiC,iBAE1B,SAASC,EAAsCC,EAAWC,EAA6B,CAK5F,OAJYA,EAASD,CAAM,CAK7B,CACO,SAASE,EAA4CC,EAAsD,CAChH,OAAK,MAAM,QAAQA,CAAQ,IACzBA,EAAW,OAAO,OAAOA,CAAQ,GAE5BA,CACT,CACO,SAASC,EAAcC,EAAwB,CACpD,SAAQ,YAAQA,CAAK,KAAI,YAAQA,CAAK,EAAIA,CAC5C,CACO,SAASC,GAAkDC,EAA2CN,EAA6BO,EAAkE,CAC1MD,EAAcL,EAAoBK,CAAW,EAC7C,IAAME,EAAmBL,EAAWI,EAAM,GAAG,EACvCE,EAAc,IAAI,IAAQD,CAAgB,EAC1CE,EAAa,CAAC,EACdC,EAAW,IAAI,IAAQ,CAAC,CAAC,EACzBC,EAA2B,CAAC,EAClC,QAAWb,KAAUO,EAAa,CAChC,IAAMO,EAAKf,EAAcC,EAAQC,CAAQ,EACrCS,EAAY,IAAII,CAAE,GAAKF,EAAS,IAAIE,CAAE,EACxCD,EAAQ,KAAK,CACX,GAAAC,EACA,QAASd,CACX,CAAC,GAEDY,EAAS,IAAIE,CAAE,EACfH,EAAM,KAAKX,CAAM,EAErB,CACA,MAAO,CAACW,EAAOE,EAASJ,CAAgB,CAC1C,CCnCO,SAASM,GAAmDC,EAAwD,CAEzH,SAASC,EAAcC,EAAWC,EAAgB,CAChD,IAAMC,EAAMC,EAAcH,EAAQF,CAAQ,EACtCI,KAAOD,EAAM,WAGjBA,EAAM,IAAI,KAAKC,CAAqB,EACnCD,EAAM,SAA2BC,CAAG,EAAIF,EAC3C,CACA,SAASI,EAAeC,EAA2CJ,EAAgB,CACjFI,EAAcC,EAAoBD,CAAW,EAC7C,QAAWL,KAAUK,EACnBN,EAAcC,EAAQC,CAAK,CAE/B,CACA,SAASM,EAAcP,EAAWC,EAAgB,CAChD,IAAMC,EAAMC,EAAcH,EAAQF,CAAQ,EACpCI,KAAOD,EAAM,UACjBA,EAAM,IAAI,KAAKC,CAAqB,EAGrCD,EAAM,SAA2BC,CAAG,EAAIF,CAC3C,CACA,SAASQ,EAAeH,EAA2CJ,EAAgB,CACjFI,EAAcC,EAAoBD,CAAW,EAC7C,QAAWL,KAAUK,EACnBE,EAAcP,EAAQC,CAAK,CAE/B,CACA,SAASQ,EAAcJ,EAA2CJ,EAAgB,CAChFI,EAAcC,EAAoBD,CAAW,EAC7CJ,EAAM,IAAM,CAAC,EACbA,EAAM,SAAW,CAAC,EAClBG,EAAeC,EAAaJ,CAAK,CACnC,CACA,SAASS,EAAiBR,EAASD,EAAgB,CACjD,OAAOU,EAAkB,CAACT,CAAG,EAAGD,CAAK,CACvC,CACA,SAASU,EAAkBC,EAAqBX,EAAgB,CAC9D,IAAIY,EAAY,GAChBD,EAAK,QAAQV,GAAO,CACdA,KAAOD,EAAM,WACf,OAAQA,EAAM,SAA2BC,CAAG,EAC5CW,EAAY,GAEhB,CAAC,EACGA,IACFZ,EAAM,IAAOA,EAAM,IAAa,OAAOa,GAAMA,KAAMb,EAAM,QAAQ,EAErE,CACA,SAASc,EAAiBd,EAAgB,CACxC,OAAO,OAAOA,EAAO,CACnB,IAAK,CAAC,EACN,SAAU,CAAC,CACb,CAAC,CACH,CACA,SAASe,EAAWJ,EAEjBK,EAAuBhB,EAAmB,CAC3C,IAAMiB,EAA2BjB,EAAM,SAA2BgB,EAAO,EAAE,EAC3E,GAAIC,IAAa,OACf,MAAO,GAET,IAAMC,EAAa,OAAO,OAAO,CAAC,EAAGD,EAAUD,EAAO,OAAO,EACvDG,EAASjB,EAAcgB,EAASrB,CAAQ,EACxCuB,EAAYD,IAAWH,EAAO,GACpC,OAAII,IACFT,EAAKK,EAAO,EAAE,EAAIG,EAClB,OAAQnB,EAAM,SAA2BgB,EAAO,EAAE,GAGnDhB,EAAM,SAA2BmB,CAAM,EAAID,EACrCE,CACT,CACA,SAASC,EAAiBL,EAAuBhB,EAAgB,CAC/D,OAAOsB,EAAkB,CAACN,CAAM,EAAGhB,CAAK,CAC1C,CACA,SAASsB,EAAkBC,EAAuCvB,EAAgB,CAChF,IAAMwB,EAEF,CAAC,EACCC,EAEF,CAAC,EACLF,EAAQ,QAAQP,GAAU,CAEpBA,EAAO,MAAMhB,EAAM,WAErByB,EAAiBT,EAAO,EAAE,EAAI,CAC5B,GAAIA,EAAO,GAGX,QAAS,CACP,GAAGS,EAAiBT,EAAO,EAAE,GAAG,QAChC,GAAGA,EAAO,OACZ,CACF,EAEJ,CAAC,EACDO,EAAU,OAAO,OAAOE,CAAgB,EACdF,EAAQ,OAAS,GAEpBA,EAAQ,OAAOP,GAAUD,EAAWS,EAASR,EAAQhB,CAAK,CAAC,EAAE,OAAS,IAEzFA,EAAM,IAAM,OAAO,OAAOA,EAAM,QAAQ,EAAE,IAAI0B,GAAKxB,EAAcwB,EAAQ7B,CAAQ,CAAC,EAGxF,CACA,SAAS8B,EAAiB5B,EAAWC,EAAgB,CACnD,OAAO4B,EAAkB,CAAC7B,CAAM,EAAGC,CAAK,CAC1C,CACA,SAAS4B,EAAkBxB,EAA2CJ,EAAgB,CACpF,GAAM,CAAC6B,EAAOX,CAAO,EAAIY,GAAiC1B,EAAaP,EAAUG,CAAK,EACtFG,EAAe0B,EAAO7B,CAAK,EAC3BsB,EAAkBJ,EAASlB,CAAK,CAClC,CACA,MAAO,CACL,UAAW+B,GAAkCjB,CAAgB,EAC7D,OAAQkB,EAAoBlC,CAAa,EACzC,QAASkC,EAAoB7B,CAAc,EAC3C,OAAQ6B,EAAoB1B,CAAa,EACzC,QAAS0B,EAAoBzB,CAAc,EAC3C,OAAQyB,EAAoBxB,CAAa,EACzC,UAAWwB,EAAoBX,CAAgB,EAC/C,WAAYW,EAAoBV,CAAiB,EACjD,UAAWU,EAAoBL,CAAgB,EAC/C,WAAYK,EAAoBJ,CAAiB,EACjD,UAAWI,EAAoBvB,CAAgB,EAC/C,WAAYuB,EAAoBtB,CAAiB,CACnD,CACF,CCjIO,SAASuB,GAAmBC,EAAkBC,EAASC,EAAyC,CACrG,IAAIC,EAAW,EACXC,EAAYJ,EAAY,OAC5B,KAAOG,EAAWC,GAAW,CAC3B,IAAIC,EAAcF,EAAWC,IAAc,EACrCE,EAAcN,EAAYK,CAAW,EAC/BH,EAAmBD,EAAMK,CAAW,GACrC,EACTH,EAAWE,EAAc,EAEzBD,EAAYC,CAEhB,CACA,OAAOF,CACT,CACO,SAASI,GAAUP,EAAkBC,EAASC,EAAsC,CACzF,IAAMM,EAAgBT,GAAgBC,EAAaC,EAAMC,CAAkB,EAC3E,OAAAF,EAAY,OAAOQ,EAAe,EAAGP,CAAI,EAClCD,CACT,CACO,SAASS,GAAiDC,EAA6BC,EAAkD,CAE9I,GAAM,CACJ,UAAAC,EACA,WAAAC,EACA,UAAAC,CACF,EAAIC,GAA2BL,CAAQ,EACvC,SAASM,EAAcC,EAAWC,EAAgB,CAChD,OAAOC,EAAe,CAACF,CAAM,EAAGC,CAAK,CACvC,CACA,SAASC,EAAeC,EAA2CF,EAAUG,EAA0B,CACrGD,EAAcE,EAAoBF,CAAW,EAC7C,IAAMG,EAAe,IAAI,IAAQF,GAAeG,EAAWN,EAAM,GAAG,CAAC,EAC/DO,EAASL,EAAY,OAAOM,GAAS,CAACH,EAAa,IAAII,EAAcD,EAAOhB,CAAQ,CAAC,CAAC,EACxFe,EAAO,SAAW,GACpBG,EAAcV,EAAOO,CAAM,CAE/B,CACA,SAASI,EAAcZ,EAAWC,EAAgB,CAChD,OAAOY,EAAe,CAACb,CAAM,EAAGC,CAAK,CACvC,CACA,SAASY,EAAeV,EAA2CF,EAAgB,CAEjF,GADAE,EAAcE,EAAoBF,CAAW,EACzCA,EAAY,SAAW,EAAG,CAC5B,QAAWnB,KAAQmB,EACjB,OAAQF,EAAM,SAA2BR,EAAST,CAAI,CAAC,EAEzD2B,EAAcV,EAAOE,CAAW,CAClC,CACF,CACA,SAASW,EAAcX,EAA2CF,EAAgB,CAChFE,EAAcE,EAAoBF,CAAW,EAC7CF,EAAM,SAAW,CAAC,EAClBA,EAAM,IAAM,CAAC,EACbC,EAAeC,EAAaF,EAAO,CAAC,CAAC,CACvC,CACA,SAASc,EAAiBC,EAAuBf,EAAgB,CAC/D,OAAOgB,EAAkB,CAACD,CAAM,EAAGf,CAAK,CAC1C,CACA,SAASgB,EAAkBC,EAAuCjB,EAAgB,CAChF,IAAIkB,EAAiB,GACjBC,EAAc,GAClB,QAASJ,KAAUE,EAAS,CAC1B,IAAMlB,EAAyBC,EAAM,SAA2Be,EAAO,EAAE,EACzE,GAAI,CAAChB,EACH,SAEFmB,EAAiB,GACjB,OAAO,OAAOnB,EAAQgB,EAAO,OAAO,EACpC,IAAMK,EAAQ5B,EAASO,CAAM,EAC7B,GAAIgB,EAAO,KAAOK,EAAO,CAGvBD,EAAc,GACd,OAAQnB,EAAM,SAA2Be,EAAO,EAAE,EAClD,IAAMM,EAAYrB,EAAM,IAAa,QAAQe,EAAO,EAAE,EACtDf,EAAM,IAAIqB,CAAQ,EAAID,EACrBpB,EAAM,SAA2BoB,CAAK,EAAIrB,CAC7C,CACF,CACImB,GACFR,EAAcV,EAAO,CAAC,EAAGkB,EAAgBC,CAAW,CAExD,CACA,SAASG,EAAiBvB,EAAWC,EAAgB,CACnD,OAAOuB,EAAkB,CAACxB,CAAM,EAAGC,CAAK,CAC1C,CACA,SAASuB,EAAkBrB,EAA2CF,EAAgB,CACpF,GAAM,CAACwB,EAAOC,EAASC,CAAgB,EAAIC,GAAiCzB,EAAaV,EAAUQ,CAAK,EACpGwB,EAAM,QACRvB,EAAeuB,EAAOxB,EAAO0B,CAAgB,EAE3CD,EAAQ,QACVT,EAAkBS,EAASzB,CAAK,CAEpC,CACA,SAAS4B,EAAeC,EAAuBC,EAAuB,CACpE,GAAID,EAAE,SAAWC,EAAE,OACjB,MAAO,GAET,QAASC,EAAI,EAAGA,EAAIF,EAAE,OAAQE,IAC5B,GAAIF,EAAEE,CAAC,IAAMD,EAAEC,CAAC,EAGhB,MAAO,GAET,MAAO,EACT,CAEA,IAAMrB,EAA+B,CAACV,EAAOgC,EAAYd,EAAgBC,IAAgB,CACvF,IAAMc,EAAkB3B,EAAWN,EAAM,QAAQ,EAC3CkC,EAAa5B,EAAWN,EAAM,GAAG,EACjCmC,EAAgBnC,EAAM,SACxBoC,EAAoBF,EACpBf,IACFiB,EAAM,IAAI,IAAIF,CAAU,GAE1B,IAAIG,EAAsB,CAAC,EAC3B,QAAWC,KAAMF,EAAK,CACpB,IAAMrC,GAASkC,EAAgBK,CAAE,EAC7BvC,IACFsC,EAAe,KAAKtC,EAAM,CAE9B,CACA,IAAMwC,EAAqBF,EAAe,SAAW,EAGrD,QAAWtD,KAAQiD,EACjBG,EAAc3C,EAAST,CAAI,CAAC,EAAIA,EAC3BwD,GAEHlD,GAAOgD,EAAgBtD,EAAMU,CAAQ,EAGrC8C,EAEFF,EAAiBL,EAAW,MAAM,EAAE,KAAKvC,CAAQ,EACxCyB,GAETmB,EAAe,KAAK5C,CAAQ,EAE9B,IAAM+C,EAAeH,EAAe,IAAI7C,CAAQ,EAC3CoC,EAAeM,EAAYM,CAAY,IAC1CxC,EAAM,IAAMwC,EAEhB,EACA,MAAO,CACL,UAAA9C,EACA,WAAAC,EACA,UAAAC,EACA,OAAQ6C,EAAoB3C,CAAa,EACzC,UAAW2C,EAAoB3B,CAAgB,EAC/C,UAAW2B,EAAoBnB,CAAgB,EAC/C,OAAQmB,EAAoB9B,CAAa,EACzC,QAAS8B,EAAoB7B,CAAc,EAC3C,OAAQ6B,EAAoB5B,CAAa,EACzC,QAAS4B,EAAoBxC,CAAc,EAC3C,WAAYwC,EAAoBzB,CAAiB,EACjD,WAAYyB,EAAoBlB,CAAiB,CACnD,CACF,CCrJO,SAASmB,GAAuBC,EAA6C,CAAC,EAA+B,CAClH,GAAM,CACJ,SAAAC,EACA,aAAAC,CACF,EAAiD,CAC/C,aAAc,GACd,SAAWC,GAAkBA,EAAS,GACtC,GAAGH,CACL,EACMI,EAAeF,EAAeG,GAAyBJ,EAAUC,CAAY,EAAII,GAA2BL,CAAQ,EACpHM,EAAeC,GAA0BJ,CAAY,EACrDK,EAAmBC,GAAoC,EAC7D,MAAO,CACL,SAAAT,EACA,aAAAC,EACA,GAAGK,EACH,GAAGE,EACH,GAAGL,CACL,CACF,CClCA,IAAAO,GAAyB,iBCDzB,IAAMC,GAAO,OACPC,GAAW,WACXC,GAAY,YACZC,GAAY,YAGLC,GAAgB,QAAQD,EAAS,GACjCE,GAAgB,QAAQH,EAAS,GACjCI,GAAoB,GAAGL,EAAQ,IAAIE,EAAS,GAC5CI,GAAoB,GAAGN,EAAQ,IAAIC,EAAS,GAC5CM,EAAN,KAAgD,CAGrD,YAAmBC,EAA0B,CAA1B,UAAAA,EACjB,KAAK,QAAU,GAAGT,EAAI,IAAIG,EAAS,aAAaM,CAAI,GACtD,CAJA,KAAO,iBACP,OAIF,ECfO,IAAMC,GAAuG,CAACC,EAAeC,IAAqB,CACvJ,GAAI,OAAOD,GAAS,WAClB,MAAM,IAAI,UAAkDE,EAAwB,EAAE,CAAmC,CAE7H,EACaC,EAAO,IAAM,CAAC,EACdC,GAAiB,CAAKC,EAAqBC,EAAUH,KAChEE,EAAQ,MAAMC,CAAO,EACdD,GAEIE,GAAyB,CAACC,EAA0BC,KAC/DD,EAAY,iBAAiB,QAASC,EAAU,CAC9C,KAAM,EACR,CAAC,EACM,IAAMD,EAAY,oBAAoB,QAASC,CAAQ,GAanDC,EAA4B,CAAKC,EAAkCC,IAAoB,CAElG,IAAMC,EAASF,EAAgB,OAC3BE,EAAO,UAQL,WAAYA,GAChB,OAAO,eAAeA,EAAQ,SAAU,CACtC,WAAY,GACZ,MAAOD,EACP,aAAc,GACd,SAAU,EACZ,CAAC,EAGFD,EAAgB,MAAkCC,CAAM,EAC3D,ECxCO,IAAME,EAAkBC,GAA8B,CAC3D,GAAIA,EAAO,QAAS,CAClB,GAAM,CACJ,OAAAC,CACF,EAAID,EACJ,MAAM,IAAIE,EAAeD,CAAM,CACjC,CACF,EAOO,SAASE,GAAkBH,EAAuCI,EAAiC,CACxG,IAAIC,EAAUC,EACd,OAAO,IAAI,QAAW,CAACC,EAASC,IAAW,CACzC,IAAMC,EAAkB,IAAMD,EAAO,IAAIN,EAAeF,EAAO,MAAM,CAAC,EACtE,GAAIA,EAAO,QAAS,CAClBS,EAAgB,EAChB,MACF,CACAJ,EAAUK,GAAuBV,EAAQS,CAAe,EACxDL,EAAQ,QAAQ,IAAMC,EAAQ,CAAC,EAAE,KAAKE,EAASC,CAAM,CACvD,CAAC,EAAE,QAAQ,IAAM,CAEfH,EAAUC,CACZ,CAAC,CACH,CASO,IAAMK,GAAU,MAAWC,EAAwBC,IAAiD,CACzG,GAAI,CACF,aAAM,QAAQ,QAAQ,EAEf,CACL,OAAQ,KACR,MAHY,MAAMD,EAAK,CAIzB,CACF,OAASE,EAAY,CACnB,MAAO,CACL,OAAQA,aAAiBZ,EAAiB,YAAc,WACxD,MAAAY,CACF,CACF,QAAE,CACAD,IAAU,CACZ,CACF,EASaE,EAAmBf,GACtBI,GACCY,GAAeb,GAAeH,EAAQI,CAAO,EAAE,KAAKa,IACzDlB,EAAeC,CAAM,EACdiB,EACR,CAAC,EAUOC,GAAelB,GAAwB,CAClD,IAAMmB,EAAQJ,EAAkBf,CAAM,EACtC,OAAQoB,GACCD,EAAM,IAAI,QAAcZ,GAAW,WAAWA,EAASa,CAAS,CAAC,CAAC,CAE7E,EH9EA,GAAM,CACJ,OAAAC,CACF,EAAI,OAIEC,GAAqB,CAAC,EACtBC,GAAM,qBACNC,GAAa,CAACC,EAAmDC,IAA2C,CAChH,IAAMC,EAAmBC,GAAgCC,GAAuBJ,EAAmB,IAAMK,EAA0BF,EAAYH,EAAkB,MAAM,CAAC,EACxK,MAAO,CAAKM,EAAqCC,IAAsC,CACrFC,GAAeF,EAAc,cAAc,EAC3C,IAAMG,EAAuB,IAAI,gBACjCP,EAAgBO,CAAoB,EACpC,IAAMC,EAASC,GAAW,SAAwB,CAChDC,EAAeZ,CAAiB,EAChCY,EAAeH,EAAqB,MAAM,EAC1C,IAAMC,EAAU,MAAMJ,EAAa,CACjC,MAAOO,EAAYJ,EAAqB,MAAM,EAC9C,MAAOK,GAAYL,EAAqB,MAAM,EAC9C,OAAQA,EAAqB,MAC/B,CAAC,EACD,OAAAG,EAAeH,EAAqB,MAAM,EACnCC,CACT,EAAG,IAAML,EAA0BI,EAAsBM,EAAa,CAAC,EACvE,OAAIR,GAAM,UACRN,EAAuB,KAAKS,EAAO,MAAMM,CAAI,CAAC,EAEzC,CACL,OAAQH,EAA2Bb,CAAiB,EAAEU,CAAM,EAC5D,QAAS,CACPL,EAA0BI,EAAsBQ,EAAa,CAC/D,CACF,CACF,CACF,EACMC,GAAoB,CAAKC,EAAwEC,IAAwC,CAQ7I,IAAMC,EAAO,MAA2CC,EAAcC,IAAgC,CACpGX,EAAeQ,CAAM,EAGrB,IAAII,EAAmC,IAAM,CAAC,EAiBxCC,EAAwD,CAhBzC,IAAI,QAAwB,CAACC,EAASC,IAAW,CAEpE,IAAIC,EAAgBT,EAAe,CACjC,UAAWG,EACX,OAAQ,CAACO,EAAQC,IAAsB,CAErCA,EAAY,YAAY,EAExBJ,EAAQ,CAACG,EAAQC,EAAY,SAAS,EAAGA,EAAY,iBAAiB,CAAC,CAAC,CAC1E,CACF,CAAC,EACDN,EAAc,IAAM,CAClBI,EAAc,EACdD,EAAO,CACT,CACF,CAAC,CAC0E,EACvEJ,GAAW,MACbE,EAAS,KAAK,IAAI,QAAcC,GAAW,WAAWA,EAASH,EAAS,IAAI,CAAC,CAAC,EAEhF,GAAI,CACF,IAAMQ,EAAS,MAAMC,GAAeZ,EAAQ,QAAQ,KAAKK,CAAQ,CAAC,EAClE,OAAAb,EAAeQ,CAAM,EACdW,CACT,QAAE,CAEAP,EAAY,CACd,CACF,EACA,MAAQ,CAACF,EAAoCC,IAAgCU,GAAeZ,EAAKC,EAAWC,CAAO,CAAC,CACtH,EACMW,GAA6BC,GAAwC,CACzE,GAAI,CACF,KAAAC,EACA,cAAAC,EACA,QAAAC,EACA,UAAAhB,EACA,OAAAiB,CACF,EAAIJ,EACJ,GAAIC,EACFd,EAAYkB,EAAaJ,CAAI,EAAE,cACtBC,EACTD,EAAOC,EAAe,KACtBf,EAAYe,EAAc,cACjBC,EACThB,EAAYgB,UACH,CAAAhB,EAGT,MAAM,IAAI,MAA8CmB,EAAwB,EAAE,CAA6F,EAEjL,OAAAjC,GAAe+B,EAAQ,kBAAkB,EAClC,CACL,UAAAjB,EACA,KAAAc,EACA,OAAAG,CACF,CACF,EAGaG,GAAwE9C,EAAQuC,GAAwC,CACnI,GAAM,CACJ,KAAAC,EACA,UAAAd,EACA,OAAAiB,CACF,EAAIL,GAA0BC,CAAO,EAWrC,MAVsC,CACpC,GAAIQ,EAAO,EACX,OAAAJ,EACA,KAAAH,EACA,UAAAd,EACA,QAAS,IAAI,IACb,YAAa,IAAM,CACjB,MAAM,IAAI,MAA8CmB,EAAyB,EAAE,CAAiC,CACtH,CACF,CAEF,EAAG,CACD,UAAW,IAAMC,EACnB,CAAC,EACKE,GAAoB,CAACC,EAAyCV,IAAwC,CAC1G,GAAM,CACJ,KAAAC,EACA,OAAAG,EACA,UAAAjB,CACF,EAAIY,GAA0BC,CAAO,EACrC,OAAO,MAAM,KAAKU,EAAY,OAAO,CAAC,EAAE,KAAKC,IACd,OAAOV,GAAS,SAAWU,EAAM,OAASV,EAAOU,EAAM,YAAcxB,IACnEwB,EAAM,SAAWP,CACjD,CACH,EACMQ,GAAyBD,GAA2D,CACxFA,EAAM,QAAQ,QAAQ3C,GAAc,CAClCE,EAA0BF,EAAY6C,EAAiB,CACzD,CAAC,CACH,EACMC,GAAiCJ,GAC9B,IAAM,CACXA,EAAY,QAAQE,EAAqB,EACzCF,EAAY,MAAM,CACpB,EAUIK,GAAoB,CAACC,EAAoCC,EAAwBC,IAAuC,CAC5H,GAAI,CACFF,EAAaC,EAAeC,CAAS,CACvC,OAASC,EAAmB,CAG1B,WAAW,IAAM,CACf,MAAMA,CACR,EAAG,CAAC,CACN,CACF,EAKaC,GAA6B3D,EAAsB4C,EAAa,GAAG1C,EAAG,MAAM,EAAG,CAC1F,UAAW,IAAMyD,EACnB,CAAC,EAKYC,GAAmChB,EAAa,GAAG1C,EAAG,YAAY,EAKlE2D,GAAgC7D,EAAsB4C,EAAa,GAAG1C,EAAG,SAAS,EAAG,CAChG,UAAW,IAAM2D,EACnB,CAAC,EACKC,GAA4C,IAAIC,IAAoB,CACxE,QAAQ,MAAM,GAAG7D,EAAG,SAAU,GAAG6D,CAAI,CACvC,EAKaC,GAA2B,CAAyIC,EAAoE,CAAC,IAAM,CAC1P,IAAMhB,EAAc,IAAI,IAClB,CACJ,MAAAiB,EACA,QAAAC,EAAUL,EACZ,EAAIG,EACJrD,GAAeuD,EAAS,SAAS,EACjC,IAAMC,EAAelB,IACnBA,EAAM,YAAc,IAAMD,EAAY,OAAOC,EAAM,EAAE,EACrDD,EAAY,IAAIC,EAAM,GAAIA,CAAK,EACvBmB,GAA+C,CACrDnB,EAAM,YAAY,EACdmB,GAAe,cACjBlB,GAAsBD,CAAK,CAE/B,GAEI3B,EAAmBgB,GAAwC,CAC/D,IAAMW,EAAQF,GAAkBC,EAAaV,CAAO,GAAKO,GAAoBP,CAAc,EAC3F,OAAO6B,EAAYlB,CAAK,CAC1B,EACAlD,EAAOuB,EAAgB,CACrB,UAAW,IAAMA,CACnB,CAAC,EACD,IAAMS,EAAiBO,GAA8E,CACnG,IAAMW,EAAQF,GAAkBC,EAAaV,CAAO,EACpD,OAAIW,IACFA,EAAM,YAAY,EACdX,EAAQ,cACVY,GAAsBD,CAAK,GAGxB,CAAC,CAACA,CACX,EACAlD,EAAOgC,EAAe,CACpB,UAAW,IAAMA,CACnB,CAAC,EACD,IAAMsC,EAAiB,MAAOpB,EAAwDjB,EAAiBsC,EAAoBC,IAAsC,CAC/J,IAAMC,EAAyB,IAAI,gBAC7BhD,EAAOH,GAAkBC,EAA6CkD,EAAuB,MAAM,EACnGC,EAAmC,CAAC,EAC1C,GAAI,CACFxB,EAAM,QAAQ,IAAIuB,CAAsB,EACxC,MAAM,QAAQ,QAAQvB,EAAM,OAAOjB,EAEnCjC,EAAO,CAAC,EAAGuE,EAAK,CACd,iBAAAC,EACA,UAAW,CAAC9C,EAAsCC,IAAqBF,EAAKC,EAAWC,CAAO,EAAE,KAAK,OAAO,EAC5G,KAAAF,EACA,MAAOP,GAAYuD,EAAuB,MAAM,EAChD,MAAOxD,EAAiBwD,EAAuB,MAAM,EACrD,MAAAP,EACA,OAAQO,EAAuB,OAC/B,KAAMtE,GAAWsE,EAAuB,OAAQC,CAAgB,EAChE,YAAaxB,EAAM,YACnB,UAAW,IAAM,CACfD,EAAY,IAAIC,EAAM,GAAIA,CAAK,CACjC,EACA,sBAAuB,IAAM,CAC3BA,EAAM,QAAQ,QAAQ,CAAC3C,EAAYoE,EAAGC,IAAQ,CACxCrE,IAAekE,IACjBhE,EAA0BF,EAAY6C,EAAiB,EACvDwB,EAAI,OAAOrE,CAAU,EAEzB,CAAC,CACH,EACA,OAAQ,IAAM,CACZE,EAA0BgE,EAAwBrB,EAAiB,EACnEF,EAAM,QAAQ,OAAOuB,CAAsB,CAC7C,EACA,iBAAkB,IAAM,CACtBzD,EAAeyD,EAAuB,MAAM,CAC9C,CACF,CAAC,CAAC,CAAC,CACL,OAASI,EAAe,CAChBA,aAAyBC,GAC7BxB,GAAkBa,EAASU,EAAe,CACxC,SAAU,QACZ,CAAC,CAEL,QAAE,CACA,MAAM,QAAQ,IAAIH,CAAgB,EAClCjE,EAA0BgE,EAAwBM,EAAiB,EACnE7B,EAAM,QAAQ,OAAOuB,CAAsB,CAC7C,CACF,EACMO,EAA0B3B,GAA8BJ,CAAW,EA0DzE,MAAO,CACL,WA1D6EsB,GAAOU,GAAQhD,GAAU,CACtG,GAAI,IAAC,aAASA,CAAM,EAElB,OAAOgD,EAAKhD,CAAM,EAEpB,GAAI0B,GAAY,MAAM1B,CAAM,EAC1B,OAAOV,EAAeU,EAAO,OAAc,EAE7C,GAAI2B,GAAkB,MAAM3B,CAAM,EAAG,CACnC+C,EAAwB,EACxB,MACF,CACA,GAAInB,GAAe,MAAM5B,CAAM,EAC7B,OAAOD,EAAcC,EAAO,OAAO,EAIrC,IAAIiD,EAAuDX,EAAI,SAAS,EAIlEC,EAAmB,IAAiB,CACxC,GAAIU,IAAkBjF,GACpB,MAAM,IAAI,MAA8C4C,EAAyB,EAAE,CAA+D,EAEpJ,OAAOqC,CACT,EACIpE,EACJ,GAAI,CAGF,GADAA,EAASmE,EAAKhD,CAAM,EAChBgB,EAAY,KAAO,EAAG,CACxB,IAAMkC,EAAeZ,EAAI,SAAS,EAE5Ba,EAAkB,MAAM,KAAKnC,EAAY,OAAO,CAAC,EACvD,QAAWC,KAASkC,EAAiB,CACnC,IAAIC,EAAc,GAClB,GAAI,CACFA,EAAcnC,EAAM,UAAUjB,EAAQkD,EAAcD,CAAa,CACnE,OAASI,EAAgB,CACvBD,EAAc,GACd/B,GAAkBa,EAASmB,EAAgB,CACzC,SAAU,WACZ,CAAC,CACH,CACKD,GAGLf,EAAepB,EAAOjB,EAAQsC,EAAKC,CAAgB,CACrD,CACF,CACF,QAAE,CAEAU,EAAgBjF,EAClB,CACA,OAAOa,CACT,EAGE,eAAAS,EACA,cAAAS,EACA,eAAgBgD,CAClB,CACF,EIvWA,IAAAO,GAAwB,iBAOxB,IAAMC,GAA8GC,IAA4F,CAC9M,WAAAA,EACA,QAAS,IAAI,GACf,GACMC,GAAiBC,GAAwBC,GAI1CA,GAAQ,MAAM,aAAeD,EACrBE,GAA0B,IAA2I,CAChL,IAAMF,EAAaG,EAAO,EACpBC,EAAgB,IAAI,IACpBC,EAAiB,OAAO,OAAOC,EAAa,wBAAyB,IAAIC,KAAyD,CACtI,QAASA,EACT,KAAM,CACJ,WAAAP,CACF,CACF,EAAE,EAAG,CACH,UAAW,IAAMK,CACnB,CAAC,EACKG,EAAgB,OAAO,OAAO,YAA0BD,EAAqD,CACjHA,EAAY,QAAQT,GAAc,CAChCW,EAAoBL,EAAeN,EAAYD,EAAqB,CACtE,CAAC,CACH,EAAG,CACD,UAAW,IAAMW,CACnB,CAAC,EACKE,EAA0DC,GAAO,CACrE,IAAMC,EAAoB,MAAM,KAAKR,EAAc,OAAO,CAAC,EAAE,IAAIS,GAASJ,EAAoBI,EAAM,QAASF,EAAKE,EAAM,UAAU,CAAC,EACnI,SAAO,YAAQ,GAAGD,CAAiB,CACrC,EACME,EAAmBC,EAAQV,EAAgBN,GAAcC,CAAU,CAAC,EAQ1E,MAAO,CACL,WARyDW,GAAOK,GAAQf,GACpEa,EAAiBb,CAAM,GACzBO,EAAc,GAAGP,EAAO,OAAO,EACxBU,EAAI,UAEND,EAAmBC,CAAG,EAAEK,CAAI,EAAEf,CAAM,EAI3C,cAAAO,EACA,eAAAH,EACA,WAAAL,CACF,CACF,ECnDA,IAAAiB,GAAgC,iBAqOhC,IAAMC,GAAeC,GAA8E,gBAAiBA,GAAkB,OAAOA,EAAe,aAAgB,SACtKC,GAAeC,GAA6CA,EAAO,QAAQC,GAAcJ,GAAYI,CAAU,EAAI,CAAC,CAACA,EAAW,YAAaA,EAAW,OAAO,CAAU,EAAI,OAAO,QAAQA,CAAU,CAAC,EACvMC,GAAiB,OAAO,IAAI,0BAA0B,EACtDC,GAAgBC,GAAe,CAAC,CAACA,GAAS,CAAC,CAACA,EAAMF,EAAc,EAChEG,GAAgB,IAAI,QACpBC,GAAmB,CAAwBC,EAAcC,EAAmDC,IAAoDC,EAAoBL,GAAeE,EAAO,IAAM,IAAI,MAAMA,EAAO,CACrO,IAAK,CAACI,EAAQC,EAAMC,IAAa,CAC/B,GAAID,IAASV,GAAgB,OAAOS,EACpC,IAAMG,EAAS,QAAQ,IAAIH,EAAQC,EAAMC,CAAQ,EACjD,GAAI,OAAOC,EAAW,IAAa,CACjC,IAAMC,EAASN,EAAkBG,CAAI,EACrC,GAAI,OAAOG,EAAW,IAAa,OAAOA,EAC1C,IAAMC,EAAUR,EAAWI,CAAI,EAC/B,GAAII,EAAS,CAEX,IAAMC,EAAgBD,EAAQ,OAAW,CACvC,KAAME,EAAO,CACf,CAAC,EACD,GAAI,OAAOD,EAAkB,IAC3B,MAAM,IAAI,MAA8CE,EAAwB,EAAE,CAAwV,EAE5a,OAAAV,EAAkBG,CAAI,EAAIK,EACnBA,CACT,CACF,CACA,OAAOH,CACT,CACF,CAAC,CAAC,EACIM,GAAYb,GAAe,CAC/B,GAAI,CAACJ,GAAaI,CAAK,EACrB,MAAM,IAAI,MAA8CY,EAAyB,EAAE,CAA0C,EAE/H,OAAOZ,EAAML,EAAc,CAC7B,EACMmB,GAAc,CAAC,EACfC,GAA4C,CAACf,EAAQc,KAAgBd,EACpE,SAASgB,MAAkEvB,EAAgE,CAChJ,IAAMQ,EAAa,OAAO,YAAqBT,GAAYC,CAAM,CAAC,EAC5DwB,EAAa,IAAM,OAAO,KAAKhB,CAAU,EAAE,UAAS,oBAAgBA,CAAU,EAAIc,GACpFN,EAAUQ,EAAW,EACzB,SAASC,EAAgBlB,EAAgCmB,EAAuB,CAC9E,OAAOV,EAAQT,EAAOmB,CAAM,CAC9B,CACAD,EAAgB,qBAAuB,IAAMA,EAC7C,IAAMhB,EAAkD,CAAC,EACnDkB,EAAS,CAACC,EAAqBC,EAAuB,CAAC,IAA8B,CACzF,GAAM,CACJ,YAAAC,EACA,QAASC,CACX,EAAIH,EACEI,EAAiBxB,EAAWsB,CAAW,EAC7C,MAAI,CAACD,EAAO,kBAAoBG,GAAkBA,IAAmBD,GAC/D,OAAO,QAAY,IAGhBN,IAELI,EAAO,kBAAoBG,IAAmBD,GAChD,OAAOtB,EAAkBqB,CAAW,EAEtCtB,EAAWsB,CAAW,EAAIC,EAC1Bf,EAAUQ,EAAW,EACdC,EACT,EACMQ,EAAW,OAAO,OAAO,SAA2EC,EAAkDC,EAA8D,CACxN,OAAO,SAAkB5B,KAAiB6B,EAAY,CACpD,OAAOF,EAAW5B,GAAiB6B,EAAcA,EAAY5B,EAAc,GAAG6B,CAAI,EAAI7B,EAAOC,EAAYC,CAAiB,EAAG,GAAG2B,CAAI,CACtI,CACF,EAAG,CACD,SAAAhB,EACF,CAAC,EACD,OAAO,OAAO,OAAOK,EAAiB,CACpC,OAAAE,EACA,SAAAM,CACF,CAAC,CACH,CC3SO,SAASI,EAAuBC,EAAc,CACnD,MAAO,iCAAiCA,CAAI,oDAAoDA,CAAI,iFACtG", "names": ["src_exports", "__export", "ReducerType", "SHOULD_AUTOBATCH", "TaskAbortError", "<PERSON><PERSON>", "addListener", "asyncThunkCreator", "autoBatchEnhancer", "buildCreateSlice", "clearAllListeners", "combineSlices", "configureStore", "createAction", "createActionCreatorInvariantMiddleware", "createAsyncThunk", "createDraftSafeSelector", "createDraftSafeSelectorCreator", "createDynamicMiddleware", "createEntityAdapter", "createImmutableStateInvariantMiddleware", "createListenerMiddleware", "createReducer", "createSerializableStateInvariantMiddleware", "createSlice", "findNonSerializableValue", "formatProdErrorMessage", "isActionCreator", "isAllOf", "isAnyOf", "isAsyncThunkAction", "isFSA", "isFulfilled", "isImmutableDefault", "isPending", "<PERSON><PERSON><PERSON>", "isRejected", "isRejectedWithValue", "miniSerializeError", "nanoid", "prepareAutoBatched", "removeListener", "unwrapResult", "__toCommonJS", "__reExport", "import_immer", "import_reselect", "import_immer", "import_reselect", "createDraftSafeSelectorCreator", "args", "createSelector", "createDraftSafeSelector", "selector", "wrappedSelector", "value", "rest", "import_redux", "import_redux", "composeWithDevTools", "devToolsEnhancer", "noop", "import_redux_thunk", "import_redux", "hasMatchFunction", "v", "createAction", "type", "prepareAction", "actionCreator", "args", "prepared", "formatProdErrorMessage", "action", "isActionCreator", "hasMatchFunction", "isFSA", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "getMessage", "type", "splitType", "actionName", "createActionCreatorInvariantMiddleware", "options", "next", "action", "import_immer", "<PERSON><PERSON>", "_<PERSON><PERSON>", "items", "arr", "freezeDraftable", "val", "createNextState", "getOrInsertComputed", "map", "key", "compute", "isImmutableDefault", "value", "createImmutableStateInvariantMiddleware", "options", "next", "action", "stringify", "getSerialize", "import_redux", "<PERSON><PERSON><PERSON>", "val", "type", "findNonSerializableValue", "value", "path", "isSerializable", "getEntries", "ignoredPaths", "cache", "foundNestedSerializable", "entries", "hasIgnoredPaths", "key", "nestedV<PERSON>ue", "nested<PERSON>ath", "ignored", "isNestedFrozen", "createSerializableStateInvariantMiddleware", "options", "next", "action", "isBoolean", "x", "buildGetDefaultMiddleware", "options", "thunk", "immutableCheck", "serializableCheck", "actionCreatorCheck", "middlewareArray", "<PERSON><PERSON>", "thunkMiddleware", "SHOULD_AUTOBATCH", "prepareAutoBatched", "payload", "createQueueWithTimer", "timeout", "notify", "autoBatchEnhancer", "options", "next", "args", "store", "notifying", "shouldNotifyAtEndOfTick", "notificationQueued", "listeners", "queue<PERSON>allback", "notifyListeners", "l", "listener", "wrappedListener", "unsubscribe", "action", "buildGetDefaultEnhancers", "middlewareEnhancer", "options", "autoBatch", "enhancerArray", "<PERSON><PERSON>", "autoBatchEnhancer", "configureStore", "options", "getDefaultMiddleware", "buildGetDefaultMiddleware", "reducer", "middleware", "devTools", "duplicateMiddlewareCheck", "preloadedState", "enhancers", "rootReducer", "formatProdErrorMessage", "finalMiddleware", "finalCompose", "composeWithDevTools", "middlewareEnhancer", "getDefaultEnhancers", "buildGetDefaultEnhancers", "storeEnhancers", "composedEnhancer", "import_immer", "executeReducerBuilderCallback", "builderCallback", "actionsMap", "actionMatchers", "defaultCaseReducer", "builder", "typeOrActionCreator", "reducer", "type", "formatProdErrorMessage", "matcher", "isStateFunction", "x", "createReducer", "initialState", "mapOrBuilderCallback", "actionsMap", "finalActionMatchers", "finalDefaultCaseReducer", "executeReducerBuilderCallback", "getInitialState", "freezeDraftable", "frozenInitialState", "reducer", "state", "action", "caseReducers", "matcher", "cr", "previousState", "caseReducer", "result", "createNextState", "draft", "matches", "matcher", "action", "hasMatchFunction", "isAnyOf", "matchers", "isAllOf", "hasExpectedRequestMetadata", "validStatus", "hasValidRequestId", "hasValidRequestStatus", "isAsyncThunkArray", "a", "isPending", "asyncThunks", "asyncThunk", "isRejected", "isRejectedWithValue", "hasFlag", "isFulfilled", "isAsyncThunkAction", "url<PERSON>l<PERSON><PERSON>", "nanoid", "size", "id", "i", "commonProperties", "RejectWithValue", "payload", "meta", "FulfillWithMeta", "miniSerializeError", "value", "simpleError", "property", "externalAbortMessage", "createAsyncThunk", "typePrefix", "payloadCreator", "options", "fulfilled", "createAction", "requestId", "arg", "pending", "rejected", "error", "actionCreator", "signal", "dispatch", "getState", "extra", "nanoid", "abortController", "abor<PERSON><PERSON><PERSON><PERSON>", "abortReason", "abort", "reason", "promise", "finalAction", "conditionResult", "isThenable", "abortedPromise", "_", "reject", "result", "err", "unwrapResult", "isAnyOf", "action", "asyncThunkSymbol", "asyncThunkCreator", "createAsyncThunk", "ReducerType", "getType", "slice", "action<PERSON>ey", "buildCreateSlice", "creators", "cAT", "options", "name", "reducerPath", "formatProdErrorMessage", "reducers", "buildReducerCreators", "reducerNames", "context", "contextMethods", "typeOrActionCreator", "reducer", "type", "matcher", "actionCreator", "reducerName", "reducerDefinition", "reducerDetails", "isAsyncThunkSliceReducerDefinition", "handleThunkCaseReducerDefinition", "handleNormalReducerDefinition", "buildReducer", "extraReducers", "actionMatchers", "defaultCaseReducer", "executeReducerBuilderCallback", "finalCaseReducers", "createReducer", "builder", "key", "sM", "m", "selectSelf", "state", "injectedSelectorCache", "injectedStateCache", "_reducer", "action", "getInitialState", "makeSelectorProps", "injected", "selectSlice", "sliceState", "getOrInsertComputed", "getSelectors", "selectState", "selectorCache", "map", "selector", "wrapSelector", "injectable", "pathOpt", "config", "newReducerPath", "wrapper", "rootState", "args", "createSlice", "asyncThunk", "payloadCreator", "caseReducer", "prepare", "createNotation", "maybeReducerWithPrepare", "prepareCallback", "isCaseReducerWithPrepareDefinition", "createAction", "fulfilled", "pending", "rejected", "settled", "thunk", "noop", "getInitialEntityState", "createInitialStateFactory", "stateAdapter", "getInitialState", "additionalState", "entities", "state", "createSelectorsFactory", "getSelectors", "selectState", "options", "createSelector", "createDraftSafeSelector", "selectIds", "state", "selectEntities", "selectAll", "ids", "entities", "id", "selectId", "_", "selectById", "selectTotal", "selectGlobalizedEntities", "import_immer", "isDraftTyped", "createSingleArgumentStateOperator", "mutator", "operator", "createStateOperator", "_", "state", "arg", "isPayloadActionArgument", "isFSA", "runMutator", "draft", "createNextState", "import_immer", "selectIdValue", "entity", "selectId", "ensureEntitiesArray", "entities", "get<PERSON>urrent", "value", "splitAddedUpdatedEntities", "newEntities", "state", "existingIdsArray", "existingIds", "added", "addedIds", "updated", "id", "createUnsortedStateAdapter", "selectId", "addOneMutably", "entity", "state", "key", "selectIdValue", "addManyMutably", "newEntities", "ensureEntitiesArray", "setOneMutably", "setManyMutably", "setAllMutably", "removeOneMutably", "removeManyMutably", "keys", "didMutate", "id", "removeAllMutably", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "update", "original", "updated", "new<PERSON>ey", "has<PERSON>ew<PERSON><PERSON>", "updateOneMutably", "updateManyMutably", "updates", "newKeys", "updatesPerEntity", "e", "upsertOneMutably", "upsertManyMutably", "added", "splitAddedUpdatedEntities", "createSingleArgumentStateOperator", "createStateOperator", "findInsertIndex", "sortedItems", "item", "comparisonFunction", "lowIndex", "highIndex", "middleIndex", "currentItem", "insert", "insertAtIndex", "createSortedStateAdapter", "selectId", "comparer", "removeOne", "remove<PERSON>any", "removeAll", "createUnsortedStateAdapter", "addOneMutably", "entity", "state", "addManyMutably", "newEntities", "existingIds", "ensureEntitiesArray", "existingKeys", "get<PERSON>urrent", "models", "model", "selectIdValue", "mergeFunction", "setOneMutably", "setManyMutably", "setAllMutably", "updateOneMutably", "update", "updateManyMutably", "updates", "appliedUpdates", "replacedIds", "newId", "oldIndex", "upsertOneMutably", "upsertManyMutably", "added", "updated", "existingIdsArray", "splitAddedUpdatedEntities", "areArraysEqual", "a", "b", "i", "addedItems", "currentEntities", "currentIds", "stateEntities", "ids", "sortedEntities", "id", "was<PERSON>revious<PERSON><PERSON><PERSON><PERSON>", "newSortedIds", "createStateOperator", "createEntityAdapter", "options", "selectId", "sortComparer", "instance", "stateAdapter", "createSortedStateAdapter", "createUnsortedStateAdapter", "stateFactory", "createInitialStateFactory", "selectorsFactory", "createSelectorsFactory", "import_redux", "task", "listener", "completed", "cancelled", "taskCancelled", "taskCompleted", "listenerCancelled", "listenerCompleted", "TaskAbortError", "code", "assertFunction", "func", "expected", "formatProdErrorMessage", "noop", "catchRejection", "promise", "onError", "addAbortSignalListener", "abortSignal", "callback", "abortControllerWithReason", "abortController", "reason", "signal", "validateActive", "signal", "reason", "TaskAbortError", "raceWithSignal", "promise", "cleanup", "noop", "resolve", "reject", "notifyRejection", "addAbortSignalListener", "runTask", "task", "cleanUp", "error", "createPause", "catchRejection", "output", "createDelay", "pause", "timeoutMs", "assign", "INTERNAL_NIL_TOKEN", "alm", "createFork", "parentAbortSignal", "parentBlockingPromises", "linkControllers", "controller", "addAbortSignalListener", "abortControllerWithReason", "taskExecutor", "opts", "assertFunction", "childAbortController", "result", "runTask", "validateActive", "createPause", "createDelay", "taskCompleted", "noop", "taskCancelled", "createTakePattern", "startListening", "signal", "take", "predicate", "timeout", "unsubscribe", "promises", "resolve", "reject", "stopListening", "action", "listenerApi", "output", "raceWithSignal", "catchRejection", "getListenerEntryPropsFrom", "options", "type", "actionCreator", "matcher", "effect", "createAction", "formatProdErrorMessage", "createListenerEntry", "nanoid", "findListenerEntry", "listenerMap", "entry", "cancelActiveListeners", "listenerCancelled", "createClearListenerMiddleware", "safelyNotifyError", "<PERSON><PERSON><PERSON><PERSON>", "errorToNotify", "errorInfo", "errorHandlerError", "addListener", "clearAllListeners", "removeListener", "defaultErrorHandler", "args", "createListenerMiddleware", "middlewareOptions", "extra", "onError", "insertEntry", "cancelOptions", "notifyL<PERSON>ener", "api", "getOriginalState", "internalTaskController", "autoJoinPromises", "_", "set", "listenerError", "TaskAbortError", "listenerCompleted", "clearListenerMiddleware", "next", "originalState", "currentState", "listenerEntries", "runListener", "predicateError", "import_redux", "createMiddlewareEntry", "middleware", "matchInstance", "instanceId", "action", "createDynamicMiddleware", "nanoid", "middlewareMap", "withMiddleware", "createAction", "middlewares", "addMiddleware", "getOrInsertComputed", "getFinalMiddleware", "api", "appliedMiddleware", "entry", "isWithMiddleware", "isAllOf", "next", "import_redux", "isSliceLike", "maybeSliceLike", "getReducers", "slices", "sliceOrMap", "ORIGINAL_STATE", "isStateProxy", "value", "stateProxyMap", "createStateProxy", "state", "reducerMap", "initialStateCache", "getOrInsertComputed", "target", "prop", "receiver", "result", "cached", "reducer", "reducerResult", "nanoid", "formatProdErrorMessage", "original", "emptyObject", "noopReducer", "combineSlices", "getReducer", "combinedReducer", "action", "inject", "slice", "config", "reducerPath", "reducerToInject", "currentReducer", "selector", "selectorFn", "selectState", "args", "formatProdErrorMessage", "code"]}