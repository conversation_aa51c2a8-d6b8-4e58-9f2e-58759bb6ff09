#!/usr/bin/env python
"""
Test script to verify Django models are working correctly
Creates sample data and tests model relationships
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'arada_dms.settings')
django.setup()

from accounts.models import User
from organizations.models import Organization, Department
from locations.models import Building, Shelf, Kent
from documents.models import DocumentType, Document
from requests.models import DocumentRequest, AuditLog

def test_models():
    print("🧪 Testing Django Models...")
    
    # Test 1: Create Organization
    print("\n1. Testing Organization Model...")
    org, created = Organization.objects.get_or_create(
        name="Gondar Arada Sub-City Revenue Office",
        defaults={
            'short_name': 'ARADA',
            'email': '<EMAIL>',
            'phone': '+************',
            'address_line1': 'Piassa Area',
            'city': 'Gondar',
            'state_province': 'Amhara',
            'country': 'Ethiopia'
        }
    )
    print(f"✅ Organization: {org} ({'Created' if created else 'Exists'})")
    
    # Test 2: Create Users with different roles
    print("\n2. Testing User Model with Roles...")
    users_data = [
        ('manager1', 'Manager', '<PERSON>', 'Doe', 'manager'),
        ('clerk1', 'Clerk', 'Jane', 'Smith', 'clerk'),
        ('auditor1', 'Auditor', 'Bob', 'Wilson', 'auditor'),
    ]
    
    for username, role_display, first_name, last_name, role in users_data:
        user, created = User.objects.get_or_create(
            username=username,
            defaults={
                'first_name': first_name,
                'last_name': last_name,
                'email': f'{username}@arada.gov.et',
                'role': role,
                'organization': org,
                'employee_id': f'EMP-{username.upper()}',
                'department': 'Revenue Collection'
            }
        )
        if created:
            user.set_password('password123')
            user.save()
        print(f"✅ User: {user.full_name} - {role_display} ({'Created' if created else 'Exists'})")
    
    # Test 3: Create Location Hierarchy
    print("\n3. Testing Location Hierarchy (Building → Shelf → Kent)...")
    
    # Create Building
    building, created = Building.objects.get_or_create(
        organization=org,
        code='B1',
        defaults={
            'name': 'Main Building',
            'description': 'Primary document storage building',
            'address': 'Ground floor, Revenue Office'
        }
    )
    print(f"✅ Building: {building} ({'Created' if created else 'Exists'})")
    
    # Create Shelf
    shelf, created = Shelf.objects.get_or_create(
        building=building,
        code='S1',
        defaults={
            'name': 'Shelf 1',
            'description': 'Tax documents shelf',
            'capacity': 50
        }
    )
    print(f"✅ Shelf: {shelf.full_code} ({'Created' if created else 'Exists'})")
    
    # Create Kent (Box)
    kent, created = Kent.objects.get_or_create(
        shelf=shelf,
        code='K1',
        defaults={
            'name': 'Kent 1',
            'description': 'Business license documents',
            'capacity': 100,
            'color': 'Blue',
            'material': 'Cardboard'
        }
    )
    print(f"✅ Kent: {kent.full_code} - {kent.location_path} ({'Created' if created else 'Exists'})")
    
    # Test 4: Create Document Types
    print("\n4. Testing Document Types...")
    doc_types_data = [
        ('TAX', 'Tax Record', 'Individual and business tax records'),
        ('BLI', 'Business License', 'Business registration and licensing documents'),
        ('PRP', 'Property Deed', 'Property ownership and transfer documents'),
    ]
    
    for code, name, description in doc_types_data:
        doc_type, created = DocumentType.objects.get_or_create(
            organization=org,
            code=code,
            defaults={
                'name': name,
                'description': description,
                'confidentiality_level': 'internal',
                'requires_expiry_date': code == 'BLI',
                'allowed_file_extensions': ['pdf', 'jpg', 'png'],
                'created_by': User.objects.filter(role='admin').first()
            }
        )
        print(f"✅ Document Type: {doc_type} ({'Created' if created else 'Exists'})")
    
    # Test 5: Create Sample Documents
    print("\n5. Testing Document Model...")
    tax_type = DocumentType.objects.get(code='TAX')
    
    document, created = Document.objects.get_or_create(
        title="Business Tax Record - ABC Trading",
        defaults={
            'description': 'Annual tax record for ABC Trading Company',
            'document_type': tax_type,
            'mode': Document.DocumentMode.PHYSICAL,
            'kent': kent,
            'reference_number': 'TAX-2024-001',
            'tags': ['tax', 'business', '2024'],
            'created_by': User.objects.filter(role='clerk').first()
        }
    )
    print(f"✅ Document: {document} ({'Created' if created else 'Exists'})")
    print(f"   Location: {document.location_display}")
    print(f"   Mode: {document.get_mode_display()}")
    
    # Test 6: Test Model Methods and Properties
    print("\n6. Testing Model Methods and Properties...")
    print(f"✅ Kent capacity check: {kent.get_document_count()}/{kent.capacity} documents")
    print(f"✅ Document can be requested: {document.can_be_requested()}")
    print(f"✅ User permissions - Can approve: {User.objects.get(username='manager1').can_approve_requests()}")
    print(f"✅ User permissions - Can manage docs: {User.objects.get(username='clerk1').can_manage_documents()}")
    print(f"✅ User permissions - Read only: {User.objects.get(username='auditor1').is_read_only()}")
    
    # Test 7: Create Document Request
    print("\n7. Testing Document Request Workflow...")
    from datetime import date, timedelta
    
    request, created = DocumentRequest.objects.get_or_create(
        requested_by=User.objects.get(username='clerk1'),
        defaults={
            'purpose': 'Annual audit review',
            'required_date': date.today() + timedelta(days=1),
            'due_date': date.today() + timedelta(days=7),
            'priority': DocumentRequest.Priority.NORMAL
        }
    )
    if created:
        request.documents.add(document)
    print(f"✅ Document Request: {request} ({'Created' if created else 'Exists'})")
    print(f"   Status: {request.get_status_display()}")
    print(f"   Days until due: {request.days_until_due}")
    
    # Test 8: Audit Logging
    print("\n8. Testing Audit Logging...")
    AuditLog.log_action(
        user=User.objects.get(username='clerk1'),
        action=AuditLog.Action.CREATE,
        obj=document,
        description="Created test document during system review",
        ip_address='127.0.0.1'
    )
    print(f"✅ Audit log created for document creation")
    
    print("\n🎉 All model tests completed successfully!")
    print(f"📊 Database Summary:")
    print(f"   - Organizations: {Organization.objects.count()}")
    print(f"   - Users: {User.objects.count()}")
    print(f"   - Buildings: {Building.objects.count()}")
    print(f"   - Shelves: {Shelf.objects.count()}")
    print(f"   - Kents: {Kent.objects.count()}")
    print(f"   - Document Types: {DocumentType.objects.count()}")
    print(f"   - Documents: {Document.objects.count()}")
    print(f"   - Requests: {DocumentRequest.objects.count()}")
    print(f"   - Audit Logs: {AuditLog.objects.count()}")

if __name__ == '__main__':
    test_models()
