#!/usr/bin/env python
"""
Simple script to create sample taxpayers
"""
import os
import sys
import django
import random
from datetime import date, timedelta

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'document_management.settings')
django.setup()

from taxpayers.models import (
    TaxPayerLevel, BusinessSector, BusinessSubSector, OrganizationBusinessType,
    IndividualTaxPayer, OrganizationTaxPayer
)
from locations.location_hierarchy_models import Country, Region, Zone, City, SubCity, Kebele

def create_sample_taxpayers():
    print("Creating sample taxpayers...")
    
    # Get existing data
    tax_levels = list(TaxPayerLevel.objects.all())
    sectors = list(BusinessSector.objects.all())
    sub_sectors = list(BusinessSubSector.objects.all())
    org_types = list(OrganizationBusinessType.objects.all())
    kebeles = list(Kebele.objects.all())
    
    if not all([tax_levels, sectors, sub_sectors, kebeles]):
        print("Missing required data. Please create basic data first.")
        return
    
    ethiopia = Country.objects.get(code='ET')
    addis_region = Region.objects.get(code='AA')
    addis_city = City.objects.get(code='AA01')
    kirkos_subcity = SubCity.objects.get(code='KRK')
    
    # Ethiopian names
    ethiopian_first_names_male = [
        'Abebe', 'Tesfaye', 'Girma', 'Haile', 'Daniel', 'Samuel', 'Yosef', 'Michael', 'Gebre', 'Tekle',
        'Asfaw', 'Birhanu', 'Dereje', 'Fikru', 'Getachew', 'Habtamu', 'Kidane', 'Lema', 'Mengistu', 'Nega'
    ]
    
    ethiopian_first_names_female = [
        'Almaz', 'Bezayit', 'Fatima', 'Hana', 'Kibret', 'Lydia', 'Martha', 'Nazi', 'Sara', 'Tadelech',
        'Askalech', 'Beletch', 'Desta', 'Emebet', 'Freweyni', 'Genet', 'Henok', 'Kidane', 'Lela', 'Meryam'
    ]
    
    ethiopian_last_names = [
        'Abebe', 'Tesfaye', 'Wolde', 'Gebre', 'Tekle', 'Mengistu', 'Haile', 'Daniel', 'Samuel', 'Yosef',
        'Michael', 'Asfaw', 'Birhanu', 'Dereje', 'Fikru', 'Getachew', 'Habtamu', 'Kidane', 'Lema', 'Nega'
    ]
    
    ethiopian_company_names = [
        'Ethio Trade PLC',
        'Addis Ababa Construction Company',
        'Selam Food Processing PLC',
        'Birhan Technology Service',
        'Ethiopia Coffee Export Company',
        'Abebe and Brothers Trading House',
        'Haile Agricultural Products Company',
        'Tesfa Transport Service',
        'Daniel Hotel and Tourism Company',
        'Samuel Textile Manufacturing PLC'
    ]
    
    # Create Individual Taxpayers
    individual_count = 0
    for i in range(100):
        try:
            tin = f"{random.randint(**********, **********)}"
            
            if IndividualTaxPayer.objects.filter(tin=tin).exists():
                continue
            
            gender = random.choice(['M', 'F'])
            first_names = ethiopian_first_names_male if gender == 'M' else ethiopian_first_names_female
            
            individual = IndividualTaxPayer.objects.create(
                tin=tin,
                first_name=random.choice(first_names),
                middle_name=random.choice(ethiopian_first_names_male + ethiopian_first_names_female) if random.choice([True, False]) else '',
                last_name=random.choice(ethiopian_last_names),
                nationality='ET',
                gender=gender,
                date_of_birth=date.today() - timedelta(days=random.randint(18*365, 70*365)),
                tax_payer_level=random.choice(tax_levels),
                business_sector=random.choice(sectors),
                business_sub_sector=random.choice(sub_sectors),
                business_registration_date=date.today() - timedelta(days=random.randint(30, 1825)),
                business_name=f"{random.choice(ethiopian_first_names_male)} Trading House" if random.choice([True, False]) else '',
                business_license_number=f"BL{random.randint(100000, 999999)}" if random.choice([True, False]) else '',
                phone=f"+251{random.choice(['9', '7'])}{random.randint(10000000, 99999999)}",
                phone_secondary=f"+251{random.choice(['9', '7'])}{random.randint(10000000, 99999999)}" if random.choice([True, False]) else '',
                email=f"user{i}@example.com" if random.choice([True, False]) else '',
                country=ethiopia,
                region=addis_region,
                city=addis_city,
                subcity=kirkos_subcity,
                kebele=random.choice(kebeles),
                house_number=str(random.randint(1, 9999)),
                street_address=f"House No {random.randint(1, 999)}" if random.choice([True, False]) else '',
                postal_code=str(random.randint(1000, 9999)) if random.choice([True, False]) else '',
                is_active=random.choice([True, True, True, False])  # 75% active
            )
            individual_count += 1
            
            if individual_count % 20 == 0:
                print(f"Created {individual_count} individual taxpayers...")
                
        except Exception as e:
            print(f"Error creating individual taxpayer: {e}")
            continue
    
    # Create Organization Taxpayers
    organization_count = 0
    for i in range(40):
        try:
            tin = f"{random.randint(**********, **********)}"
            
            if OrganizationTaxPayer.objects.filter(tin=tin).exists():
                continue
            
            org_type = random.choice(org_types)
            requires_vat = org_type.requires_vat_registration
            
            organization = OrganizationTaxPayer.objects.create(
                tin=tin,
                business_name=random.choice(ethiopian_company_names),
                trade_name=f"{random.choice(ethiopian_first_names_male)} Trading" if random.choice([True, False]) else '',
                organization_business_type=org_type,
                tax_payer_level=random.choice(tax_levels),
                business_sector=random.choice(sectors),
                business_sub_sector=random.choice(sub_sectors),
                business_registration_date=date.today() - timedelta(days=random.randint(365, 3650)),
                business_license_number=f"BL{random.randint(100000, 999999)}" if random.choice([True, False]) else '',
                capital_amount=random.randint(100000, 50000000) if random.choice([True, False]) else None,
                number_of_employees=random.randint(5, 1000) if random.choice([True, False]) else None,
                manager_first_name=random.choice(ethiopian_first_names_male),
                manager_middle_name=random.choice(ethiopian_first_names_male + ethiopian_first_names_female) if random.choice([True, False]) else '',
                manager_last_name=random.choice(ethiopian_last_names),
                manager_title=random.choice(['General Manager', 'CEO', 'Director', 'Owner', 'President']),
                vat_registration_date=date.today() - timedelta(days=random.randint(30, 1825)) if requires_vat else None,
                vat_number=f"ET{random.randint(**********, **********)}" if requires_vat else None,
                phone=f"+251{random.choice(['9', '7'])}{random.randint(10000000, 99999999)}",
                phone_secondary=f"+251{random.choice(['9', '7'])}{random.randint(10000000, 99999999)}" if random.choice([True, False]) else '',
                email=f"company{i}@example.com" if random.choice([True, False]) else '',
                country=ethiopia,
                region=addis_region,
                city=addis_city,
                subcity=kirkos_subcity,
                kebele=random.choice(kebeles),
                house_number=str(random.randint(1, 9999)),
                street_address=f"Building No {random.randint(1, 999)}" if random.choice([True, False]) else '',
                postal_code=str(random.randint(1000, 9999)) if random.choice([True, False]) else '',
                is_active=random.choice([True, True, True, False])  # 75% active
            )
            organization_count += 1
            
            if organization_count % 10 == 0:
                print(f"Created {organization_count} organization taxpayers...")
                
        except Exception as e:
            print(f"Error creating organization taxpayer: {e}")
            continue
    
    print(f"\nSample data creation completed!")
    print(f"Summary:")
    print(f"   - Individual Taxpayers: {IndividualTaxPayer.objects.count()}")
    print(f"   - Organization Taxpayers: {OrganizationTaxPayer.objects.count()}")
    print(f"   - Total Taxpayers: {IndividualTaxPayer.objects.count() + OrganizationTaxPayer.objects.count()}")
    print(f"   - Kebeles: {Kebele.objects.count()}")

if __name__ == '__main__':
    create_sample_taxpayers()
