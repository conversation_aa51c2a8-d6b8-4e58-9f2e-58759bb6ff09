import apiClient from './api';

export interface File {
  id: string;
  kent: number;
  kent_location: string;
  kent_code: string;
  building_name: string;
  shelf_name: string;
  organization_name: string;
  name: string;
  file_number: string;
  full_code: string;
  location_path: string;
  file_type: number;
  file_type_name: string;
  file_type_code: string;
  file_type_color: string;
  file_type_icon: string;
  description?: string;
  status: string;
  business_name?: string;
  tin_number?: string;
  vat_number?: string;
  owner_name?: string;
  contact_phone?: string;
  contact_email?: string;
  address?: string;
  tags: string[];
  created_date?: string;
  last_activity_date?: string;
  document_count: number;
  document_types: string[];
  barcode_image?: string;
  qr_code_image?: string;
  is_active: boolean;
  created_by: number;
  created_by_name: string;
  // Taxpayer linking fields
  linked_individual_taxpayer?: number;
  linked_organization_taxpayer?: number;
  linked_taxpayer?: {
    id: string;
    type: 'individual' | 'organization';
    name: string;
    tin: string;
    phone?: string;
    email?: string;
  };
  created_at: string;
  updated_at: string;
}

export interface FileCreate {
  kent: number | string;
  name: string;
  file_number: string;
  file_type: number | string;
  description?: string;
  business_name?: string;
  tin_number?: string;
  vat_number?: string;
  owner_name?: string;
  contact_phone?: string;
  contact_email?: string;
  address?: string;
  tags?: string[];
  created_date?: string;
  // Taxpayer linking fields
  taxpayer_id?: string;
  taxpayer_type?: 'individual' | 'organization';
}

export interface FileUpdate extends Partial<FileCreate> {
  status?: string;
  is_active?: boolean;
}

export interface FileWithDocuments extends File {
  documents: any[]; // Will be populated with document data
}

export interface FileListResponse {
  count: number;
  next?: string;
  previous?: string;
  results: File[];
}

export interface FileStats {
  total_files: number;
  active_files: number;
  archived_files: number;
  total_documents: number;
  files_by_type: Record<string, number>;
  recent_activity: number;
}

class FileService {
  private baseUrl = '/locations/files';

  // File CRUD operations
  async getFiles(params?: {
    page?: number;
    page_size?: number;
    search?: string;
    kent?: number;
    building?: number;
    shelf?: number;
    organization?: number;
    file_type?: string;
    status?: string;
    is_active?: boolean;
  }): Promise<FileListResponse> {
    const response = await apiClient.get(`${this.baseUrl}/`, { params });
    return response.data;
  }

  async getFile(id: string): Promise<File> {
    const response = await apiClient.get(`${this.baseUrl}/${id}/`);
    return response.data;
  }

  async getFileWithDocuments(id: string): Promise<FileWithDocuments> {
    const response = await apiClient.get(`${this.baseUrl}/${id}/documents/`);
    return response.data;
  }

  async createFile(data: FileCreate): Promise<File> {
    const response = await apiClient.post(`${this.baseUrl}/`, data);
    return response.data;
  }

  async updateFile(id: string, data: FileUpdate): Promise<File> {
    const response = await apiClient.patch(`${this.baseUrl}/${id}/`, data);
    return response.data;
  }

  async deleteFile(id: string): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/${id}/`);
  }

  // Kent-specific file operations
  async getKentFiles(kentId: number): Promise<File[]> {
    const response = await apiClient.get(`/locations/kents/${kentId}/files/`);
    return response.data.results || response.data;
  }

  // File statistics
  async getFileStats(organizationId?: number): Promise<FileStats> {
    const params = organizationId ? { organization: organizationId } : {};
    const response = await apiClient.get(`${this.baseUrl}/stats/`, { params });
    return response.data;
  }

  // Search files
  async searchFiles(query: string, filters?: {
    kent?: number;
    building?: number;
    shelf?: number;
    organization?: number;
    file_type?: string;
    status?: string;
  }): Promise<File[]> {
    const params = { q: query, ...filters };
    const response = await apiClient.get(`${this.baseUrl}/search/`, { params });
    return response.data.results || response.data;
  }

  // FileType CRUD operations
  async getFileTypes(params?: {
    page?: number;
    page_size?: number;
    search?: string;
    is_active?: boolean;
  }): Promise<{ results: any[]; count: number }> {
    const response = await apiClient.get('/locations/file-types/', { params });
    return response.data;
  }

  async getFileType(id: number): Promise<any> {
    const response = await apiClient.get(`/locations/file-types/${id}/`);
    return response.data;
  }

  async createFileType(data: any): Promise<any> {
    const response = await apiClient.post('/locations/file-types/', data);
    return response.data;
  }

  async updateFileType(id: number, data: any): Promise<any> {
    const response = await apiClient.patch(`/locations/file-types/${id}/`, data);
    return response.data;
  }

  async deleteFileType(id: number): Promise<void> {
    await apiClient.delete(`/locations/file-types/${id}/`);
  }

  // Utility methods
  getFileTypeOptions() {
    return [
      { value: 'business', label: 'Business File' },
      { value: 'individual', label: 'Individual File' },
      { value: 'property', label: 'Property File' },
      { value: 'legal', label: 'Legal File' },
      { value: 'financial', label: 'Financial File' },
      { value: 'other', label: 'Other' },
    ];
  }

  getStatusOptions() {
    return [
      { value: 'active', label: 'Active' },
      { value: 'archived', label: 'Archived' },
      { value: 'closed', label: 'Closed' },
      { value: 'transferred', label: 'Transferred' },
    ];
  }

  validateFileNumber(fileNumber: string): { isValid: boolean; error?: string } {
    if (!fileNumber) return { isValid: false, error: 'File number is required' };
    if (fileNumber.length < 1 || fileNumber.length > 50) {
      return { isValid: false, error: 'File number must be 1-50 characters' };
    }
    return { isValid: true };
  }

  formatFileType(fileType: string): string {
    const option = this.getFileTypeOptions().find(opt => opt.value === fileType);
    return option ? option.label : fileType;
  }

  formatStatus(status: string): string {
    const option = this.getStatusOptions().find(opt => opt.value === status);
    return option ? option.label : status;
  }

  getStatusColor(status: string): 'success' | 'warning' | 'error' | 'default' {
    switch (status) {
      case 'active':
        return 'success';
      case 'archived':
        return 'warning';
      case 'closed':
      case 'transferred':
        return 'error';
      default:
        return 'default';
    }
  }

  parseLocationPath(path: string): { building: string; shelf: string; kent: string; file: string } {
    const parts = path.split(' → ');
    return {
      building: parts[0] || '',
      shelf: parts[1] || '',
      kent: parts[2] || '',
      file: parts[3] || '',
    };
  }

  generateQRCodeData(file: File): string {
    return JSON.stringify({
      type: 'file',
      id: file.id,
      code: file.full_code,
      path: file.location_path,
      name: file.name,
      business: file.business_name,
    });
  }

  formatTags(tags: string[]): string {
    return tags.join(', ');
  }

  parseTags(tagsString: string): string[] {
    return tagsString
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);
  }

  validateBusinessInfo(data: Partial<FileCreate>): { isValid: boolean; errors: Record<string, string> } {
    const errors: Record<string, string> = {};

    if (data.file_type === 'business') {
      if (!data.business_name?.trim()) {
        errors.business_name = 'Business name is required for business files';
      }
    }

    if (data.tin_number && !/^\d{10}$/.test(data.tin_number)) {
      errors.tin_number = 'TIN number must be 10 digits';
    }

    if (data.vat_number && !/^\d{10}$/.test(data.vat_number)) {
      errors.vat_number = 'VAT number must be 10 digits';
    }

    if (data.contact_email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.contact_email)) {
      errors.contact_email = 'Invalid email format';
    }

    if (data.contact_phone && !/^[\+]?[\d\s\-\(\)]{7,15}$/.test(data.contact_phone)) {
      errors.contact_phone = 'Invalid phone number format';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
    };
  }

  getFileIcon(fileType: string): string {
    switch (fileType) {
      case 'business':
        return '🏢';
      case 'individual':
        return '👤';
      case 'property':
        return '🏠';
      case 'legal':
        return '⚖️';
      case 'financial':
        return '💰';
      default:
        return '📁';
    }
  }

  calculateFileAge(createdAt: string): string {
    const created = new Date(createdAt);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - created.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return '1 day ago';
    if (diffDays < 30) return `${diffDays} days ago`;
    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
    return `${Math.floor(diffDays / 365)} years ago`;
  }
}

export default new FileService();

// Explicit type re-exports to ensure compatibility
export type {
  File,
  FileCreate,
  FileUpdate,
  FileWithDocuments,
  FileListResponse,
  FileStats
};
