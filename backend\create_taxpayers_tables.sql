-- SQL script to create taxpayers tables manually
-- Run this in PostgreSQL to create the required tables

-- Create taxpayers_businesssector table
CREATE TABLE IF NOT EXISTS taxpayers_businesssector (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(10) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by_id INTEGER REFERENCES auth_user(id) ON DELETE SET NULL
);

-- Create taxpayers_taxpayerlevel table
CREATE TABLE IF NOT EXISTS taxpayers_taxpayerlevel (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    code VARCHAR(5) UNIQUE NOT NULL,
    description TEXT,
    minimum_annual_turnover DECIMAL(15,2),
    maximum_annual_turnover DECIMAL(15,2),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create taxpayers_organizationbusinesstype table
CREATE TABLE IF NOT EXISTS taxpayers_organizationbusinesstype (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(10) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    requires_vat_registration BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create taxpayers_businesssubsector table
CREATE TABLE IF NOT EXISTS taxpayers_businesssubsector (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(10) NOT NULL,
    name VARCHAR(150) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    business_sector_id UUID NOT NULL REFERENCES taxpayers_businesssector(id) ON DELETE CASCADE,
    created_by_id INTEGER REFERENCES auth_user(id) ON DELETE SET NULL,
    UNIQUE(business_sector_id, code)
);

-- Create taxpayers_individualtaxpayer table
CREATE TABLE IF NOT EXISTS taxpayers_individualtaxpayer (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tin VARCHAR(10) UNIQUE NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    middle_name VARCHAR(50),
    last_name VARCHAR(50) NOT NULL,
    nationality VARCHAR(10) DEFAULT 'ET',
    gender VARCHAR(1) NOT NULL,
    date_of_birth DATE NOT NULL,
    profile_picture VARCHAR(100),
    business_registration_date DATE NOT NULL,
    business_name VARCHAR(200),
    business_license_number VARCHAR(50),
    phone VARCHAR(20) NOT NULL,
    secondary_phone VARCHAR(20),
    email VARCHAR(254),
    house_number VARCHAR(20),
    street_address VARCHAR(200),
    is_active BOOLEAN DEFAULT TRUE,
    registration_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    business_sector_id UUID NOT NULL REFERENCES taxpayers_businesssector(id) ON DELETE PROTECT,
    business_sub_sector_id UUID REFERENCES taxpayers_businesssubsector(id) ON DELETE PROTECT,
    created_by_id INTEGER REFERENCES auth_user(id) ON DELETE SET NULL,
    file_id UUID REFERENCES locations_file(id) ON DELETE SET NULL,
    kebele_id UUID REFERENCES locations_kebele(id) ON DELETE SET NULL,
    subcity_id UUID REFERENCES locations_subcity(id) ON DELETE SET NULL,
    tax_payer_level_id UUID NOT NULL REFERENCES taxpayers_taxpayerlevel(id) ON DELETE PROTECT
);

-- Create taxpayers_organizationtaxpayer table
CREATE TABLE IF NOT EXISTS taxpayers_organizationtaxpayer (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tin VARCHAR(10) UNIQUE NOT NULL,
    business_name VARCHAR(200) NOT NULL,
    trade_name VARCHAR(200),
    business_registration_date DATE NOT NULL,
    business_license_number VARCHAR(50),
    capital_amount DECIMAL(15,2),
    number_of_employees INTEGER,
    manager_first_name VARCHAR(50) NOT NULL,
    manager_middle_name VARCHAR(50),
    manager_last_name VARCHAR(50) NOT NULL,
    manager_title VARCHAR(100),
    vat_registration_date DATE,
    vat_number VARCHAR(12),
    phone VARCHAR(20) NOT NULL,
    secondary_phone VARCHAR(20),
    email VARCHAR(254),
    house_number VARCHAR(20),
    street_address VARCHAR(200),
    is_active BOOLEAN DEFAULT TRUE,
    registration_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    business_sector_id UUID NOT NULL REFERENCES taxpayers_businesssector(id) ON DELETE PROTECT,
    business_sub_sector_id UUID REFERENCES taxpayers_businesssubsector(id) ON DELETE PROTECT,
    created_by_id INTEGER REFERENCES auth_user(id) ON DELETE SET NULL,
    file_id UUID REFERENCES locations_file(id) ON DELETE SET NULL,
    kebele_id UUID REFERENCES locations_kebele(id) ON DELETE SET NULL,
    organization_business_type_id UUID NOT NULL REFERENCES taxpayers_organizationbusinesstype(id) ON DELETE PROTECT,
    subcity_id UUID REFERENCES locations_subcity(id) ON DELETE SET NULL,
    tax_payer_level_id UUID NOT NULL REFERENCES taxpayers_taxpayerlevel(id) ON DELETE PROTECT
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS taxpayers_businesssector_code_idx ON taxpayers_businesssector(code);
CREATE INDEX IF NOT EXISTS taxpayers_businesssector_name_idx ON taxpayers_businesssector(name);
CREATE INDEX IF NOT EXISTS taxpayers_businesssubsector_sector_idx ON taxpayers_businesssubsector(business_sector_id);
CREATE INDEX IF NOT EXISTS taxpayers_individualtaxpayer_tin_idx ON taxpayers_individualtaxpayer(tin);
CREATE INDEX IF NOT EXISTS taxpayers_individualtaxpayer_sector_idx ON taxpayers_individualtaxpayer(business_sector_id);
CREATE INDEX IF NOT EXISTS taxpayers_organizationtaxpayer_tin_idx ON taxpayers_organizationtaxpayer(tin);
CREATE INDEX IF NOT EXISTS taxpayers_organizationtaxpayer_sector_idx ON taxpayers_organizationtaxpayer(business_sector_id);

-- Record migration as applied
INSERT INTO django_migrations (app, name, applied) 
VALUES ('taxpayers', '0001_initial', NOW())
ON CONFLICT (app, name) DO NOTHING;
