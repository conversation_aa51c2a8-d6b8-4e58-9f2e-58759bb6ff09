import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
} from '@mui/material';
import {
  Shelves,
  Edit,
  Delete,
  Home,
  LocationOn,
  Info,
  Storage,
  Business,
  Inventory,
  GridView,
  QrCode,
} from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import locationService from '../../services/locationService';
import type { Shelf } from '../../services/locationService';
import DetailPageTemplate from '../../components/common/DetailPageTemplate';
import ConfirmationDialog from '../../components/common/ConfirmationDialog';

const ShelfDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showNotification } = useNotification();
  
  const [shelf, setShelf] = useState<Shelf | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    if (id) {
      loadShelf();
    }
  }, [id]);

  const loadShelf = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await locationService.getShelf(Number(id));
      setShelf(data);
    } catch (error) {
      console.error('Error loading shelf:', error);
      setError('Failed to load shelf');
      showNotification('Failed to load shelf', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    // Navigate back to shelves page with edit state
    navigate('/locations/shelves', {
      state: {
        editShelf: shelf,
        showForm: true
      }
    });
  };

  const handleDelete = () => {
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!shelf) return;
    
    try {
      setDeleting(true);
      await locationService.deleteShelf(shelf.id);
      showNotification('Shelf deleted successfully', 'success');
      navigate('/locations/shelves');
    } catch (error) {
      console.error('Error deleting shelf:', error);
      showNotification('Failed to delete shelf', 'error');
    } finally {
      setDeleting(false);
      setDeleteDialogOpen(false);
    }
  };

  const handleBack = () => {
    navigate('/locations/shelves');
  };

  if (!shelf && !loading && !error) {
    setError('Shelf not found');
  }

  const breadcrumbs = [
    { label: 'Dashboard', path: '/dashboard', icon: <Home fontSize="small" /> },
    { label: 'Locations', path: '/locations', icon: <LocationOn fontSize="small" /> },
    { label: 'Shelves', path: '/locations/shelves', icon: <Shelves fontSize="small" /> },
    { label: shelf?.name || 'Shelf', path: undefined, icon: <Storage fontSize="small" /> },
  ];

  const actions = [
    {
      label: 'Edit',
      icon: <Edit />,
      onClick: handleEdit,
      color: 'primary' as const,
    },
    {
      label: 'Delete',
      icon: <Delete />,
      onClick: handleDelete,
      color: 'error' as const,
    },
  ];

  const chips = shelf ? [
    {
      label: shelf.is_active ? 'Active' : 'Inactive',
      color: shelf.is_active ? 'success' as const : 'error' as const,
    },
    ...(shelf.code ? [{
      label: `Code: ${shelf.code}`,
      color: 'info' as const,
    }] : []),
    ...(shelf.building_name ? [{
      label: shelf.building_name,
      color: 'primary' as const,
    }] : []),
  ] : [];

  const sections = shelf ? [
    {
      title: 'Basic Information',
      icon: <Info />,
      content: (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'primary.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'primary.200'
          }}>
            <Storage sx={{ color: 'primary.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="primary.main" sx={{ fontWeight: 600 }}>
                Shelf Name
              </Typography>
              <Typography variant="body2" color="primary.dark">
                {shelf.name}
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'info.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'info.200'
          }}>
            <QrCode sx={{ color: 'info.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="info.main" sx={{ fontWeight: 600 }}>
                Full Code
              </Typography>
              <Typography variant="body2" color="info.dark">
                {shelf.full_code}
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'secondary.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'secondary.200'
          }}>
            <Business sx={{ color: 'secondary.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="secondary.main" sx={{ fontWeight: 600 }}>
                Building
              </Typography>
              <Typography variant="body2" color="secondary.dark">
                {shelf.building_name}
              </Typography>
            </Box>
          </Box>
        </Box>
      ),
    },
    {
      title: 'Capacity & Layout',
      icon: <GridView />,
      content: (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'success.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'success.200'
          }}>
            <GridView sx={{ color: 'success.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="success.main" sx={{ fontWeight: 600 }}>
                Dimensions
              </Typography>
              <Typography variant="body2" color="success.dark">
                {shelf.rows} rows × {shelf.columns} columns
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'warning.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'warning.200'
          }}>
            <Storage sx={{ color: 'warning.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="warning.main" sx={{ fontWeight: 600 }}>
                Total Capacity
              </Typography>
              <Typography variant="body2" color="warning.dark">
                {shelf.capacity || (shelf.rows * shelf.columns)} positions
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'info.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'info.200'
          }}>
            <Inventory sx={{ color: 'info.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="info.main" sx={{ fontWeight: 600 }}>
                Utilization
              </Typography>
              <Typography variant="body2" color="info.dark">
                {shelf.utilization?.toFixed(1) || '0.0'}% utilized
              </Typography>
            </Box>
          </Box>
        </Box>
      ),
    },
    {
      title: 'Storage Statistics',
      icon: <Inventory />,
      content: (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'primary.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'primary.200'
          }}>
            <Storage sx={{ color: 'primary.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="primary.main" sx={{ fontWeight: 600 }}>
                Boxes
              </Typography>
              <Typography variant="body2" color="primary.dark">
                {shelf.box_count || 0} boxes
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'success.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'success.200'
          }}>
            <Inventory sx={{ color: 'success.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="success.main" sx={{ fontWeight: 600 }}>
                Kents
              </Typography>
              <Typography variant="body2" color="success.dark">
                {shelf.kent_count || 0} kents
              </Typography>
            </Box>
          </Box>
        </Box>
      ),
    },
  ] : [];

  return (
    <>
      <DetailPageTemplate
        loading={loading}
        error={error}
        breadcrumbs={breadcrumbs}
        title={shelf?.name || 'Shelf'}
        subtitle={shelf?.full_code ? `Code: ${shelf.full_code}` : undefined}
        avatar={{
          fallbackIcon: <Storage sx={{ fontSize: 40 }} />,
          alt: shelf?.name || 'Shelf',
        }}
        chips={chips}
        actions={actions}
        sections={sections}
        onBack={handleBack}
      />

      <ConfirmationDialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleConfirmDelete}
        title="Delete Shelf"
        itemName={shelf?.name}
        itemType="Shelf"
        message={`Are you sure you want to delete "${shelf?.name}"? This will also delete all boxes and kents within this shelf.`}
        confirmText="Delete Shelf"
        severity="error"
        loading={deleting}
      />
    </>
  );
};

export default ShelfDetailPage;
