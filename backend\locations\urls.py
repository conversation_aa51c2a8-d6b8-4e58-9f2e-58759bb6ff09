from django.urls import path
from . import views
from . import location_hierarchy_views

app_name = 'locations'

urlpatterns = [
    # Building endpoints
    path('buildings/', views.BuildingListCreateView.as_view(), name='building_list_create'),
    path('buildings/<int:pk>/', views.BuildingDetailView.as_view(), name='building_detail'),
    
    # Shelf endpoints
    path('shelves/', views.ShelfListCreateView.as_view(), name='shelf_list_create'),
    path('shelves/<int:pk>/', views.ShelfDetailView.as_view(), name='shelf_detail'),
    path('buildings/<int:building_pk>/shelves/', views.BuildingShelfListView.as_view(), name='building_shelves'),

    # Box endpoints
    path('boxes/', views.BoxListCreateView.as_view(), name='box_list_create'),
    path('boxes/<int:pk>/', views.BoxDetailView.as_view(), name='box_detail'),
    path('shelves/<int:shelf_pk>/boxes/', views.ShelfBoxListView.as_view(), name='shelf_boxes'),

    # Kent endpoints
    path('kents/', views.KentListCreateView.as_view(), name='kent_list_create'),
    path('kents/<int:pk>/', views.KentDetailView.as_view(), name='kent_detail'),
    path('boxes/<int:box_pk>/kents/', views.BoxKentListView.as_view(), name='box_kents'),
    path('shelves/<int:shelf_pk>/kents/', views.ShelfKentListView.as_view(), name='shelf_kents'),
    path('shelves/<int:shelf_pk>/grid/', views.ShelfGridView.as_view(), name='shelf_grid'),

    # FileType endpoints
    path('file-types/', views.FileTypeListCreateView.as_view(), name='filetype_list_create'),
    path('file-types/<int:pk>/', views.FileTypeDetailView.as_view(), name='filetype_detail'),

    # File endpoints
    path('files/', views.FileListCreateView.as_view(), name='file_list_create'),
    path('files/<uuid:pk>/', views.FileDetailView.as_view(), name='file_detail'),
    path('files/<uuid:pk>/documents/', views.FileWithDocumentsView.as_view(), name='file_documents'),
    path('kents/<int:kent_pk>/files/', views.KentFileListView.as_view(), name='kent_files'),

    # Hierarchy and search
    path('hierarchy/', views.LocationHierarchyView.as_view(), name='location_hierarchy'),
    path('search/', views.LocationSearchView.as_view(), name='location_search'),
    path('summary/', views.LocationSummaryView.as_view(), name='location_summary'),

    # Location Hierarchy Management
    # Countries
    path('countries/', location_hierarchy_views.CountryListCreateView.as_view(), name='country_list_create'),
    path('countries/<int:pk>/', location_hierarchy_views.CountryDetailView.as_view(), name='country_detail'),
    path('countries/select/', location_hierarchy_views.countries_select, name='countries_select'),

    # Regions
    path('regions/', location_hierarchy_views.RegionListCreateView.as_view(), name='region_list_create'),
    path('regions/<int:pk>/', location_hierarchy_views.RegionDetailView.as_view(), name='region_detail'),
    path('regions/select/', location_hierarchy_views.regions_select, name='regions_select'),

    # Zones
    path('zones/', location_hierarchy_views.ZoneListCreateView.as_view(), name='zone_list_create'),
    path('zones/<int:pk>/', location_hierarchy_views.ZoneDetailView.as_view(), name='zone_detail'),
    path('zones/select/', location_hierarchy_views.zones_select, name='zones_select'),

    # Cities
    path('cities/', location_hierarchy_views.CityListCreateView.as_view(), name='city_list_create'),
    path('cities/<int:pk>/', location_hierarchy_views.CityDetailView.as_view(), name='city_detail'),
    path('cities/select/', location_hierarchy_views.cities_select, name='cities_select'),

    # SubCities/Woredas
    path('subcities/', location_hierarchy_views.SubCityListCreateView.as_view(), name='subcity_list_create'),
    path('subcities/<int:pk>/', location_hierarchy_views.SubCityDetailView.as_view(), name='subcity_detail'),
    path('subcities/select/', location_hierarchy_views.subcities_select, name='subcities_select'),

    # Kebeles
    path('kebeles/', location_hierarchy_views.KebeleListCreateView.as_view(), name='kebele_list_create'),
    path('kebeles/<int:pk>/', location_hierarchy_views.KebeleDetailView.as_view(), name='kebele_detail'),
    path('kebeles/select/', location_hierarchy_views.kebeles_select, name='kebeles_select'),

    # Special Locations
    path('special-locations/', location_hierarchy_views.SpecialLocationListCreateView.as_view(), name='special_location_list_create'),
    path('special-locations/<int:pk>/', location_hierarchy_views.SpecialLocationDetailView.as_view(), name='special_location_detail'),
    path('special-locations/select/', location_hierarchy_views.special_location_select_list, name='special_locations_select'),
]
