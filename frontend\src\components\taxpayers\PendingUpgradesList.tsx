import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Chip,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  Avatar,
  Divider,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  TrendingUp,
  CheckCircle,
  Cancel,
  Person,
  Business,
  AttachMoney,
  CalendarToday,
  Close,
  Refresh,
} from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import taxpayerService from '../../services/taxpayerService';

interface IncomeAnalysis {
  id: string;
  analysis_year: number;
  taxpayer_type: 'individual' | 'organization';
  taxpayer_name: string;
  total_annual_income: number;
  average_daily_income: number;
  current_level_name: string;
  recommended_level_name: string;
  requires_level_upgrade: boolean;
  status: string;
  created_at: string;
}

interface PendingUpgradesListProps {
  onUpgradeProcessed: () => void;
}

const PendingUpgradesList: React.FC<PendingUpgradesListProps> = ({
  onUpgradeProcessed,
}) => {
  const { showNotification } = useNotification();
  const [analyses, setAnalyses] = useState<IncomeAnalysis[]>([]);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState<string | null>(null);
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false);
  const [selectedAnalysis, setSelectedAnalysis] = useState<IncomeAnalysis | null>(null);
  const [rejectReason, setRejectReason] = useState('');

  const loadPendingUpgrades = async () => {
    try {
      setLoading(true);
      const data = await taxpayerService.getIncomeAnalyses({
        status: 'pending',
        ordering: '-created_at',
      });
      
      // Filter only those that require upgrade
      const pendingUpgrades = data.results.filter((analysis: IncomeAnalysis) => 
        analysis.requires_level_upgrade
      );
      
      setAnalyses(pendingUpgrades);
    } catch (error: any) {
      console.error('Failed to load pending upgrades:', error);
      showNotification('Failed to load pending upgrades', 'error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadPendingUpgrades();
  }, []);

  const handleApprove = async (analysisId: string) => {
    setProcessing(analysisId);
    try {
      await taxpayerService.approveLevelUpgrade(analysisId);
      showNotification('Level upgrade approved successfully', 'success');
      loadPendingUpgrades();
      onUpgradeProcessed();
    } catch (error: any) {
      console.error('Failed to approve upgrade:', error);
      const errorMessage = error.response?.data?.error || 'Failed to approve upgrade';
      showNotification(errorMessage, 'error');
    } finally {
      setProcessing(null);
    }
  };

  const handleRejectClick = (analysis: IncomeAnalysis) => {
    setSelectedAnalysis(analysis);
    setRejectDialogOpen(true);
  };

  const handleRejectConfirm = async () => {
    if (!selectedAnalysis) return;

    setProcessing(selectedAnalysis.id);
    try {
      await taxpayerService.rejectLevelUpgrade(selectedAnalysis.id, rejectReason);
      showNotification('Level upgrade rejected', 'success');
      setRejectDialogOpen(false);
      setRejectReason('');
      setSelectedAnalysis(null);
      loadPendingUpgrades();
      onUpgradeProcessed();
    } catch (error: any) {
      console.error('Failed to reject upgrade:', error);
      const errorMessage = error.response?.data?.error || 'Failed to reject upgrade';
      showNotification(errorMessage, 'error');
    } finally {
      setProcessing(null);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
        <CircularProgress />
      </Box>
    );
  }

  if (analyses.length === 0) {
    return (
      <Alert severity="info">
        <Typography variant="body2">
          No pending level upgrades at this time. All analyses have been reviewed or no upgrades are recommended.
        </Typography>
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h6" component="h3" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <TrendingUp color="primary" />
          Pending Level Upgrades ({analyses.length})
        </Typography>
        <Tooltip title="Refresh List">
          <IconButton onClick={loadPendingUpgrades}>
            <Refresh />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Upgrades List */}
      <Grid container spacing={2}>
        {analyses.map((analysis) => (
          <Grid item xs={12} key={analysis.id}>
            <Card variant="outlined">
              <CardContent>
                <Box display="flex" alignItems="flex-start" justifyContent="space-between">
                  {/* Taxpayer Info */}
                  <Box display="flex" alignItems="center" gap={2} flex={1}>
                    <Avatar sx={{ bgcolor: 'primary.main' }}>
                      {analysis.taxpayer_type === 'individual' ? <Person /> : <Business />}
                    </Avatar>
                    <Box>
                      <Typography variant="h6" component="div">
                        {analysis.taxpayer_name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {analysis.taxpayer_type === 'individual' ? 'Individual Taxpayer' : 'Organization'}
                      </Typography>
                      <Box display="flex" alignItems="center" gap={1} mt={1}>
                        <CalendarToday fontSize="small" color="action" />
                        <Typography variant="caption">
                          Analysis Year: {analysis.analysis_year}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>

                  {/* Income Info */}
                  <Box textAlign="center" mx={2}>
                    <Typography variant="body2" color="text.secondary">
                      Daily Income
                    </Typography>
                    <Typography variant="h6" color="success.main" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      <AttachMoney fontSize="small" />
                      {formatCurrency(analysis.average_daily_income)}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Annual: {formatCurrency(analysis.total_annual_income)}
                    </Typography>
                  </Box>

                  {/* Level Change */}
                  <Box textAlign="center" mx={2}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Level Change
                    </Typography>
                    <Box display="flex" alignItems="center" gap={1}>
                      <Chip 
                        label={analysis.current_level_name} 
                        size="small" 
                        color="default"
                      />
                      <TrendingUp color="success" fontSize="small" />
                      <Chip 
                        label={analysis.recommended_level_name} 
                        size="small" 
                        color="success"
                      />
                    </Box>
                  </Box>

                  {/* Actions */}
                  <Box display="flex" flexDirection="column" gap={1} minWidth={120}>
                    <Button
                      variant="contained"
                      color="success"
                      size="small"
                      startIcon={processing === analysis.id ? <CircularProgress size={16} /> : <CheckCircle />}
                      onClick={() => handleApprove(analysis.id)}
                      disabled={processing === analysis.id}
                      fullWidth
                    >
                      Approve
                    </Button>
                    <Button
                      variant="outlined"
                      color="error"
                      size="small"
                      startIcon={<Cancel />}
                      onClick={() => handleRejectClick(analysis)}
                      disabled={processing === analysis.id}
                      fullWidth
                    >
                      Reject
                    </Button>
                  </Box>
                </Box>

                <Divider sx={{ my: 2 }} />

                {/* Additional Info */}
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Typography variant="caption" color="text.secondary">
                    Created: {formatDate(analysis.created_at)}
                  </Typography>
                  <Chip 
                    label="Pending Review" 
                    color="warning" 
                    size="small"
                    variant="outlined"
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Reject Dialog */}
      <Dialog 
        open={rejectDialogOpen} 
        onClose={() => setRejectDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Cancel color="error" />
          Reject Level Upgrade
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            You are about to reject the level upgrade for <strong>{selectedAnalysis?.taxpayer_name}</strong>.
            Please provide a reason for rejection.
          </Typography>
          <TextField
            label="Rejection Reason"
            multiline
            rows={4}
            value={rejectReason}
            onChange={(e) => setRejectReason(e.target.value)}
            placeholder="e.g., Income documentation insufficient, requires additional verification, etc."
            fullWidth
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRejectDialogOpen(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleRejectConfirm}
            color="error"
            variant="contained"
            disabled={processing === selectedAnalysis?.id}
            startIcon={processing === selectedAnalysis?.id ? <CircularProgress size={16} /> : <Cancel />}
          >
            {processing === selectedAnalysis?.id ? 'Rejecting...' : 'Reject Upgrade'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default PendingUpgradesList;
