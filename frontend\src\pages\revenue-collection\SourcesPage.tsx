/**
 * Revenue Sources Management Page
 * 
 * Manages both Regional and City Service revenue sources
 * with tabbed interface and CRUD operations.
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  CircularProgress,
  Alert,
  Tooltip,
  Grid,
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Visibility,
  AccountBalance,
  Business,
  Source,
} from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import revenueCollectionService from '../../services/revenueCollectionService';
import type {
  RegionalRevenueSource,
  CityServiceRevenueSource,
  RegionalCategory,
  CityServiceCategory,
  RegionalRevenueSourceCreate,
  CityServiceRevenueSourceCreate,
} from '../../services/revenueCollectionService';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`source-tabpanel-${index}`}
      aria-labelledby={`source-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const SourcesPage: React.FC = () => {
  const { showSuccess, showError } = useNotification();
  
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(true);
  const [regionalSources, setRegionalSources] = useState<RegionalRevenueSource[]>([]);
  const [cityServiceSources, setCityServiceSources] = useState<CityServiceRevenueSource[]>([]);
  const [regionalCategories, setRegionalCategories] = useState<RegionalCategory[]>([]);
  const [cityServiceCategories, setCityServiceCategories] = useState<CityServiceCategory[]>([]);
  
  // Dialog states
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogMode, setDialogMode] = useState<'create' | 'edit'>('create');
  const [dialogType, setDialogType] = useState<'regional' | 'city_service'>('regional');
  const [editingItem, setEditingItem] = useState<RegionalRevenueSource | CityServiceRevenueSource | null>(null);
  
  // Form states
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    description: '',
    category: '',
    is_active: true,
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [
        regionalSourcesResponse,
        cityServiceSourcesResponse,
        regionalCategoriesResponse,
        cityServiceCategoriesResponse,
      ] = await Promise.all([
        revenueCollectionService.getRegionalRevenueSources(),
        revenueCollectionService.getCityServiceRevenueSources(),
        revenueCollectionService.getRegionalCategories(),
        revenueCollectionService.getCityServiceCategories(),
      ]);
      
      setRegionalSources(regionalSourcesResponse.results);
      setCityServiceSources(cityServiceSourcesResponse.results);
      setRegionalCategories(regionalCategoriesResponse.results);
      setCityServiceCategories(cityServiceCategoriesResponse.results);
    } catch (error) {
      console.error('Error loading data:', error);
      showError('Failed to load revenue sources');
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleOpenDialog = (
    mode: 'create' | 'edit',
    type: 'regional' | 'city_service',
    item?: RegionalRevenueSource | CityServiceRevenueSource
  ) => {
    setDialogMode(mode);
    setDialogType(type);
    setEditingItem(item || null);
    
    if (mode === 'edit' && item) {
      setFormData({
        name: item.name,
        code: item.code,
        description: item.description,
        category: item.category,
        is_active: item.is_active,
      });
    } else {
      setFormData({
        name: '',
        code: '',
        description: '',
        category: '',
        is_active: true,
      });
    }
    
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingItem(null);
    setFormData({
      name: '',
      code: '',
      description: '',
      category: '',
      is_active: true,
    });
  };

  const handleSubmit = async () => {
    try {
      if (dialogMode === 'create') {
        if (dialogType === 'regional') {
          await revenueCollectionService.createRegionalRevenueSource(formData as RegionalRevenueSourceCreate);
          showSuccess('Regional revenue source created successfully');
        } else {
          await revenueCollectionService.createCityServiceRevenueSource(formData as CityServiceRevenueSourceCreate);
          showSuccess('City service revenue source created successfully');
        }
      } else if (editingItem) {
        if (dialogType === 'regional') {
          await revenueCollectionService.updateRegionalRevenueSource(editingItem.id, formData);
          showSuccess('Regional revenue source updated successfully');
        } else {
          await revenueCollectionService.updateCityServiceRevenueSource(editingItem.id, formData);
          showSuccess('City service revenue source updated successfully');
        }
      }
      
      handleCloseDialog();
      loadData();
    } catch (error) {
      console.error('Error saving revenue source:', error);
      showError('Failed to save revenue source');
    }
  };

  const handleDelete = async (type: 'regional' | 'city_service', id: string) => {
    if (!window.confirm('Are you sure you want to delete this revenue source?')) {
      return;
    }

    try {
      if (type === 'regional') {
        await revenueCollectionService.deleteRegionalRevenueSource(id);
        showSuccess('Regional revenue source deleted successfully');
      } else {
        await revenueCollectionService.deleteCityServiceRevenueSource(id);
        showSuccess('City service revenue source deleted successfully');
      }
      
      loadData();
    } catch (error) {
      console.error('Error deleting revenue source:', error);
      showError('Failed to delete revenue source');
    }
  };

  const renderSourceTable = (
    sources: (RegionalRevenueSource | CityServiceRevenueSource)[],
    type: 'regional' | 'city_service'
  ) => (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Code</TableCell>
            <TableCell>Name</TableCell>
            <TableCell>Category</TableCell>
            <TableCell>Description</TableCell>
            <TableCell>Collections</TableCell>
            <TableCell>Status</TableCell>
            <TableCell>Created</TableCell>
            <TableCell>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {sources.map((source) => (
            <TableRow key={source.id}>
              <TableCell>
                <Typography variant="body2" fontWeight="medium">
                  {source.code}
                </Typography>
              </TableCell>
              <TableCell>
                <Typography variant="body2">
                  {source.name}
                </Typography>
              </TableCell>
              <TableCell>
                <Chip
                  label={source.category_name}
                  size="small"
                  color="primary"
                  variant="outlined"
                />
                <Typography variant="caption" display="block" color="text.secondary">
                  {source.category_code}
                </Typography>
              </TableCell>
              <TableCell>
                <Typography variant="body2" color="text.secondary">
                  {source.description || 'No description'}
                </Typography>
              </TableCell>
              <TableCell>
                <Chip
                  label={`${source.collections_count} collections`}
                  size="small"
                  color="secondary"
                  variant="outlined"
                />
              </TableCell>
              <TableCell>
                <Chip
                  label={source.is_active ? 'Active' : 'Inactive'}
                  color={source.is_active ? 'success' : 'default'}
                  size="small"
                />
              </TableCell>
              <TableCell>
                <Typography variant="body2" color="text.secondary">
                  {new Date(source.created_at).toLocaleDateString()}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  by {source.created_by_name}
                </Typography>
              </TableCell>
              <TableCell>
                <Tooltip title="Edit">
                  <IconButton
                    size="small"
                    onClick={() => handleOpenDialog('edit', type, source)}
                  >
                    <Edit />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Delete">
                  <IconButton
                    size="small"
                    color="error"
                    onClick={() => handleDelete(type, source.id)}
                  >
                    <Delete />
                  </IconButton>
                </Tooltip>
              </TableCell>
            </TableRow>
          ))}
          {sources.length === 0 && (
            <TableRow>
              <TableCell colSpan={8} align="center">
                <Typography variant="body2" color="text.secondary">
                  No revenue sources found
                </Typography>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </TableContainer>
  );

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  const availableCategories = dialogType === 'regional' ? regionalCategories : cityServiceCategories;

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Revenue Sources
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Manage regional and city service revenue sources
        </Typography>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <AccountBalance color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6" component="div">
                  Regional Sources
                </Typography>
              </Box>
              <Typography variant="h4" component="div" color="primary">
                {regionalSources.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Business color="secondary" sx={{ mr: 1 }} />
                <Typography variant="h6" component="div">
                  City Service Sources
                </Typography>
              </Box>
              <Typography variant="h4" component="div" color="secondary">
                {cityServiceSources.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Source color="success" sx={{ mr: 1 }} />
                <Typography variant="h6" component="div">
                  Total Sources
                </Typography>
              </Box>
              <Typography variant="h4" component="div" color="success.main">
                {regionalSources.length + cityServiceSources.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Source color="warning" sx={{ mr: 1 }} />
                <Typography variant="h6" component="div">
                  Active Sources
                </Typography>
              </Box>
              <Typography variant="h4" component="div" color="warning.main">
                {regionalSources.filter(s => s.is_active).length + cityServiceSources.filter(s => s.is_active).length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab
              label="Regional Revenue Sources"
              icon={<AccountBalance />}
              iconPosition="start"
            />
            <Tab
              label="City Service Revenue Sources"
              icon={<Business />}
              iconPosition="start"
            />
          </Tabs>
        </Box>

        {/* Regional Sources Tab */}
        <TabPanel value={tabValue} index={0}>
          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">
              Regional Revenue Sources ({regionalSources.length})
            </Typography>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => handleOpenDialog('create', 'regional')}
            >
              Add Regional Source
            </Button>
          </Box>
          {renderSourceTable(regionalSources, 'regional')}
        </TabPanel>

        {/* City Service Sources Tab */}
        <TabPanel value={tabValue} index={1}>
          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">
              City Service Revenue Sources ({cityServiceSources.length})
            </Typography>
            <Button
              variant="contained"
              color="secondary"
              startIcon={<Add />}
              onClick={() => handleOpenDialog('create', 'city_service')}
            >
              Add City Service Source
            </Button>
          </Box>
          {renderSourceTable(cityServiceSources, 'city_service')}
        </TabPanel>
      </Card>

      {/* Create/Edit Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {dialogMode === 'create' ? 'Create' : 'Edit'} {' '}
          {dialogType === 'regional' ? 'Regional' : 'City Service'} Revenue Source
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <FormControl fullWidth margin="normal" required>
              <InputLabel>Category</InputLabel>
              <Select
                value={formData.category}
                onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                label="Category"
              >
                {availableCategories.map((category) => (
                  <MenuItem key={category.id} value={category.id}>
                    {category.code} - {category.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <TextField
              fullWidth
              label="Source Name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              margin="normal"
              required
            />
            <TextField
              fullWidth
              label="Source Code"
              value={formData.code}
              onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
              margin="normal"
              required
              helperText="Unique code for this source (e.g., INC001, BUS001)"
            />
            <TextField
              fullWidth
              label="Description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              margin="normal"
              multiline
              rows={3}
            />
            <FormControlLabel
              control={
                <Switch
                  checked={formData.is_active}
                  onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                />
              }
              label="Active"
              sx={{ mt: 2 }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={!formData.name || !formData.code || !formData.category}
          >
            {dialogMode === 'create' ? 'Create' : 'Update'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SourcesPage;
