import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  TextField,
  InputAdornment,
  Avatar,
  Divider,
  Stack,
  Fade,
  Alert,
  CircularProgress,
  IconButton,
  Breadcrumbs,
  Link,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TablePagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
} from '@mui/material';
import {
  Category,
  ArrowBack,
  Home,
  Add,
  Edit,
  Delete,
  Visibility,
  Business,
  Palette,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useNotification } from '../../contexts/NotificationContext';
import fileService from '../../services/fileService';
import ConfirmationDialog from '../../components/common/ConfirmationDialog';
import { normalizeCode, createCodeBlurHandler } from '../../utils/codeUtils';
import type { FileType, FileTypeCreate } from '../../services/types';

const FileTypesPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { showNotification } = useNotification();

  const [fileTypes, setFileTypes] = useState<FileType[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingFileType, setEditingFileType] = useState<FileType | null>(null);
  const [formData, setFormData] = useState<FileTypeCreate>({
    name: '',
    code: '',
    description: '',
    color: '#2196f3',
    icon: 'folder',
    requires_business_name: false,
    requires_tin_number: false,
    requires_license_number: false,
    requires_owner_name: false,
    default_document_types: '',
  });
  const [formErrors, setFormErrors] = useState<any>({});
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [fileTypeToDelete, setFileTypeToDelete] = useState<FileType | null>(null);
  const [deleting, setDeleting] = useState(false);

  const colorOptions = [
    { value: '#2196f3', label: 'Blue' },
    { value: '#4caf50', label: 'Green' },
    { value: '#ff9800', label: 'Orange' },
    { value: '#f44336', label: 'Red' },
    { value: '#9c27b0', label: 'Purple' },
    { value: '#607d8b', label: 'Blue Grey' },
    { value: '#795548', label: 'Brown' },
    { value: '#000000', label: 'Black' },
  ];

  const iconOptions = [
    { value: 'folder', label: 'Folder' },
    { value: 'business', label: 'Business' },
    { value: 'description', label: 'Document' },
    { value: 'account_balance', label: 'Bank' },
    { value: 'gavel', label: 'Legal' },
    { value: 'engineering', label: 'Technical' },
    { value: 'receipt', label: 'Receipt' },
    { value: 'assignment', label: 'Assignment' },
  ];

  useEffect(() => {
    loadFileTypes();
  }, [page, rowsPerPage]);

  // Handle navigation state from detail page
  useEffect(() => {
    const state = location.state as any;
    if (state?.editFileType && state?.showForm) {
      handleEdit(state.editFileType);
      // Clear the state to prevent re-triggering
      navigate(location.pathname, { replace: true });
    }
  }, [location.state]);

  const loadFileTypes = async () => {
    try {
      setLoading(true);
      const response = await fileService.getFileTypes({
        page: page + 1,
        page_size: rowsPerPage,
      });
      setFileTypes(response.results);
      setTotalCount(response.count);
    } catch (error) {
      console.error('Error loading file types:', error);
      showNotification('Failed to load file types', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setSubmitting(true);
      setFormErrors({});

      if (editingFileType) {
        await fileService.updateFileType(editingFileType.id, formData);
        showNotification('File type updated successfully', 'success');
      } else {
        await fileService.createFileType(formData);
        showNotification('File type created successfully', 'success');
      }

      resetForm();
      loadFileTypes();
    } catch (error: any) {
      console.error('Error submitting form:', error);
      if (error.response?.data) {
        setFormErrors(error.response.data);
      }
      showNotification('Failed to save file type', 'error');
    } finally {
      setSubmitting(false);
    }
  };

  const handleViewDetails = (fileType: FileType) => {
    navigate(`/document-center/file-types/${fileType.id}`);
  };

  const handleEdit = (fileType: FileType) => {
    setEditingFileType(fileType);
    setFormData({
      name: fileType.name,
      code: fileType.code,
      description: fileType.description || '',
      color: fileType.color || '#2196f3',
      icon: fileType.icon || 'folder',
      requires_business_name: fileType.requires_business_name || false,
      requires_tin_number: fileType.requires_tin_number || false,
      requires_license_number: fileType.requires_license_number || false,
      requires_owner_name: fileType.requires_owner_name || false,
      default_document_types: fileType.default_document_types || '',
    });
    setShowForm(true);
  };

  const handleDelete = (fileType: FileType) => {
    setFileTypeToDelete(fileType);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!fileTypeToDelete) return;
    
    try {
      setDeleting(true);
      await fileService.deleteFileType(fileTypeToDelete.id);
      showNotification('File type deleted successfully', 'success');
      loadFileTypes();
      handleCloseDeleteDialog();
    } catch (error) {
      console.error('Error deleting file type:', error);
      showNotification('Failed to delete file type', 'error');
    } finally {
      setDeleting(false);
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setFileTypeToDelete(null);
    setDeleting(false);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      code: '',
      description: '',
      color: '#2196f3',
      icon: 'folder',
      requires_business_name: false,
      requires_tin_number: false,
      requires_license_number: false,
      requires_owner_name: false,
      default_document_types: '',
    });
    setFormErrors({});
    setEditingFileType(null);
    setShowForm(false);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Fade in timeout={800}>
        <Box sx={{ mb: 4 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs sx={{ mb: 2 }}>
            <Link
              component="button"
              variant="body1"
              onClick={() => navigate('/document-center')}
              sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
            >
              <Home fontSize="small" />
              Document Management Center
            </Link>
            <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <Category fontSize="small" />
              File Types
            </Typography>
          </Breadcrumbs>

          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'between', mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <IconButton onClick={() => navigate('/document-center')} sx={{ mr: 1 }}>
                <ArrowBack />
              </IconButton>
              <Avatar sx={{ bgcolor: 'warning.main', width: 56, height: 56 }}>
                <Category />
              </Avatar>
              <Box>
                <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
                  File Types Management
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Configure business file type categories and requirements
                </Typography>
              </Box>
            </Box>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setShowForm(true)}
              size="large"
              sx={{ ml: 'auto' }}
            >
              Add File Type
            </Button>
          </Box>
        </Box>
      </Fade>

      {/* Form Section */}
      {showForm && (
        <Fade in timeout={600}>
          <Card sx={{ mb: 4 }}>
            <CardContent sx={{ p: 4 }}>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                {editingFileType ? 'Edit File Type' : 'Add New File Type'}
              </Typography>
              <Divider sx={{ mb: 3 }} />
              
              <form onSubmit={handleSubmit}>
                <Stack spacing={3}>
                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                    <TextField
                      label="File Type Name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      error={!!formErrors.name}
                      helperText={formErrors.name || 'Enter the file type name'}
                      required
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Category color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                    
                    <TextField
                      label="File Type Code"
                      value={formData.code}
                      onChange={(e) => setFormData({ ...formData, code: e.target.value })}
                      onBlur={createCodeBlurHandler(
                        (field, value) => setFormData(prev => ({ ...prev, [field]: value })),
                        'code'
                      )}
                      error={!!formErrors.code}
                      helperText={formErrors.code || 'Code will be automatically converted to uppercase (e.g., BUS, LEG, FIN)'}
                      required
                      inputProps={{ maxLength: 10 }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Business color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>

                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                    <FormControl fullWidth error={!!formErrors.color}>
                      <InputLabel>Color</InputLabel>
                      <Select
                        value={formData.color}
                        onChange={(e) => setFormData({ ...formData, color: e.target.value })}
                        label="Color"
                        startAdornment={
                          <InputAdornment position="start">
                            <Palette color="action" />
                          </InputAdornment>
                        }
                      >
                        {colorOptions.map((option) => (
                          <MenuItem key={option.value} value={option.value}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Box
                                sx={{
                                  width: 16,
                                  height: 16,
                                  borderRadius: '50%',
                                  bgcolor: option.value,
                                  border: '1px solid #ccc',
                                }}
                              />
                              {option.label}
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                      {formErrors.color && (
                        <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 2 }}>
                          {formErrors.color}
                        </Typography>
                      )}
                    </FormControl>

                    <FormControl fullWidth error={!!formErrors.icon}>
                      <InputLabel>Icon</InputLabel>
                      <Select
                        value={formData.icon}
                        onChange={(e) => setFormData({ ...formData, icon: e.target.value })}
                        label="Icon"
                      >
                        {iconOptions.map((option) => (
                          <MenuItem key={option.value} value={option.value}>
                            {option.label}
                          </MenuItem>
                        ))}
                      </Select>
                      {formErrors.icon && (
                        <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 2 }}>
                          {formErrors.icon}
                        </Typography>
                      )}
                    </FormControl>
                  </Box>

                  <TextField
                    label="Description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    error={!!formErrors.description}
                    helperText={formErrors.description || 'Optional description'}
                    multiline
                    rows={3}
                    fullWidth
                  />

                  <TextField
                    label="Default Document Types"
                    value={formData.default_document_types}
                    onChange={(e) => setFormData({ ...formData, default_document_types: e.target.value })}
                    error={!!formErrors.default_document_types}
                    helperText={formErrors.default_document_types || 'Comma-separated list of default document types'}
                    multiline
                    rows={2}
                    fullWidth
                  />

                  <Box>
                    <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                      Required Fields
                    </Typography>
                    <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.requires_business_name}
                            onChange={(e) => setFormData({ ...formData, requires_business_name: e.target.checked })}
                          />
                        }
                        label="Requires Business Name"
                      />
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.requires_tin_number}
                            onChange={(e) => setFormData({ ...formData, requires_tin_number: e.target.checked })}
                          />
                        }
                        label="Requires TIN Number"
                      />
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.requires_license_number}
                            onChange={(e) => setFormData({ ...formData, requires_license_number: e.target.checked })}
                          />
                        }
                        label="Requires License Number"
                      />
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.requires_owner_name}
                            onChange={(e) => setFormData({ ...formData, requires_owner_name: e.target.checked })}
                          />
                        }
                        label="Requires Owner Name"
                      />
                    </Box>
                  </Box>

                  <Box sx={{ display: 'flex', gap: 2, pt: 2 }}>
                    <Button
                      type="submit"
                      variant="contained"
                      disabled={submitting}
                      startIcon={submitting ? <CircularProgress size={20} /> : <Add />}
                      size="large"
                    >
                      {submitting ? 'Saving...' : editingFileType ? 'Update File Type' : 'Create File Type'}
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={resetForm}
                      size="large"
                    >
                      Cancel
                    </Button>
                  </Box>
                </Stack>
              </form>
            </CardContent>
          </Card>
        </Fade>
      )}

      {/* File Types Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
            File Types List
          </Typography>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : fileTypes.length === 0 ? (
            <Alert severity="info">
              No file types found. Click "Add File Type" to create your first file type.
            </Alert>
          ) : (
            <>
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>File Type</TableCell>
                      <TableCell>Requirements</TableCell>
                      <TableCell>Files Count</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Created</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {fileTypes.map((fileType) => (
                      <TableRow key={fileType.id} hover>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <Avatar
                              sx={{
                                bgcolor: fileType.color || '#2196f3',
                                width: 32,
                                height: 32
                              }}
                            >
                              <Category fontSize="small" />
                            </Avatar>
                            <Box>
                              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                {fileType.name}
                              </Typography>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Chip label={fileType.code} size="small" color="primary" />
                                <Typography variant="caption" color="text.secondary">
                                  {fileType.description || 'No description'}
                                </Typography>
                              </Box>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            {fileType.requires_business_name && (
                              <Chip label="Business Name" size="small" variant="outlined" />
                            )}
                            {fileType.requires_tin_number && (
                              <Chip label="TIN" size="small" variant="outlined" />
                            )}
                            {fileType.requires_license_number && (
                              <Chip label="License" size="small" variant="outlined" />
                            )}
                            {fileType.requires_owner_name && (
                              <Chip label="Owner" size="small" variant="outlined" />
                            )}
                            {!fileType.requires_business_name && !fileType.requires_tin_number &&
                             !fileType.requires_license_number && !fileType.requires_owner_name && (
                              <Typography variant="caption" color="text.secondary">
                                No requirements
                              </Typography>
                            )}
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {fileType.file_count || 0} files
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={fileType.is_active ? 'Active' : 'Inactive'}
                            color={fileType.is_active ? 'success' : 'default'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {new Date(fileType.created_at).toLocaleDateString()}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                            <IconButton
                              size="small"
                              onClick={() => handleViewDetails(fileType)}
                              color="info"
                              title="View Details"
                            >
                              <Visibility fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleEdit(fileType)}
                              color="primary"
                              title="Edit"
                            >
                              <Edit fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleDelete(fileType)}
                              color="error"
                              title="Delete"
                            >
                              <Delete fontSize="small" />
                            </IconButton>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              <TablePagination
                component="div"
                count={totalCount}
                page={page}
                onPageChange={handleChangePage}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                rowsPerPageOptions={[5, 10, 25, 50]}
              />
            </>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        title="Delete File Type"
        itemName={fileTypeToDelete?.name}
        itemType="File Type"
        message={`Are you sure you want to delete "${fileTypeToDelete?.name}"? This will affect all files using this type. This action cannot be undone.`}
        confirmText="Delete File Type"
        severity="error"
        loading={deleting}
      />
    </Container>
  );
};

export default FileTypesPage;
