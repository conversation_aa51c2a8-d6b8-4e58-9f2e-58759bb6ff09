# Generated by Django 5.2.3 on 2025-07-14 21:57

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("locations", "0002_initial"),
        ("taxpayers", "0002_alter_organizationbusinesstype_code_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="individualtaxpayer",
            name="city",
            field=models.ForeignKey(
                blank=True,
                help_text="City",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="locations.city",
            ),
        ),
        migrations.AddField(
            model_name="individualtaxpayer",
            name="country",
            field=models.ForeignKey(
                blank=True,
                help_text="Country",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="locations.country",
            ),
        ),
        migrations.AddField(
            model_name="individualtaxpayer",
            name="region",
            field=models.ForeignKey(
                blank=True,
                help_text="Region/State",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="locations.region",
            ),
        ),
        migrations.AddField(
            model_name="individualtaxpayer",
            name="zone",
            field=models.ForeignKey(
                blank=True,
                help_text="Zone",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="locations.zone",
            ),
        ),
        migrations.AddField(
            model_name="organizationtaxpayer",
            name="city",
            field=models.ForeignKey(
                blank=True,
                help_text="City",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="locations.city",
            ),
        ),
        migrations.AddField(
            model_name="organizationtaxpayer",
            name="country",
            field=models.ForeignKey(
                blank=True,
                help_text="Country",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="locations.country",
            ),
        ),
        migrations.AddField(
            model_name="organizationtaxpayer",
            name="region",
            field=models.ForeignKey(
                blank=True,
                help_text="Region/State",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="locations.region",
            ),
        ),
        migrations.AddField(
            model_name="organizationtaxpayer",
            name="zone",
            field=models.ForeignKey(
                blank=True,
                help_text="Zone",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="locations.zone",
            ),
        ),
    ]
