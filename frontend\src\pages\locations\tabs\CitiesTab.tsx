import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  InputAdornment,
  Typography,
  Menu,
  Alert,
  Grid,
} from '@mui/material';
import {
  Add,
  Search,
  Edit,
  Delete,
  MoreVert,
  LocationCity,
  Business,
  Star,
} from '@mui/icons-material';
import { useNotification } from '../../../contexts/NotificationContext';
import locationHierarchyService from '../../../services/locationHierarchyService';
import type {
  City,
  CityCreate,
  ZoneSelect
} from '../../../services/locationHierarchyService';

interface CitiesTabProps {
  onStatsChange: () => void;
}

const CitiesTab: React.FC<CitiesTabProps> = ({ onStatsChange }) => {
  const { showSuccess, showError } = useNotification();

  const [cities, setCities] = useState<City[]>([]);
  const [zones, setZones] = useState<ZoneSelect[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedZone, setSelectedZone] = useState<number | ''>('');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingCity, setEditingCity] = useState<City | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedCity, setSelectedCity] = useState<City | null>(null);
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);

  const [formData, setFormData] = useState<CityCreate>({
    zone: 0,
    name: '',
    code: '',
    population: undefined,
    area_km2: undefined,
    is_capital: false,
    is_active: true,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    loadZones();
    loadCities();
  }, []);

  useEffect(() => {
    loadCities();
  }, [searchTerm, selectedZone]);

  const loadZones = async () => {
    try {
      const response = await locationHierarchyService.getZonesSelect();
      setZones(response);
    } catch (error) {
      showError('Failed to load zones');
    }
  };

  const loadCities = async () => {
    try {
      setLoading(true);
      const response = await locationHierarchyService.getCities({
        search: searchTerm,
        zone: selectedZone || undefined,
        page_size: 100,
      });
      setCities(response.results);
    } catch (error) {
      showError('Failed to load cities');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (city?: City) => {
    if (city) {
      setEditingCity(city);
      setFormData({
        zone: city.zone,
        name: city.name,
        code: city.code,
        population: city.population,
        area_km2: city.area_km2,
        is_capital: city.is_capital,
        is_active: city.is_active,
      });
    } else {
      setEditingCity(null);
      setFormData({
        zone: selectedZone as number || 2,
        name: '',
        code: '',
        population: undefined,
        area_km2: undefined,
        is_capital: false,
        is_active: true,
      });
    }
    setErrors({});
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingCity(null);
    setErrors({});
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.zone) {
      newErrors.zone = 'Zone is required';
    }
    if (!formData.name.trim()) {
      newErrors.name = 'City name is required';
    }
    if (!formData.code.trim()) {
      newErrors.code = 'City code is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      if (editingCity) {
        await locationHierarchyService.updateCity(editingCity.id, formData);
        showSuccess('City updated successfully');
      } else {
        await locationHierarchyService.createCity(formData);
        showSuccess('City created successfully');
      }

      handleCloseDialog();
      loadCities();
      onStatsChange();
    } catch (error: any) {
      showError(error.response?.data?.detail || 'Failed to save city');
    }
  };

  const handleDelete = async () => {
    if (!selectedCity) return;

    try {
      await locationHierarchyService.deleteCity(selectedCity.id);
      showSuccess('City deleted successfully');
      setDeleteDialogOpen(false);
      setSelectedCity(null);
      loadCities();
      onStatsChange();
    } catch (error: any) {
      showError(error.response?.data?.detail || 'Failed to delete city');
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, city: City) => {
    setMenuAnchor(event.currentTarget);
    setSelectedCity(city);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
    setSelectedCity(null);
  };

  const handleInputChange = (field: keyof CityCreate) => (
    event: React.ChangeEvent<HTMLInputElement> | { target: { value: unknown } }
  ) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">Cities Management</Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => handleOpenDialog()}
          disabled={zones.length === 0}
        >
          Add City
        </Button>
      </Box>

      {zones.length === 0 && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          No zones available. Please create zones first before adding cities.
        </Alert>
      )}

      {/* Search and Filters */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid size={{ xs: 12, md: 4 }}>
          <TextField
            fullWidth
            placeholder="Search cities..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
          />
        </Grid>
        <Grid size={{ xs: 12, md: 4 }}>
          <FormControl fullWidth>
            <InputLabel>Filter by Zone</InputLabel>
            <Select
              value={selectedZone}
              onChange={(e) => setSelectedZone(e.target.value as number)}
              label="Filter by Zone"
            >
              <MenuItem value="">All Zones</MenuItem>
              {zones.map((zone) => (
                <MenuItem key={zone.id} value={zone.id}>
                  {zone.name} ({zone.code})
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
      </Grid>

      {/* Cities Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>Code</TableCell>
              <TableCell>Zone</TableCell>
              <TableCell>Population</TableCell>
              <TableCell>Capital</TableCell>
              <TableCell>SubCities</TableCell>
              <TableCell>Status</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {cities.map((city) => (
              <TableRow key={city.id}>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <LocationCity fontSize="small" />
                    {city.name}
                    {city.is_capital && <Star fontSize="small" color="warning" />}
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip label={city.code} size="small" />
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Business fontSize="small" />
                    {city.zone_name}
                  </Box>
                </TableCell>
                <TableCell>
                  {city.population ? city.population.toLocaleString() : '-'}
                </TableCell>
                <TableCell>
                  {city.is_capital && (
                    <Chip label="Capital" size="small" color="warning" />
                  )}
                </TableCell>
                <TableCell>
                  <Chip label={city.subcity_count} size="small" color="primary" />
                </TableCell>
                <TableCell>
                  <Chip
                    label={city.is_active ? 'Active' : 'Inactive'}
                    color={city.is_active ? 'success' : 'default'}
                    size="small"
                  />
                </TableCell>
                <TableCell align="right">
                  <IconButton
                    onClick={(e) => handleMenuOpen(e, city)}
                    size="small"
                  >
                    <MoreVert />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Action Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
      >
        <MenuItem
          onClick={() => {
            handleOpenDialog(selectedCity!);
            handleMenuClose();
          }}
        >
          <Edit fontSize="small" sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        <MenuItem
          onClick={() => {
            setDeleteDialogOpen(true);
            handleMenuClose();
          }}
          sx={{ color: 'error.main' }}
        >
          <Delete fontSize="small" sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>

      {/* Create/Edit Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingCity ? 'Edit City' : 'Add New City'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid size={{ xs: 12, sm: 6 }}>
              <FormControl fullWidth required error={!!errors.zone}>
                <InputLabel>Zone</InputLabel>
                <Select
                  value={formData.zone || ''}
                  onChange={handleInputChange('zone')}
                  label="Zone"
                >
                  {zones.map((zone) => (
                    <MenuItem key={zone.id} value={zone.id}>
                      {zone.name} ({zone.code})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="City Name"
                value={formData.name}
                onChange={handleInputChange('name')}
                error={!!errors.name}
                helperText={errors.name}
                required
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="City Code"
                value={formData.code}
                onChange={handleInputChange('code')}
                error={!!errors.code}
                helperText={errors.code}
                required
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="Population"
                type="number"
                value={formData.population || ''}
                onChange={handleInputChange('population')}
              />
            </Grid>
            <Grid size={{ xs: 12 }}>
              <TextField
                fullWidth
                label="Area (km²)"
                type="number"
                value={formData.area_km2 || ''}
                onChange={handleInputChange('area_km2')}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.is_capital}
                    onChange={handleInputChange('is_capital')}
                  />
                }
                label="Capital City"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.is_active}
                    onChange={handleInputChange('is_active')}
                  />
                }
                label="Active"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSubmit} variant="contained">
            {editingCity ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 2 }}>
            Are you sure you want to delete "{selectedCity?.name}"? This action cannot be undone.
          </Alert>
          {selectedCity && selectedCity.subcity_count > 0 && (
            <Alert severity="error">
              This city has {selectedCity.subcity_count} subcities.
              Please delete or reassign them first.
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleDelete}
            color="error"
            variant="contained"
            disabled={selectedCity ? selectedCity.subcity_count > 0 : false}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CitiesTab;
