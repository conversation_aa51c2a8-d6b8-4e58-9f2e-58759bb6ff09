import React, { useState } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Di<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
} from '@mui/material';
import {
  ViewModule,
  ViewList,
  LocationOn,
  Refresh,
} from '@mui/icons-material';
import { useNotification } from '../contexts/NotificationContext';
import ModernLocationHierarchy from '../components/locations/ModernLocationHierarchy';
import CompactLocationSelector from '../components/locations/CompactLocationSelector';

const LocationHierarchyDemo: React.FC = () => {
  const { showNotification } = useNotification();
  
  const [cardSelection, setCardSelection] = useState<{
    country?: any;
    region?: any;
    city?: any;
    subcity?: any;
    kebele?: any;
  }>({});

  const [compactSelection, setCompactSelection] = useState<{
    country?: number | null;
    region?: number | null;
    city?: number | null;
    subcity?: number | null;
    kebele?: number | null;
  }>({});

  // Handle location selection in card view
  const handleCardLocationSelect = (location: any) => {
    setCardSelection(location);
    showNotification('Location selected in card view', 'success');
  };

  // Handle location change in compact view
  const handleCompactLocationChange = (location: any) => {
    setCompactSelection(location);
    showNotification('Location changed in compact view', 'info');
  };

  // Get location summary for card view
  const getCardLocationSummary = () => {
    const parts = [
      cardSelection.country?.name,
      cardSelection.region?.name,
      cardSelection.city?.name,
      cardSelection.subcity?.name,
      cardSelection.kebele?.name,
    ].filter(Boolean);
    return parts.length > 0 ? parts.join(' → ') : 'No location selected';
  };

  // Get location summary for compact view
  const getCompactLocationSummary = () => {
    const parts = [
      compactSelection.country && 'Country',
      compactSelection.region && 'Region',
      compactSelection.city && 'City',
      compactSelection.subcity && 'SubCity',
      compactSelection.kebele && 'Kebele',
    ].filter(Boolean);
    return parts.length > 0 ? `${parts.length} level(s) selected` : 'No location selected';
  };

  const resetSelections = () => {
    setCardSelection({});
    setCompactSelection({});
    showNotification('All selections cleared', 'success');
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Modern Location Hierarchy Demo
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          Experience our modern card-based and compact location hierarchy interfaces
        </Typography>

        {/* Selection Summary */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap', gap: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <LocationOn color="primary" />
                <Box>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                    Current Selections
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Card View: {getCardLocationSummary()}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Compact View: {getCompactLocationSummary()}
                  </Typography>
                </Box>
              </Box>
              
              <Button
                variant="outlined"
                startIcon={<Refresh />}
                onClick={resetSelections}
              >
                Reset All
              </Button>
            </Box>
          </CardContent>
        </Card>

        {/* Info Alert */}
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="body2">
            <strong>Demo Features:</strong> Both interfaces connect to the same backend API and support the full Ethiopian administrative hierarchy. 
            The card view provides an interactive exploration experience, while the compact view is perfect for forms and space-constrained layouts.
          </Typography>
        </Alert>
      </Box>

      {/* Demo Content */}
      <Grid container spacing={4}>
        {/* Card View Demo */}
        <Grid size={{ xs: 12 }}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
                <ViewModule color="primary" />
                <Typography variant="h5" component="h2">
                  Card-Based Interface
                </Typography>
                <Chip label="Interactive" color="primary" size="small" />
              </Box>

              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Modern card-based interface with visual feedback, animations, and comprehensive location exploration.
                Perfect for location discovery and administrative hierarchy visualization.
              </Typography>

              <ModernLocationHierarchy
                onLocationSelect={handleCardLocationSelect}
                selectedLocation={{
                  countryId: cardSelection.country?.id,
                  regionId: cardSelection.region?.id,
                  cityId: cardSelection.city?.id,
                  subcityId: cardSelection.subcity?.id,
                  kebeleId: cardSelection.kebele?.id,
                }}
                showBreadcrumbs={true}
              />
            </CardContent>
          </Card>
        </Grid>

        {/* Divider */}
        <Grid size={{ xs: 12 }}>
          <Divider sx={{ my: 2 }}>
            <Chip label="Alternative Interface" />
          </Divider>
        </Grid>

        {/* Compact View Demo */}
        <Grid size={{ xs: 12 }}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
                <ViewList color="secondary" />
                <Typography variant="h5" component="h2">
                  Compact Selector Interface
                </Typography>
                <Chip label="Form-Friendly" color="secondary" size="small" />
              </Box>
              
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Compact dropdown-based interface optimized for forms and space-constrained layouts.
                Provides the same functionality with a more traditional form-based approach.
              </Typography>

              <CompactLocationSelector
                value={compactSelection}
                onChange={handleCompactLocationChange}
                showBreadcrumbs={true}
                title="Location Selection Form"
              />
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Features Summary */}
      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Interface Features Comparison
          </Typography>
          <Grid container spacing={3}>
            <Grid size={{ xs: 12, md: 6 }}>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                Card-Based Interface
              </Typography>
              <Box component="ul" sx={{ pl: 2, m: 0 }}>
                <li>Visual exploration with interactive cards</li>
                <li>Real-time loading states and animations</li>
                <li>Breadcrumb navigation</li>
                <li>Color-coded hierarchy levels</li>
                <li>Hover effects and visual feedback</li>
                <li>Badge counters for available items</li>
              </Box>
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                Compact Selector Interface
              </Typography>
              <Box component="ul" sx={{ pl: 2, m: 0 }}>
                <li>Traditional dropdown-based selection</li>
                <li>Form validation and error handling</li>
                <li>Cascading dependency management</li>
                <li>Compact layout for forms</li>
                <li>Breadcrumb path display</li>
                <li>Collapsible sections support</li>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Container>
  );
};

export default LocationHierarchyDemo;
