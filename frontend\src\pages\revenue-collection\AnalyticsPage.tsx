/**
 * Revenue Analytics Dashboard
 * 
 * Comprehensive analytics and reporting for revenue collection
 * with charts, summaries, and export functionality.
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
} from 'recharts';
import {
  Analytics,
  TrendingUp,
  AccountBalance,
  Business,
  GetApp,
  DateRange,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { useNotification } from '../../contexts/NotificationContext';
import revenueCollectionService from '../../services/revenueCollectionService';
import type { RevenuePeriod } from '../../services/revenueCollectionService';

interface AnalyticsData {
  periodBreakdown: Array<{
    period__name: string;
    period__start_date: string;
    period__end_date: string;
    regional_total: number;
    city_service_total: number;
    grand_total: number;
  }>;
  locationBreakdown: Array<{
    region__name: string;
    city__name: string;
    regional_total: number;
    city_service_total: number;
    grand_total: number;
  }>;
  summary: {
    total_regional: number;
    total_city_service: number;
    total_grand: number;
    count: number;
  };
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

const AnalyticsPage: React.FC = () => {
  const { showError } = useNotification();
  
  const [loading, setLoading] = useState(true);
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [periods, setPeriods] = useState<RevenuePeriod[]>([]);
  
  // Filter states
  const [selectedPeriods, setSelectedPeriods] = useState<string[]>([]);
  const [selectedRegion, setSelectedRegion] = useState('');
  const [selectedCity, setSelectedCity] = useState('');
  const [dateRange, setDateRange] = useState<{
    start: Date | null;
    end: Date | null;
  }>({
    start: null,
    end: null,
  });

  useEffect(() => {
    loadPeriods();
    loadAnalytics();
  }, []);

  useEffect(() => {
    loadAnalytics();
  }, [selectedPeriods, selectedRegion, selectedCity]);

  const loadPeriods = async () => {
    try {
      const response = await revenueCollectionService.getRevenuePeriods();
      setPeriods(response.results);
    } catch (error) {
      console.error('Error loading periods:', error);
    }
  };

  const loadAnalytics = async () => {
    try {
      setLoading(true);

      // Load actual collections data instead of using analytics endpoint
      const [regionalResponse, cityServiceResponse, periodsResponse] = await Promise.all([
        revenueCollectionService.getRegionalRevenueCollections({
          page_size: 1000, // Get more data for analytics
        }),
        revenueCollectionService.getCityServiceRevenueCollections({
          page_size: 1000,
        }),
        revenueCollectionService.getRevenuePeriods(),
      ]);

      // Process the data to create analytics
      const allCollections = [
        ...regionalResponse.results.map(c => ({ ...c, type: 'regional' as const })),
        ...cityServiceResponse.results.map(c => ({ ...c, type: 'city_service' as const })),
      ];

      // Group by period
      const periodMap = new Map();
      periodsResponse.results.forEach(period => {
        periodMap.set(period.id, period);
      });

      const periodBreakdown = periodsResponse.results.map(period => {
        const periodCollections = allCollections.filter(c => c.period === period.id);
        const regionalTotal = periodCollections
          .filter(c => c.type === 'regional')
          .reduce((sum, c) => sum + c.amount, 0);
        const cityServiceTotal = periodCollections
          .filter(c => c.type === 'city_service')
          .reduce((sum, c) => sum + c.amount, 0);

        return {
          period__name: period.name,
          period__start_date: period.start_date,
          period__end_date: period.end_date,
          regional_total: regionalTotal,
          city_service_total: cityServiceTotal,
          grand_total: regionalTotal + cityServiceTotal,
        };
      }).filter(p => p.grand_total > 0); // Only show periods with data

      // Calculate summary
      const totalRegional = allCollections
        .filter(c => c.type === 'regional')
        .reduce((sum, c) => sum + c.amount, 0);
      const totalCityService = allCollections
        .filter(c => c.type === 'city_service')
        .reduce((sum, c) => sum + c.amount, 0);

      const analyticsData: AnalyticsData = {
        periodBreakdown,
        locationBreakdown: [], // We'll implement this later
        summary: {
          total_regional: totalRegional,
          total_city_service: totalCityService,
          total_grand: totalRegional + totalCityService,
          count: allCollections.length,
        },
      };

      setAnalyticsData(analyticsData);
    } catch (error) {
      console.error('Error loading analytics:', error);
      showError('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ET', {
      style: 'currency',
      currency: 'ETB',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-ET', {
      year: 'numeric',
      month: 'short',
    });
  };

  const handleExport = () => {
    if (!analyticsData) return;

    // Create CSV content
    const csvContent = [
      ['Period', 'Start Date', 'End Date', 'Regional Revenue', 'City Service Revenue', 'Total Revenue'],
      ...analyticsData.periodBreakdown.map(period => [
        period.period__name,
        formatDate(period.period__start_date),
        formatDate(period.period__end_date),
        period.regional_total.toString(),
        period.city_service_total.toString(),
        period.grand_total.toString(),
      ])
    ].map(row => row.join(',')).join('\n');

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `revenue_analytics_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (!analyticsData) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">Failed to load analytics data</Alert>
      </Box>
    );
  }

  // Prepare chart data
  const periodChartData = analyticsData.periodBreakdown.map(item => ({
    name: item.period__name,
    Regional: item.regional_total,
    'City Service': item.city_service_total,
    Total: item.grand_total,
  }));

  const pieChartData = [
    { name: 'Regional Revenue', value: analyticsData.summary.total_regional },
    { name: 'City Service Revenue', value: analyticsData.summary.total_city_service },
  ];

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box sx={{ flexGrow: 1, p: 3 }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Revenue Analytics
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Comprehensive revenue analysis and reporting
          </Typography>
        </Box>

        {/* Filters */}
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Filters
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Periods</InputLabel>
                  <Select
                    multiple
                    value={selectedPeriods}
                    onChange={(e) => setSelectedPeriods(e.target.value as string[])}
                    label="Periods"
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {(selected as string[]).map((value) => {
                          const period = periods.find(p => p.id === value);
                          return (
                            <Chip key={value} label={period?.name || value} size="small" />
                          );
                        })}
                      </Box>
                    )}
                  >
                    {periods.map((period) => (
                      <MenuItem key={period.id} value={period.id}>
                        {period.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <DatePicker
                  label="Start Date"
                  value={dateRange.start}
                  onChange={(date) => setDateRange({ ...dateRange, start: date })}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <DatePicker
                  label="End Date"
                  value={dateRange.end}
                  onChange={(date) => setDateRange({ ...dateRange, end: date })}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Button
                  variant="outlined"
                  startIcon={<GetApp />}
                  onClick={handleExport}
                  fullWidth
                >
                  Export Report
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Summary Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <AccountBalance color="primary" sx={{ mr: 1 }} />
                  <Typography variant="h6" component="div">
                    Regional Revenue
                  </Typography>
                </Box>
                <Typography variant="h4" component="div" color="primary">
                  {formatCurrency(analyticsData.summary.total_regional)}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Business color="secondary" sx={{ mr: 1 }} />
                  <Typography variant="h6" component="div">
                    City Service Revenue
                  </Typography>
                </Box>
                <Typography variant="h4" component="div" color="secondary">
                  {formatCurrency(analyticsData.summary.total_city_service)}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <TrendingUp color="success" sx={{ mr: 1 }} />
                  <Typography variant="h6" component="div">
                    Total Revenue
                  </Typography>
                </Box>
                <Typography variant="h4" component="div" color="success.main">
                  {formatCurrency(analyticsData.summary.total_grand)}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Analytics color="warning" sx={{ mr: 1 }} />
                  <Typography variant="h6" component="div">
                    Total Records
                  </Typography>
                </Box>
                <Typography variant="h4" component="div" color="warning.main">
                  {analyticsData.summary.count.toLocaleString()}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Charts */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          {/* Period Breakdown Chart */}
          <Grid item xs={12} lg={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Revenue by Period
                </Typography>
                <ResponsiveContainer width="100%" height={400}>
                  <BarChart data={periodChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis tickFormatter={(value) => formatCurrency(value)} />
                    <Tooltip formatter={(value) => formatCurrency(value as number)} />
                    <Legend />
                    <Bar dataKey="Regional" fill="#1976d2" />
                    <Bar dataKey="City Service" fill="#9c27b0" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Revenue Distribution Pie Chart */}
          <Grid item xs={12} lg={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Revenue Distribution
                </Typography>
                <ResponsiveContainer width="100%" height={400}>
                  <PieChart>
                    <Pie
                      data={pieChartData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={120}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {pieChartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => formatCurrency(value as number)} />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Period Breakdown Table */}
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Period Breakdown
            </Typography>
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Period</TableCell>
                    <TableCell>Start Date</TableCell>
                    <TableCell>End Date</TableCell>
                    <TableCell align="right">Regional Revenue</TableCell>
                    <TableCell align="right">City Service Revenue</TableCell>
                    <TableCell align="right">Total Revenue</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {analyticsData.periodBreakdown.map((period, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <Typography variant="body2" fontWeight="medium">
                          {period.period__name}
                        </Typography>
                      </TableCell>
                      <TableCell>{formatDate(period.period__start_date)}</TableCell>
                      <TableCell>{formatDate(period.period__end_date)}</TableCell>
                      <TableCell align="right">
                        <Typography variant="body2" color="primary">
                          {formatCurrency(period.regional_total)}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body2" color="secondary">
                          {formatCurrency(period.city_service_total)}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body2" fontWeight="medium">
                          {formatCurrency(period.grand_total)}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                  {analyticsData.periodBreakdown.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={6} align="center">
                        <Typography variant="body2" color="text.secondary">
                          No data available for the selected filters
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>

        {/* Location Breakdown Table */}
        {analyticsData.locationBreakdown.length > 0 && (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Location Breakdown
              </Typography>
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Region</TableCell>
                      <TableCell>City</TableCell>
                      <TableCell align="right">Regional Revenue</TableCell>
                      <TableCell align="right">City Service Revenue</TableCell>
                      <TableCell align="right">Total Revenue</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {analyticsData.locationBreakdown.map((location, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Typography variant="body2" fontWeight="medium">
                            {location.region__name || 'N/A'}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {location.city__name || 'N/A'}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="body2" color="primary">
                            {formatCurrency(location.regional_total)}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="body2" color="secondary">
                            {formatCurrency(location.city_service_total)}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="body2" fontWeight="medium">
                            {formatCurrency(location.grand_total)}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        )}
      </Box>
    </LocalizationProvider>
  );
};

export default AnalyticsPage;
