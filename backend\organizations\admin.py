from django.contrib import admin
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from django.urls import reverse
from django.db.models import Count
from .models import Organization, Department
from .admin_forms import OrganizationAdminForm, DepartmentAdminForm


@admin.register(Organization)
class OrganizationAdmin(admin.ModelAdmin):
    """Comprehensive Organization admin with all model fields"""

    # Use custom form
    form = OrganizationAdminForm

    # Enhanced list display with more informative columns
    list_display = [
        'logo_thumbnail', 'name', 'short_name', 'status_badge', 'default_badge',
        'contact_info', 'location_summary', 'user_count', 'department_count',
        'created_at'
    ]

    # Comprehensive filtering options
    list_filter = [
        'is_active', 'is_default', 'country', 'state_province', 'city', 'subcity',
        'created_at', 'updated_at', 'established_date', 'document_retention_days'
    ]

    # Extensive search fields covering all text fields
    search_fields = [
        'name', 'short_name', 'email', 'phone', 'fax', 'city', 'state_province',
        'description', 'motto', 'tagline', 'website', 'registration_number',
        'tax_id', 'license_number', 'address_line1', 'address_line2'
    ]

    # Read-only fields including computed properties
    readonly_fields = [
        'created_at', 'updated_at', 'logo_preview', 'full_address',
        'location_hierarchy_display', 'office_hours_display', 'user_count',
        'department_count', 'social_media_display', 'color_preview'
    ]

    # Default ordering
    ordering = ['name']

    # Items per page
    list_per_page = 25

    # Enable date hierarchy
    date_hierarchy = 'created_at'

    # Bulk actions
    actions = ['make_active', 'make_inactive', 'set_as_default']

    # Comprehensive fieldsets with all model fields organized logically
    fieldsets = (
        ('Basic Information', {
            'fields': (
                ('name', 'short_name'),
                ('logo', 'logo_preview'),
                ('is_active', 'is_default'),
                'description'
            ),
            'description': 'Core organization details and status'
        }),

        ('Branding & Identity', {
            'fields': (
                ('motto', 'tagline'),
                'website',
                ('primary_color', 'secondary_color', 'accent_color'),
                'color_preview',
                'social_media',
                'social_media_display'
            ),
            'classes': ('collapse',),
            'description': 'Brand identity, colors, and social media presence'
        }),

        ('Contact Information', {
            'fields': (
                ('email', 'phone', 'fax'),
                'full_address',
                'location_hierarchy_display'
            ),
            'description': 'Communication and contact details'
        }),

        ('Location Hierarchy', {
            'fields': (
                ('country', 'state_province'),
                ('city', 'subcity'),
                'kebele'
            ),
            'description': 'Location hierarchy from geographical location system'
        }),

        ('Address Details', {
            'fields': (
                ('address_line1', 'address_line2'),
                'postal_code'
            ),
            'classes': ('collapse',),
            'description': 'Physical address information'
        }),

        ('Office Hours & Operations', {
            'fields': (
                ('office_hours_start', 'office_hours_end'),
                'office_hours_display',
                'established_date'
            ),
            'classes': ('collapse',),
            'description': 'Operational hours and establishment details'
        }),

        ('Legal & Registration', {
            'fields': (
                ('registration_number', 'tax_id'),
                'license_number'
            ),
            'classes': ('collapse',),
            'description': 'Legal registration and identification numbers'
        }),

        ('Document Management Settings', {
            'fields': (
                ('document_retention_days', 'max_file_size_mb'),
            ),
            'classes': ('collapse',),
            'description': 'System configuration for document handling'
        }),

        ('Tax Collection Settings', {
            'fields': (
                ('individual_penalty_rate', 'individual_interest_rate'),
                ('organization_penalty_rate', 'organization_interest_rate'),
            ),
            'classes': ('collapse',),
            'description': 'Penalty and interest rates for tax collection'
        }),

        ('Statistics & Relationships', {
            'fields': (
                ('user_count', 'department_count'),
            ),
            'classes': ('collapse',),
            'description': 'Organization metrics and related record counts'
        }),

        ('Audit Information', {
            'fields': (
                'created_by',
                ('created_at', 'updated_at')
            ),
            'classes': ('collapse',),
            'description': 'System tracking and audit trail'
        }),
    )

    def get_queryset(self, request):
        """Optimize queryset with annotations for counts"""
        return super().get_queryset(request).annotate(
            user_count=Count('users', distinct=True),
            department_count=Count('departments', distinct=True)
        ).select_related(
            'created_by', 'country', 'state_province',
            'city', 'subcity', 'kebele'
        )

    # Custom display methods for list view
    def logo_thumbnail(self, obj):
        """Small logo thumbnail for list view"""
        if obj.logo:
            return format_html(
                '<img src="{}" width="40" height="40" style="object-fit: cover; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.2);" />',
                obj.logo.url
            )
        return format_html(
            '<div style="width: 40px; height: 40px; background: #f0f0f0; border-radius: 4px; display: flex; align-items: center; justify-content: center; font-size: 12px; color: #666;">No Logo</div>'
        )
    logo_thumbnail.short_description = 'Logo'

    def logo_preview(self, obj):
        """Large logo preview for detail view"""
        if obj.logo:
            return format_html(
                '<img src="{}" width="150" height="150" style="object-fit: cover; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);" />',
                obj.logo.url
            )
        return format_html(
            '<div style="width: 150px; height: 150px; background: #f8f9fa; border: 2px dashed #dee2e6; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #6c757d; font-size: 14px;">No Logo Uploaded</div>'
        )
    logo_preview.short_description = 'Logo Preview'

    def status_badge(self, obj):
        """Status badge with color coding"""
        if obj.is_active:
            return format_html(
                '<span style="background: #28a745; color: white; padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">ACTIVE</span>'
            )
        return format_html(
            '<span style="background: #dc3545; color: white; padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">INACTIVE</span>'
        )
    status_badge.short_description = 'Status'

    def default_badge(self, obj):
        """Default organization badge"""
        if obj.is_default:
            return format_html(
                '<span style="background: #ffc107; color: #212529; padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">⭐ DEFAULT</span>'
            )
        return '-'
    default_badge.short_description = 'Default'

    def contact_info(self, obj):
        """Formatted contact information"""
        info = []
        if obj.email:
            info.append(f'📧 {obj.email}')
        if obj.phone:
            info.append(f'📞 {obj.phone}')
        if obj.website:
            info.append(f'🌐 <a href="{obj.website}" target="_blank">Website</a>')
        return format_html('<br>'.join(info)) if info else '-'
    contact_info.short_description = 'Contact'

    def location_summary(self, obj):
        """Brief location summary using location hierarchy"""
        parts = []

        if obj.city:
            parts.append(obj.city.name)
        if obj.state_province:
            parts.append(obj.state_province.name)
        if obj.country:
            parts.append(obj.country.name)

        location = ', '.join(parts)
        return location if location else '-'
    location_summary.short_description = 'Location'

    def user_count(self, obj):
        """User count with link to users"""
        count = getattr(obj, 'user_count', 0)
        if count > 0:
            try:
                url = reverse('admin:accounts_user_changelist') + f'?organization={obj.id}'
                return format_html('<a href="{}">{} users</a>', url, count)
            except:
                return f'{count} users'
        return '0 users'
    user_count.short_description = 'Users'

    def department_count(self, obj):
        """Department count with link to departments"""
        count = getattr(obj, 'department_count', 0)
        if count > 0:
            try:
                url = reverse('admin:organizations_department_changelist') + f'?organization={obj.id}'
                return format_html('<a href="{}">{} depts</a>', url, count)
            except:
                return f'{count} depts'
        return '0 depts'
    department_count.short_description = 'Departments'

    def office_hours_display(self, obj):
        """Display formatted office hours"""
        try:
            return obj.get_office_hours_display()
        except:
            return f"{obj.office_hours_start} - {obj.office_hours_end}"
    office_hours_display.short_description = 'Office Hours'

    def social_media_display(self, obj):
        """Display social media links"""
        if not obj.social_media:
            return 'No social media links'

        links = []
        for platform, url in obj.social_media.items():
            if url:
                links.append(f'<a href="{url}" target="_blank" style="margin-right: 10px;">{platform.title()}</a>')

        return format_html(' | '.join(links)) if links else 'No social media links'
    social_media_display.short_description = 'Social Media Links'

    def color_preview(self, obj):
        """Display color swatches"""
        colors = [
            ('Primary', obj.primary_color),
            ('Secondary', obj.secondary_color),
            ('Accent', obj.accent_color)
        ]

        swatches = []
        for name, color in colors:
            swatches.append(
                f'<div style="display: inline-block; margin-right: 10px; text-align: center;">'
                f'<div style="width: 30px; height: 30px; background: {color}; border: 1px solid #ccc; border-radius: 4px; margin-bottom: 2px;"></div>'
                f'<small style="font-size: 10px;">{name}</small>'
                f'</div>'
            )

        return format_html(''.join(swatches))
    color_preview.short_description = 'Brand Colors'

    # Admin actions
    def make_active(self, request, queryset):
        """Mark selected organizations as active"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} organizations marked as active.')
    make_active.short_description = 'Mark selected organizations as active'

    def make_inactive(self, request, queryset):
        """Mark selected organizations as inactive"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} organizations marked as inactive.')
    make_inactive.short_description = 'Mark selected organizations as inactive'

    def set_as_default(self, request, queryset):
        """Set selected organization as default (only one allowed)"""
        if queryset.count() != 1:
            self.message_user(
                request,
                'Please select exactly one organization to set as default.',
                level='error'
            )
            return

        # Remove default from all organizations
        Organization.objects.update(is_default=False)
        # Set selected as default
        org = queryset.first()
        org.is_default = True
        org.save()
        self.message_user(request, f'{org.name} is now the default organization.')
    set_as_default.short_description = 'Set as default organization'

    def save_model(self, request, obj, form, change):
        """Custom save logic"""
        if not change:  # Creating new object
            obj.created_by = request.user

        # Ensure only one default organization
        if obj.is_default:
            Organization.objects.exclude(pk=obj.pk).update(is_default=False)

        super().save_model(request, obj, form, change)

    def get_readonly_fields(self, request, obj=None):
        """Dynamic readonly fields based on user permissions"""
        readonly = list(self.readonly_fields)

        # Make created_by readonly for non-superusers
        if not request.user.is_superuser:
            readonly.append('created_by')

        return readonly


@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    """Enhanced Department admin"""

    # Use custom form
    form = DepartmentAdminForm

    # Enhanced list display
    list_display = [
        'name', 'code', 'organization_link', 'head_link', 'status_badge',
        'user_count', 'created_at'
    ]

    # Comprehensive filtering
    list_filter = [
        'organization', 'is_active', 'created_at', 'updated_at'
    ]

    # Enhanced search fields
    search_fields = [
        'name', 'code', 'description', 'organization__name',
        'head__first_name', 'head__last_name', 'head__email'
    ]

    # Read-only fields
    readonly_fields = ['created_at', 'updated_at', 'user_count']

    # Ordering
    ordering = ['organization__name', 'name']

    # Items per page
    list_per_page = 25

    # Date hierarchy
    date_hierarchy = 'created_at'

    # Actions
    actions = ['make_active', 'make_inactive']

    # Enhanced fieldsets
    fieldsets = (
        ('Basic Information', {
            'fields': (
                ('name', 'code'),
                ('organization', 'head'),
                'is_active'
            ),
            'description': 'Core department details'
        }),
        ('Description', {
            'fields': ('description',),
            'classes': ('collapse',),
            'description': 'Detailed department information'
        }),
        ('Statistics', {
            'fields': ('user_count',),
            'classes': ('collapse',),
            'description': 'Department metrics'
        }),
        ('Audit Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',),
            'description': 'System tracking information'
        }),
    )

    def get_queryset(self, request):
        """Optimize queryset with annotations"""
        return super().get_queryset(request).annotate(
            user_count=Count('users', distinct=True)
        ).select_related('organization', 'head')

    def organization_link(self, obj):
        """Link to organization admin"""
        if obj.organization:
            url = reverse('admin:organizations_organization_change', args=[obj.organization.pk])
            return format_html('<a href="{}">{}</a>', url, obj.organization.name)
        return '-'
    organization_link.short_description = 'Organization'

    def head_link(self, obj):
        """Link to department head user admin"""
        if obj.head:
            try:
                url = reverse('admin:accounts_user_change', args=[obj.head.pk])
                return format_html('<a href="{}">{}</a>', url, obj.head.get_full_name() or obj.head.email)
            except:
                return str(obj.head)
        return '-'
    head_link.short_description = 'Department Head'

    def status_badge(self, obj):
        """Status badge with color coding"""
        if obj.is_active:
            return format_html(
                '<span style="background: #28a745; color: white; padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">ACTIVE</span>'
            )
        return format_html(
            '<span style="background: #dc3545; color: white; padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">INACTIVE</span>'
        )
    status_badge.short_description = 'Status'

    def user_count(self, obj):
        """User count with link to users"""
        count = getattr(obj, 'user_count', 0)
        if count > 0:
            try:
                url = reverse('admin:accounts_user_changelist') + f'?department={obj.id}'
                return format_html('<a href="{}">{} users</a>', url, count)
            except:
                return f'{count} users'
        return '0 users'
    user_count.short_description = 'Users'

    # Admin actions
    def make_active(self, request, queryset):
        """Mark selected departments as active"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} departments marked as active.')
    make_active.short_description = 'Mark selected departments as active'

    def make_inactive(self, request, queryset):
        """Mark selected departments as inactive"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} departments marked as inactive.')
    make_inactive.short_description = 'Mark selected departments as inactive'
