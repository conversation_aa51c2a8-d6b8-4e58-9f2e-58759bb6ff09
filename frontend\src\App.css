/* Global styles for Arada DMS */
#root {
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 0;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Loading animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

/* Card hover effects */
.hover-card {
  transition: all 0.2s ease-in-out;
}

.hover-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0,0,0,0.12);
}

/* Status indicators */
.status-active {
  color: #2e7d32;
}

.status-pending {
  color: #ed6c02;
}

.status-error {
  color: #d32f2f;
}

.status-warning {
  color: #f57c00;
}

/* Utility classes */
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.full-height {
  height: 100vh;
}

.center-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}

/* Spin animation for refresh buttons */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
