import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Paper,
  Chip,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  CircularProgress,
  Alert,
  Divider,
} from '@mui/material';
import {
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  AreaChart,
  Area,
} from 'recharts';
import {
  People,
  Business,
  TrendingUp,
  Assessment,
  Category,
  LocationOn,
  Male,
  Female,
  AccountBalance,
} from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import taxpayerService from '../../services/taxpayerService';

interface AnalyticsData {
  overview: {
    total_individuals: number;
    total_organizations: number;
    total_taxpayers: number;
    active_taxpayers: number;
    inactive_taxpayers: number;
  };
  gender_distribution: Array<{
    gender: string;
    count: number;
    percentage: number;
  }>;
  tax_level_distribution: Array<{
    level: string;
    individuals: number;
    organizations: number;
    total: number;
  }>;
  sector_distribution: Array<{
    sector: string;
    individuals: number;
    organizations: number;
    total: number;
  }>;
  registration_trends: Array<{
    month: string;
    individuals: number;
    organizations: number;
    total: number;
  }>;
  kebele_distribution: Array<{
    kebele: string;
    count: number;
    percentage: number;
  }>;
  organization_types: Array<{
    type: string;
    count: number;
    percentage: number;
  }>;
  vat_statistics: {
    total_vat_registered: number;
    vat_percentage: number;
    non_vat_count: number;
  };
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D', '#FFC658', '#FF7C7C'];

const TaxPayerAnalyticsDashboard: React.FC = () => {
  const { showNotification } = useNotification();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState('12months');

  useEffect(() => {
    loadAnalyticsData();
  }, [timeRange]);

  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      // Call the real analytics API
      const response = await taxpayerService.getAnalytics();
      setData(response);
    } catch (error: any) {
      console.error('Failed to load analytics data:', error);
      // Fallback to mock data if API fails
      const mockData: AnalyticsData = {
        overview: {
          total_individuals: 1250,
          total_organizations: 380,
          total_taxpayers: 1630,
          active_taxpayers: 1580,
          inactive_taxpayers: 50,
        },
        gender_distribution: [
          { gender: 'Male', count: 720, percentage: 57.6 },
          { gender: 'Female', count: 480, percentage: 38.4 },
          { gender: 'Other', count: 50, percentage: 4.0 },
        ],
        tax_level_distribution: [
          { level: 'Category A', individuals: 150, organizations: 120, total: 270 },
          { level: 'Category B', individuals: 450, organizations: 180, total: 630 },
          { level: 'Category C', individuals: 650, organizations: 80, total: 730 },
        ],
        sector_distribution: [
          { sector: 'Trade', individuals: 420, organizations: 150, total: 570 },
          { sector: 'Services', individuals: 380, organizations: 120, total: 500 },
          { sector: 'Agriculture', individuals: 280, organizations: 60, total: 340 },
          { sector: 'Manufacturing', individuals: 120, organizations: 40, total: 160 },
          { sector: 'Construction', individuals: 50, organizations: 10, total: 60 },
        ],
        registration_trends: [
          { month: 'Jan', individuals: 85, organizations: 25, total: 110 },
          { month: 'Feb', individuals: 92, organizations: 28, total: 120 },
          { month: 'Mar', individuals: 78, organizations: 32, total: 110 },
          { month: 'Apr', individuals: 105, organizations: 35, total: 140 },
          { month: 'May', individuals: 118, organizations: 42, total: 160 },
          { month: 'Jun', individuals: 95, organizations: 38, total: 133 },
          { month: 'Jul', individuals: 110, organizations: 45, total: 155 },
          { month: 'Aug', individuals: 125, organizations: 48, total: 173 },
          { month: 'Sep', individuals: 102, organizations: 40, total: 142 },
          { month: 'Oct', individuals: 88, organizations: 35, total: 123 },
          { month: 'Nov', individuals: 115, organizations: 52, total: 167 },
          { month: 'Dec', individuals: 132, organizations: 60, total: 192 },
        ],
        kebele_distribution: [
          { kebele: 'Kirkos Kebele 01', count: 45, percentage: 22.5 },
          { kebele: 'Kirkos Kebele 02', count: 38, percentage: 19.0 },
          { kebele: 'Kirkos Kebele 03', count: 32, percentage: 16.0 },
          { kebele: 'Kirkos Kebele 04', count: 28, percentage: 14.0 },
          { kebele: 'Kirkos Kebele 05', count: 25, percentage: 12.5 },
          { kebele: 'Kirkos Kebele 06', count: 18, percentage: 9.0 },
          { kebele: 'Kirkos Kebele 07', count: 14, percentage: 7.0 },
        ],
        organization_types: [
          { type: 'Private Limited Company', count: 180, percentage: 47.4 },
          { type: 'Sole Proprietorship', count: 120, percentage: 31.6 },
          { type: 'Cooperative', count: 50, percentage: 13.2 },
          { type: 'Share Company', count: 20, percentage: 5.3 },
          { type: 'NGO', count: 10, percentage: 2.6 },
        ],
        vat_statistics: {
          total_vat_registered: 220,
          vat_percentage: 57.9,
          non_vat_count: 160,
        },
      };

      setData(mockData);
      setError('Using fallback data - API connection failed');
      showNotification('Using fallback analytics data', 'warning');
    } finally {
      setLoading(false);
    }
  };

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    icon: React.ReactNode;
    color: string;
    subtitle?: string;
  }> = ({ title, value, icon, color, subtitle }) => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography color="textSecondary" gutterBottom variant="h6">
              {title}
            </Typography>
            <Typography variant="h4" component="div" sx={{ color }}>
              {typeof value === 'number' ? value.toLocaleString() : value}
            </Typography>
            {subtitle && (
              <Typography variant="body2" color="textSecondary">
                {subtitle}
              </Typography>
            )}
          </Box>
          <Box sx={{ color, fontSize: '3rem' }}>
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error || !data) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error || 'Failed to load analytics data'}</Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Taxpayer Analytics Dashboard
        </Typography>
        <FormControl size="small" sx={{ minWidth: 150 }}>
          <InputLabel>Time Range</InputLabel>
          <Select
            value={timeRange}
            label="Time Range"
            onChange={(e) => setTimeRange(e.target.value)}
          >
            <MenuItem value="3months">Last 3 Months</MenuItem>
            <MenuItem value="6months">Last 6 Months</MenuItem>
            <MenuItem value="12months">Last 12 Months</MenuItem>
            <MenuItem value="all">All Time</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* Overview Stats */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <StatCard
            title="Total Taxpayers"
            value={data.overview.total_taxpayers}
            icon={<People />}
            color="#1976d2"
            subtitle="All registered taxpayers"
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <StatCard
            title="Individual Taxpayers"
            value={data.overview.total_individuals}
            icon={<People />}
            color="#2e7d32"
            subtitle={`${((data.overview.total_individuals / data.overview.total_taxpayers) * 100).toFixed(1)}% of total`}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <StatCard
            title="Organizations"
            value={data.overview.total_organizations}
            icon={<Business />}
            color="#ed6c02"
            subtitle={`${((data.overview.total_organizations / data.overview.total_taxpayers) * 100).toFixed(1)}% of total`}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <StatCard
            title="Active Taxpayers"
            value={data.overview.active_taxpayers}
            icon={<TrendingUp />}
            color="#9c27b0"
            subtitle={`${((data.overview.active_taxpayers / data.overview.total_taxpayers) * 100).toFixed(1)}% active`}
          />
        </Grid>
      </Grid>

      {/* Charts Row 1 */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Gender Distribution */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Male color="primary" />
                Gender Distribution (Individuals)
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={data.gender_distribution}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percentage }) => `${name}: ${percentage}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="count"
                  >
                    {data.gender_distribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Tax Level Distribution */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Category color="primary" />
                Tax Payer Level Distribution
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={data.tax_level_distribution}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="level" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="individuals" stackId="a" fill="#8884d8" name="Individuals" />
                  <Bar dataKey="organizations" stackId="a" fill="#82ca9d" name="Organizations" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts Row 2 */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Sector Distribution */}
        <Grid size={{ xs: 12, md: 8 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Business color="primary" />
                Business Sector Distribution
              </Typography>
              <ResponsiveContainer width="100%" height={350}>
                <BarChart data={data.sector_distribution} layout="horizontal">
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" />
                  <YAxis dataKey="sector" type="category" width={100} />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="individuals" fill="#8884d8" name="Individuals" />
                  <Bar dataKey="organizations" fill="#82ca9d" name="Organizations" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Organization Types */}
        <Grid size={{ xs: 12, md: 4 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <AccountBalance color="primary" />
                Organization Types
              </Typography>
              <ResponsiveContainer width="100%" height={350}>
                <PieChart>
                  <Pie
                    data={data.organization_types}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ type, percentage }) => `${type}: ${percentage}%`}
                    outerRadius={100}
                    fill="#8884d8"
                    dataKey="count"
                    nameKey="type"
                  >
                    {data.organization_types.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value, name) => [`${value} organizations`, name]}
                    labelFormatter={(label) => `Type: ${label}`}
                  />
                  <Legend
                    formatter={(value, entry) => `${value} (${entry.payload.count})`}
                  />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts Row 3 */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Registration Trends */}
        <Grid size={{ xs: 12 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <TrendingUp color="primary" />
                Registration Trends (Last 12 Months)
              </Typography>
              <ResponsiveContainer width="100%" height={400}>
                <AreaChart data={data.registration_trends}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Area
                    type="monotone"
                    dataKey="individuals"
                    stackId="1"
                    stroke="#8884d8"
                    fill="#8884d8"
                    name="Individuals"
                  />
                  <Area
                    type="monotone"
                    dataKey="organizations"
                    stackId="1"
                    stroke="#82ca9d"
                    fill="#82ca9d"
                    name="Organizations"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts Row 4 */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Kebele Distribution */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <LocationOn color="primary" />
                Kebele Distribution (Kirkos SubCity)
              </Typography>
              <ResponsiveContainer width="100%" height={350}>
                <BarChart data={data.kebele_distribution}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="kebele" angle={-45} textAnchor="end" height={100} />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="count" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* VAT Statistics */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Assessment color="primary" />
                VAT Registration Statistics
              </Typography>
              <Box sx={{ p: 2 }}>
                <Grid container spacing={3}>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Paper sx={{ p: 2, textAlign: 'center', bgcolor: 'success.light', color: 'success.contrastText' }}>
                      <Typography variant="h4">{data.vat_statistics.total_vat_registered}</Typography>
                      <Typography variant="body2">VAT Registered</Typography>
                      <Typography variant="caption">{data.vat_statistics.vat_percentage}% of organizations</Typography>
                    </Paper>
                  </Grid>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Paper sx={{ p: 2, textAlign: 'center', bgcolor: 'warning.light', color: 'warning.contrastText' }}>
                      <Typography variant="h4">{data.vat_statistics.non_vat_count}</Typography>
                      <Typography variant="body2">Non-VAT</Typography>
                      <Typography variant="caption">{(100 - data.vat_statistics.vat_percentage).toFixed(1)}% of organizations</Typography>
                    </Paper>
                  </Grid>
                </Grid>
                <Box sx={{ mt: 3 }}>
                  <ResponsiveContainer width="100%" height={200}>
                    <PieChart>
                      <Pie
                        data={[
                          { name: 'VAT Registered', value: data.vat_statistics.total_vat_registered },
                          { name: 'Non-VAT', value: data.vat_statistics.non_vat_count }
                        ]}
                        cx="50%"
                        cy="50%"
                        outerRadius={60}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        <Cell fill="#4caf50" />
                        <Cell fill="#ff9800" />
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Summary Cards */}
      <Grid container spacing={3}>
        <Grid size={{ xs: 12 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Key Insights
              </Typography>
              <Divider sx={{ mb: 2 }} />
              <Grid container spacing={2}>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <Typography variant="h5" color="primary">
                      {((data.overview.total_individuals / data.overview.total_taxpayers) * 100).toFixed(1)}%
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Individual Taxpayers
                    </Typography>
                  </Box>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <Typography variant="h5" color="secondary">
                      {data.sector_distribution[0]?.sector || 'Trade'}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Largest Business Sector
                    </Typography>
                  </Box>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <Typography variant="h5" color="success.main">
                      {data.kebele_distribution[0]?.kebele || 'Kirkos Kebele 01'}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Top Registration Kebele
                    </Typography>
                  </Box>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <Typography variant="h5" color="warning.main">
                      {data.vat_statistics.vat_percentage.toFixed(1)}%
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      VAT Registration Rate
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default TaxPayerAnalyticsDashboard;
