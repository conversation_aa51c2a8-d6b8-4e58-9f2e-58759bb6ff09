from django.urls import path
from . import views

app_name = 'requests'

urlpatterns = [
    # Document Request endpoints
    path('', views.DocumentRequestListCreateView.as_view(), name='document_request_list_create'),
    path('<uuid:pk>/', views.DocumentRequestDetailView.as_view(), name='document_request_detail'),
    path('<uuid:pk>/approve/', views.RequestApproveView.as_view(), name='request_approve'),
    path('<uuid:pk>/reject/', views.RequestRejectView.as_view(), name='request_reject'),
    path('<uuid:pk>/checkout/', views.RequestCheckoutView.as_view(), name='request_checkout'),
    path('<uuid:pk>/return/', views.RequestReturnView.as_view(), name='request_return'),
    # path('<uuid:pk>/cancel/', views.RequestCancelView.as_view(), name='request_cancel'),
    path('<uuid:pk>/approvals/', views.RequestApprovalsView.as_view(), name='request_approvals'),
    path('stats/', views.RequestStatsView.as_view(), name='request_stats'),
    
    # Request Approval endpoints
    path('approvals/', views.RequestApprovalListView.as_view(), name='request_approval_list'),
    path('<uuid:request_pk>/approvals/', views.RequestApprovalCreateView.as_view(), name='request_approval_create'),
    
    # Audit Log endpoints
    path('audit/', views.AuditLogListView.as_view(), name='audit_log_list'),
    path('audit/<int:pk>/', views.AuditLogDetailView.as_view(), name='audit_log_detail'),
    
    # Notification endpoints
    path('notifications/', views.NotificationListView.as_view(), name='notification_list'),
    path('notifications/<int:pk>/', views.NotificationDetailView.as_view(), name='notification_detail'),
    path('notifications/<int:pk>/mark-read/', views.NotificationMarkReadView.as_view(), name='notification_mark_read'),
    
    # Dashboard and analytics
    path('dashboard/', views.DashboardStatsView.as_view(), name='dashboard_stats'),
    path('user-activity/', views.UserActivityView.as_view(), name='user_activity'),
    path('system-health/', views.SystemHealthView.as_view(), name='system_health'),
]
