from django.db import models
from django.core.validators import RegexValidator
from django.utils import timezone


class Organization(models.Model):
    """
    Organization model for multi-tenant support
    Represents government offices, departments, or agencies
    """

    name = models.Char<PERSON>ield(
        max_length=200,
        unique=True,
        help_text="Official organization name"
    )

    short_name = models.Char<PERSON>ield(
        max_length=50,
        unique=True,
        help_text="Abbreviated organization name"
    )

    logo = models.ImageField(
        upload_to='organizations/logos/',
        null=True,
        blank=True,
        help_text="Organization logo"
    )

    # Branding Information
    motto = models.Char<PERSON>ield(
        max_length=500,
        null=True,
        blank=True,
        help_text="Organization motto or mission statement"
    )

    tagline = models.CharField(
        max_length=200,
        null=True,
        blank=True,
        help_text="Brief tagline or slogan"
    )

    description = models.TextField(
        null=True,
        blank=True,
        help_text="Detailed description of the organization"
    )

    website = models.URLField(
        null=True,
        blank=True,
        help_text="Organization website URL"
    )

    # Contact Information
    email = models.EmailField(
        null=True,
        blank=True,
        help_text="Primary contact email"
    )

    phone = models.CharField(
        max_length=15,
        validators=[RegexValidator(
            regex=r'^\+?1?\d{9,15}$',
            message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed."
        )],
        null=True,
        blank=True,
        help_text="Primary contact phone"
    )

    fax = models.CharField(
        max_length=15,
        validators=[RegexValidator(
            regex=r'^\+?1?\d{9,15}$',
            message="Fax number must be entered in the format: '+999999999'. Up to 15 digits allowed."
        )],
        null=True,
        blank=True,
        help_text="Fax number"
    )

    # Address Information
    address_line1 = models.CharField(max_length=255, null=True, blank=True)
    address_line2 = models.CharField(max_length=255, null=True, blank=True)
    postal_code = models.CharField(max_length=20, null=True, blank=True)

    # Location Hierarchy (Foreign Keys to location hierarchy models)
    country = models.ForeignKey(
        'locations.Country',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Country from location hierarchy",
        related_name='organizations'
    )

    state_province = models.ForeignKey(
        'locations.Region',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Region/State/Province from location hierarchy",
        related_name='organizations'
    )

    city = models.ForeignKey(
        'locations.City',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="City from location hierarchy",
        related_name='organizations'
    )

    subcity = models.ForeignKey(
        'locations.SubCity',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="SubCity/Woreda from location hierarchy",
        related_name='organizations'
    )

    kebele = models.ForeignKey(
        'locations.Kebele',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Kebele from location hierarchy",
        related_name='organizations'
    )

    # Office Hours
    office_hours_start = models.TimeField(
        default=timezone.datetime.strptime('08:00', '%H:%M').time(),
        help_text="Office opening time"
    )
    office_hours_end = models.TimeField(
        default=timezone.datetime.strptime('17:00', '%H:%M').time(),
        help_text="Office closing time"
    )

    # Additional Information
    established_date = models.DateField(
        null=True,
        blank=True,
        help_text="Date when the organization was established"
    )

    registration_number = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        help_text="Official registration number"
    )

    tax_id = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        help_text="Tax identification number"
    )

    license_number = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        help_text="Business license number"
    )

    # Branding Colors
    primary_color = models.CharField(
        max_length=7,
        default='#1976d2',
        help_text="Primary brand color (hex format)"
    )

    secondary_color = models.CharField(
        max_length=7,
        default='#1565c0',
        help_text="Secondary brand color (hex format)"
    )

    accent_color = models.CharField(
        max_length=7,
        default='#ff9800',
        help_text="Accent brand color (hex format)"
    )

    # Social Media
    social_media = models.JSONField(
        default=dict,
        blank=True,
        help_text="Social media links (JSON format)"
    )

    # Settings
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this organization is active"
    )

    is_default = models.BooleanField(
        default=False,
        help_text="Whether this is the default organization for system branding"
    )

    # Document Management Settings
    document_retention_days = models.PositiveIntegerField(
        default=2555,  # 7 years
        help_text="Default document retention period in days"
    )

    max_file_size_mb = models.PositiveIntegerField(
        default=50,
        help_text="Maximum file upload size in MB"
    )

    # Tax Collection Settings
    individual_penalty_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=5.0,
        help_text="Penalty rate percentage for individual taxpayers"
    )

    individual_interest_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=2.0,
        help_text="Monthly interest rate percentage for individual taxpayers"
    )

    organization_penalty_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=10.0,
        help_text="Penalty rate percentage for organization taxpayers"
    )

    organization_interest_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=3.0,
        help_text="Monthly interest rate percentage for organization taxpayers"
    )

    # Audit fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_organizations'
    )

    class Meta:
        db_table = 'organizations_organization'
        verbose_name = 'Organization'
        verbose_name_plural = 'Organizations'
        ordering = ['name']

    def __str__(self):
        return self.name

    @property
    def full_address(self):
        """Return formatted full address using location hierarchy"""
        address_parts = [
            self.address_line1,
            self.address_line2,
            self.kebele.name if self.kebele else None,
            self.subcity.name if self.subcity else None,
            self.city.name if self.city else None,
            self.state_province.name if self.state_province else None,
            self.postal_code,
            self.country.name if self.country else None
        ]
        return ', '.join(filter(None, address_parts))

    @property
    def location_hierarchy_display(self):
        """Return hierarchical location display"""
        parts = []
        if self.country:
            parts.append(self.country.name)
        if self.state_province:
            parts.append(self.state_province.name)
        if self.city:
            parts.append(self.city.name)
        if self.subcity:
            parts.append(self.subcity.name)
        if self.kebele:
            parts.append(f"Kebele {self.kebele.number} ({self.kebele.name})")
        return ' → '.join(parts) if parts else 'No location specified'

    @property
    def country_name(self):
        """Get country name"""
        return self.country.name if self.country else None

    @property
    def region_name(self):
        """Get region/state/province name"""
        return self.state_province.name if self.state_province else None

    @property
    def city_name(self):
        """Get city name"""
        return self.city.name if self.city else None

    @property
    def subcity_name(self):
        """Get subcity name"""
        return self.subcity.name if self.subcity else None

    @property
    def kebele_name(self):
        """Get kebele name"""
        return self.kebele.name if self.kebele else None

    def get_office_hours_display(self):
        """Return formatted office hours"""
        return f"{self.office_hours_start.strftime('%H:%M')} - {self.office_hours_end.strftime('%H:%M')}"

    def set_as_default(self):
        """Set this organization as the default, removing default from others"""
        # Remove default from all other organizations
        Organization.objects.filter(is_default=True).update(is_default=False)
        # Set this organization as default
        self.is_default = True
        self.save(update_fields=['is_default'])

    @classmethod
    def get_default(cls):
        """Get the default organization"""
        try:
            return cls.objects.get(is_default=True)
        except cls.DoesNotExist:
            return None


class Department(models.Model):
    """
    Departments within an organization
    """
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name='departments'
    )

    name = models.CharField(max_length=100)
    code = models.CharField(
        max_length=10,
        help_text="Department code (e.g., HR, FIN, IT)"
    )
    description = models.TextField(null=True, blank=True)

    # Department head
    head = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='headed_departments',
        help_text="Department head/manager"
    )

    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'organizations_department'
        verbose_name = 'Department'
        verbose_name_plural = 'Departments'
        unique_together = ['organization', 'code']
        ordering = ['organization', 'name']

    def __str__(self):
        return f"{self.organization.short_name} - {self.name}"

    def save(self, *args, **kwargs):
        # Convert code to uppercase for case-insensitive storage
        if self.code:
            self.code = self.code.upper()
        super().save(*args, **kwargs)
