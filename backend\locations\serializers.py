from rest_framework import serializers
from .models import Building, Shelf, Box, Kent, FileType, File


class BuildingSerializer(serializers.ModelSerializer):
    """Building serializer"""
    
    organization_name = serializers.CharField(source='organization.name', read_only=True)
    shelf_count = serializers.SerializerMethodField()
    kent_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Building
        fields = [
            'id', 'organization', 'organization_name', 'name', 'code', 
            'description', 'address', 'barcode_image', 'qr_code_image',
            'is_active', 'shelf_count', 'kent_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'barcode_image', 'qr_code_image', 'created_at', 'updated_at']
    
    def get_shelf_count(self, obj):
        """Get number of shelves in building"""
        return obj.shelves.filter(is_active=True).count()
    
    def get_kent_count(self, obj):
        """Get total number of kents in building"""
        return Kent.objects.filter(box__shelf__building=obj, is_active=True).count()


class BuildingCreateSerializer(serializers.ModelSerializer):
    """Building creation serializer"""
    
    class Meta:
        model = Building
        fields = ['organization', 'name', 'code', 'description', 'address']
    
    def validate_code(self, value):
        """Validate building code uniqueness within organization"""
        organization = self.initial_data.get('organization')
        if organization and Building.objects.filter(
            organization=organization, code=value
        ).exists():
            raise serializers.ValidationError(
                "Building code must be unique within the organization"
            )
        return value


class ShelfSerializer(serializers.ModelSerializer):
    """Shelf serializer"""
    
    building_name = serializers.CharField(source='building.name', read_only=True)
    organization_name = serializers.CharField(source='building.organization.name', read_only=True)
    full_code = serializers.ReadOnlyField()
    box_count = serializers.SerializerMethodField()
    kent_count = serializers.SerializerMethodField()
    utilization = serializers.SerializerMethodField()

    class Meta:
        model = Shelf
        fields = [
            'id', 'building', 'building_name', 'organization_name', 'name', 'code',
            'full_code', 'description', 'rows', 'columns', 'capacity', 'barcode_image', 'qr_code_image',
            'is_active', 'box_count', 'kent_count', 'utilization', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'capacity', 'barcode_image', 'qr_code_image', 'created_at', 'updated_at']

    def get_box_count(self, obj):
        """Get number of boxes on shelf"""
        return obj.boxes.filter(is_active=True).count()

    def get_kent_count(self, obj):
        """Get total number of kents on shelf (through boxes)"""
        return Kent.objects.filter(box__shelf=obj, is_active=True).count()

    def get_utilization(self, obj):
        """Get shelf utilization percentage based on boxes"""
        if obj.capacity:
            box_count = obj.boxes.filter(is_active=True).count()
            return round((box_count / obj.capacity) * 100, 1)
        return None


class ShelfCreateSerializer(serializers.ModelSerializer):
    """Shelf creation serializer"""
    
    class Meta:
        model = Shelf
        fields = ['building', 'name', 'code', 'description', 'rows', 'columns']
    
    def validate_code(self, value):
        """Validate shelf code uniqueness within building"""
        building = self.initial_data.get('building')
        if building and Shelf.objects.filter(
            building=building, code=value
        ).exists():
            raise serializers.ValidationError(
                "Shelf code must be unique within the building"
            )
        return value


class BoxSerializer(serializers.ModelSerializer):
    """Box (Position) serializer"""

    shelf_name = serializers.CharField(source='shelf.name', read_only=True)
    building_name = serializers.CharField(source='shelf.building.name', read_only=True)
    organization_name = serializers.CharField(source='shelf.building.organization.name', read_only=True)
    full_code = serializers.ReadOnlyField()
    position_code = serializers.ReadOnlyField()
    kent_count = serializers.SerializerMethodField()
    file_count = serializers.SerializerMethodField()
    document_count = serializers.SerializerMethodField()

    class Meta:
        model = Box
        fields = [
            'id', 'shelf', 'shelf_name', 'building_name', 'organization_name',
            'row', 'column', 'position_code', 'full_code', 'name', 'description',
            'color', 'material', 'barcode_image', 'qr_code_image',
            'is_active', 'kent_count', 'file_count', 'document_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'position_code', 'full_code', 'barcode_image', 'qr_code_image', 'created_at', 'updated_at']

    def get_kent_count(self, obj):
        """Get number of kents in box"""
        return obj.get_kent_count()

    def get_file_count(self, obj):
        """Get total number of files in box"""
        return obj.get_file_count()

    def get_document_count(self, obj):
        """Get total number of documents in box"""
        return obj.get_document_count()

    def validate(self, data):
        """Validate box position within shelf limits"""
        shelf = data.get('shelf')
        row = data.get('row')
        column = data.get('column')

        if shelf and row and column:
            if row > shelf.rows:
                raise serializers.ValidationError(
                    f"Row {row} exceeds shelf capacity of {shelf.rows} rows"
                )
            if column > shelf.columns:
                raise serializers.ValidationError(
                    f"Column {column} exceeds shelf capacity of {shelf.columns} columns"
                )

        return data


class BoxCreateSerializer(serializers.ModelSerializer):
    """Box creation serializer"""

    class Meta:
        model = Box
        fields = ['shelf', 'row', 'column', 'name', 'description', 'color', 'material']

    def validate(self, data):
        """Validate box position uniqueness and shelf limits"""
        shelf = data.get('shelf')
        row = data.get('row')
        column = data.get('column')

        if shelf and row and column:
            # Check if position already exists
            if Box.objects.filter(shelf=shelf, row=row, column=column).exists():
                raise serializers.ValidationError(
                    f"Position R{row:02d}C{column:02d} is already occupied in this shelf"
                )

            # Check shelf limits
            if row > shelf.rows:
                raise serializers.ValidationError(
                    f"Row {row} exceeds shelf capacity of {shelf.rows} rows"
                )
            if column > shelf.columns:
                raise serializers.ValidationError(
                    f"Column {column} exceeds shelf capacity of {shelf.columns} columns"
                )

        return data


class FileTypeSerializer(serializers.ModelSerializer):
    """FileType serializer"""

    file_count = serializers.SerializerMethodField()
    default_document_types_list = serializers.ReadOnlyField(source='get_default_document_types_list')

    class Meta:
        model = FileType
        fields = [
            'id', 'name', 'code', 'description', 'color', 'icon',
            'requires_business_name', 'requires_tin_number', 'requires_license_number', 'requires_owner_name',
            'default_document_types', 'default_document_types_list', 'file_count',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'file_count', 'default_document_types_list', 'created_at', 'updated_at']

    def get_file_count(self, obj):
        """Get number of files using this file type"""
        return obj.get_file_count()


class FileTypeCreateSerializer(serializers.ModelSerializer):
    """FileType creation serializer"""

    class Meta:
        model = FileType
        fields = [
            'name', 'code', 'description', 'color', 'icon',
            'requires_business_name', 'requires_tin_number', 'requires_license_number', 'requires_owner_name',
            'default_document_types'
        ]

    def validate_code(self, value):
        """Validate file type code uniqueness"""
        if FileType.objects.filter(code=value).exists():
            raise serializers.ValidationError(
                "File type code must be unique"
            )
        return value


class KentSerializer(serializers.ModelSerializer):
    """Kent serializer"""

    box_position = serializers.CharField(source='box.position_code', read_only=True)
    shelf_name = serializers.CharField(source='box.shelf.name', read_only=True)
    building_name = serializers.CharField(source='box.shelf.building.name', read_only=True)
    organization_name = serializers.CharField(source='box.shelf.building.organization.name', read_only=True)
    full_code = serializers.ReadOnlyField()
    location_path = serializers.ReadOnlyField()
    position_code = serializers.ReadOnlyField()
    file_count = serializers.SerializerMethodField()
    document_count = serializers.SerializerMethodField()
    utilization = serializers.SerializerMethodField()
    is_full = serializers.ReadOnlyField()

    class Meta:
        model = Kent
        fields = [
            'id', 'box', 'box_position', 'shelf_name', 'building_name', 'organization_name',
            'name', 'code', 'full_code', 'location_path', 'description',
            'position_code', 'capacity', 'color', 'material',
            'barcode_image', 'qr_code_image', 'is_active', 'file_count', 'document_count',
            'utilization', 'is_full', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'position_code', 'barcode_image', 'qr_code_image', 'created_at', 'updated_at']
    
    def get_file_count(self, obj):
        """Get number of files in kent"""
        return obj.get_file_count()

    def get_document_count(self, obj):
        """Get number of documents in kent"""
        return obj.get_document_count()

    def get_utilization(self, obj):
        """Get kent utilization percentage based on files"""
        if obj.capacity:
            file_count = obj.get_file_count()
            return round((file_count / obj.capacity) * 100, 1)
        return None


class KentCreateSerializer(serializers.ModelSerializer):
    """Kent creation serializer"""

    class Meta:
        model = Kent
        fields = ['box', 'name', 'code', 'description', 'capacity', 'color', 'material']

    def validate_code(self, value):
        """Validate kent code uniqueness within box"""
        box = self.initial_data.get('box')
        if box and Kent.objects.filter(
            box=box, code=value
        ).exists():
            raise serializers.ValidationError(
                "Kent code must be unique within the box"
            )
        return value


class LocationHierarchySerializer(serializers.ModelSerializer):
    """Complete location hierarchy serializer"""
    
    shelves = ShelfSerializer(many=True, read_only=True)
    
    class Meta:
        model = Building
        fields = ['id', 'name', 'code', 'description', 'shelves']


class ShelfWithBoxesSerializer(serializers.ModelSerializer):
    """Shelf serializer with boxes and kents"""

    boxes = BoxSerializer(many=True, read_only=True)
    full_code = serializers.ReadOnlyField()

    class Meta:
        model = Shelf
        fields = ['id', 'name', 'code', 'full_code', 'description', 'capacity', 'boxes']


class BoxWithKentsSerializer(serializers.ModelSerializer):
    """Box serializer with kents"""

    kents = KentSerializer(many=True, read_only=True)
    position_code = serializers.ReadOnlyField()
    full_code = serializers.ReadOnlyField()

    class Meta:
        model = Box
        fields = ['id', 'row', 'column', 'position_code', 'full_code', 'name', 'description', 'kents']


class LocationSummarySerializer(serializers.Serializer):
    """Location summary for dashboard"""

    total_buildings = serializers.IntegerField()
    total_shelves = serializers.IntegerField()
    total_boxes = serializers.IntegerField()
    total_kents = serializers.IntegerField()
    total_capacity = serializers.IntegerField()
    used_capacity = serializers.IntegerField()
    utilization_percentage = serializers.FloatField()


class FileSerializer(serializers.ModelSerializer):
    """File serializer"""

    kent_location = serializers.SerializerMethodField()
    kent_code = serializers.SerializerMethodField()
    building_name = serializers.SerializerMethodField()
    shelf_name = serializers.SerializerMethodField()
    organization_name = serializers.SerializerMethodField()
    file_type_name = serializers.SerializerMethodField()
    file_type_code = serializers.SerializerMethodField()
    file_type_color = serializers.SerializerMethodField()
    file_type_icon = serializers.SerializerMethodField()
    full_code = serializers.ReadOnlyField()
    location_path = serializers.ReadOnlyField()
    document_count = serializers.SerializerMethodField()
    document_types = serializers.SerializerMethodField()
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)

    linked_taxpayer = serializers.ReadOnlyField()

    class Meta:
        model = File
        fields = [
            'id', 'kent', 'kent_location', 'kent_code', 'building_name', 'shelf_name', 'organization_name',
            'name', 'file_number', 'full_code', 'location_path', 'file_type', 'file_type_name', 'file_type_code',
            'file_type_color', 'file_type_icon', 'description', 'status',
            'business_name', 'tin_number', 'vat_number', 'owner_name', 'contact_phone', 'contact_email', 'address',
            'tags', 'created_date', 'last_activity_date', 'document_count', 'document_types',
            'barcode_image', 'qr_code_image', 'is_active', 'created_by', 'created_by_name',
            'linked_individual_taxpayer', 'linked_organization_taxpayer', 'linked_taxpayer',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'full_code', 'location_path', 'barcode_image', 'qr_code_image', 'linked_taxpayer', 'created_at', 'updated_at']

    def get_kent_location(self, obj):
        """Get kent location path safely"""
        try:
            return obj.kent.location_path if obj.kent else None
        except:
            return None

    def get_kent_code(self, obj):
        """Get kent full code safely"""
        try:
            return obj.kent.full_code if obj.kent else None
        except:
            return None

    def get_building_name(self, obj):
        """Get building name safely"""
        try:
            return obj.kent.box.shelf.building.name if obj.kent and obj.kent.box and obj.kent.box.shelf and obj.kent.box.shelf.building else None
        except:
            return None

    def get_shelf_name(self, obj):
        """Get shelf name safely"""
        try:
            return obj.kent.box.shelf.name if obj.kent and obj.kent.box and obj.kent.box.shelf else None
        except:
            return None

    def get_organization_name(self, obj):
        """Get organization name safely"""
        try:
            return obj.kent.box.shelf.building.organization.name if obj.kent and obj.kent.box and obj.kent.box.shelf and obj.kent.box.shelf.building and obj.kent.box.shelf.building.organization else None
        except:
            return None

    def get_file_type_name(self, obj):
        """Get file type name safely"""
        try:
            return obj.file_type.name if obj.file_type else None
        except:
            return None

    def get_file_type_code(self, obj):
        """Get file type code safely"""
        try:
            return obj.file_type.code if obj.file_type else None
        except:
            return None

    def get_file_type_color(self, obj):
        """Get file type color safely"""
        try:
            return obj.file_type.color if obj.file_type else None
        except:
            return None

    def get_file_type_icon(self, obj):
        """Get file type icon safely"""
        try:
            return obj.file_type.icon if obj.file_type else None
        except:
            return None

    def get_document_count(self, obj):
        """Get number of documents in file"""
        return obj.get_document_count()

    def get_document_types(self, obj):
        """Get document types in file"""
        return obj.get_document_types()
        return obj.get_document_types()


class FileCreateSerializer(serializers.ModelSerializer):
    """File creation serializer"""

    class Meta:
        model = File
        fields = [
            'kent', 'name', 'file_number', 'file_type', 'description', 'status',
            'business_name', 'tin_number', 'vat_number', 'owner_name',
            'contact_phone', 'contact_email', 'address', 'tags', 'created_date'
        ]

    def validate_file_number(self, value):
        """Validate file number uniqueness within kent"""
        kent = self.initial_data.get('kent')
        if kent:
            queryset = File.objects.filter(kent=kent, file_number=value)
            # Exclude current instance during updates
            if self.instance:
                queryset = queryset.exclude(pk=self.instance.pk)
            if queryset.exists():
                raise serializers.ValidationError(
                    "File number must be unique within the kent"
                )
        return value


class FileWithDocumentsSerializer(serializers.ModelSerializer):
    """File serializer with documents"""

    documents = serializers.SerializerMethodField()
    kent_location = serializers.CharField(source='kent.location_path', read_only=True)
    full_code = serializers.ReadOnlyField()
    location_path = serializers.ReadOnlyField()
    document_count = serializers.SerializerMethodField()

    class Meta:
        model = File
        fields = [
            'id', 'name', 'file_number', 'full_code', 'location_path', 'kent_location',
            'file_type', 'business_name', 'status', 'document_count', 'documents'
        ]

    def get_documents(self, obj):
        """Get documents in file"""
        from documents.serializers import DocumentSerializer
        return DocumentSerializer(obj.documents.filter(is_active=True), many=True).data

    def get_document_count(self, obj):
        """Get number of documents in file"""
        return obj.get_document_count()


class LocationSearchSerializer(serializers.Serializer):
    """Location search serializer"""

    query = serializers.CharField(max_length=100)
    location_type = serializers.ChoiceField(
        choices=['building', 'shelf', 'kent', 'file', 'all'],
        default='all'
    )
    organization = serializers.IntegerField(required=False)
