import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  InputAdornment,
  Typography,
  Menu,
  Alert,
  Grid,
} from '@mui/material';
import {
  Add,
  Search,
  Edit,
  Delete,
  MoreVert,
  Home,
  LocationCity,
} from '@mui/icons-material';
import { useNotification } from '../../../contexts/NotificationContext';
import locationHierarchyService from '../../../services/locationHierarchyService';
import type {
  SubCity,
  SubCityCreate,
  CitySelect
} from '../../../services/locationHierarchyService';

interface SubCitiesTabProps {
  onStatsChange: () => void;
}

const SubCitiesTab: React.FC<SubCitiesTabProps> = ({ onStatsChange }) => {
  const { showSuccess, showError } = useNotification();

  const [subCities, setSubCities] = useState<SubCity[]>([]);
  const [cities, setCities] = useState<CitySelect[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCity, setSelectedCity] = useState<number | ''>('');
  const [selectedType, setSelectedType] = useState<string>('');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingSubCity, setEditingSubCity] = useState<SubCity | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedSubCity, setSelectedSubCity] = useState<SubCity | null>(null);
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);

  const [formData, setFormData] = useState<SubCityCreate>({
    city: 0,
    name: '',
    code: '',
    type: 'subcity',
    population: undefined,
    area_km2: undefined,
    is_active: true,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const typeOptions = locationHierarchyService.getSubCityTypeOptions();

  useEffect(() => {
    loadCities();
    loadSubCities();
  }, []);

  useEffect(() => {
    loadSubCities();
  }, [searchTerm, selectedCity, selectedType]);

  const loadCities = async () => {
    try {
      const response = await locationHierarchyService.getCitiesSelect();
      setCities(response);
    } catch (error) {
      showError('Failed to load cities');
    }
  };

  const loadSubCities = async () => {
    try {
      setLoading(true);
      const response = await locationHierarchyService.getSubCities({
        search: searchTerm,
        city: selectedCity || undefined,
        type: selectedType || undefined,
        page_size: 100,
      });
      setSubCities(response.results);
    } catch (error) {
      showError('Failed to load subcities');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (subCity?: SubCity) => {
    if (subCity) {
      setEditingSubCity(subCity);
      setFormData({
        city: subCity.city,
        name: subCity.name,
        code: subCity.code,
        type: subCity.type,
        population: subCity.population,
        area_km2: subCity.area_km2,
        is_active: subCity.is_active,
      });
    } else {
      setEditingSubCity(null);
      setFormData({
        city: selectedCity as number || 2,
        name: '',
        code: '',
        type: 'subcity',
        population: undefined,
        area_km2: undefined,
        is_active: true,
      });
    }
    setErrors({});
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingSubCity(null);
    setErrors({});
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.city) {
      newErrors.city = 'City is required';
    }
    if (!formData.name.trim()) {
      newErrors.name = 'SubCity/Woreda name is required';
    }
    if (!formData.code.trim()) {
      newErrors.code = 'SubCity/Woreda code is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      if (editingSubCity) {
        await locationHierarchyService.updateSubCity(editingSubCity.id, formData);
        showSuccess('SubCity/Woreda updated successfully');
      } else {
        await locationHierarchyService.createSubCity(formData);
        showSuccess('SubCity/Woreda created successfully');
      }

      handleCloseDialog();
      loadSubCities();
      onStatsChange();
    } catch (error: any) {
      showError(error.response?.data?.detail || 'Failed to save subcity/woreda');
    }
  };

  const handleDelete = async () => {
    if (!selectedSubCity) return;

    try {
      await locationHierarchyService.deleteSubCity(selectedSubCity.id);
      showSuccess('SubCity/Woreda deleted successfully');
      setDeleteDialogOpen(false);
      setSelectedSubCity(null);
      loadSubCities();
      onStatsChange();
    } catch (error: any) {
      showError(error.response?.data?.detail || 'Failed to delete subcity/woreda');
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, subCity: SubCity) => {
    setMenuAnchor(event.currentTarget);
    setSelectedSubCity(subCity);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
    setSelectedSubCity(null);
  };

  const handleInputChange = (field: keyof SubCityCreate) => (
    event: React.ChangeEvent<HTMLInputElement> | { target: { value: unknown } }
  ) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">SubCities/Woredas Management</Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => handleOpenDialog()}
          disabled={cities.length === 0}
        >
          Add SubCity/Woreda
        </Button>
      </Box>

      {cities.length === 0 && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          No cities available. Please create cities first before adding subcities/woredas.
        </Alert>
      )}

      {/* Search and Filters */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid size={{ xs: 12, md: 3 }}>
          <TextField
            fullWidth
            placeholder="Search subcities/woredas..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
          />
        </Grid>
        <Grid size={{ xs: 12, md: 3 }}>
          <FormControl fullWidth>
            <InputLabel>Filter by City</InputLabel>
            <Select
              value={selectedCity}
              onChange={(e) => setSelectedCity(e.target.value as number)}
              label="Filter by City"
            >
              <MenuItem value="">All Cities</MenuItem>
              {cities.map((city) => (
                <MenuItem key={city.id} value={city.id}>
                  {city.name} ({city.code})
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        <Grid size={{ xs: 12, md: 3 }}>
          <FormControl fullWidth>
            <InputLabel>Filter by Type</InputLabel>
            <Select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value as string)}
              label="Filter by Type"
            >
              <MenuItem value="">All Types</MenuItem>
              {typeOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
      </Grid>

      {/* SubCities Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>Code</TableCell>
              <TableCell>Type</TableCell>
              <TableCell>City</TableCell>
              <TableCell>Population</TableCell>
              <TableCell>Kebeles</TableCell>
              <TableCell>Status</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {subCities.map((subCity) => (
              <TableRow key={subCity.id}>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Home fontSize="small" />
                    {subCity.name}
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip label={subCity.code} size="small" />
                </TableCell>
                <TableCell>
                  <Chip
                    label={subCity.type_display}
                    size="small"
                    color="secondary"
                  />
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <LocationCity fontSize="small" />
                    {subCity.city_name}
                  </Box>
                </TableCell>
                <TableCell>
                  {subCity.population ? subCity.population.toLocaleString() : '-'}
                </TableCell>
                <TableCell>
                  <Chip label={subCity.kebele_count} size="small" color="primary" />
                </TableCell>
                <TableCell>
                  <Chip
                    label={subCity.is_active ? 'Active' : 'Inactive'}
                    color={subCity.is_active ? 'success' : 'default'}
                    size="small"
                  />
                </TableCell>
                <TableCell align="right">
                  <IconButton
                    onClick={(e) => handleMenuOpen(e, subCity)}
                    size="small"
                  >
                    <MoreVert />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Action Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
      >
        <MenuItem
          onClick={() => {
            handleOpenDialog(selectedSubCity!);
            handleMenuClose();
          }}
        >
          <Edit fontSize="small" sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        <MenuItem
          onClick={() => {
            setDeleteDialogOpen(true);
            handleMenuClose();
          }}
          sx={{ color: 'error.main' }}
        >
          <Delete fontSize="small" sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>

      {/* Create/Edit Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingSubCity ? 'Edit SubCity/Woreda' : 'Add New SubCity/Woreda'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required error={!!errors.city}>
                <InputLabel>City</InputLabel>
                <Select
                  value={formData.city || ''}
                  onChange={handleInputChange('city')}
                  label="City"
                >
                  {cities.map((city) => (
                    <MenuItem key={city.id} value={city.id}>
                      {city.name} ({city.code})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>Type</InputLabel>
                <Select
                  value={formData.type}
                  onChange={handleInputChange('type')}
                  label="Type"
                >
                  {typeOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Name"
                value={formData.name}
                onChange={handleInputChange('name')}
                error={!!errors.name}
                helperText={errors.name}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Code"
                value={formData.code}
                onChange={handleInputChange('code')}
                error={!!errors.code}
                helperText={errors.code}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Population"
                type="number"
                value={formData.population || ''}
                onChange={handleInputChange('population')}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Area (km²)"
                type="number"
                value={formData.area_km2 || ''}
                onChange={handleInputChange('area_km2')}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.is_active}
                    onChange={handleInputChange('is_active')}
                  />
                }
                label="Active"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSubmit} variant="contained">
            {editingSubCity ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 2 }}>
            Are you sure you want to delete "{selectedSubCity?.name}"? This action cannot be undone.
          </Alert>
          {selectedSubCity && selectedSubCity.kebele_count > 0 && (
            <Alert severity="error">
              This subcity/woreda has {selectedSubCity.kebele_count} kebeles.
              Please delete or reassign them first.
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleDelete}
            color="error"
            variant="contained"
            disabled={selectedSubCity ? selectedSubCity.kebele_count > 0 : false}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SubCitiesTab;
