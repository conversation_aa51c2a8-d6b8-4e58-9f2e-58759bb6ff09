from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import (
    BusinessSector, BusinessSubSector, TaxPayerLevel,
    OrganizationBusinessType, IndividualTaxPayer, OrganizationTaxPayer,
    DailyIncomeAnalysis, LevelUpgradeNotification
)


@admin.register(BusinessSector)
class BusinessSectorAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'sub_sectors_count', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['code', 'name', 'description']
    ordering = ['code']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        (None, {
            'fields': ('code', 'name', 'description', 'is_active')
        }),
        ('Metadata', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def sub_sectors_count(self, obj):
        return obj.sub_sectors.count()
    sub_sectors_count.short_description = 'Sub-Sectors'
    
    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(BusinessSubSector)
class BusinessSubSectorAdmin(admin.ModelAdmin):
    list_display = ['full_code', 'name', 'business_sector', 'is_active', 'created_at']
    list_filter = ['business_sector', 'is_active', 'created_at']
    search_fields = ['code', 'name', 'description', 'business_sector__name']
    ordering = ['business_sector__code', 'code']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        (None, {
            'fields': ('business_sector', 'code', 'name', 'description', 'is_active')
        }),
        ('Metadata', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(TaxPayerLevel)
class TaxPayerLevelAdmin(admin.ModelAdmin):
    list_display = [
        'code', 'name', 'priority', 'daily_income_range', 'tax_rate_percentage',
        'requires_annual_assessment', 'is_active', 'created_at'
    ]
    list_filter = ['is_active', 'requires_annual_assessment', 'priority', 'created_at']
    search_fields = ['code', 'name', 'description']
    ordering = ['priority', 'code']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('Basic Information', {
            'fields': ('code', 'name', 'description', 'priority', 'is_active')
        }),
        ('Daily Income Assessment', {
            'fields': ('min_daily_income', 'max_daily_income', 'tax_rate_percentage', 'requires_annual_assessment')
        }),
        ('Legacy Annual Turnover (Optional)', {
            'fields': ('minimum_annual_turnover', 'maximum_annual_turnover'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def daily_income_range(self, obj):
        """Display daily income range"""
        min_income = f"${obj.min_daily_income:,.2f}"
        if obj.max_daily_income:
            max_income = f"${obj.max_daily_income:,.2f}"
            return f"{min_income} - {max_income}"
        return f"{min_income}+"
    daily_income_range.short_description = 'Daily Income Range'

    def turnover_range(self, obj):
        if obj.minimum_annual_turnover and obj.maximum_annual_turnover:
            return f"{obj.minimum_annual_turnover:,.2f} - {obj.maximum_annual_turnover:,.2f}"
        elif obj.minimum_annual_turnover:
            return f"≥ {obj.minimum_annual_turnover:,.2f}"
        elif obj.maximum_annual_turnover:
            return f"≤ {obj.maximum_annual_turnover:,.2f}"
        return "Not specified"
    turnover_range.short_description = 'Turnover Range'


@admin.register(OrganizationBusinessType)
class OrganizationBusinessTypeAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'requires_vat_registration', 'is_active', 'created_at']
    list_filter = ['requires_vat_registration', 'is_active', 'created_at']
    search_fields = ['code', 'name', 'description']
    ordering = ['name']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        (None, {
            'fields': ('code', 'name', 'description', 'requires_vat_registration', 'is_active')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


class BaseTaxPayerAdmin(admin.ModelAdmin):
    """Base admin class for tax payers"""
    readonly_fields = ['registration_date', 'last_updated']
    list_filter = [
        'tax_payer_level', 
        'business_sector', 
        'is_active', 
        'registration_date',
        'business_registration_date'
    ]
    search_fields = ['tin']
    date_hierarchy = 'registration_date'
    
    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)
    
    def tax_file_link(self, obj):
        if obj.tax_file:
            url = reverse('admin:locations_file_change', args=[obj.tax_file.pk])
            return format_html('<a href="{}">{}</a>', url, obj.tax_file.name)
        return "No file attached"
    tax_file_link.short_description = 'Tax File'


@admin.register(IndividualTaxPayer)
class IndividualTaxPayerAdmin(BaseTaxPayerAdmin):
    list_display = [
        'tin', 'get_full_name', 'nationality', 'gender', 
        'tax_payer_level', 'business_sector', 'is_active'
    ]
    search_fields = ['tin', 'first_name', 'last_name', 'business_name']
    
    fieldsets = (
        ('Personal Information', {
            'fields': (
                'first_name', 'middle_name', 'last_name', 
                'nationality', 'gender', 'date_of_birth', 'profile_picture'
            )
        }),
        ('Tax Information', {
            'fields': (
                'tin', 'tax_payer_level', 'business_sector', 'business_sub_sector',
                'business_registration_date'
            )
        }),
        ('Business Information', {
            'fields': ('business_name', 'business_license_number'),
            'classes': ('collapse',)
        }),
        ('Contact Information', {
            'fields': (
                'phone', 'phone_secondary', 'email',
                'subcity', 'kebele', 'house_number', 'street_address', 'postal_code'
            )
        }),
        ('File Management', {
            'fields': ('tax_file',),
        }),
        ('Status & Metadata', {
            'fields': ('is_active', 'created_by', 'registration_date', 'last_updated'),
            'classes': ('collapse',)
        }),
    )
    
    def profile_image(self, obj):
        if obj.profile_picture:
            return mark_safe(f'<img src="{obj.profile_picture.url}" width="50" height="50" />')
        return "No image"
    profile_image.short_description = 'Photo'


@admin.register(OrganizationTaxPayer)
class OrganizationTaxPayerAdmin(BaseTaxPayerAdmin):
    list_display = [
        'tin', 'business_name', 'organization_business_type', 
        'tax_payer_level', 'vat_number', 'is_active'
    ]
    search_fields = ['tin', 'business_name', 'trade_name', 'vat_number']
    
    fieldsets = (
        ('Organization Information', {
            'fields': (
                'business_name', 'trade_name', 'organization_business_type',
                'business_license_number', 'capital_amount', 'number_of_employees'
            )
        }),
        ('Tax Information', {
            'fields': (
                'tin', 'tax_payer_level', 'business_sector', 'business_sub_sector',
                'business_registration_date'
            )
        }),
        ('VAT Information', {
            'fields': ('vat_registration_date', 'vat_number'),
        }),
        ('Manager/Owner Information', {
            'fields': (
                'manager_first_name', 'manager_middle_name', 'manager_last_name',
                'manager_title'
            )
        }),
        ('Contact Information', {
            'fields': (
                'phone', 'phone_secondary', 'email',
                'subcity', 'kebele', 'house_number', 'street_address', 'postal_code'
            )
        }),
        ('File Management', {
            'fields': ('tax_file',),
        }),
        ('Status & Metadata', {
            'fields': ('is_active', 'created_by', 'registration_date', 'last_updated'),
            'classes': ('collapse',)
        }),
    )
    
    def get_manager_name(self, obj):
        return obj.get_manager_full_name()
    get_manager_name.short_description = 'Manager/Owner'


@admin.register(DailyIncomeAnalysis)
class DailyIncomeAnalysisAdmin(admin.ModelAdmin):
    list_display = [
        'analysis_year', 'taxpayer_name_display', 'taxpayer_type',
        'average_daily_income', 'current_level', 'recommended_level',
        'status', 'requires_upgrade', 'created_at'
    ]
    list_filter = [
        'analysis_year', 'taxpayer_type', 'status',
        'current_level', 'recommended_level', 'created_at'
    ]
    search_fields = ['taxpayer_id', 'review_notes']
    ordering = ['-analysis_year', '-created_at']
    readonly_fields = ['created_at', 'updated_at', 'taxpayer_name_display']

    fieldsets = (
        ('Analysis Information', {
            'fields': ('analysis_year', 'analysis_date', 'taxpayer_type', 'taxpayer_id', 'taxpayer_name_display')
        }),
        ('Income Data', {
            'fields': ('total_annual_income', 'average_daily_income', 'working_days')
        }),
        ('Level Assessment', {
            'fields': ('current_level', 'recommended_level', 'status')
        }),
        ('Review Information', {
            'fields': ('reviewed_by', 'reviewed_at', 'review_notes')
        }),
        ('Metadata', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def taxpayer_name_display(self, obj):
        """Display taxpayer name"""
        return obj.taxpayer_name
    taxpayer_name_display.short_description = 'Taxpayer Name'

    def requires_upgrade(self, obj):
        """Display if upgrade is required"""
        if obj.requires_level_upgrade:
            return format_html('<span style="color: green;">✓ Yes</span>')
        return format_html('<span style="color: gray;">✗ No</span>')
    requires_upgrade.short_description = 'Requires Upgrade'

    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(LevelUpgradeNotification)
class LevelUpgradeNotificationAdmin(admin.ModelAdmin):
    list_display = [
        'title', 'analysis_year', 'taxpayer_name_display',
        'current_to_recommended', 'is_read', 'is_dismissed', 'created_at'
    ]
    list_filter = ['is_read', 'is_dismissed', 'analysis__analysis_year', 'created_at']
    search_fields = ['title', 'message', 'analysis__taxpayer_id']
    ordering = ['-created_at']
    readonly_fields = ['created_at', 'read_at', 'dismissed_at', 'taxpayer_name_display', 'analysis_year']

    fieldsets = (
        ('Notification Details', {
            'fields': ('title', 'message', 'analysis')
        }),
        ('Status', {
            'fields': ('is_read', 'read_at', 'is_dismissed', 'dismissed_at')
        }),
        ('Related Analysis', {
            'fields': ('taxpayer_name_display', 'analysis_year', 'current_to_recommended'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )

    def taxpayer_name_display(self, obj):
        """Display taxpayer name from analysis"""
        return obj.analysis.taxpayer_name
    taxpayer_name_display.short_description = 'Taxpayer Name'

    def analysis_year(self, obj):
        """Display analysis year"""
        return obj.analysis.analysis_year
    analysis_year.short_description = 'Analysis Year'

    def current_to_recommended(self, obj):
        """Display level change"""
        return f"{obj.analysis.current_level.code} → {obj.analysis.recommended_level.code}"
    current_to_recommended.short_description = 'Level Change'
