import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Typography,
  Alert,
  CircularProgress,
  Divider,
  Checkbox,
  FormControlLabel,
} from '@mui/material';
import {
  Business,
  Save,
  Cancel,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { useNotification } from '../../../contexts/NotificationContext';
// Import the service
import taxpayerService from '../../../services/taxpayerService';

// Define interfaces locally to avoid import issues
interface OrganizationTaxPayer {
  id: string;
  tin: string;
  business_name: string;
  trade_name: string;
  display_name: string;
  organization_business_type: string;
  organization_business_type_name: string;
  tax_payer_level: string;
  tax_payer_level_name: string;
  business_sector: string;
  business_sector_name: string;
  business_sub_sector: string;
  business_sub_sector_name: string;
  business_registration_date: string;
  business_license_number: string;
  capital_amount?: number;
  number_of_employees?: number;
  manager_first_name: string;
  manager_middle_name: string;
  manager_last_name: string;
  manager_title: string;
  manager_full_name: string;
  vat_registration_date?: string;
  vat_number?: string;
  phone: string;
  phone_secondary: string;
  email: string;
  country: string;
  country_name: string;
  region: string;
  region_name: string;
  zone: string;
  zone_name: string;
  city: string;
  city_name: string;
  subcity: string;
  subcity_name: string;
  kebele: string;
  kebele_name: string;
  house_number: string;
  street_address: string;
  postal_code: string;
  location_display: string;

  tax_file_name?: string;
  is_active: boolean;
  registration_date: string;
  last_updated: string;
}

interface OrganizationTaxPayerCreate {
  tin: string;
  business_name: string;
  trade_name?: string;
  organization_business_type: string;
  tax_payer_level: string;
  business_sector: string;
  business_sub_sector: string;
  business_registration_date: string;
  business_license_number?: string;
  capital_amount?: number;
  number_of_employees?: number;
  manager_first_name: string;
  manager_middle_name?: string;
  manager_last_name: string;
  manager_title?: string;
  vat_registration_date?: string;
  vat_number?: string;
  phone: string;
  phone_secondary?: string;
  email?: string;
  country?: string;
  region?: string;
  zone?: string;
  city?: string;
  subcity?: string;
  kebele?: string;
  house_number?: string;
  street_address?: string;
  postal_code?: string;

}

interface BusinessSector {
  id: string;
  code: string;
  name: string;
  description: string;
  is_active: boolean;
  sub_sectors_count: number;
  created_at: string;
  updated_at: string;
}

interface BusinessSubSector {
  id: string;
  business_sector: string;
  business_sector_name: string;
  business_sector_code: string;
  code: string;
  name: string;
  description: string;
  full_code: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface TaxPayerLevel {
  id: string;
  name: string;
  code: string;
  description: string;
  minimum_annual_turnover?: number;
  maximum_annual_turnover?: number;
  turnover_range: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface OrganizationBusinessType {
  id: string;
  code: string;
  name: string;
  description: string;
  requires_vat_registration: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}
import locationService from '../../../services/locationService';
import locationHierarchyService from '../../../services/locationHierarchyService';

// Define location hierarchy interfaces locally
interface SubCity {
  id: number;
  city: number;
  city_name: string;
  zone_name: string;
  region_name: string;
  country_name: string;
  name: string;
  code: string;
  type: 'subcity' | 'woreda' | 'district';
  type_display: string;
  population?: number;
  area_km2?: number;
  is_active: boolean;
  full_name: string;
  kebele_count: number;
  created_at: string;
  updated_at: string;
}

interface Kebele {
  id: number;
  subcity: number;
  subcity_name: string;
  city_name: string;
  zone_name: string;
  region_name: string;
  country_name: string;
  name: string;
  code: string;
  number: number;
  population?: number;
  area_km2?: number;
  is_active: boolean;
  full_name: string;
  display_name: string;
  created_at: string;
  updated_at: string;
}



import CompactLocationSelector from '../../../components/locations/CompactLocationSelector';

interface OrganizationTaxPayerFormProps {
  taxpayer?: OrganizationTaxPayer | null;
  onSubmit: () => void;
  onCancel: () => void;
}

const OrganizationTaxPayerForm: React.FC<OrganizationTaxPayerFormProps> = ({
  taxpayer,
  onSubmit,
  onCancel,
}) => {
  const { showNotification } = useNotification();
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Form data
  const [formData, setFormData] = useState<OrganizationTaxPayerCreate>({
    tin: '',
    business_name: '',
    trade_name: '',
    organization_business_type: '',
    tax_payer_level: '',
    business_sector: '',
    business_sub_sector: '',
    business_registration_date: '',
    business_license_number: '',
    capital_amount: 0,
    number_of_employees: 0,
    manager_first_name: '',
    manager_middle_name: '',
    manager_last_name: '',
    manager_title: 'Manager',
    vat_registration_date: '',
    vat_number: '',
    phone: '',
    phone_secondary: '',
    email: '',
    country: '',
    region: '',
    zone: '',
    city: '',
    subcity: '',
    kebele: '',
    house_number: '',
    street_address: '',
    postal_code: '',
  });

  // Options
  const [levels, setLevels] = useState<TaxPayerLevel[]>([]);
  const [sectors, setSectors] = useState<BusinessSector[]>([]);
  const [subSectors, setSubSectors] = useState<BusinessSubSector[]>([]);
  const [businessTypes, setBusinessTypes] = useState<OrganizationBusinessType[]>([]);

  const [requiresVat, setRequiresVat] = useState(false);

  useEffect(() => {
    loadOptions();
    if (taxpayer) {
      populateForm(taxpayer);
    }
  }, [taxpayer]);

  useEffect(() => {
    if (formData.business_sector) {
      loadSubSectors(formData.business_sector);
    } else {
      setSubSectors([]);
      setFormData(prev => ({ ...prev, business_sub_sector: '' }));
    }
  }, [formData.business_sector]);

  useEffect(() => {
    if (formData.organization_business_type) {
      const selectedType = businessTypes.find(type => type.id === formData.organization_business_type);
      setRequiresVat(selectedType?.requires_vat_registration || false);
      
      if (!selectedType?.requires_vat_registration) {
        setFormData(prev => ({ 
          ...prev, 
          vat_number: '', 
          vat_registration_date: '' 
        }));
      }
    }
  }, [formData.organization_business_type, businessTypes]);

  const loadOptions = async () => {
    try {
      const [levelsData, sectorsData, businessTypesData] = await Promise.all([
        taxpayerService.getTaxPayerLevelsSimple(),
        taxpayerService.getBusinessSectorsSimple(),
        taxpayerService.getOrganizationBusinessTypesSimple(),
      ]);

      setLevels(levelsData);
      setSectors(sectorsData);
      setBusinessTypes(businessTypesData);
    } catch (error) {
      console.error('Failed to load options:', error);
      showNotification('Failed to load form options', 'error');
    }
  };

  const loadSubSectors = async (sectorId: string) => {
    try {
      const subSectorsData = await taxpayerService.getBusinessSubSectorsSimple(sectorId);
      setSubSectors(subSectorsData);
    } catch (error) {
      console.error('Failed to load sub-sectors:', error);
    }
  };

  const populateForm = (taxpayer: OrganizationTaxPayer) => {
    setFormData({
      tin: taxpayer.tin,
      business_name: taxpayer.business_name,
      trade_name: taxpayer.trade_name || '',
      organization_business_type: taxpayer.organization_business_type,
      tax_payer_level: taxpayer.tax_payer_level,
      business_sector: taxpayer.business_sector,
      business_sub_sector: taxpayer.business_sub_sector,
      business_registration_date: taxpayer.business_registration_date,
      business_license_number: taxpayer.business_license_number || '',
      capital_amount: taxpayer.capital_amount || 0,
      number_of_employees: taxpayer.number_of_employees || 0,
      manager_first_name: taxpayer.manager_first_name,
      manager_middle_name: taxpayer.manager_middle_name || '',
      manager_last_name: taxpayer.manager_last_name,
      manager_title: taxpayer.manager_title || 'Manager',
      vat_registration_date: taxpayer.vat_registration_date || '',
      vat_number: taxpayer.vat_number || '',
      phone: taxpayer.phone,
      phone_secondary: taxpayer.phone_secondary || '',
      email: taxpayer.email || '',
      country: taxpayer.country || '',
      region: taxpayer.region || '',
      zone: taxpayer.zone || '',
      city: taxpayer.city || '',
      subcity: taxpayer.subcity || '',
      kebele: taxpayer.kebele || '',
      house_number: taxpayer.house_number || '',
      street_address: taxpayer.street_address || '',
      postal_code: taxpayer.postal_code || '',

    });
  };

  const handleInputChange = (field: keyof OrganizationTaxPayerCreate, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleLocationChange = (locationData: any) => {
    setFormData(prev => ({
      ...prev,
      country: locationData.country || '',
      region: locationData.region || '',
      zone: locationData.zone || '',
      city: locationData.city || '',
      subcity: locationData.subcity || '',
      kebele: locationData.kebele || '',
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.tin) newErrors.tin = 'TIN is required';
    else if (!/^\d{10}$/.test(formData.tin)) newErrors.tin = 'TIN must be exactly 10 digits';

    if (!formData.business_name) newErrors.business_name = 'Business name is required';
    if (!formData.organization_business_type) newErrors.organization_business_type = 'Organization business type is required';
    if (!formData.tax_payer_level) newErrors.tax_payer_level = 'Tax payer level is required';
    if (!formData.business_sector) newErrors.business_sector = 'Business sector is required';
    if (!formData.business_sub_sector) newErrors.business_sub_sector = 'Business sub-sector is required';
    if (!formData.business_registration_date) newErrors.business_registration_date = 'Business registration date is required';
    if (!formData.manager_first_name) newErrors.manager_first_name = 'Manager first name is required';
    if (!formData.manager_last_name) newErrors.manager_last_name = 'Manager last name is required';
    if (!formData.phone) newErrors.phone = 'Phone number is required';

    // VAT validation
    if (requiresVat) {
      if (!formData.vat_number) newErrors.vat_number = 'VAT number is required for this business type';
      else if (!/^ET\d{10}$/.test(formData.vat_number)) newErrors.vat_number = 'VAT number must be in format ET followed by 10 digits';
      
      if (!formData.vat_registration_date) newErrors.vat_registration_date = 'VAT registration date is required for this business type';
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Invalid email format';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!validateForm()) {
      showNotification('Please fix the form errors', 'error');
      return;
    }

    try {
      setLoading(true);

      // Clean up form data - remove empty strings for optional fields
      const cleanedData = { ...formData };

      // Convert empty strings to null for optional fields
      const optionalFields = [
        'trade_name', 'business_license_number', 'manager_middle_name', 'manager_title',
        'phone_secondary', 'email', 'country', 'region', 'zone', 'city', 'subcity',
        'kebele', 'house_number', 'street_address', 'postal_code'
      ];

      optionalFields.forEach(field => {
        if (cleanedData[field as keyof OrganizationTaxPayerCreate] === '') {
          delete cleanedData[field as keyof OrganizationTaxPayerCreate];
        }
      });

      // Handle numeric fields
      if (cleanedData.capital_amount === 0) {
        delete cleanedData.capital_amount;
      }
      if (cleanedData.number_of_employees === 0) {
        delete cleanedData.number_of_employees;
      }

      // Handle VAT fields - only include if business type requires VAT
      if (!requiresVat) {
        delete cleanedData.vat_number;
        delete cleanedData.vat_registration_date;
      } else {
        // If VAT is required but fields are empty, keep them for validation
        if (cleanedData.vat_number === '') {
          delete cleanedData.vat_number;
        }
        if (cleanedData.vat_registration_date === '') {
          delete cleanedData.vat_registration_date;
        }
      }

      // Debug: Log the form data being sent
      console.log('Cleaned form data being sent:', cleanedData);

      if (taxpayer) {
        await taxpayerService.updateOrganizationTaxPayer(taxpayer.id, cleanedData);
        showNotification('Organization taxpayer updated successfully', 'success');
      } else {
        await taxpayerService.createOrganizationTaxPayer(cleanedData);
        showNotification('Organization taxpayer created successfully', 'success');
      }

      onSubmit();
    } catch (error: any) {
      console.error('Failed to save taxpayer:', error);
      console.error('Error response:', error.response?.data);

      if (error.response?.data) {
        setErrors(error.response.data);
        showNotification('Please fix the form errors', 'error');
      } else {
        showNotification('Failed to save taxpayer', 'error');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
        <Grid container spacing={3}>
          {/* Organization Information */}
          <Grid size={{ xs: 12 }}>
            <Typography variant="h6" gutterBottom>
              Organization Information
            </Typography>
            <Divider sx={{ mb: 2 }} />
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <TextField
              fullWidth
              label="TIN *"
              value={formData.tin}
              onChange={(e) => handleInputChange('tin', e.target.value)}
              error={!!errors.tin}
              helperText={errors.tin}
              inputProps={{ maxLength: 10 }}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <FormControl fullWidth error={!!errors.organization_business_type}>
              <InputLabel>Organization Business Type *</InputLabel>
              <Select
                value={formData.organization_business_type}
                onChange={(e) => handleInputChange('organization_business_type', e.target.value)}
                label="Organization Business Type *"
              >
                {businessTypes.map((type) => (
                  <MenuItem key={type.id} value={type.id}>
                    {type.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <TextField
              fullWidth
              label="Business Name *"
              value={formData.business_name}
              onChange={(e) => handleInputChange('business_name', e.target.value)}
              error={!!errors.business_name}
              helperText={errors.business_name}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <TextField
              fullWidth
              label="Trade Name"
              value={formData.trade_name}
              onChange={(e) => handleInputChange('trade_name', e.target.value)}
              helperText="If different from business name"
            />
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <TextField
              fullWidth
              label="Business License Number"
              value={formData.business_license_number}
              onChange={(e) => handleInputChange('business_license_number', e.target.value)}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <DatePicker
              label="Business Registration Date *"
              value={formData.business_registration_date ? new Date(formData.business_registration_date) : null}
              onChange={(date) => handleInputChange('business_registration_date', date?.toISOString().split('T')[0] || '')}
              slotProps={{
                textField: {
                  fullWidth: true,
                  error: !!errors.business_registration_date,
                  helperText: errors.business_registration_date,
                }
              }}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <TextField
              fullWidth
              label="Registered Capital Amount"
              type="number"
              value={formData.capital_amount || ''}
              onChange={(e) => handleInputChange('capital_amount', parseFloat(e.target.value) || 0)}
              inputProps={{ min: 0, step: 0.01 }}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <TextField
              fullWidth
              label="Number of Employees"
              type="number"
              value={formData.number_of_employees || ''}
              onChange={(e) => handleInputChange('number_of_employees', parseInt(e.target.value) || 0)}
              inputProps={{ min: 0 }}
            />
          </Grid>

          {/* Tax Information */}
          <Grid size={{ xs: 12 }}>
            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
              Tax Information
            </Typography>
            <Divider sx={{ mb: 2 }} />
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <FormControl fullWidth error={!!errors.tax_payer_level}>
              <InputLabel>Tax Payer Level *</InputLabel>
              <Select
                value={formData.tax_payer_level}
                onChange={(e) => handleInputChange('tax_payer_level', e.target.value)}
                label="Tax Payer Level *"
              >
                {levels.map((level) => (
                  <MenuItem key={level.id} value={level.id}>
                    {level.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <FormControl fullWidth error={!!errors.business_sector}>
              <InputLabel>Business Sector *</InputLabel>
              <Select
                value={formData.business_sector}
                onChange={(e) => handleInputChange('business_sector', e.target.value)}
                label="Business Sector *"
              >
                {sectors.map((sector) => (
                  <MenuItem key={sector.id} value={sector.id}>
                    {sector.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <FormControl fullWidth error={!!errors.business_sub_sector}>
              <InputLabel>Business Sub-Sector *</InputLabel>
              <Select
                value={formData.business_sub_sector}
                onChange={(e) => handleInputChange('business_sub_sector', e.target.value)}
                label="Business Sub-Sector *"
                disabled={!formData.business_sector}
              >
                {subSectors.map((subSector) => (
                  <MenuItem key={subSector.id} value={subSector.id}>
                    {subSector.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* VAT Information */}
          {requiresVat && (
            <>
              <Grid size={{ xs: 12 }}>
                <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                  VAT Information
                </Typography>
                <Divider sx={{ mb: 2 }} />
              </Grid>

              <Grid size={{ xs: 12, md: 6 }}>
                <TextField
                  fullWidth
                  label="VAT Number *"
                  value={formData.vat_number}
                  onChange={(e) => handleInputChange('vat_number', e.target.value.toUpperCase())}
                  error={!!errors.vat_number}
                  helperText={errors.vat_number || 'Format: ET followed by 10 digits'}
                  placeholder="ET1234567890"
                />
              </Grid>

              <Grid size={{ xs: 12, md: 6 }}>
                <DatePicker
                  label="VAT Registration Date *"
                  value={formData.vat_registration_date ? new Date(formData.vat_registration_date) : null}
                  onChange={(date) => {
                    if (date && !isNaN(date.getTime())) {
                      handleInputChange('vat_registration_date', date.toISOString().split('T')[0]);
                    } else {
                      handleInputChange('vat_registration_date', '');
                    }
                  }}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      error: !!errors.vat_registration_date,
                      helperText: errors.vat_registration_date,
                    }
                  }}
                />
              </Grid>
            </>
          )}

          {/* Manager Information */}
          <Grid size={{ xs: 12 }}>
            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
              Manager/Owner Information
            </Typography>
            <Divider sx={{ mb: 2 }} />
          </Grid>

          <Grid size={{ xs: 12, md: 4 }}>
            <TextField
              fullWidth
              label="Manager First Name *"
              value={formData.manager_first_name}
              onChange={(e) => handleInputChange('manager_first_name', e.target.value)}
              error={!!errors.manager_first_name}
              helperText={errors.manager_first_name}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 4 }}>
            <TextField
              fullWidth
              label="Manager Middle Name"
              value={formData.manager_middle_name}
              onChange={(e) => handleInputChange('manager_middle_name', e.target.value)}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 4 }}>
            <TextField
              fullWidth
              label="Manager Last Name *"
              value={formData.manager_last_name}
              onChange={(e) => handleInputChange('manager_last_name', e.target.value)}
              error={!!errors.manager_last_name}
              helperText={errors.manager_last_name}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <TextField
              fullWidth
              label="Manager Title"
              value={formData.manager_title}
              onChange={(e) => handleInputChange('manager_title', e.target.value)}
              placeholder="Manager, CEO, Owner, etc."
            />
          </Grid>

          {/* Contact Information */}
          <Grid size={{ xs: 12 }}>
            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
              Contact Information
            </Typography>
            <Divider sx={{ mb: 2 }} />
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <TextField
              fullWidth
              label="Phone Number *"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              error={!!errors.phone}
              helperText={errors.phone}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <TextField
              fullWidth
              label="Secondary Phone"
              value={formData.phone_secondary}
              onChange={(e) => handleInputChange('phone_secondary', e.target.value)}
            />
          </Grid>

          <Grid size={{ xs: 12 }}>
            <TextField
              fullWidth
              label="Email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              error={!!errors.email}
              helperText={errors.email}
            />
          </Grid>

          {/* Location */}
          <Grid size={{ xs: 12 }}>
            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
              Address Information
            </Typography>
            <Divider sx={{ mb: 2 }} />
          </Grid>

          <Grid size={{ xs: 12 }}>
            <CompactLocationSelector
              value={{
                country: formData.country ? parseInt(formData.country) : null,
                region: formData.region ? parseInt(formData.region) : null,
                zone: formData.zone ? parseInt(formData.zone) : null,
                city: formData.city ? parseInt(formData.city) : null,
                subcity: formData.subcity ? parseInt(formData.subcity) : null,
                kebele: formData.kebele ? parseInt(formData.kebele) : null,
              }}
              onChange={handleLocationChange}
              errors={{}}
              required={true}
              title="Address Information"
            />
          </Grid>

          <Grid size={{ xs: 12, md: 4 }}>
            <TextField
              fullWidth
              label="House/Building Number"
              value={formData.house_number}
              onChange={(e) => handleInputChange('house_number', e.target.value)}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 4 }}>
            <TextField
              fullWidth
              label="Street Address"
              value={formData.street_address}
              onChange={(e) => handleInputChange('street_address', e.target.value)}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 4 }}>
            <TextField
              fullWidth
              label="Postal Code"
              value={formData.postal_code}
              onChange={(e) => handleInputChange('postal_code', e.target.value)}
            />
          </Grid>

          {/* File Management */}
          <Grid size={{ xs: 12 }}>
            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
              File Management
            </Typography>
            <Divider sx={{ mb: 2 }} />
          </Grid>


        </Grid>

        {/* Form Actions */}
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 4 }}>
          <Button
            variant="outlined"
            onClick={onCancel}
            disabled={loading}
            startIcon={<Cancel />}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : <Save />}
          >
            {loading ? 'Saving...' : taxpayer ? 'Update' : 'Create'}
          </Button>
        </Box>

        {Object.keys(errors).length > 0 && (
          <Alert severity="error" sx={{ mt: 2 }}>
            Please fix the errors above and try again.
          </Alert>
        )}
      </Box>
    </LocalizationProvider>
  );
};

export default OrganizationTaxPayerForm;
