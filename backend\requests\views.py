from rest_framework import generics, permissions, status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.decorators import action
from django.utils import timezone
from django.db.models import Q, Count
from .models import DocumentRequest, RequestApproval, AuditLog, Notification
from .serializers import (
    DocumentRequestSerializer,
    DocumentRequestCreateSerializer,
    DocumentRequestUpdateSerializer,
    RequestApprovalSerializer,
    AuditLogSerializer,
    NotificationSerializer
)


class DocumentRequestListCreateView(generics.ListCreateAPIView):
    serializer_class = DocumentRequestSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = DocumentRequest.objects.select_related(
            'requested_by', 'approved_by', 'checked_out_by', 'returned_to'
        ).prefetch_related('documents')

        # Filter by search query
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(request_number__icontains=search) |
                Q(purpose__icontains=search) |
                Q(requested_by__username__icontains=search) |
                Q(requested_by__first_name__icontains=search) |
                Q(requested_by__last_name__icontains=search)
            )

        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by priority
        priority_filter = self.request.query_params.get('priority')
        if priority_filter:
            queryset = queryset.filter(priority=priority_filter)

        # Filter by requested_by
        requested_by = self.request.query_params.get('requested_by')
        if requested_by:
            queryset = queryset.filter(requested_by_id=requested_by)

        # Filter by approved_by
        approved_by = self.request.query_params.get('approved_by')
        if approved_by:
            queryset = queryset.filter(approved_by_id=approved_by)

        # Filter by overdue
        is_overdue = self.request.query_params.get('is_overdue')
        if is_overdue == 'true':
            queryset = queryset.filter(
                status=DocumentRequest.Status.CHECKED_OUT,
                due_date__lt=timezone.now()
            )

        return queryset.order_by('-created_at')

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return DocumentRequestCreateSerializer
        return DocumentRequestSerializer

class DocumentRequestDetailView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = DocumentRequestSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return DocumentRequest.objects.select_related(
            'requested_by', 'approved_by', 'checked_out_by', 'returned_to'
        ).prefetch_related('documents')

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return DocumentRequestUpdateSerializer
        return DocumentRequestSerializer

class RequestApprovalListView(generics.ListAPIView):
    queryset = RequestApproval.objects.all()
    serializer_class = RequestApprovalSerializer
    permission_classes = [permissions.IsAuthenticated]

class RequestApprovalCreateView(generics.CreateAPIView):
    queryset = RequestApproval.objects.all()
    serializer_class = RequestApprovalSerializer
    permission_classes = [permissions.IsAuthenticated]

class AuditLogListView(generics.ListAPIView):
    queryset = AuditLog.objects.all()
    serializer_class = AuditLogSerializer
    permission_classes = [permissions.IsAuthenticated]

class AuditLogDetailView(generics.RetrieveAPIView):
    queryset = AuditLog.objects.all()
    serializer_class = AuditLogSerializer
    permission_classes = [permissions.IsAuthenticated]

class NotificationListView(generics.ListAPIView):
    queryset = Notification.objects.all()
    serializer_class = NotificationSerializer
    permission_classes = [permissions.IsAuthenticated]

class NotificationDetailView(generics.RetrieveUpdateAPIView):
    queryset = Notification.objects.all()
    serializer_class = NotificationSerializer
    permission_classes = [permissions.IsAuthenticated]

# Placeholder API views
class RequestApproveView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk):
        try:
            doc_request = DocumentRequest.objects.get(pk=pk)

            # Check if user can approve
            if doc_request.requested_by == request.user:
                return Response(
                    {'error': 'You cannot approve your own request'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if doc_request.status != DocumentRequest.Status.PENDING:
                return Response(
                    {'error': 'Request is not in pending status'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Approve the request
            doc_request.status = DocumentRequest.Status.APPROVED
            doc_request.approved_by = request.user
            doc_request.approved_date = timezone.now()
            doc_request.save()

            # Create approval record
            comments = request.data.get('comments', '')
            RequestApproval.objects.create(
                request=doc_request,
                approver=request.user,
                action=RequestApproval.Action.APPROVED,
                comments=comments
            )

            serializer = DocumentRequestSerializer(doc_request)
            return Response(serializer.data)

        except DocumentRequest.DoesNotExist:
            return Response(
                {'error': 'Request not found'},
                status=status.HTTP_404_NOT_FOUND
            )

class RequestRejectView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk):
        try:
            doc_request = DocumentRequest.objects.get(pk=pk)

            # Check if user can reject
            if doc_request.requested_by == request.user:
                return Response(
                    {'error': 'You cannot reject your own request'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if doc_request.status != DocumentRequest.Status.PENDING:
                return Response(
                    {'error': 'Request is not in pending status'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            comments = request.data.get('comments', '')
            if not comments:
                return Response(
                    {'error': 'Comments are required when rejecting a request'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Reject the request
            doc_request.status = DocumentRequest.Status.REJECTED
            doc_request.approved_by = request.user
            doc_request.approved_date = timezone.now()
            doc_request.rejection_reason = comments
            doc_request.save()

            # Create approval record
            RequestApproval.objects.create(
                request=doc_request,
                approver=request.user,
                action=RequestApproval.Action.REJECTED,
                comments=comments
            )

            serializer = DocumentRequestSerializer(doc_request)
            return Response(serializer.data)

        except DocumentRequest.DoesNotExist:
            return Response(
                {'error': 'Request not found'},
                status=status.HTTP_404_NOT_FOUND
            )

class RequestCheckoutView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk):
        try:
            doc_request = DocumentRequest.objects.get(pk=pk)

            if doc_request.status != DocumentRequest.Status.APPROVED:
                return Response(
                    {'error': 'Request must be approved before checkout'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Checkout the request
            doc_request.status = DocumentRequest.Status.CHECKED_OUT
            doc_request.checked_out_by = request.user
            doc_request.checked_out_date = timezone.now()
            doc_request.save()

            serializer = DocumentRequestSerializer(doc_request)
            return Response(serializer.data)

        except DocumentRequest.DoesNotExist:
            return Response(
                {'error': 'Request not found'},
                status=status.HTTP_404_NOT_FOUND
            )

class RequestCancelView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk):
        try:
            doc_request = DocumentRequest.objects.get(pk=pk)

            # Check if user can cancel
            if doc_request.requested_by != request.user and not request.user.is_staff:
                return Response(
                    {'error': 'You can only cancel your own requests'},
                    status=status.HTTP_403_FORBIDDEN
                )

            if doc_request.status not in [DocumentRequest.Status.PENDING, DocumentRequest.Status.APPROVED]:
                return Response(
                    {'error': 'Request cannot be cancelled in current status'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Cancel the request
            doc_request.status = DocumentRequest.Status.CANCELLED

            comments = request.data.get('comments', '')
            if comments:
                doc_request.notes = comments

            doc_request.save()

            serializer = DocumentRequestSerializer(doc_request)
            return Response(serializer.data)

        except DocumentRequest.DoesNotExist:
            return Response(
                {'error': 'Request not found'},
                status=status.HTTP_404_NOT_FOUND
            )

class RequestApprovalsView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, pk):
        try:
            doc_request = DocumentRequest.objects.get(pk=pk)
            approvals = RequestApproval.objects.filter(request=doc_request).order_by('-created_at')
            serializer = RequestApprovalSerializer(approvals, many=True)
            return Response(serializer.data)

        except DocumentRequest.DoesNotExist:
            return Response(
                {'error': 'Request not found'},
                status=status.HTTP_404_NOT_FOUND
            )

class RequestReturnView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk):
        try:
            doc_request = DocumentRequest.objects.get(pk=pk)

            if doc_request.status != DocumentRequest.Status.CHECKED_OUT:
                return Response(
                    {'error': 'Request must be checked out before return'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Return the request
            doc_request.status = DocumentRequest.Status.RETURNED
            doc_request.returned_to = request.user
            doc_request.returned_date = timezone.now()

            condition_notes = request.data.get('condition_notes', '')
            if condition_notes:
                doc_request.notes = condition_notes

            doc_request.save()

            serializer = DocumentRequestSerializer(doc_request)
            return Response(serializer.data)

        except DocumentRequest.DoesNotExist:
            return Response(
                {'error': 'Request not found'},
                status=status.HTTP_404_NOT_FOUND
            )

class RequestStatsView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        user_filter = request.query_params.get('user')
        queryset = DocumentRequest.objects.all()

        if user_filter:
            queryset = queryset.filter(requested_by_id=user_filter)

        # Basic stats
        total_requests = queryset.count()
        pending_requests = queryset.filter(status=DocumentRequest.Status.PENDING).count()
        approved_requests = queryset.filter(status=DocumentRequest.Status.APPROVED).count()
        checked_out_requests = queryset.filter(status=DocumentRequest.Status.CHECKED_OUT).count()
        returned_requests = queryset.filter(status=DocumentRequest.Status.RETURNED).count()
        rejected_requests = queryset.filter(status=DocumentRequest.Status.REJECTED).count()

        # Overdue requests
        overdue_requests = queryset.filter(
            status=DocumentRequest.Status.CHECKED_OUT,
            due_date__lt=timezone.now()
        ).count()

        # Recent requests (last 7 days)
        recent_requests = queryset.filter(
            created_at__gte=timezone.now() - timezone.timedelta(days=7)
        ).count()

        # Requests by priority
        priority_stats = {}
        for priority, _ in DocumentRequest.Priority.choices:
            priority_stats[priority] = queryset.filter(priority=priority).count()

        # Requests by status
        status_stats = {}
        for status_choice, _ in DocumentRequest.Status.choices:
            status_stats[status_choice] = queryset.filter(status=status_choice).count()

        # User-specific stats
        my_requests = 0
        my_pending_approvals = 0
        if request.user:
            my_requests = queryset.filter(requested_by=request.user).count()
            my_pending_approvals = DocumentRequest.objects.filter(
                status=DocumentRequest.Status.PENDING
            ).exclude(requested_by=request.user).count()

        return Response({
            'total_requests': total_requests,
            'pending_requests': pending_requests,
            'approved_requests': approved_requests,
            'checked_out_requests': checked_out_requests,
            'returned_requests': returned_requests,
            'rejected_requests': rejected_requests,
            'overdue_requests': overdue_requests,
            'recent_requests': recent_requests,
            'requests_by_priority': priority_stats,
            'requests_by_status': status_stats,
            'my_requests': my_requests,
            'my_pending_approvals': my_pending_approvals,
        })

class NotificationMarkReadView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    def post(self, request, pk):
        return Response({'message': f'Mark notification {pk} as read'})

class DashboardStatsView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    def get(self, request):
        return Response({'message': 'Dashboard stats endpoint'})

class UserActivityView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    def get(self, request):
        return Response({'message': 'User activity endpoint'})

class SystemHealthView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    def get(self, request):
        return Response({'message': 'System health endpoint'})
