import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  Pagination,
  Alert,
  Skeleton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Avatar,
  Tooltip,
} from '@mui/material';
import {
  Add,
  Search,
  MoreVert,
  Edit,
  Delete,
  Visibility,
  Business,
  Star,
  StarBorder,
  FilterList,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useNotification } from '../../contexts/NotificationContext';
import organizationService, { type Organization } from '../../services/organizationService';

const OrganizationListPage: React.FC = () => {
  const navigate = useNavigate();
  const { showNotification } = useNotification();

  // State management
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [selectedOrg, setSelectedOrg] = useState<Organization | null>(null);
  const [menuAnchor, setMenuAnchor] = useState<HTMLElement | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [orgToDelete, setOrgToDelete] = useState<Organization | null>(null);

  // Load organizations
  const loadOrganizations = async (page = 1, search = '') => {
    try {
      setLoading(true);
      const response = await organizationService.getOrganizations({
        page,
        page_size: 10,
        search: search.trim() || undefined,
        ordering: '-created_at',
      });

      setOrganizations(response.results);
      setTotalCount(response.count);
      setTotalPages(Math.ceil(response.count / 10));
    } catch (error) {
      console.error('Error loading organizations:', error);
      showNotification('Failed to load organizations', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    loadOrganizations(currentPage, searchTerm);
  }, [currentPage]);

  // Search handler
  const handleSearch = () => {
    setCurrentPage(1);
    loadOrganizations(1, searchTerm);
  };

  // Handle search on Enter key
  const handleSearchKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      handleSearch();
    }
  };

  // Menu handlers
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, org: Organization) => {
    setMenuAnchor(event.currentTarget);
    setSelectedOrg(org);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
    setSelectedOrg(null);
  };

  // Action handlers
  const handleView = (org: Organization) => {
    navigate(`/organizations/${org.id}`);
    handleMenuClose();
  };

  const handleEdit = (org: Organization) => {
    navigate(`/organizations/${org.id}/edit`);
    handleMenuClose();
  };

  const handleDelete = (org: Organization) => {
    setOrgToDelete(org);
    setDeleteDialogOpen(true);
    handleMenuClose();
  };

  const confirmDelete = async () => {
    if (!orgToDelete) return;

    try {
      await organizationService.deleteOrganization(orgToDelete.id);
      showNotification('Organization deleted successfully', 'success');
      loadOrganizations(currentPage, searchTerm);
    } catch (error) {
      console.error('Error deleting organization:', error);
      showNotification('Failed to delete organization', 'error');
    } finally {
      setDeleteDialogOpen(false);
      setOrgToDelete(null);
    }
  };

  const handleSetDefault = async (org: Organization) => {
    try {
      await organizationService.setAsDefault(org.id);
      showNotification('Default organization updated', 'success');
      loadOrganizations(currentPage, searchTerm);
    } catch (error) {
      console.error('Error setting default organization:', error);
      showNotification('Failed to set default organization', 'error');
    }
    handleMenuClose();
  };

  // Render loading skeleton
  const renderSkeleton = () => (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Organization</TableCell>
            <TableCell>Contact</TableCell>
            <TableCell>Location</TableCell>
            <TableCell>Status</TableCell>
            <TableCell align="right">Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {[...Array(5)].map((_, index) => (
            <TableRow key={index}>
              <TableCell>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Skeleton variant="circular" width={40} height={40} />
                  <Box>
                    <Skeleton variant="text" width={120} />
                    <Skeleton variant="text" width={80} />
                  </Box>
                </Box>
              </TableCell>
              <TableCell>
                <Skeleton variant="text" width={150} />
                <Skeleton variant="text" width={120} />
              </TableCell>
              <TableCell>
                <Skeleton variant="text" width={100} />
              </TableCell>
              <TableCell>
                <Skeleton variant="rectangular" width={60} height={24} />
              </TableCell>
              <TableCell align="right">
                <Skeleton variant="circular" width={32} height={32} />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Organizations
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage your organizations and their settings
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => navigate('/organizations/create')}
          size="large"
        >
          Add Organization
        </Button>
      </Box>

      {/* Search and Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <TextField
              placeholder="Search organizations..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyPress={handleSearchKeyPress}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
              sx={{ flexGrow: 1 }}
            />
            <Button
              variant="outlined"
              onClick={handleSearch}
              startIcon={<Search />}
            >
              Search
            </Button>
            <Button
              variant="outlined"
              startIcon={<FilterList />}
            >
              Filter
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Results Summary */}
      {!loading && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            Showing {organizations.length} of {totalCount} organizations
          </Typography>
        </Box>
      )}

      {/* Organizations Table */}
      {loading ? (
        renderSkeleton()
      ) : organizations.length === 0 ? (
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 8 }}>
            <Business sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              No organizations found
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              {searchTerm ? 'Try adjusting your search criteria' : 'Get started by creating your first organization'}
            </Typography>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => navigate('/organizations/create')}
            >
              Add Organization
            </Button>
          </CardContent>
        </Card>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Organization</TableCell>
                <TableCell>Contact</TableCell>
                <TableCell>Location</TableCell>
                <TableCell>Status</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {organizations.map((org) => (
                <TableRow key={org.id} hover>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Avatar sx={{ bgcolor: 'primary.main' }}>
                        {org.name.charAt(0).toUpperCase()}
                      </Avatar>
                      <Box>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                          {org.name}
                          {org.is_default && (
                            <Tooltip title="Default Organization">
                              <Star sx={{ ml: 1, fontSize: 16, color: 'warning.main' }} />
                            </Tooltip>
                          )}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {org.short_name}
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">{org.email}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {org.phone}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {org.location_hierarchy_display || 'No location set'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={organizationService.getStatusText(org)}
                      color={organizationService.getStatusColor(org)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell align="right">
                    <IconButton
                      onClick={(e) => handleMenuOpen(e, org)}
                      size="small"
                    >
                      <MoreVert />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
          <Pagination
            count={totalPages}
            page={currentPage}
            onChange={(_, page) => setCurrentPage(page)}
            color="primary"
            size="large"
          />
        </Box>
      )}

      {/* Action Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => selectedOrg && handleView(selectedOrg)}>
          <Visibility sx={{ mr: 1 }} />
          View Details
        </MenuItem>
        <MenuItem onClick={() => selectedOrg && handleEdit(selectedOrg)}>
          <Edit sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        {selectedOrg && !selectedOrg.is_default && (
          <MenuItem onClick={() => selectedOrg && handleSetDefault(selectedOrg)}>
            <Star sx={{ mr: 1 }} />
            Set as Default
          </MenuItem>
        )}
        <MenuItem 
          onClick={() => selectedOrg && handleDelete(selectedOrg)}
          sx={{ color: 'error.main' }}
        >
          <Delete sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Delete Organization</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete "{orgToDelete?.name}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default OrganizationListPage;
