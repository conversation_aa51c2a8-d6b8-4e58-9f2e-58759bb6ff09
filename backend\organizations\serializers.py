from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.core.validators import validate_email
from django.core.exceptions import ValidationError as DjangoValidationError
import re
from .models import Organization, Department
from locations.models import Country, Region, Zone, City, SubCity, Kebele

User = get_user_model()


class OrganizationSerializer(serializers.ModelSerializer):
    """Organization serializer with location hierarchy"""

    full_address = serializers.ReadOnlyField()
    location_hierarchy_display = serializers.ReadOnlyField()
    office_hours_display = serializers.ReadOnlyField(source='get_office_hours_display')
    user_count = serializers.SerializerMethodField()
    department_count = serializers.SerializerMethodField()

    # Location hierarchy names for easy access
    country_name = serializers.ReadOnlyField()
    region_name = serializers.ReadOnlyField()
    city_name = serializers.ReadOnlyField()
    subcity_name = serializers.ReadOnlyField()
    kebele_name = serializers.ReadOnlyField()

    class Meta:
        model = Organization
        fields = [
            'id', 'name', 'short_name', 'logo', 'motto', 'tagline', 'description', 'website',
            'email', 'phone', 'fax', 'address_line1', 'address_line2', 'postal_code',
            'city', 'state_province', 'country', 'subcity', 'kebele',
            'city_name', 'region_name', 'country_name', 'subcity_name', 'kebele_name',
            'full_address', 'location_hierarchy_display',
            'office_hours_start', 'office_hours_end', 'office_hours_display',
            'established_date', 'registration_number', 'tax_id', 'license_number',
            'primary_color', 'secondary_color', 'accent_color', 'social_media',
            'is_active', 'is_default', 'document_retention_days', 'max_file_size_mb',
            'individual_penalty_rate', 'individual_interest_rate',
            'organization_penalty_rate', 'organization_interest_rate',
            'user_count', 'department_count', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'created_at', 'updated_at', 'full_address', 'location_hierarchy_display',
            'country_name', 'region_name', 'city_name', 'subcity_name', 'kebele_name'
        ]
    
    def get_user_count(self, obj):
        """Get number of users in organization"""
        return obj.users.filter(is_active=True).count()
    
    def get_department_count(self, obj):
        """Get number of departments in organization"""
        return obj.departments.filter(is_active=True).count()

    def update(self, instance, validated_data):
        """Custom update method with debugging for tax collection settings"""
        import logging
        logger = logging.getLogger(__name__)

        # Log the incoming data
        logger.info(f"OrganizationSerializer.update called for organization {instance.id}")
        logger.info(f"Validated data keys: {list(validated_data.keys())}")

        # Check specifically for tax collection fields
        tax_fields = {
            'individual_penalty_rate': validated_data.get('individual_penalty_rate'),
            'individual_interest_rate': validated_data.get('individual_interest_rate'),
            'organization_penalty_rate': validated_data.get('organization_penalty_rate'),
            'organization_interest_rate': validated_data.get('organization_interest_rate'),
        }
        logger.info(f"Tax collection fields in validated_data: {tax_fields}")

        # Log current values before update
        current_values = {
            'individual_penalty_rate': instance.individual_penalty_rate,
            'individual_interest_rate': instance.individual_interest_rate,
            'organization_penalty_rate': instance.organization_penalty_rate,
            'organization_interest_rate': instance.organization_interest_rate,
        }
        logger.info(f"Current tax collection values: {current_values}")

        # Perform the update
        updated_instance = super().update(instance, validated_data)

        # Log values after update
        updated_values = {
            'individual_penalty_rate': updated_instance.individual_penalty_rate,
            'individual_interest_rate': updated_instance.individual_interest_rate,
            'organization_penalty_rate': updated_instance.organization_penalty_rate,
            'organization_interest_rate': updated_instance.organization_interest_rate,
        }
        logger.info(f"Updated tax collection values: {updated_values}")

        return updated_instance


class OrganizationCreateSerializer(serializers.ModelSerializer):
    """Organization creation serializer with comprehensive validation"""

    class Meta:
        model = Organization
        fields = [
            'name', 'short_name', 'logo', 'motto', 'tagline', 'description', 'website',
            'email', 'phone', 'fax', 'address_line1', 'address_line2', 'postal_code',
            'city', 'state_province', 'country', 'subcity', 'kebele',
            'office_hours_start', 'office_hours_end',
            'established_date', 'registration_number', 'tax_id', 'license_number',
            'primary_color', 'secondary_color', 'accent_color', 'social_media',
            'document_retention_days', 'max_file_size_mb',
            'individual_penalty_rate', 'individual_interest_rate',
            'organization_penalty_rate', 'organization_interest_rate'
        ]

    def validate_name(self, value):
        """Validate organization name"""
        if not value or len(value.strip()) < 3:
            raise serializers.ValidationError("Organization name must be at least 3 characters long")

        # Check for duplicate names (excluding current instance in update)
        queryset = Organization.objects.filter(name__iexact=value.strip())
        if self.instance:
            queryset = queryset.exclude(pk=self.instance.pk)
        if queryset.exists():
            raise serializers.ValidationError("An organization with this name already exists")

        return value.strip()

    def validate_short_name(self, value):
        """Validate short name"""
        if not value or len(value.strip()) < 2:
            raise serializers.ValidationError("Short name must be at least 2 characters long")

        # Check for duplicate short names (excluding current instance in update)
        short_name_upper = value.strip().upper()
        queryset = Organization.objects.filter(short_name__iexact=short_name_upper)
        if self.instance:
            queryset = queryset.exclude(pk=self.instance.pk)
        if queryset.exists():
            raise serializers.ValidationError("An organization with this short name already exists")

        return short_name_upper

    def validate_email(self, value):
        """Validate email format"""
        if value:
            try:
                validate_email(value)
            except DjangoValidationError:
                raise serializers.ValidationError("Please enter a valid email address")
        return value

    def validate_phone(self, value):
        """Validate phone number"""
        if value:
            # Remove spaces and common separators
            phone = re.sub(r'[\s\-\(\)]', '', value)
            if not re.match(r'^\+?[\d]{10,15}$', phone):
                raise serializers.ValidationError("Please enter a valid phone number (10-15 digits)")
        return value

    def validate_website(self, value):
        """Validate website URL"""
        if value:
            if not value.startswith(('http://', 'https://')):
                value = f'https://{value}'
            # Basic URL validation
            if not re.match(r'^https?://[^\s/$.?#].[^\s]*$', value):
                raise serializers.ValidationError("Please enter a valid website URL")
        return value

    def validate_document_retention_days(self, value):
        """Validate document retention days"""
        if value is not None and (value < 30 or value > 10000):
            raise serializers.ValidationError("Document retention days must be between 30 and 10000")
        return value

    def validate_max_file_size_mb(self, value):
        """Validate max file size"""
        if value is not None and (value < 1 or value > 1000):
            raise serializers.ValidationError("Max file size must be between 1 and 1000 MB")
        return value

    def validate(self, data):
        """Cross-field validation"""
        # Validate location hierarchy consistency
        country = data.get('country')
        state_province = data.get('state_province')
        city = data.get('city')
        subcity = data.get('subcity')
        kebele = data.get('kebele')

        # If region is selected, it must belong to the selected country
        if state_province and country:
            if state_province.country != country:
                raise serializers.ValidationError({
                    'state_province': 'Selected region does not belong to the selected country'
                })

        # If city is selected, it must belong to the selected zone/region
        if city and state_province:
            if hasattr(city, 'zone') and city.zone and city.zone.region != state_province:
                raise serializers.ValidationError({
                    'city': 'Selected city does not belong to the selected region'
                })

        # If subcity is selected, it must belong to the selected city
        if subcity and city:
            if subcity.city != city:
                raise serializers.ValidationError({
                    'subcity': 'Selected subcity does not belong to the selected city'
                })

        # If kebele is selected, it must belong to the selected subcity
        if kebele and subcity:
            if kebele.subcity != subcity:
                raise serializers.ValidationError({
                    'kebele': 'Selected kebele does not belong to the selected subcity'
                })

        # Validate office hours
        office_start = data.get('office_hours_start')
        office_end = data.get('office_hours_end')
        if office_start and office_end:
            if office_start >= office_end:
                raise serializers.ValidationError({
                    'office_hours_end': 'Office end time must be after start time'
                })

        return data

    def create(self, validated_data):
        if 'request' in self.context:
            validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class OrganizationUpdateSerializer(OrganizationCreateSerializer):
    """Organization update serializer inheriting comprehensive validation"""

    class Meta(OrganizationCreateSerializer.Meta):
        fields = OrganizationCreateSerializer.Meta.fields + ['is_active']


class DepartmentSerializer(serializers.ModelSerializer):
    """Department serializer"""
    
    organization_name = serializers.CharField(source='organization.name', read_only=True)
    head_name = serializers.CharField(source='head.full_name', read_only=True)
    user_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Department
        fields = [
            'id', 'organization', 'organization_name', 'name', 'code', 
            'description', 'head', 'head_name', 'is_active', 
            'user_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_user_count(self, obj):
        """Get number of users in department"""
        return obj.organization.users.filter(department=obj.name, is_active=True).count()


class DepartmentCreateSerializer(serializers.ModelSerializer):
    """Department creation serializer"""
    
    class Meta:
        model = Department
        fields = ['organization', 'name', 'code', 'description', 'head']
    
    def validate(self, attrs):
        # Ensure head belongs to the same organization
        if attrs.get('head') and attrs.get('organization'):
            if attrs['head'].organization != attrs['organization']:
                raise serializers.ValidationError(
                    "Department head must belong to the same organization"
                )
        return attrs


class DepartmentUpdateSerializer(serializers.ModelSerializer):
    """Department update serializer"""
    
    class Meta:
        model = Department
        fields = ['name', 'code', 'description', 'head', 'is_active']
    
    def validate_head(self, value):
        """Validate that head belongs to the same organization"""
        if value and self.instance:
            if value.organization != self.instance.organization:
                raise serializers.ValidationError(
                    "Department head must belong to the same organization"
                )
        return value


class OrganizationSummarySerializer(serializers.ModelSerializer):
    """Organization summary serializer for listings"""
    
    class Meta:
        model = Organization
        fields = ['id', 'name', 'short_name', 'logo', 'is_active']


class DepartmentSummarySerializer(serializers.ModelSerializer):
    """Department summary serializer for listings"""
    
    organization_name = serializers.CharField(source='organization.short_name', read_only=True)
    
    class Meta:
        model = Department
        fields = ['id', 'name', 'code', 'organization_name', 'is_active']
