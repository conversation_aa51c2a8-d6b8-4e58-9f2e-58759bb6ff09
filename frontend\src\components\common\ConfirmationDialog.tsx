import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Typography,
  Box,
  Avatar,
  Fade,
} from '@mui/material';
import {
  Warning,
  Delete,
  Cancel,
} from '@mui/icons-material';

interface ConfirmationDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title?: string;
  message?: string;
  itemName?: string;
  itemType?: string;
  confirmText?: string;
  cancelText?: string;
  severity?: 'warning' | 'error' | 'info';
  loading?: boolean;
}

const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({
  open,
  onClose,
  onConfirm,
  title,
  message,
  itemName,
  itemType = 'item',
  confirmText = 'Delete',
  cancelText = 'Cancel',
  severity = 'warning',
  loading = false,
}) => {
  const getIcon = () => {
    switch (severity) {
      case 'error':
        return <Delete sx={{ fontSize: 40 }} />;
      case 'warning':
        return <Warning sx={{ fontSize: 40 }} />;
      default:
        return <Warning sx={{ fontSize: 40 }} />;
    }
  };

  const getColor = () => {
    switch (severity) {
      case 'error':
        return 'error.main';
      case 'warning':
        return 'warning.main';
      default:
        return 'warning.main';
    }
  };

  const defaultTitle = title || `Delete ${itemType}`;
  const defaultMessage = message || `Are you sure you want to delete "${itemName}"? This action cannot be undone.`;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
        }
      }}
      TransitionComponent={Fade}
      TransitionProps={{ timeout: 300 }}
    >
      <DialogTitle sx={{ textAlign: 'center', pt: 4, pb: 2 }}>
        <Avatar
          sx={{
            bgcolor: getColor(),
            width: 80,
            height: 80,
            mx: 'auto',
            mb: 2,
            boxShadow: '0 8px 24px rgba(0,0,0,0.15)',
          }}
        >
          {getIcon()}
        </Avatar>
        <Typography variant="h5" sx={{ fontWeight: 600, color: 'text.primary' }}>
          {defaultTitle}
        </Typography>
      </DialogTitle>

      <DialogContent sx={{ textAlign: 'center', px: 4, pb: 2 }}>
        <Typography variant="body1" color="text.secondary" sx={{ lineHeight: 1.6 }}>
          {defaultMessage}
        </Typography>
        
        {itemName && (
          <Box
            sx={{
              mt: 3,
              p: 2,
              bgcolor: 'action.hover',
              borderRadius: 2,
              border: '1px solid',
              borderColor: 'divider',
            }}
          >
            <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
              {itemType} to be deleted:
            </Typography>
            <Typography variant="body1" sx={{ fontWeight: 600 }}>
              {itemName}
            </Typography>
          </Box>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 4, pt: 2, gap: 2, justifyContent: 'center' }}>
        <Button
          onClick={onClose}
          variant="outlined"
          size="large"
          startIcon={<Cancel />}
          sx={{ minWidth: 120 }}
          disabled={loading}
        >
          {cancelText}
        </Button>
        <Button
          onClick={onConfirm}
          variant="contained"
          color={severity === 'error' ? 'error' : 'warning'}
          size="large"
          startIcon={<Delete />}
          sx={{ minWidth: 120 }}
          disabled={loading}
        >
          {loading ? 'Deleting...' : confirmText}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ConfirmationDialog;
