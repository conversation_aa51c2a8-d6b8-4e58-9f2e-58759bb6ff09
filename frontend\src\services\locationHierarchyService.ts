import apiClient from './api';

// Interfaces
export interface Country {
  id: number;
  name: string;
  code: string;
  phone_code?: string;
  currency?: string;
  is_active: boolean;
  region_count: number;
  created_at: string;
  updated_at: string;
}

export interface Region {
  id: number;
  country: number;
  country_name: string;
  country_code: string;
  name: string;
  code: string;
  population?: number;
  area_km2?: number;
  capital_city?: string;
  is_active: boolean;
  full_name: string;
  zone_count: number;
  created_at: string;
  updated_at: string;
}

export interface Zone {
  id: number;
  region: number;
  region_name: string;
  country_name: string;
  name: string;
  code: string;
  population?: number;
  area_km2?: number;
  is_active: boolean;
  full_name: string;
  city_count: number;
  created_at: string;
  updated_at: string;
}

export interface City {
  id: number;
  zone: number;
  zone_name: string;
  region_name: string;
  country_name: string;
  name: string;
  code: string;
  population?: number;
  area_km2?: number;
  is_capital: boolean;
  is_active: boolean;
  full_name: string;
  subcity_count: number;
  created_at: string;
  updated_at: string;
}

export interface SubCity {
  id: number;
  city: number;
  city_name: string;
  zone_name: string;
  region_name: string;
  country_name: string;
  name: string;
  code: string;
  type: 'subcity' | 'woreda' | 'district';
  type_display: string;
  population?: number;
  area_km2?: number;
  is_active: boolean;
  full_name: string;
  kebele_count: number;
  created_at: string;
  updated_at: string;
}

export interface Kebele {
  id: number;
  subcity: number;
  subcity_name: string;
  city_name: string;
  zone_name: string;
  region_name: string;
  country_name: string;
  name: string;
  code: string;
  number: number;
  population?: number;
  area_km2?: number;
  is_active: boolean;
  full_name: string;
  display_name: string;
  created_at: string;
  updated_at: string;
}

// Create interfaces
export interface CountryCreate {
  name: string;
  code: string;
  phone_code?: string;
  currency?: string;
  is_active?: boolean;
}

export interface RegionCreate {
  country: number;
  name: string;
  code: string;
  population?: number;
  area_km2?: number;
  capital_city?: string;
  is_active?: boolean;
}

export interface ZoneCreate {
  region: number;
  name: string;
  code: string;
  population?: number;
  area_km2?: number;
  is_active?: boolean;
}

export interface CityCreate {
  zone: number;
  name: string;
  code: string;
  population?: number;
  area_km2?: number;
  is_capital?: boolean;
  is_active?: boolean;
}

export interface SubCityCreate {
  city: number;
  name: string;
  code: string;
  type: 'subcity' | 'woreda' | 'district';
  population?: number;
  area_km2?: number;
  is_active?: boolean;
}

export interface KebeleCreate {
  subcity: number;
  name: string;
  code: string;
  number: number;
  population?: number;
  area_km2?: number;
  is_active?: boolean;
}

// Select interfaces for dropdowns
export interface CountrySelect {
  id: number;
  name: string;
  code: string;
}

export interface RegionSelect {
  id: number;
  name: string;
  code: string;
  country: number;
}

export interface ZoneSelect {
  id: number;
  name: string;
  code: string;
  region: number;
}

export interface CitySelect {
  id: number;
  name: string;
  code: string;
  zone: number;
}

export interface SubCitySelect {
  id: number;
  name: string;
  code: string;
  type: string;
  city: number;
}

export interface KebeleSelect {
  id: number;
  name: string;
  code: string;
  number: number;
  display_name: string;
  subcity: number;
}

export interface SpecialLocation {
  id: number;
  kebele: number;
  kebele_name: string;
  kebele_full_name: string;
  name: string;
  is_active: boolean;
  location_path: string;
  display_name: string;
  full_name: string;
  created_at: string;
  updated_at: string;
}

export interface SpecialLocationCreate {
  kebele: number;
  name: string;
  is_active: boolean;
}

export interface SpecialLocationSelect {
  id: number;
  name: string;
  display_name: string;
  kebele: number;
}

// Response interfaces
export interface LocationHierarchyListResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

class LocationHierarchyService {
  private baseUrl = '/locations';

  // Countries
  async getCountries(params?: {
    page?: number;
    page_size?: number;
    search?: string;
    is_active?: boolean;
  }): Promise<LocationHierarchyListResponse<Country>> {
    const response = await apiClient.get(`${this.baseUrl}/countries/`, { params });
    return response.data;
  }

  async getCountry(id: number): Promise<Country> {
    const response = await apiClient.get(`${this.baseUrl}/countries/${id}/`);
    return response.data;
  }

  async createCountry(data: CountryCreate): Promise<Country> {
    const response = await apiClient.post(`${this.baseUrl}/countries/`, data);
    return response.data;
  }

  async updateCountry(id: number, data: Partial<CountryCreate>): Promise<Country> {
    const response = await apiClient.patch(`${this.baseUrl}/countries/${id}/`, data);
    return response.data;
  }

  async deleteCountry(id: number): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/countries/${id}/`);
  }

  async getCountriesSelect(): Promise<CountrySelect[]> {
    const response = await apiClient.get(`${this.baseUrl}/countries/select/`);
    return response.data;
  }

  // Regions
  async getRegions(params?: {
    page?: number;
    page_size?: number;
    search?: string;
    country?: number;
    is_active?: boolean;
  }): Promise<LocationHierarchyListResponse<Region>> {
    const response = await apiClient.get(`${this.baseUrl}/regions/`, { params });
    return response.data;
  }

  async getRegion(id: number): Promise<Region> {
    const response = await apiClient.get(`${this.baseUrl}/regions/${id}/`);
    return response.data;
  }

  async createRegion(data: RegionCreate): Promise<Region> {
    const response = await apiClient.post(`${this.baseUrl}/regions/`, data);
    return response.data;
  }

  async updateRegion(id: number, data: Partial<RegionCreate>): Promise<Region> {
    const response = await apiClient.patch(`${this.baseUrl}/regions/${id}/`, data);
    return response.data;
  }

  async deleteRegion(id: number): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/regions/${id}/`);
  }

  async getRegionsSelect(countryId?: number): Promise<RegionSelect[]> {
    const params = countryId ? { country: countryId } : {};
    const response = await apiClient.get(`${this.baseUrl}/regions/select/`, { params });
    return response.data;
  }

  // Zones
  async getZones(params?: {
    page?: number;
    page_size?: number;
    search?: string;
    region?: number;
    is_active?: boolean;
  }): Promise<LocationHierarchyListResponse<Zone>> {
    const response = await apiClient.get(`${this.baseUrl}/zones/`, { params });
    return response.data;
  }

  async getZone(id: number): Promise<Zone> {
    const response = await apiClient.get(`${this.baseUrl}/zones/${id}/`);
    return response.data;
  }

  async createZone(data: ZoneCreate): Promise<Zone> {
    const response = await apiClient.post(`${this.baseUrl}/zones/`, data);
    return response.data;
  }

  async updateZone(id: number, data: Partial<ZoneCreate>): Promise<Zone> {
    const response = await apiClient.patch(`${this.baseUrl}/zones/${id}/`, data);
    return response.data;
  }

  async deleteZone(id: number): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/zones/${id}/`);
  }

  async getZonesSelect(regionId?: number): Promise<ZoneSelect[]> {
    const params = regionId ? { region: regionId } : {};
    const response = await apiClient.get(`${this.baseUrl}/zones/select/`, { params });
    return response.data;
  }

  // Cities
  async getCities(params?: {
    page?: number;
    page_size?: number;
    search?: string;
    zone?: number;
    is_active?: boolean;
  }): Promise<LocationHierarchyListResponse<City>> {
    const response = await apiClient.get(`${this.baseUrl}/cities/`, { params });
    return response.data;
  }

  async getCity(id: number): Promise<City> {
    const response = await apiClient.get(`${this.baseUrl}/cities/${id}/`);
    return response.data;
  }

  async createCity(data: CityCreate): Promise<City> {
    const response = await apiClient.post(`${this.baseUrl}/cities/`, data);
    return response.data;
  }

  async updateCity(id: number, data: Partial<CityCreate>): Promise<City> {
    const response = await apiClient.patch(`${this.baseUrl}/cities/${id}/`, data);
    return response.data;
  }

  async deleteCity(id: number): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/cities/${id}/`);
  }

  async getCitiesSelect(zoneId?: number): Promise<CitySelect[]> {
    const params = zoneId ? { zone: zoneId } : {};
    const response = await apiClient.get(`${this.baseUrl}/cities/select/`, { params });
    return response.data;
  }

  // SubCities
  async getSubCities(params?: {
    page?: number;
    page_size?: number;
    search?: string;
    city?: number;
    type?: string;
    is_active?: boolean;
  }): Promise<LocationHierarchyListResponse<SubCity>> {
    const response = await apiClient.get(`${this.baseUrl}/subcities/`, { params });
    return response.data;
  }

  async getSubCity(id: number): Promise<SubCity> {
    const response = await apiClient.get(`${this.baseUrl}/subcities/${id}/`);
    return response.data;
  }

  async createSubCity(data: SubCityCreate): Promise<SubCity> {
    const response = await apiClient.post(`${this.baseUrl}/subcities/`, data);
    return response.data;
  }

  async updateSubCity(id: number, data: Partial<SubCityCreate>): Promise<SubCity> {
    const response = await apiClient.patch(`${this.baseUrl}/subcities/${id}/`, data);
    return response.data;
  }

  async deleteSubCity(id: number): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/subcities/${id}/`);
  }

  async getSubCitiesSelect(cityId?: number): Promise<SubCitySelect[]> {
    const params = cityId ? { city: cityId } : {};
    const response = await apiClient.get(`${this.baseUrl}/subcities/select/`, { params });
    return response.data;
  }

  // Kebeles
  async getKebeles(params?: {
    page?: number;
    page_size?: number;
    search?: string;
    subcity?: number;
    is_active?: boolean;
  }): Promise<LocationHierarchyListResponse<Kebele>> {
    const response = await apiClient.get(`${this.baseUrl}/kebeles/`, { params });
    return response.data;
  }

  async getKebele(id: number): Promise<Kebele> {
    const response = await apiClient.get(`${this.baseUrl}/kebeles/${id}/`);
    return response.data;
  }

  async createKebele(data: KebeleCreate): Promise<Kebele> {
    const response = await apiClient.post(`${this.baseUrl}/kebeles/`, data);
    return response.data;
  }

  async updateKebele(id: number, data: Partial<KebeleCreate>): Promise<Kebele> {
    const response = await apiClient.patch(`${this.baseUrl}/kebeles/${id}/`, data);
    return response.data;
  }

  async deleteKebele(id: number): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/kebeles/${id}/`);
  }

  async getKebelesSelect(subCityId?: number): Promise<KebeleSelect[]> {
    const params = subCityId ? { subcity: subCityId } : {};
    const response = await apiClient.get(`${this.baseUrl}/kebeles/select/`, { params });
    return response.data;
  }

  // Special Location methods
  async getSpecialLocations(params?: { search?: string; kebele?: string; page?: number; page_size?: number }): Promise<LocationHierarchyListResponse<SpecialLocation>> {
    const response = await apiClient.get(`${this.baseUrl}/special-locations/`, { params });
    return response.data;
  }

  async getSpecialLocation(id: number): Promise<SpecialLocation> {
    const response = await apiClient.get(`${this.baseUrl}/special-locations/${id}/`);
    return response.data;
  }

  async createSpecialLocation(data: SpecialLocationCreate): Promise<SpecialLocation> {
    const response = await apiClient.post(`${this.baseUrl}/special-locations/`, data);
    return response.data;
  }

  async updateSpecialLocation(id: number, data: SpecialLocationCreate): Promise<SpecialLocation> {
    const response = await apiClient.put(`${this.baseUrl}/special-locations/${id}/`, data);
    return response.data;
  }

  async deleteSpecialLocation(id: number): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/special-locations/${id}/`);
  }

  async getSpecialLocationsSelect(kebeleId?: number): Promise<SpecialLocationSelect[]> {
    const params = kebeleId ? { kebele: kebeleId } : {};
    const response = await apiClient.get(`${this.baseUrl}/special-locations/select/`, { params });
    return response.data;
  }

  // Utility methods
  getSubCityTypeOptions() {
    return [
      { value: 'subcity', label: 'Sub City' },
      { value: 'woreda', label: 'Woreda' },
      { value: 'district', label: 'District' },
    ];
  }

  getStatusColor(isActive: boolean): 'success' | 'default' {
    return isActive ? 'success' : 'default';
  }

  getStatusText(isActive: boolean): string {
    return isActive ? 'Active' : 'Inactive';
  }
}

export default new LocationHierarchyService();

// Explicit re-exports to ensure all interfaces are available
export type {
  Country,
  Region,
  Zone,
  City,
  SubCity,
  Kebele,
  CountryCreate,
  RegionCreate,
  ZoneCreate,
  CityCreate,
  SubCityCreate,
  KebeleCreate,
  CountrySelect,
  RegionSelect,
  ZoneSelect,
  CitySelect,
  SubCitySelect,
  KebeleSelect,
  SpecialLocation,
  SpecialLocationCreate,
  SpecialLocationSelect,
  LocationHierarchyListResponse
};
