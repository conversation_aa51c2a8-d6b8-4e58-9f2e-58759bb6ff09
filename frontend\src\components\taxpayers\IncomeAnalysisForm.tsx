import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Grid,
  Autocomplete,
  InputAdornment,
} from '@mui/material';
import {
  Assessment,
  Cancel,
  Save,
  AttachMoney,
  CalendarToday,
  Person,
  Business,
  TrendingUp,
} from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import taxpayerService from '../../services/taxpayerService';

interface TaxPayerLevel {
  id: string;
  code: string;
  name: string;
  daily_income_range: string;
  min_daily_income: string;
  max_daily_income?: string;
  tax_rate_percentage: string;
  priority: number;
}

interface Taxpayer {
  id: string;
  name: string;
  tin: string;
  type: 'individual' | 'organization';
  current_level?: TaxPayerLevel;
}

interface IncomeAnalysisFormData {
  analysis_year: number;
  taxpayer_type: 'individual' | 'organization';
  taxpayer_id: string;
  total_annual_income: string;
  working_days: number;
  current_level: string;
}

interface IncomeAnalysisFormProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  preselectedTaxpayer?: Taxpayer;
}

const IncomeAnalysisForm: React.FC<IncomeAnalysisFormProps> = ({
  open,
  onClose,
  onSuccess,
  preselectedTaxpayer,
}) => {
  const { showNotification } = useNotification();
  const [loading, setLoading] = useState(false);
  const [levels, setLevels] = useState<TaxPayerLevel[]>([]);
  const [taxpayers, setTaxpayers] = useState<Taxpayer[]>([]);
  const [selectedTaxpayer, setSelectedTaxpayer] = useState<Taxpayer | null>(preselectedTaxpayer || null);
  const [formData, setFormData] = useState<IncomeAnalysisFormData>({
    analysis_year: new Date().getFullYear(),
    taxpayer_type: 'individual',
    taxpayer_id: '',
    total_annual_income: '',
    working_days: 365,
    current_level: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (open) {
      loadLevels();
      if (!preselectedTaxpayer) {
        loadTaxpayers();
      }
    }
  }, [open, preselectedTaxpayer]);

  useEffect(() => {
    if (preselectedTaxpayer) {
      setSelectedTaxpayer(preselectedTaxpayer);
      setFormData(prev => ({
        ...prev,
        taxpayer_type: preselectedTaxpayer.type,
        taxpayer_id: preselectedTaxpayer.id,
        current_level: preselectedTaxpayer.current_level?.id || '',
      }));

      // Clear current level error if taxpayer has a level
      if (preselectedTaxpayer.current_level?.id && errors.current_level) {
        setErrors(prev => ({ ...prev, current_level: '' }));
      }
    }
  }, [preselectedTaxpayer]);

  const loadLevels = async () => {
    try {
      const data = await taxpayerService.getTaxPayerLevelsSimple();
      setLevels(data);
    } catch (error) {
      console.error('Failed to load levels:', error);
      showNotification('Failed to load taxpayer levels', 'error');
    }
  };

  const loadTaxpayers = async () => {
    try {
      // Load both individual and organization taxpayers with expanded data
      const [individuals, organizations] = await Promise.all([
        taxpayerService.getIndividualTaxPayers({ page_size: 200 }), // Increased page size
        taxpayerService.getOrganizationTaxPayers({ page_size: 200 }), // Increased page size
      ]);

      const allTaxpayers: Taxpayer[] = [
        ...individuals.results.map((tp: any) => ({
          id: tp.id,
          name: tp.full_name,
          tin: tp.tin,
          type: 'individual' as const,
          current_level: tp.tax_payer_level_details || (tp.tax_payer_level ? {
            id: tp.tax_payer_level.id,
            code: tp.tax_payer_level.code,
            name: tp.tax_payer_level.name,
          } : null),
        })),
        ...organizations.results.map((tp: any) => ({
          id: tp.id,
          name: tp.business_name,
          tin: tp.tin,
          type: 'organization' as const,
          current_level: tp.tax_payer_level_details || (tp.tax_payer_level ? {
            id: tp.tax_payer_level.id,
            code: tp.tax_payer_level.code,
            name: tp.tax_payer_level.name,
          } : null),
        })),
      ];

      // Sort taxpayers: those with levels first, then by name
      allTaxpayers.sort((a, b) => {
        if (a.current_level && !b.current_level) return -1;
        if (!a.current_level && b.current_level) return 1;
        return a.name.localeCompare(b.name);
      });

      setTaxpayers(allTaxpayers);

      // Debug logging
      console.log('Loaded taxpayers:', allTaxpayers.length);
      console.log('Taxpayers with levels:', allTaxpayers.filter(tp => tp.current_level).length);
      console.log('Sample taxpayer with level:', allTaxpayers.find(tp => tp.current_level));
    } catch (error) {
      console.error('Failed to load taxpayers:', error);
      showNotification('Failed to load taxpayers', 'error');
    }
  };

  const handleInputChange = (field: keyof IncomeAnalysisFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleTaxpayerChange = (taxpayer: Taxpayer | null) => {
    setSelectedTaxpayer(taxpayer);
    if (taxpayer) {
      setFormData(prev => ({
        ...prev,
        taxpayer_type: taxpayer.type,
        taxpayer_id: taxpayer.id,
        current_level: taxpayer.current_level?.id || '',
      }));

      // Clear current level error if taxpayer has a level
      if (taxpayer.current_level?.id && errors.current_level) {
        setErrors(prev => ({ ...prev, current_level: '' }));
      }
    } else {
      setFormData(prev => ({
        ...prev,
        taxpayer_id: '',
        current_level: '',
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Analysis year validation
    const currentYear = new Date().getFullYear();
    if (!formData.analysis_year) {
      newErrors.analysis_year = 'Analysis year is required';
    } else if (formData.analysis_year < 2020) {
      newErrors.analysis_year = 'Analysis year cannot be before 2020';
    } else if (formData.analysis_year > currentYear) {
      newErrors.analysis_year = 'Analysis year cannot be in the future';
    }

    // Taxpayer validation
    if (!formData.taxpayer_id) {
      newErrors.taxpayer_id = 'Please select a taxpayer for analysis';
    }

    // Annual income validation
    const annualIncome = parseFloat(formData.total_annual_income);
    if (!formData.total_annual_income) {
      newErrors.total_annual_income = 'Annual income is required';
    } else if (isNaN(annualIncome) || annualIncome <= 0) {
      newErrors.total_annual_income = 'Please enter a valid positive income amount';
    } else if (annualIncome > *********) {
      newErrors.total_annual_income = 'Income amount seems unreasonably high';
    }

    // Working days validation
    if (!formData.working_days) {
      newErrors.working_days = 'Working days is required';
    } else if (formData.working_days < 1) {
      newErrors.working_days = 'Working days must be at least 1';
    } else if (formData.working_days > 366) {
      newErrors.working_days = 'Working days cannot exceed 366';
    }

    // Current level validation - should be auto-populated from taxpayer
    if (!formData.current_level) {
      if (formData.taxpayer_id) {
        newErrors.current_level = 'Selected taxpayer does not have a current level assigned. Please assign a level to this taxpayer first.';
      } else {
        newErrors.current_level = 'Current level will be automatically determined when you select a taxpayer';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    // Ensure current level is set from selected taxpayer
    const currentTaxpayer = selectedTaxpayer || preselectedTaxpayer;
    if (!currentTaxpayer?.current_level?.id) {
      showNotification('Selected taxpayer does not have a current level assigned', 'error');
      return;
    }

    setLoading(true);
    try {
      const analysisData = {
        ...formData,
        total_annual_income: parseFloat(formData.total_annual_income),
        current_level: currentTaxpayer.current_level.id, // Ensure we use the taxpayer's actual level
      };

      await taxpayerService.createIncomeAnalysis(analysisData);
      showNotification('Income analysis created successfully', 'success');
      onSuccess();
      handleClose();
    } catch (error: any) {
      console.error('Failed to create income analysis:', error);
      const errorMessage = error.response?.data?.error ||
                          error.response?.data?.detail ||
                          'Failed to create income analysis';
      showNotification(errorMessage, 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setFormData({
        analysis_year: new Date().getFullYear(),
        taxpayer_type: 'individual',
        taxpayer_id: '',
        total_annual_income: '',
        working_days: 365,
        current_level: '',
      });
      setSelectedTaxpayer(null);
      setErrors({});
      onClose();
    }
  };

  const calculateDailyIncome = () => {
    const annual = parseFloat(formData.total_annual_income);
    const days = formData.working_days;
    if (annual > 0 && days > 0) {
      return (annual / days).toFixed(2);
    }
    return '0.00';
  };

  return (
    <Dialog 
      open={open} 
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
        }
      }}
    >
      <DialogTitle sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        gap: 2,
        pb: 1,
        borderBottom: '1px solid',
        borderColor: 'divider'
      }}>
        <Assessment color="primary" />
        <Box>
          <Typography variant="h6" component="div">
            Create Income Analysis
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Analyze taxpayer income for level assessment
          </Typography>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ pt: 3 }}>
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <CalendarToday color="primary" />
            Analysis Period
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Specify the year and working period for income analysis
          </Typography>

          <Grid container spacing={2}>
            {/* Analysis Year */}
            <Grid item xs={12} sm={6}>
              <TextField
                label="Analysis Year *"
                type="number"
                value={formData.analysis_year}
                onChange={(e) => handleInputChange('analysis_year', parseInt(e.target.value))}
                error={!!errors.analysis_year}
                helperText={errors.analysis_year || 'Year for income analysis'}
                fullWidth
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <CalendarToday />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>

            {/* Working Days */}
            <Grid item xs={12} sm={6}>
              <TextField
                label="Working Days *"
                type="number"
                value={formData.working_days}
                onChange={(e) => handleInputChange('working_days', parseInt(e.target.value))}
                error={!!errors.working_days}
                helperText={errors.working_days || 'Number of working days in the year (default: 365)'}
                fullWidth
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <CalendarToday />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
          </Grid>
        </Box>

        {/* Taxpayer Selection Section */}
        {!preselectedTaxpayer && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Person color="primary" />
              Taxpayer Selection
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Search and select the taxpayer for income analysis
            </Typography>

            <Autocomplete
              options={taxpayers}
              getOptionLabel={(option) => `${option.name} (${option.tin})`}
              value={selectedTaxpayer}
              onChange={(_, value) => handleTaxpayerChange(value)}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Select Taxpayer *"
                  error={!!errors.taxpayer_id}
                  helperText={errors.taxpayer_id || 'Search by name or TIN number'}
                  InputProps={{
                    ...params.InputProps,
                    startAdornment: (
                      <InputAdornment position="start">
                        {selectedTaxpayer?.type === 'individual' ? <Person /> : <Business />}
                      </InputAdornment>
                    ),
                  }}
                />
              )}
              renderOption={(props, option) => (
                <Box component="li" {...props}>
                  <Box display="flex" alignItems="center" gap={2} width="100%">
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: 40,
                      height: 40,
                      borderRadius: 1,
                      bgcolor: option.type === 'individual' ? 'primary.light' : 'secondary.light',
                      color: 'white'
                    }}>
                      {option.type === 'individual' ? <Person /> : <Business />}
                    </Box>
                    <Box flex={1}>
                      <Typography variant="body1" fontWeight="medium">
                        {option.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        TIN: {option.tin} • {option.type === 'individual' ? 'Individual' : 'Organization'}
                      </Typography>
                      <Box display="flex" alignItems="center" gap={1}>
                        <Typography variant="caption" color="text.secondary">
                          Current Level:
                        </Typography>
                        {option.current_level?.code ? (
                          <Box display="flex" alignItems="center" gap={0.5}>
                            <Box sx={{
                              minWidth: 20,
                              height: 16,
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              borderRadius: 0.5,
                              bgcolor: option.current_level.code === 'A' ? 'success.main' :
                                      option.current_level.code === 'B' ? 'warning.main' : 'info.main',
                              color: 'white',
                              fontSize: '0.6rem',
                              fontWeight: 'bold'
                            }}>
                              {option.current_level.code}
                            </Box>
                            <Typography variant="caption" color="success.main" fontWeight="medium">
                              ✅ {option.current_level.name}
                            </Typography>
                          </Box>
                        ) : (
                          <Typography variant="caption" color="error.main" fontWeight="medium">
                            ⚠️ Not assigned (needs level assignment)
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  </Box>
                </Box>
              )}
              noOptionsText="No taxpayers found. Try a different search term."
              loading={taxpayers.length === 0}
            />

            {/* Helper info about taxpayers without levels */}
            {taxpayers.length > 0 && (
              <Box sx={{ mt: 2 }}>
                {(() => {
                  const taxpayersWithoutLevels = taxpayers.filter(tp => !tp.current_level);
                  const taxpayersWithLevels = taxpayers.filter(tp => tp.current_level);

                  return (
                    <Alert severity="info" sx={{ fontSize: '0.875rem' }}>
                      <Typography variant="body2" gutterBottom>
                        <strong>Taxpayer Status Summary:</strong>
                      </Typography>
                      <Typography variant="body2">
                        • <strong>{taxpayersWithLevels.length}</strong> taxpayers have assigned levels (ready for analysis)
                        {taxpayersWithoutLevels.length > 0 && (
                          <>
                            <br />
                            • <strong>{taxpayersWithoutLevels.length}</strong> taxpayers need level assignment first
                          </>
                        )}
                      </Typography>
                      {taxpayersWithoutLevels.length > 0 && (
                        <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                          💡 Tip: Assign levels to taxpayers in their detail pages before creating income analyses
                        </Typography>
                      )}
                    </Alert>
                  );
                })()}
              </Box>
            )}
          </Box>
        )}

        {/* Selected Taxpayer Info for Preselected */}
        {preselectedTaxpayer && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Person color="primary" />
              Selected Taxpayer
            </Typography>
            <Alert severity="info" sx={{ mb: 2 }}>
              <Box display="flex" alignItems="center" gap={2}>
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 40,
                  height: 40,
                  borderRadius: 1,
                  bgcolor: preselectedTaxpayer.type === 'individual' ? 'primary.light' : 'secondary.light',
                  color: 'white'
                }}>
                  {preselectedTaxpayer.type === 'individual' ? <Person /> : <Business />}
                </Box>
                <Box>
                  <Typography variant="body1" fontWeight="medium">
                    {preselectedTaxpayer.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    TIN: {preselectedTaxpayer.tin} • {preselectedTaxpayer.type === 'individual' ? 'Individual' : 'Organization'}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Current Level: {preselectedTaxpayer.current_level?.code ? `${preselectedTaxpayer.current_level.code} - ${preselectedTaxpayer.current_level.name}` : 'Not assigned'}
                  </Typography>
                </Box>
              </Box>
            </Alert>
          </Box>
        )}

        {/* Current Level Section */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Assessment color="primary" />
            Current Taxpayer Level
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            The current level is automatically determined from the selected taxpayer's record
          </Typography>

          {(selectedTaxpayer?.current_level || preselectedTaxpayer?.current_level) ? (
            // Show current level information card
            <Alert severity="info" sx={{ mb: 2 }}>
              <Box display="flex" alignItems="center" gap={2}>
                <Box sx={{
                  minWidth: 50,
                  height: 50,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderRadius: 2,
                  bgcolor: (() => {
                    const level = selectedTaxpayer?.current_level || preselectedTaxpayer?.current_level;
                    return level?.code === 'A' ? 'success.main' : level?.code === 'B' ? 'warning.main' : 'info.main';
                  })(),
                  color: 'white',
                  fontSize: '1.2rem',
                  fontWeight: 'bold'
                }}>
                  {(selectedTaxpayer?.current_level || preselectedTaxpayer?.current_level)?.code}
                </Box>
                <Box flex={1}>
                  <Typography variant="h6" fontWeight="bold">
                    Level {(selectedTaxpayer?.current_level || preselectedTaxpayer?.current_level)?.code} - {(selectedTaxpayer?.current_level || preselectedTaxpayer?.current_level)?.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {(() => {
                      const level = levels.find(l => l.id === (selectedTaxpayer?.current_level?.id || preselectedTaxpayer?.current_level?.id));
                      return level ? `${level.daily_income_range} • ${level.tax_rate_percentage}% tax rate` : 'Level details loading...';
                    })()}
                  </Typography>
                  <Typography variant="caption" color="success.main" sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mt: 0.5 }}>
                    ✅ Automatically detected from taxpayer record
                  </Typography>
                </Box>
              </Box>
            </Alert>
          ) : formData.taxpayer_id ? (
            // Show error if taxpayer selected but no level
            <Alert severity="warning">
              <Typography variant="body2">
                <strong>No Current Level Assigned</strong>
              </Typography>
              <Typography variant="body2" color="text.secondary">
                The selected taxpayer does not have a current level assigned. Please assign a level to this taxpayer before creating an income analysis.
              </Typography>
            </Alert>
          ) : (
            // Show instruction if no taxpayer selected
            <Alert severity="info">
              <Typography variant="body2">
                <strong>Select a Taxpayer First</strong>
              </Typography>
              <Typography variant="body2" color="text.secondary">
                The current level will be automatically populated when you select a taxpayer above.
              </Typography>
            </Alert>
          )}
        </Box>

        {/* Income Analysis Section */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <AttachMoney color="primary" />
            Income Information
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Enter the total annual income for the analysis period
          </Typography>

          <TextField
            label="Total Annual Income *"
            type="number"
            value={formData.total_annual_income}
            onChange={(e) => handleInputChange('total_annual_income', e.target.value)}
            error={!!errors.total_annual_income}
            helperText={errors.total_annual_income || 'Enter the total income earned during the analysis year'}
            fullWidth
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <AttachMoney />
                </InputAdornment>
              ),
            }}
          />
        </Box>

        {/* Daily Income Preview */}
        {formData.total_annual_income && formData.working_days && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <TrendingUp color="primary" />
              Analysis Preview
            </Typography>

            <Alert severity="info" sx={{ mb: 2 }}>
              <Box display="flex" alignItems="center" gap={2}>
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 60,
                  height: 60,
                  borderRadius: 2,
                  bgcolor: 'info.light',
                  color: 'white'
                }}>
                  <AttachMoney sx={{ fontSize: 30 }} />
                </Box>
                <Box>
                  <Typography variant="h5" fontWeight="bold" color="info.main">
                    ${calculateDailyIncome()}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Average Daily Income
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Based on ${parseFloat(formData.total_annual_income || '0').toLocaleString()} annual income ÷ {formData.working_days} days
                  </Typography>
                </Box>
              </Box>
            </Alert>

            {/* Level Recommendation Preview */}
            {(() => {
              const dailyIncome = parseFloat(calculateDailyIncome());
              const recommendedLevel = levels.find(level => {
                const min = parseFloat(level.min_daily_income || '0');
                const max = level.max_daily_income ? parseFloat(level.max_daily_income) : Infinity;
                return dailyIncome >= min && dailyIncome <= max;
              });

              if (recommendedLevel) {
                const currentLevel = levels.find(l => l.id === formData.current_level);
                const isUpgrade = currentLevel && recommendedLevel.priority < currentLevel.priority;
                const isDowngrade = currentLevel && recommendedLevel.priority > currentLevel.priority;
                const isSame = currentLevel && recommendedLevel.id === currentLevel.id;

                return (
                  <Alert
                    severity={isUpgrade ? "success" : isDowngrade ? "warning" : "info"}
                    sx={{ mt: 2 }}
                  >
                    <Typography variant="body2" gutterBottom>
                      <strong>Recommended Level:</strong> {recommendedLevel.code} - {recommendedLevel.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {recommendedLevel.daily_income_range} • {recommendedLevel.tax_rate_percentage}% tax rate
                    </Typography>
                    {isUpgrade && (
                      <Typography variant="body2" color="success.main" sx={{ mt: 1 }}>
                        ⬆️ <strong>Level Upgrade Recommended</strong> - This taxpayer qualifies for a higher level
                      </Typography>
                    )}
                    {isDowngrade && (
                      <Typography variant="body2" color="warning.main" sx={{ mt: 1 }}>
                        ⬇️ <strong>Level Downgrade Suggested</strong> - Current level may be too high
                      </Typography>
                    )}
                    {isSame && (
                      <Typography variant="body2" color="info.main" sx={{ mt: 1 }}>
                        ✅ <strong>Current Level Appropriate</strong> - No level change needed
                      </Typography>
                    )}
                  </Alert>
                );
              }
              return null;
            })()}
          </Box>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 2, borderTop: '1px solid', borderColor: 'divider' }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" width="100%">
          <Typography variant="caption" color="text.secondary">
            {(() => {
              const currentTaxpayer = selectedTaxpayer || preselectedTaxpayer;
              if (!formData.taxpayer_id) {
                return 'Select a taxpayer to begin analysis';
              } else if (!currentTaxpayer?.current_level) {
                return 'Selected taxpayer needs a current level assignment';
              } else if (!formData.total_annual_income) {
                return 'Enter annual income to calculate daily average';
              } else if (formData.total_annual_income && formData.working_days) {
                return `Analysis will calculate $${calculateDailyIncome()} daily income`;
              }
              return 'Fill in the form to see analysis preview';
            })()}
          </Typography>
          <Box display="flex" gap={1}>
            <Button
              onClick={handleClose}
              disabled={loading}
              startIcon={<Cancel />}
              size="large"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              variant="contained"
              disabled={(() => {
                const currentTaxpayer = selectedTaxpayer || preselectedTaxpayer;
                return loading ||
                       !formData.taxpayer_id ||
                       !formData.total_annual_income ||
                       !currentTaxpayer?.current_level?.id;
              })()}
              startIcon={loading ? <CircularProgress size={16} /> : <Save />}
              size="large"
            >
              {loading ? 'Creating Analysis...' : 'Create Analysis'}
            </Button>
          </Box>
        </Box>
      </DialogActions>
    </Dialog>
  );
};

export default IncomeAnalysisForm;
