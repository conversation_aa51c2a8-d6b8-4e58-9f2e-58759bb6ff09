import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Grid,
  LinearProgress,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert,
  Skeleton,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Tooltip,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  Warning,
  CheckCircle,
  Business,
  Shelves,
  Inventory,
  Description,
  Refresh,
  Download,
  Analytics,
} from '@mui/icons-material';
import { Pie<PERSON>hart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
import { useNotification } from '../../contexts/NotificationContext';
import locationService from '../../services/locationService';
import organizationService from '../../services/organizationService';
import type { Organization } from '../../services/types';

interface LocationAnalytics {
  total_buildings: number;
  total_shelves: number;
  total_kents: number;
  total_files: number;
  total_capacity: number;
  used_capacity: number;
  utilization_percentage: number;
  recent_files_count: number;
  building_stats: Array<{
    id: number;
    name: string;
    code: string;
    shelves_count: number;
    kents_count: number;
    files_count: number;
    capacity: number;
    documents_count: number;
    utilization_percentage: number;
  }>;
  shelf_utilization_distribution: {
    empty: number;
    low: number;
    medium: number;
    high: number;
    full: number;
  };
  organization_id?: number;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

const LocationAnalytics: React.FC = () => {
  const { showError } = useNotification();

  const [analytics, setAnalytics] = useState<LocationAnalytics | null>(null);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [selectedOrganization, setSelectedOrganization] = useState<number | ''>('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    loadAnalytics();
  }, [selectedOrganization]);

  const loadInitialData = async () => {
    try {
      const orgsData = await organizationService.getOrganizations();
      setOrganizations(orgsData.results);
    } catch (err) {
      showError('Failed to load organizations');
    }
  };

  const loadAnalytics = async () => {
    try {
      setLoading(true);
      const data = await locationService.getLocationSummary(
        selectedOrganization ? selectedOrganization as number : undefined
      );
      setAnalytics(data);
    } catch (err) {
      showError('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  const getUtilizationColor = (utilization: number) => {
    if (utilization < 50) return 'success';
    if (utilization < 80) return 'warning';
    return 'error';
  };

  const getUtilizationIcon = (utilization: number) => {
    if (utilization < 50) return <CheckCircle color="success" />;
    if (utilization < 80) return <Warning color="warning" />;
    return <TrendingUp color="error" />;
  };

  const renderSummaryCards = () => {
    if (!analytics) return null;

    const cards = [
      {
        title: 'Total Buildings',
        value: analytics.total_buildings,
        icon: <Business />,
        color: 'primary',
      },
      {
        title: 'Total Shelves',
        value: analytics.total_shelves,
        icon: <Shelves />,
        color: 'secondary',
      },
      {
        title: 'Total Kents',
        value: analytics.total_kents,
        icon: <Inventory />,
        color: 'success',
      },
      {
        title: 'Total Files',
        value: analytics.total_files,
        icon: <Description />,
        color: 'info',
      },
      {
        title: 'Overall Utilization',
        value: `${analytics.utilization_percentage.toFixed(1)}%`,
        icon: getUtilizationIcon(analytics.utilization_percentage),
        color: getUtilizationColor(analytics.utilization_percentage),
        subtitle: `${analytics.used_capacity} / ${analytics.total_capacity} documents`,
      },
      {
        title: 'Recent Activity',
        value: analytics.recent_files_count,
        icon: <TrendingUp />,
        color: 'warning',
        subtitle: 'Files created in last 30 days',
      },
    ];

    return (
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {cards.map((card, index) => (
          <Grid item xs={12} sm={6} md={4} lg={2} key={index}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{ color: `${card.color}.main` }}>
                    {card.icon}
                  </Box>
                  <Box sx={{ flexGrow: 1 }}>
                    <Typography variant="h5" component="div">
                      {card.value}
                    </Typography>
                    <Typography color="text.secondary" variant="body2">
                      {card.title}
                    </Typography>
                    {card.subtitle && (
                      <Typography color="text.secondary" variant="caption">
                        {card.subtitle}
                      </Typography>
                    )}
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    );
  };

  const renderUtilizationDistribution = () => {
    if (!analytics) return null;

    const data = [
      { name: 'Empty (0%)', value: analytics.shelf_utilization_distribution.empty, color: '#E0E0E0' },
      { name: 'Low (1-30%)', value: analytics.shelf_utilization_distribution.low, color: '#4CAF50' },
      { name: 'Medium (31-70%)', value: analytics.shelf_utilization_distribution.medium, color: '#FF9800' },
      { name: 'High (71-90%)', value: analytics.shelf_utilization_distribution.high, color: '#F44336' },
      { name: 'Full (91-100%)', value: analytics.shelf_utilization_distribution.full, color: '#9C27B0' },
    ];

    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Shelf Utilization Distribution
          </Typography>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, value, percent }) => `${name}: ${value} (${(percent * 100).toFixed(0)}%)`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <RechartsTooltip />
            </PieChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    );
  };

  const renderBuildingPerformance = () => {
    if (!analytics || !analytics.building_stats.length) return null;

    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Building Performance
          </Typography>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Building</TableCell>
                  <TableCell align="right">Shelves</TableCell>
                  <TableCell align="right">Kents</TableCell>
                  <TableCell align="right">Files</TableCell>
                  <TableCell align="right">Documents</TableCell>
                  <TableCell align="right">Utilization</TableCell>
                  <TableCell align="right">Status</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {analytics.building_stats.map((building) => (
                  <TableRow key={building.id} hover>
                    <TableCell>
                      <Box>
                        <Typography variant="subtitle2" fontWeight="bold">
                          {building.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {building.code}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell align="right">{building.shelves_count}</TableCell>
                    <TableCell align="right">{building.kents_count}</TableCell>
                    <TableCell align="right">{building.files_count}</TableCell>
                    <TableCell align="right">{building.documents_count}</TableCell>
                    <TableCell align="right">
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Box sx={{ width: 60 }}>
                          <LinearProgress
                            variant="determinate"
                            value={Math.min(building.utilization_percentage, 100)}
                            color={getUtilizationColor(building.utilization_percentage)}
                          />
                        </Box>
                        <Typography variant="body2">
                          {building.utilization_percentage.toFixed(1)}%
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell align="right">
                      <Chip
                        size="small"
                        label={
                          building.utilization_percentage < 50 ? 'Underutilized' :
                          building.utilization_percentage < 80 ? 'Optimal' :
                          building.utilization_percentage < 95 ? 'High Usage' : 'Overcapacity'
                        }
                        color={getUtilizationColor(building.utilization_percentage)}
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    );
  };

  const renderOptimizationSuggestions = () => {
    if (!analytics) return null;

    const suggestions = [];

    // Check for underutilized buildings
    const underutilizedBuildings = analytics.building_stats.filter(b => b.utilization_percentage < 30);
    if (underutilizedBuildings.length > 0) {
      suggestions.push({
        type: 'info',
        title: 'Underutilized Space',
        message: `${underutilizedBuildings.length} building(s) have low utilization. Consider consolidating files or repurposing space.`,
      });
    }

    // Check for overcapacity buildings
    const overcapacityBuildings = analytics.building_stats.filter(b => b.utilization_percentage > 95);
    if (overcapacityBuildings.length > 0) {
      suggestions.push({
        type: 'warning',
        title: 'Capacity Warning',
        message: `${overcapacityBuildings.length} building(s) are near or at capacity. Consider expanding or redistributing files.`,
      });
    }

    // Check overall utilization
    if (analytics.utilization_percentage > 90) {
      suggestions.push({
        type: 'error',
        title: 'System Capacity Alert',
        message: 'Overall system utilization is very high. Immediate action required to prevent storage issues.',
      });
    }

    // Check for empty shelves
    if (analytics.shelf_utilization_distribution.empty > analytics.total_shelves * 0.3) {
      suggestions.push({
        type: 'info',
        title: 'Empty Shelves',
        message: 'Many shelves are empty. Consider optimizing shelf allocation or reducing physical storage footprint.',
      });
    }

    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Optimization Suggestions
          </Typography>
          {suggestions.length === 0 ? (
            <Alert severity="success">
              Your location utilization is well-balanced. No immediate optimizations needed.
            </Alert>
          ) : (
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              {suggestions.map((suggestion, index) => (
                <Alert key={index} severity={suggestion.type as any}>
                  <Typography variant="subtitle2" fontWeight="bold">
                    {suggestion.title}
                  </Typography>
                  <Typography variant="body2">
                    {suggestion.message}
                  </Typography>
                </Alert>
              ))}
            </Box>
          )}
        </CardContent>
      </Card>
    );
  };

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Skeleton variant="rectangular" height={200} sx={{ mb: 3 }} />
        <Grid container spacing={3}>
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Grid item xs={12} sm={6} md={4} lg={2} key={i}>
              <Skeleton variant="rectangular" height={120} />
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" component="h1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Analytics color="primary" />
          Location Analytics
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <FormControl size="small" sx={{ minWidth: 200 }}>
            <InputLabel>Organization</InputLabel>
            <Select
              value={selectedOrganization}
              onChange={(e) => setSelectedOrganization(e.target.value as number)}
              label="Organization"
            >
              <MenuItem value="">All Organizations</MenuItem>
              {organizations.map((org) => (
                <MenuItem key={org.id} value={org.id}>
                  {org.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={loadAnalytics}
          >
            Refresh
          </Button>
          <Button
            variant="outlined"
            startIcon={<Download />}
            onClick={() => {
              // TODO: Implement export functionality
              console.log('Export analytics data');
            }}
          >
            Export
          </Button>
        </Box>
      </Box>

      {/* Summary Cards */}
      {renderSummaryCards()}

      {/* Charts and Tables */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          {renderUtilizationDistribution()}
        </Grid>
        <Grid item xs={12} md={6}>
          {renderOptimizationSuggestions()}
        </Grid>
        <Grid item xs={12}>
          {renderBuildingPerformance()}
        </Grid>
      </Grid>
    </Box>
  );
};

export default LocationAnalytics;
