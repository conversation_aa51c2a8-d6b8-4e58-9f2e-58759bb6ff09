"""
Location Hierarchy Models for Ethiopian Administrative Structure
Country → Region → Zone → City → SubCity/Woreda → Kebele
"""

from django.db import models
from django.core.validators import RegexValidator
from django.utils.translation import gettext_lazy as _


class Country(models.Model):
    """Country model"""
    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(
        max_length=3, 
        unique=True,
        validators=[RegexValidator(r'^[A-Z]{2,3}$', 'Country code must be 2-3 uppercase letters')]
    )
    phone_code = models.CharField(max_length=10, blank=True)
    currency = models.CharField(max_length=10, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Countries"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"

    def save(self, *args, **kwargs):
        # Convert code to uppercase for case-insensitive storage
        if self.code:
            self.code = self.code.upper()
        super().save(*args, **kwargs)


class Region(models.Model):
    """Region/State model"""
    country = models.ForeignKey(Country, on_delete=models.CASCADE, related_name='regions')
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=20)
    population = models.PositiveIntegerField(null=True, blank=True)
    area_km2 = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    capital_city = models.CharField(max_length=100, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['country', 'code']
        ordering = ['name']

    def __str__(self):
        return f"{self.name}, {self.country.name}"

    @property
    def full_name(self):
        return f"{self.name}, {self.country.name}"

    def save(self, *args, **kwargs):
        # Convert code to uppercase for case-insensitive storage
        if self.code:
            self.code = self.code.upper()
        super().save(*args, **kwargs)


class Zone(models.Model):
    """Zone model"""
    region = models.ForeignKey(Region, on_delete=models.CASCADE, related_name='zones')
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=20)
    population = models.PositiveIntegerField(null=True, blank=True)
    area_km2 = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['region', 'code']
        ordering = ['name']

    def __str__(self):
        return f"{self.name}, {self.region.name}"

    @property
    def full_name(self):
        return f"{self.name}, {self.region.name}, {self.region.country.name}"

    def save(self, *args, **kwargs):
        # Convert code to uppercase for case-insensitive storage
        if self.code:
            self.code = self.code.upper()
        super().save(*args, **kwargs)


class City(models.Model):
    """City model"""
    zone = models.ForeignKey(Zone, on_delete=models.CASCADE, related_name='cities')
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=20)
    population = models.PositiveIntegerField(null=True, blank=True)
    area_km2 = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    is_capital = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['zone', 'code']
        ordering = ['name']
        verbose_name_plural = "Cities"

    def __str__(self):
        return f"{self.name}, {self.zone.name}"

    @property
    def full_name(self):
        return f"{self.name}, {self.zone.name}, {self.zone.region.name}, {self.zone.region.country.name}"

    def save(self, *args, **kwargs):
        # Convert code to uppercase for case-insensitive storage
        if self.code:
            self.code = self.code.upper()
        super().save(*args, **kwargs)


class SubCity(models.Model):
    """SubCity/Woreda model"""
    city = models.ForeignKey(City, on_delete=models.CASCADE, related_name='subcities')
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=20)
    type = models.CharField(
        max_length=20,
        choices=[
            ('subcity', 'Sub City'),
            ('woreda', 'Woreda'),
            ('district', 'District'),
        ],
        default='subcity'
    )
    population = models.PositiveIntegerField(null=True, blank=True)
    area_km2 = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['city', 'code']
        ordering = ['name']
        verbose_name_plural = "SubCities/Woredas"

    def __str__(self):
        return f"{self.name} ({self.get_type_display()}), {self.city.name}"

    @property
    def full_name(self):
        return f"{self.name}, {self.city.name}, {self.city.zone.name}, {self.city.zone.region.name}, {self.city.zone.region.country.name}"

    def save(self, *args, **kwargs):
        # Convert code to uppercase for case-insensitive storage
        if self.code:
            self.code = self.code.upper()
        super().save(*args, **kwargs)


class Kebele(models.Model):
    """Kebele model - smallest administrative unit"""
    subcity = models.ForeignKey(SubCity, on_delete=models.CASCADE, related_name='kebeles')
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=20)
    number = models.PositiveIntegerField(help_text="Kebele number")
    population = models.PositiveIntegerField(null=True, blank=True)
    area_km2 = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['subcity', 'code']
        ordering = ['number', 'name']

    def __str__(self):
        return f"Kebele {self.number:02d} - {self.name}, {self.subcity.name}"

    @property
    def full_name(self):
        return f"Kebele {self.number:02d} - {self.name}, {self.subcity.name}, {self.subcity.city.name}, {self.subcity.city.zone.name}, {self.subcity.city.zone.region.name}, {self.subcity.city.zone.region.country.name}"

    def save(self, *args, **kwargs):
        # Convert code to uppercase for case-insensitive storage
        if self.code:
            self.code = self.code.upper()
        super().save(*args, **kwargs)

    @property
    def display_name(self):
        return f"Kebele {self.number:02d} - {self.name}"


class SpecialLocation(models.Model):
    """Special Location/Place model - specific places within a Kebele"""
    kebele = models.ForeignKey(Kebele, on_delete=models.CASCADE, related_name='special_locations')
    name = models.CharField(max_length=100, help_text="Name of the special location/place")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['kebele', 'name']
        ordering = ['name']
        verbose_name = 'Special Location'
        verbose_name_plural = 'Special Locations'

    def __str__(self):
        return f"{self.name} - {self.kebele.display_name}"

    @property
    def full_name(self):
        return f"{self.name}, {self.kebele.full_name}"

    @property
    def display_name(self):
        return self.name

    @property
    def location_path(self):
        """Return the full hierarchical path"""
        return f"{self.kebele.subcity.city.zone.region.country.name} → {self.kebele.subcity.city.zone.region.name} → {self.kebele.subcity.city.zone.name} → {self.kebele.subcity.city.name} → {self.kebele.subcity.name} → {self.kebele.display_name} → {self.name}"
