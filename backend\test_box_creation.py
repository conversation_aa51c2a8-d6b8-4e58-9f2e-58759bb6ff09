#!/usr/bin/env python
"""
Test script to verify Box creation works correctly in admin
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'arada_dms.settings')
django.setup()

from organizations.models import Organization
from locations.models import Building, Shelf, Box

def test_box_creation():
    print("Testing Box creation and string representation")
    print("=" * 60)
    
    # Get existing organization and building
    org = Organization.objects.first()
    if not org:
        print("❌ No organization found. Run test_hierarchy.py first.")
        return
    
    building = Building.objects.first()
    if not building:
        print("❌ No building found. Run test_hierarchy.py first.")
        return
    
    shelf = Shelf.objects.first()
    if not shelf:
        print("❌ No shelf found. Run test_hierarchy.py first.")
        return
    
    print(f"Using shelf: {shelf}")
    print(f"Shelf grid: {shelf.rows}x{shelf.columns}")
    print()
    
    # Test creating a new box (simulating admin form)
    print("1. Testing new Box creation (before save)")
    new_box = Box(shelf=shelf)
    print(f"   New box string representation: {new_box}")
    print(f"   New box position code: {new_box.position_code}")
    print(f"   New box full code: {new_box.full_code}")
    print("   ✅ New box creation works without errors")
    print()
    
    # Test creating a box with position
    print("2. Testing Box with position (before save)")
    positioned_box = Box(shelf=shelf, row=2, column=3, name="Test Box")
    print(f"   Positioned box string representation: {positioned_box}")
    print(f"   Positioned box position code: {positioned_box.position_code}")
    print(f"   Positioned box full code: {positioned_box.full_code}")
    print("   ✅ Positioned box creation works without errors")
    print()
    
    # Test saving a box
    print("3. Testing Box save")
    try:
        # Check if position is available
        existing = Box.objects.filter(shelf=shelf, row=2, column=3).first()
        if existing:
            print(f"   Position R02C03 already occupied by: {existing}")
            # Try a different position
            row, col = 3, 3
            while Box.objects.filter(shelf=shelf, row=row, column=col).exists():
                col += 1
                if col > shelf.columns:
                    row += 1
                    col = 1
                if row > shelf.rows:
                    print("   ❌ No available positions in shelf")
                    return
        else:
            row, col = 2, 3
        
        test_box = Box(
            shelf=shelf,
            row=row,
            column=col,
            name=f"Test Box R{row:02d}C{col:02d}",
            description="Test box created by test script",
            color="Blue",
            material="Cardboard"
        )
        
        print(f"   Creating box at position R{row:02d}C{col:02d}")
        test_box.save()
        print(f"   Saved box: {test_box}")
        print(f"   Box ID: {test_box.id}")
        print(f"   Box full code: {test_box.full_code}")
        print("   ✅ Box saved successfully")
        
        # Test the box after save
        print(f"   Box after save: {test_box}")
        print(f"   Position code: {test_box.position_code}")
        print(f"   Full code: {test_box.full_code}")
        
    except Exception as e:
        print(f"   ❌ Error saving box: {e}")
    
    print()
    
    # Test validation
    print("4. Testing Box validation")
    try:
        invalid_box = Box(
            shelf=shelf,
            row=shelf.rows + 1,  # Exceeds shelf capacity
            column=1,
            name="Invalid Box"
        )
        invalid_box.full_clean()
        print("   ❌ Validation should have failed")
    except Exception as e:
        print(f"   ✅ Validation correctly failed: {e}")
    
    print()
    
    # Show all boxes in shelf
    print("5. Current boxes in shelf")
    boxes = Box.objects.filter(shelf=shelf).order_by('row', 'column')
    for box in boxes:
        print(f"   {box.position_code}: {box.name or 'Unnamed'} - {box}")
    
    print()
    print("✅ Box creation testing completed!")

if __name__ == '__main__':
    test_box_creation()
