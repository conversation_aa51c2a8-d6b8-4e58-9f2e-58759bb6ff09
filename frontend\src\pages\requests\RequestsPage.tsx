import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  Grid,
  Chip,
  Avatar,
  IconButton,
  Menu,
  MenuItem,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Skeleton,
  Tooltip,
} from '@mui/material';
import {
  Add,
  Search,
  FilterList,
  MoreVert,
  Assignment,
  CheckCircle,
  Block,
  AssignmentTurnedIn,
  Schedule,
  Warning,
  PriorityHigh,
  Visibility,
  Edit,
  Cancel,
} from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import { useAuth } from '../../contexts/AuthContext';
import requestService from '../../services/requestService';
import type { DocumentRequest, RequestStats } from '../../services/requestService';

const RequestsPage: React.FC = () => {
  const navigate = useNavigate();
  const { showSuccess, showError } = useNotification();
  const { user } = useAuth();

  const [requests, setRequests] = useState<DocumentRequest[]>([]);
  const [stats, setStats] = useState<RequestStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [filterPriority, setFilterPriority] = useState('');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedRequest, setSelectedRequest] = useState<DocumentRequest | null>(null);
  const [actionDialogOpen, setActionDialogOpen] = useState(false);
  const [actionType, setActionType] = useState<'approve' | 'reject' | 'cancel' | null>(null);
  const [actionComments, setActionComments] = useState('');
  const [processing, setProcessing] = useState(false);

  const requestStatuses = [
    { value: '', label: 'All Statuses' },
    { value: 'pending', label: 'Pending' },
    { value: 'approved', label: 'Approved' },
    { value: 'checked_out', label: 'Checked Out' },
    { value: 'returned', label: 'Returned' },
    { value: 'rejected', label: 'Rejected' },
    { value: 'overdue', label: 'Overdue' },
    { value: 'cancelled', label: 'Cancelled' },
  ];

  const requestPriorities = [
    { value: '', label: 'All Priorities' },
    { value: 'low', label: 'Low' },
    { value: 'normal', label: 'Normal' },
    { value: 'high', label: 'High' },
    { value: 'urgent', label: 'Urgent' },
  ];

  useEffect(() => {
    loadData();
  }, [page, rowsPerPage, searchQuery, filterStatus, filterPriority]);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      const statsResponse = await requestService.getRequestStats();
      setStats(statsResponse);
    } catch (err: any) {
      showError('Failed to load request statistics');
    }
  };

  const loadData = async () => {
    try {
      setLoading(true);
      const params = {
        page: page + 1,
        page_size: rowsPerPage,
        search: searchQuery || undefined,
        status: filterStatus || undefined,
        priority: filterPriority || undefined,
        ordering: '-created_at',
      };

      const response = await requestService.getRequests(params);
      setRequests(response.results);
      setTotalCount(response.count);
    } catch (err: any) {
      showError('Failed to load requests');
    } finally {
      setLoading(false);
    }
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
    setPage(0);
  };

  const handleFilterChange = (field: string) => (event: any) => {
    const value = event.target.value;
    switch (field) {
      case 'status':
        setFilterStatus(value);
        break;
      case 'priority':
        setFilterPriority(value);
        break;
    }
    setPage(0);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, request: DocumentRequest) => {
    setAnchorEl(event.currentTarget);
    setSelectedRequest(request);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedRequest(null);
  };

  const handleView = () => {
    if (selectedRequest) {
      navigate(`/requests/${selectedRequest.id}`);
    }
    handleMenuClose();
  };

  const handleEdit = () => {
    if (selectedRequest) {
      navigate(`/requests/${selectedRequest.id}/edit`);
    }
    handleMenuClose();
  };

  const handleActionClick = (action: 'approve' | 'reject' | 'cancel') => {
    setActionType(action);
    setActionComments('');
    setActionDialogOpen(true);
    handleMenuClose();
  };

  const handleActionSubmit = async () => {
    if (!selectedRequest || !actionType) return;

    try {
      setProcessing(true);

      switch (actionType) {
        case 'approve':
          await requestService.approveRequest(selectedRequest.id, actionComments);
          showSuccess('Request approved successfully');
          break;
        case 'reject':
          if (!actionComments.trim()) {
            showError('Comments are required when rejecting a request');
            return;
          }
          await requestService.rejectRequest(selectedRequest.id, actionComments);
          showSuccess('Request rejected');
          break;
        case 'cancel':
          await requestService.cancelRequest(selectedRequest.id, actionComments);
          showSuccess('Request cancelled');
          break;
      }

      setActionDialogOpen(false);
      loadData(); // Refresh the list
      loadStats(); // Refresh stats
    } catch (err: any) {
      showError(err.response?.data?.detail || `Failed to ${actionType} request`);
    } finally {
      setProcessing(false);
    }
  };

  const renderStatsCards = () => {
    if (!stats) return null;

    const cards = [
      {
        title: 'Total Requests',
        value: stats.total_requests,
        icon: <Assignment />,
        color: 'primary',
      },
      {
        title: 'Pending',
        value: stats.pending_requests,
        icon: <Schedule />,
        color: 'warning',
      },
      {
        title: 'Checked Out',
        value: stats.checked_out_requests,
        icon: <AssignmentTurnedIn />,
        color: 'info',
      },
      {
        title: 'Overdue',
        value: stats.overdue_requests,
        icon: <Warning />,
        color: 'error',
      },
    ];

    return (
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {cards.map((card, index) => (
          <Grid size={{ xs: 12, sm: 6, md: 3 }} key={index}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ bgcolor: `${card.color}.main` }}>
                    {card.icon}
                  </Avatar>
                  <Box>
                    <Typography variant="h4" component="div">
                      {card.value}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {card.title}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    );
  };

  const renderFilters = () => (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Grid container spacing={2} alignItems="center">
          <Grid size={{ xs: 12, md: 4 }}>
            <TextField
              fullWidth
              placeholder="Search requests..."
              value={searchQuery}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 3 }}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={filterStatus}
                label="Status"
                onChange={handleFilterChange('status')}
              >
                {requestStatuses.map((status) => (
                  <MenuItem key={status.value} value={status.value}>
                    {status.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid size={{ xs: 12, md: 3 }}>
            <FormControl fullWidth>
              <InputLabel>Priority</InputLabel>
              <Select
                value={filterPriority}
                label="Priority"
                onChange={handleFilterChange('priority')}
              >
                {requestPriorities.map((priority) => (
                  <MenuItem key={priority.value} value={priority.value}>
                    {priority.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid size={{ xs: 12, md: 2 }}>
            <Button
              variant="outlined"
              startIcon={<FilterList />}
              onClick={() => {
                setSearchQuery('');
                setFilterStatus('');
                setFilterPriority('');
                setPage(0);
              }}
              fullWidth
            >
              Clear Filters
            </Button>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );

  const renderRequestsTable = () => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">Requests</Typography>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => navigate('/requests/create')}
          >
            New Request
          </Button>
        </Box>

        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Request</TableCell>
                <TableCell>Requester</TableCell>
                <TableCell>Documents</TableCell>
                <TableCell>Priority</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Due Date</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                Array.from({ length: rowsPerPage }).map((_, index) => (
                  <TableRow key={index}>
                    <TableCell><Skeleton /></TableCell>
                    <TableCell><Skeleton /></TableCell>
                    <TableCell><Skeleton /></TableCell>
                    <TableCell><Skeleton /></TableCell>
                    <TableCell><Skeleton /></TableCell>
                    <TableCell><Skeleton /></TableCell>
                    <TableCell><Skeleton /></TableCell>
                  </TableRow>
                ))
              ) : requests.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} align="center">
                    <Typography color="text.secondary">
                      No requests found
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                requests.map((request) => {
                  const nextActions = user ? requestService.getNextActions(request, user.id) : [];
                  const daysUntilDue = requestService.getDaysUntilDue(request.due_date);
                  const isDueSoon = requestService.isDueSoon(request.due_date);

                  return (
                    <TableRow key={request.id} hover>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Avatar sx={{ bgcolor: 'primary.main' }}>
                            <Assignment />
                          </Avatar>
                          <Box>
                            <Typography variant="subtitle2" fontWeight="bold">
                              {request.request_number}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {requestService.formatDate(request.requested_date)}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {request.requested_by_name}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {request.documents_detail.length} document{request.documents_detail.length !== 1 ? 's' : ''}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={requestService.getPriorityDisplay(request.priority)}
                          color={requestService.getPriorityColor(request.priority)}
                          size="small"
                          icon={<PriorityHigh />}
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={requestService.getStatusDisplay(request.status)}
                          color={requestService.getStatusColor(request.status)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="body2" color={request.is_overdue ? 'error' : 'inherit'}>
                            {requestService.formatDate(request.due_date)}
                          </Typography>
                          {request.is_overdue && (
                            <Chip
                              icon={<Warning />}
                              label={`${Math.abs(daysUntilDue)}d overdue`}
                              color="error"
                              size="small"
                            />
                          )}
                          {isDueSoon && !request.is_overdue && (
                            <Chip
                              icon={<Schedule />}
                              label={`${daysUntilDue}d left`}
                              color="warning"
                              size="small"
                            />
                          )}
                        </Box>
                      </TableCell>
                      <TableCell align="right">
                        <Box sx={{ display: 'flex', gap: 0.5 }}>
                          {nextActions.includes('approve') && (
                            <Tooltip title="Approve">
                              <IconButton
                                onClick={() => {
                                  setSelectedRequest(request);
                                  handleActionClick('approve');
                                }}
                                color="success"
                                size="small"
                              >
                                <CheckCircle />
                              </IconButton>
                            </Tooltip>
                          )}
                          {nextActions.includes('reject') && (
                            <Tooltip title="Reject">
                              <IconButton
                                onClick={() => {
                                  setSelectedRequest(request);
                                  handleActionClick('reject');
                                }}
                                color="error"
                                size="small"
                              >
                                <Block />
                              </IconButton>
                            </Tooltip>
                          )}
                          <IconButton
                            onClick={(e) => handleMenuOpen(e, request)}
                            size="small"
                          >
                            <MoreVert />
                          </IconButton>
                        </Box>
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </TableContainer>

        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50]}
          component="div"
          count={totalCount}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </CardContent>
    </Card>
  );

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" component="h1">
          Document Requests
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => navigate('/requests/create')}
        >
          New Request
        </Button>
      </Box>

      {/* Stats Cards */}
      {renderStatsCards()}

      {/* Filters */}
      {renderFilters()}

      {/* Requests Table */}
      {renderRequestsTable()}

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleView}>
          <Visibility fontSize="small" sx={{ mr: 1 }} />
          View Details
        </MenuItem>
        {selectedRequest && user && requestService.canUserEdit(selectedRequest, user.id) && (
          <MenuItem onClick={handleEdit}>
            <Edit fontSize="small" sx={{ mr: 1 }} />
            Edit Request
          </MenuItem>
        )}
        {selectedRequest && user && requestService.canUserApprove(selectedRequest, user.id) && (
          <MenuItem onClick={() => handleActionClick('approve')}>
            <CheckCircle fontSize="small" sx={{ mr: 1 }} color="success" />
            Approve Request
          </MenuItem>
        )}
        {selectedRequest && user && requestService.canUserApprove(selectedRequest, user.id) && (
          <MenuItem onClick={() => handleActionClick('reject')}>
            <Block fontSize="small" sx={{ mr: 1 }} color="error" />
            Reject Request
          </MenuItem>
        )}
        {selectedRequest && user && requestService.canUserCancel(selectedRequest, user.id) && (
          <MenuItem onClick={() => handleActionClick('cancel')} sx={{ color: 'error.main' }}>
            <Cancel fontSize="small" sx={{ mr: 1 }} />
            Cancel Request
          </MenuItem>
        )}
      </Menu>

      {/* Action Dialog */}
      <Dialog open={actionDialogOpen} onClose={() => setActionDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {actionType === 'approve' && 'Approve Request'}
          {actionType === 'reject' && 'Reject Request'}
          {actionType === 'cancel' && 'Cancel Request'}
        </DialogTitle>
        <DialogContent>
          <Typography sx={{ mb: 2 }}>
            {actionType === 'approve' && 'Are you sure you want to approve this request?'}
            {actionType === 'reject' && 'Please provide a reason for rejecting this request:'}
            {actionType === 'cancel' && 'Are you sure you want to cancel this request?'}
          </Typography>

          <TextField
            fullWidth
            multiline
            rows={3}
            label={
              actionType === 'reject' ? 'Rejection Reason (Required)' : 'Comments (Optional)'
            }
            value={actionComments}
            onChange={(e) => setActionComments(e.target.value)}
            required={actionType === 'reject'}
            placeholder={
              actionType === 'reject' ? 'Please explain why this request is being rejected...' :
              'Add any additional comments...'
            }
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setActionDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleActionSubmit}
            variant="contained"
            disabled={processing || (actionType === 'reject' && !actionComments.trim())}
            color={actionType === 'reject' || actionType === 'cancel' ? 'error' : 'primary'}
          >
            {processing ? 'Processing...' :
             actionType === 'approve' ? 'Approve' :
             actionType === 'reject' ? 'Reject' :
             'Cancel Request'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default RequestsPage;
