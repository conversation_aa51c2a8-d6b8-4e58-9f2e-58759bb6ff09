/**
 * Revenue Collection Detail Page
 * 
 * Displays detailed information about a specific revenue collection
 * Supports both Regional and City Service collections
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Grid,
  Chip,
  Divider,
  Alert,
  CircularProgress,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from '@mui/material';
import {
  ArrowBack,
  Edit,
  Delete,
  Receipt,
  Person,
  Business,
  AccountBalance,
  DateRange,
  AttachMoney,
  Notes,
} from '@mui/icons-material';
import { useNavigate, useParams } from 'react-router-dom';
import { useNotification } from '../../contexts/NotificationContext';
import revenueCollectionService from '../../services/revenueCollectionService';
import type {
  RegionalRevenueCollection,
  CityServiceRevenueCollection,
} from '../../services/revenueCollectionService';

interface CollectionDetailPageProps {
  type: 'regional' | 'city-service';
}

const CollectionDetailPage: React.FC<CollectionDetailPageProps> = ({ type }) => {
  const navigate = useNavigate();
  const { id } = useParams();
  const { showSuccess, showError } = useNotification();
  
  const [loading, setLoading] = useState(true);
  const [collection, setCollection] = useState<RegionalRevenueCollection | CityServiceRevenueCollection | null>(null);

  useEffect(() => {
    if (id) {
      loadCollection();
    }
  }, [id, type]);

  const loadCollection = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      const data = type === 'regional'
        ? await revenueCollectionService.getRegionalRevenueCollection(id)
        : await revenueCollectionService.getCityServiceRevenueCollection(id);
      
      setCollection(data);
    } catch (error) {
      console.error('Error loading collection:', error);
      showError('Failed to load collection details');
      navigate('/revenue-collection/collections');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    navigate(`/revenue-collection/collections/${type}/${id}/edit`);
  };

  const handleDelete = async () => {
    if (!window.confirm('Are you sure you want to delete this collection?')) {
      return;
    }

    if (!id) return;

    try {
      if (type === 'regional') {
        await revenueCollectionService.deleteRegionalRevenueCollection(id);
      } else {
        await revenueCollectionService.deleteCityServiceRevenueCollection(id);
      }
      
      showSuccess('Collection deleted successfully');
      navigate('/revenue-collection/collections');
    } catch (error) {
      console.error('Error deleting collection:', error);
      showError('Failed to delete collection');
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ET', {
      style: 'currency',
      currency: 'ETB',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-ET', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-ET', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (!collection) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">Collection not found</Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Button
          startIcon={<ArrowBack />}
          onClick={() => navigate('/revenue-collection/collections')}
          sx={{ mb: 2 }}
        >
          Back to Collections
        </Button>
        <Typography variant="h4" component="h1" gutterBottom>
          {type === 'regional' ? 'Regional' : 'City Service'} Revenue Collection
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Collection ID: {collection.id}
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Main Details */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6">
                  Collection Details
                </Typography>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    variant="outlined"
                    startIcon={<Edit />}
                    onClick={handleEdit}
                  >
                    Edit
                  </Button>
                  <Button
                    variant="outlined"
                    color="error"
                    startIcon={<Delete />}
                    onClick={handleDelete}
                  >
                    Delete
                  </Button>
                </Box>
              </Box>

              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableBody>
                    <TableRow>
                      <TableCell component="th" scope="row" sx={{ fontWeight: 'medium', width: '30%' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <AttachMoney sx={{ mr: 1 }} />
                          Amount
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="h5" color="primary" fontWeight="bold">
                          {formatCurrency(collection.amount)}
                        </Typography>
                      </TableCell>
                    </TableRow>

                    <TableRow>
                      <TableCell component="th" scope="row" sx={{ fontWeight: 'medium' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <DateRange sx={{ mr: 1 }} />
                          Collection Date
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body1">
                          {formatDate(collection.collection_date)}
                        </Typography>
                      </TableCell>
                    </TableRow>

                    <TableRow>
                      <TableCell component="th" scope="row" sx={{ fontWeight: 'medium' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {collection.taxpayer_type === 'individual' ? <Person sx={{ mr: 1 }} /> : <Business sx={{ mr: 1 }} />}
                          Taxpayer
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body1" fontWeight="medium">
                          {collection.taxpayer_name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          TIN: {collection.taxpayer_tin}
                        </Typography>
                        <Chip
                          label={collection.taxpayer_type === 'individual' ? 'Individual' : 'Organization'}
                          size="small"
                          color={collection.taxpayer_type === 'individual' ? 'primary' : 'secondary'}
                          sx={{ mt: 1 }}
                        />
                      </TableCell>
                    </TableRow>

                    <TableRow>
                      <TableCell component="th" scope="row" sx={{ fontWeight: 'medium' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <AccountBalance sx={{ mr: 1 }} />
                          Revenue Source
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body1" fontWeight="medium">
                          {collection.revenue_source_name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Code: {collection.revenue_source_code}
                        </Typography>
                        <Chip
                          label={collection.category_name}
                          size="small"
                          variant="outlined"
                          sx={{ mt: 1 }}
                        />
                      </TableCell>
                    </TableRow>

                    <TableRow>
                      <TableCell component="th" scope="row" sx={{ fontWeight: 'medium' }}>
                        Period
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={collection.period_name}
                          color="info"
                          variant="outlined"
                        />
                      </TableCell>
                    </TableRow>

                    {collection.receipt_number && (
                      <TableRow>
                        <TableCell component="th" scope="row" sx={{ fontWeight: 'medium' }}>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Receipt sx={{ mr: 1 }} />
                            Receipt Number
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body1" fontFamily="monospace">
                            {collection.receipt_number}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    )}

                    {collection.notes && (
                      <TableRow>
                        <TableCell component="th" scope="row" sx={{ fontWeight: 'medium' }}>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Notes sx={{ mr: 1 }} />
                            Notes
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body1">
                            {collection.notes}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Metadata Sidebar */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Record Information
              </Typography>
              
              <Box sx={{ mb: 3 }}>
                <Typography variant="body2" color="text.secondary">
                  Collection Type
                </Typography>
                <Chip
                  label={type === 'regional' ? 'Regional Revenue' : 'City Service Revenue'}
                  color={type === 'regional' ? 'primary' : 'secondary'}
                  sx={{ mt: 1 }}
                />
              </Box>

              <Divider sx={{ my: 2 }} />

              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Recorded By
                </Typography>
                <Typography variant="body1" fontWeight="medium">
                  {collection.recorded_by_name}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {formatDateTime(collection.recorded_date)}
                </Typography>
              </Box>

              {collection.last_modified_by_name && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Last Modified By
                  </Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {collection.last_modified_by_name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {formatDateTime(collection.last_modified)}
                  </Typography>
                </Box>
              )}

              <Divider sx={{ my: 2 }} />

              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Record ID
                </Typography>
                <Typography variant="body2" fontFamily="monospace">
                  {collection.id}
                </Typography>
              </Box>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card sx={{ mt: 2 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Actions
              </Typography>
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Button
                  variant="contained"
                  startIcon={<Edit />}
                  onClick={handleEdit}
                  fullWidth
                >
                  Edit Collection
                </Button>
                
                <Button
                  variant="outlined"
                  onClick={() => navigate('/revenue-collection/collections')}
                  fullWidth
                >
                  View All Collections
                </Button>
                
                <Button
                  variant="outlined"
                  onClick={() => navigate(`/revenue-collection/collections/${type}/create`)}
                  fullWidth
                >
                  Create New Collection
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default CollectionDetailPage;
