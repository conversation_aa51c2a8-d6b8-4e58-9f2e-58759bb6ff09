import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  CircularProgress,
  Avatar,
  Fade,
  Zoom,
  Badge,
  Chip,
  Button,
} from '@mui/material';
import {
  Public,
  LocationOn,
  Business,
  LocationCity,
  Home,
  Map,
  TrendingUp,
  Place,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useNotification } from '../../contexts/NotificationContext';
import locationHierarchyService from '../../services/locationHierarchyService';
import { checkApiHealth } from '../../utils/apiHealthCheck';

// Dashboard card interface
interface DashboardCard {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  count: number;
  badge?: string;
  gradient: string;
}

const LocationHierarchyPage: React.FC = () => {
  const { showNotification } = useNotification();
  const navigate = useNavigate();

  const [stats, setStats] = useState({
    countries: 0,
    regions: 0,
    zones: 0,
    cities: 0,
    subcities: 0,
    kebeles: 0,
    specialLocations: 0,
  });
  const [loading, setLoading] = useState(false);

  // Load statistics
  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      setLoading(true);
      const [
        countriesRes,
        regionsRes,
        zonesRes,
        citiesRes,
        subcitiesRes,
        kebelesRes,
        specialLocationsRes,
      ] = await Promise.all([
        locationHierarchyService.getCountries({ page_size: 1 }),
        locationHierarchyService.getRegions({ page_size: 1 }),
        locationHierarchyService.getZones({ page_size: 1 }),
        locationHierarchyService.getCities({ page_size: 1 }),
        locationHierarchyService.getSubCities({ page_size: 1 }),
        locationHierarchyService.getKebeles({ page_size: 1 }),
        locationHierarchyService.getSpecialLocations({ page_size: 1 }),
      ]);

      setStats({
        countries: countriesRes.count,
        regions: regionsRes.count,
        zones: zonesRes.count,
        cities: citiesRes.count,
        subcities: subcitiesRes.count,
        kebeles: kebelesRes.count,
        specialLocations: specialLocationsRes.count || specialLocationsRes.results?.length || 0,
      });
    } catch (error) {
      console.error('Failed to load stats:', error);
      // Set default stats on error
      setStats({
        countries: 0,
        regions: 0,
        zones: 0,
        cities: 0,
        subcities: 0,
        kebeles: 0,
        specialLocations: 0,
      });
      showNotification('Failed to load statistics. Please check if the server is running.', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Dashboard cards configuration
  const dashboardCards: DashboardCard[] = [
    {
      id: 'countries',
      title: 'Countries',
      description: 'Manage and define countries',
      icon: <Public sx={{ fontSize: 40 }} />,
      color: '#1976d2',
      count: stats.countries,
      badge: '5+',
      gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    },
    {
      id: 'regions',
      title: 'Regions',
      description: 'Organize regions within countries',
      icon: <Map sx={{ fontSize: 40 }} />,
      color: '#7b1fa2',
      count: stats.regions,
      badge: '10+',
      gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    },
    {
      id: 'zones',
      title: 'Zones',
      description: 'Define zones within regions',
      icon: <LocationOn sx={{ fontSize: 40 }} />,
      color: '#388e3c',
      count: stats.zones,
      badge: '50+',
      gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    },
    {
      id: 'cities',
      title: 'Cities',
      description: 'Define and manage key cities',
      icon: <LocationCity sx={{ fontSize: 40 }} />,
      color: '#f57c00',
      count: stats.cities,
      badge: '100+',
      gradient: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
    },
    {
      id: 'subcities',
      title: 'SubCities (Woredas)',
      description: 'Manage subcities and woredas',
      icon: <Business sx={{ fontSize: 40 }} />,
      color: '#0288d1',
      count: stats.subcities,
      badge: '200+',
      gradient: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
    },
    {
      id: 'kebeles',
      title: 'Kebeles',
      description: 'Manage kebeles within subcities',
      icon: <Home sx={{ fontSize: 40 }} />,
      color: '#d32f2f',
      count: stats.kebeles,
      badge: 'New',
      gradient: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
    },
    {
      id: 'special-locations',
      title: 'Special Locations',
      description: 'Manage special places within kebeles',
      icon: <Place sx={{ fontSize: 40 }} />,
      color: '#ff5722',
      count: stats.specialLocations,
      badge: 'Places',
      gradient: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
    },
  ];

  const handleCardClick = (cardId: string) => {
    // Navigate to the appropriate management page
    switch (cardId) {
      case 'countries':
        navigate('/locations/countries');
        break;
      case 'regions':
        navigate('/locations/regions');
        break;
      case 'zones':
        navigate('/locations/zones');
        break;
      case 'cities':
        navigate('/locations/cities');
        break;
      case 'subcities':
        navigate('/locations/subcities');
        break;
      case 'kebeles':
        navigate('/locations/kebeles');
        break;
      case 'special-locations':
        navigate('/locations/special-locations');
        break;
      default:
        navigate('/locations/hierarchy');
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Fade in timeout={800}>
        <Box sx={{ mb: 6, textAlign: 'center' }}>
          <Typography
            variant="h3"
            component="h1"
            gutterBottom
            sx={{
              fontWeight: 700,
              background: 'linear-gradient(45deg, #1976d2, #7b1fa2)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              mb: 2
            }}
          >
            Location Hierarchy Center
          </Typography>
          <Typography variant="h6" color="text.secondary" sx={{ maxWidth: 600, mx: 'auto' }}>
            Define and manage all aspects of your geographical hierarchy
          </Typography>

          {/* Debug Button - Remove in production */}
          <Box sx={{ mt: 2 }}>
            <Button
              variant="outlined"
              size="small"
              onClick={() => checkApiHealth()}
              sx={{ opacity: 0.7 }}
            >
              🔍 Check API Health (Debug)
            </Button>
          </Box>
        </Box>
      </Fade>

      {/* Dashboard Cards */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
          <CircularProgress size={60} />
        </Box>
      ) : (
        <Box sx={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
          gap: 4,
          mb: 4
        }}>
          {dashboardCards.map((card, index) => (
            <Zoom in timeout={600 + index * 100} key={card.id}>
              <Card
                sx={{
                  position: 'relative',
                  overflow: 'hidden',
                  cursor: 'pointer',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
                  },
                  '&:active': {
                    transform: 'translateY(-4px)',
                  },
                }}
                onClick={() => handleCardClick(card.id)}
              >
                {/* Background Gradient */}
                <Box
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    height: '120px',
                    background: card.gradient,
                    opacity: 0.1,
                  }}
                />

                {/* Badge */}
                {card.badge && (
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 16,
                      right: 16,
                      zIndex: 2,
                    }}
                  >
                    <Chip
                      label={card.badge}
                      size="small"
                      sx={{
                        bgcolor: card.color,
                        color: 'white',
                        fontWeight: 600,
                        fontSize: '0.75rem',
                      }}
                    />
                  </Box>
                )}

                <CardContent sx={{ p: 4, position: 'relative', zIndex: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 3 }}>
                    {/* Icon */}
                    <Avatar
                      sx={{
                        width: 80,
                        height: 80,
                        background: card.gradient,
                        color: 'white',
                        boxShadow: '0 8px 24px rgba(0,0,0,0.15)',
                      }}
                    >
                      {card.icon}
                    </Avatar>

                    {/* Content */}
                    <Box sx={{ flex: 1, minWidth: 0 }}>
                      <Typography
                        variant="h5"
                        sx={{
                          fontWeight: 600,
                          mb: 1,
                          color: 'text.primary'
                        }}
                      >
                        {card.title}
                      </Typography>
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{ mb: 3, lineHeight: 1.6 }}
                      >
                        {card.description}
                      </Typography>

                      {/* Stats */}
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Badge
                          badgeContent={card.count}
                          color="primary"
                          max={999}
                          sx={{
                            '& .MuiBadge-badge': {
                              fontSize: '0.875rem',
                              height: '24px',
                              minWidth: '24px',
                              fontWeight: 600,
                            }
                          }}
                        >
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1,
                              px: 2,
                              py: 1,
                              borderRadius: 2,
                              bgcolor: 'action.hover',
                            }}
                          >
                            <TrendingUp sx={{ fontSize: 20, color: card.color }} />
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              Manage
                            </Typography>
                          </Box>
                        </Badge>
                      </Box>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Zoom>
          ))}
        </Box>
      )}
    </Container>
  );
};

export default LocationHierarchyPage;
