import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON>po<PERSON>,
  Button,
  List,
  ListItem,
  ListItemText,
  Chip,
  IconButton,
  Collapse,
} from '@mui/material';
import {
  BugReport,
  ExpandMore,
  ExpandLess,
  Clear,
  Refresh,
} from '@mui/icons-material';

interface LogEntry {
  id: number;
  timestamp: string;
  level: 'error' | 'warn' | 'info' | 'log';
  message: string;
  stack?: string;
}

const DebugConsole: React.FC = () => {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [logId, setLogId] = useState(0);

  useEffect(() => {
    // Capture console errors
    const originalError = console.error;
    const originalWarn = console.warn;
    const originalLog = console.log;
    const originalInfo = console.info;

    const addLog = (level: LogEntry['level'], args: any[]) => {
      try {
        const message = args.map(arg => {
          if (arg === null) return 'null';
          if (arg === undefined) return 'undefined';
          if (typeof arg === 'string') return arg;
          if (typeof arg === 'number' || typeof arg === 'boolean') return String(arg);
          if (arg instanceof Error) return `${arg.name}: ${arg.message}`;
          if (typeof arg === 'object') {
            try {
              return JSON.stringify(arg, null, 2);
            } catch {
              return String(arg);
            }
          }
          return String(arg);
        }).join(' ');

        const newLog: LogEntry = {
          id: Date.now() + Math.random(), // Use timestamp + random for unique ID
          timestamp: new Date().toLocaleTimeString(),
          level,
          message: message || `[${level.toUpperCase()}] Empty message`,
          stack: (level === 'error' && args[0]?.stack) ? args[0].stack : undefined,
        };

        setLogs(prev => {
          const updated = [newLog, ...prev].slice(0, 50);
          // Don't log to console here to avoid infinite loop
          return updated;
        });
      } catch (error) {
        // Don't log to console here to avoid infinite loop
      }
    };

    console.error = (...args) => {
      originalError(...args);
      addLog('error', args);
    };

    console.warn = (...args) => {
      originalWarn(...args);
      addLog('warn', args);
    };

    console.log = (...args) => {
      originalLog(...args);
      addLog('log', args);
    };

    console.info = (...args) => {
      originalInfo(...args);
      addLog('info', args);
    };

    // Capture unhandled errors
    const handleError = (event: ErrorEvent) => {
      const errorInfo = {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error
      };
      addLog('error', [`Window Error: ${event.message}`, errorInfo]);
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      addLog('error', [`Unhandled Promise Rejection:`, event.reason]);
    };

    // Also capture React errors
    const handleReactError = (error: Error, errorInfo: any) => {
      addLog('error', [`React Error: ${error.message}`, error, errorInfo]);
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    // Store original handlers for cleanup
    (window as any).__originalConsoleHandlers = {
      error: originalError,
      warn: originalWarn,
      log: originalLog,
      info: originalInfo
    };

    return () => {
      console.error = originalError;
      console.warn = originalWarn;
      console.log = originalLog;
      console.info = originalInfo;
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, [logId]);

  const clearLogs = () => {
    setLogs([]);
    setLogId(0);
    // Don't log to console here to avoid infinite loop
  };

  const forceRefresh = () => {
    // Force a complete refresh of the debug console
    setLogs([]);
    setLogId(0);

    // Add a test log to verify it's working
    setTimeout(() => {
      console.error('Debug Console Test Error - If you see this, the console is working');
      console.warn('Debug Console Test Warning - Console is capturing logs');
      console.log('Debug Console Test Log - All systems operational');
    }, 100);
  };

  const getLevelColor = (level: LogEntry['level']) => {
    switch (level) {
      case 'error': return 'error';
      case 'warn': return 'warning';
      case 'info': return 'info';
      case 'log': return 'default';
      default: return 'default';
    }
  };

  const errorCount = logs.filter(log => log.level === 'error').length;
  const warnCount = logs.filter(log => log.level === 'warn').length;

  return (
    <Box
      sx={{
        position: 'fixed',
        bottom: 16,
        right: 16,
        zIndex: 9999,
        maxWidth: 400,
      }}
    >
      <Card>
        <CardContent sx={{ pb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <BugReport color="primary" />
              <Typography variant="h6">Debug Console</Typography>
              {errorCount > 0 && (
                <Chip label={`${errorCount} errors`} color="error" size="small" />
              )}
              {warnCount > 0 && (
                <Chip label={`${warnCount} warnings`} color="warning" size="small" />
              )}
            </Box>
            <Box>
              <IconButton size="small" onClick={clearLogs} title="Clear logs">
                <Clear />
              </IconButton>
              <IconButton size="small" onClick={forceRefresh} title="Test console">
                <BugReport />
              </IconButton>
              <IconButton size="small" onClick={() => window.location.reload()} title="Reload page">
                <Refresh />
              </IconButton>
              <IconButton size="small" onClick={() => setIsOpen(!isOpen)}>
                {isOpen ? <ExpandLess /> : <ExpandMore />}
              </IconButton>
            </Box>
          </Box>
        </CardContent>

        <Collapse in={isOpen}>
          <CardContent sx={{ pt: 0, maxHeight: 300, overflow: 'auto' }}>
            {logs.length === 0 ? (
              <Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  No logs captured yet. Click the bug icon above to test the console.
                </Typography>
                <Button
                  size="small"
                  variant="outlined"
                  onClick={forceRefresh}
                  startIcon={<BugReport />}
                >
                  Test Console
                </Button>
              </Box>
            ) : (
              <List dense>
                {logs.map((log) => (
                  <ListItem key={log.id} sx={{ px: 0 }}>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                          <Chip
                            label={log.level.toUpperCase()}
                            color={getLevelColor(log.level) as any}
                            size="small"
                          />
                          <Typography variant="caption" color="text.secondary">
                            {log.timestamp}
                          </Typography>
                        </Box>
                      }
                      secondary={
                        <Box>
                          <Typography
                            variant="body2"
                            sx={{
                              fontFamily: 'monospace',
                              fontSize: '0.75rem',
                              wordBreak: 'break-word',
                              whiteSpace: 'pre-wrap',
                            }}
                          >
                            {log.message}
                          </Typography>
                          {log.stack && (
                            <Typography
                              variant="caption"
                              sx={{
                                fontFamily: 'monospace',
                                fontSize: '0.7rem',
                                color: 'text.secondary',
                                display: 'block',
                                mt: 0.5,
                                whiteSpace: 'pre-wrap',
                              }}
                            >
                              {log.stack}
                            </Typography>
                          )}
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            )}
          </CardContent>
        </Collapse>
      </Card>
    </Box>
  );
};

export default DebugConsole;
