/**
 * Payment Processor Component
 * 
 * Handles payment processing for revenue collections with penalty and interest calculations
 */

import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  Typography,
  Card,
  CardContent,
  Chip,
  Alert,
  Box,
  Divider,
  CircularProgress,
} from '@mui/material';
import {
  Payment,
  Warning,
  CheckCircle,
  Calculate,
  Receipt,
} from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import revenueCollectionService from '../../services/revenueCollectionService';

interface PaymentProcessorProps {
  open: boolean;
  onClose: () => void;
  collection: any;
  collectionType: 'regional' | 'city-service';
  onPaymentProcessed: () => void;
}

interface PaymentBreakdown {
  original_amount: number;
  penalty_amount: number;
  interest_amount: number;
  total_amount_due: number;
  paid_amount: number;
  outstanding_amount: number;
  payment_status: string;
  is_overdue: boolean;
  days_overdue: number;
  penalty_rate: number;
  interest_rate: number;
}

const PaymentProcessor: React.FC<PaymentProcessorProps> = ({
  open,
  onClose,
  collection,
  collectionType,
  onPaymentProcessed,
}) => {
  const [paymentAmount, setPaymentAmount] = useState<string>('');
  const [paymentDate, setPaymentDate] = useState<string>(
    new Date().toISOString().split('T')[0]
  );
  const [breakdown, setBreakdown] = useState<PaymentBreakdown | null>(null);
  const [simulation, setSimulation] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [processing, setProcessing] = useState(false);
  const { showNotification } = useNotification();

  React.useEffect(() => {
    if (open && collection) {
      loadPaymentBreakdown();
    }
  }, [open, collection]);

  const loadPaymentBreakdown = async () => {
    try {
      setLoading(true);
      const response = await revenueCollectionService.getPaymentBreakdown(
        collection.id,
        collectionType
      );
      setBreakdown(response);
    } catch (error) {
      console.error('Error loading payment breakdown:', error);
      showNotification('Failed to load payment breakdown', 'error');
    } finally {
      setLoading(false);
    }
  };

  const simulatePayment = async () => {
    if (!paymentAmount || parseFloat(paymentAmount) <= 0) {
      showNotification('Please enter a valid payment amount', 'warning');
      return;
    }

    try {
      setLoading(true);
      const response = await revenueCollectionService.simulatePayment(
        collection.id,
        collectionType,
        parseFloat(paymentAmount)
      );
      setSimulation(response);
    } catch (error) {
      console.error('Error simulating payment:', error);
      showNotification('Failed to simulate payment', 'error');
    } finally {
      setLoading(false);
    }
  };

  const processPayment = async () => {
    if (!paymentAmount || parseFloat(paymentAmount) <= 0) {
      showNotification('Please enter a valid payment amount', 'warning');
      return;
    }

    try {
      setProcessing(true);
      await revenueCollectionService.processPayment(
        collection.id,
        collectionType,
        parseFloat(paymentAmount),
        paymentDate
      );
      
      showNotification('Payment processed successfully', 'success');
      onPaymentProcessed();
      onClose();
    } catch (error) {
      console.error('Error processing payment:', error);
      showNotification('Failed to process payment', 'error');
    } finally {
      setProcessing(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'ETB',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PAID': return 'success';
      case 'PARTIAL': return 'warning';
      case 'OVERDUE': return 'error';
      default: return 'default';
    }
  };

  if (loading && !breakdown) {
    return (
      <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
        <DialogContent>
          <Box display="flex" justifyContent="center" alignItems="center" py={4}>
            <CircularProgress />
          </Box>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          <Payment color="primary" />
          Process Payment - {collection?.taxpayer_name}
        </Box>
      </DialogTitle>

      <DialogContent>
        <Grid container spacing={3}>
          {/* Current Status */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Current Payment Status
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6} sm={3}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Status
                    </Typography>
                    <Chip
                      label={breakdown?.payment_status || 'Unknown'}
                      color={getStatusColor(breakdown?.payment_status || '')}
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={6} sm={3}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Original Amount
                    </Typography>
                    <Typography variant="body1">
                      {formatCurrency(breakdown?.original_amount || 0)}
                    </Typography>
                  </Grid>
                  <Grid item xs={6} sm={3}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Paid Amount
                    </Typography>
                    <Typography variant="body1">
                      {formatCurrency(breakdown?.paid_amount || 0)}
                    </Typography>
                  </Grid>
                  <Grid item xs={6} sm={3}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Outstanding
                    </Typography>
                    <Typography variant="body1" color="error">
                      {formatCurrency(breakdown?.outstanding_amount || 0)}
                    </Typography>
                  </Grid>
                </Grid>

                {breakdown?.is_overdue && (
                  <Alert severity="warning" sx={{ mt: 2 }}>
                    <Typography variant="body2">
                      This payment is {breakdown.days_overdue} days overdue.
                      Penalty: {formatCurrency(breakdown.penalty_amount)} ({breakdown.penalty_rate}%)
                      | Interest: {formatCurrency(breakdown.interest_amount)} ({breakdown.interest_rate}% monthly)
                    </Typography>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Payment Input */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Process Payment
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Payment Amount"
                      type="number"
                      value={paymentAmount}
                      onChange={(e) => setPaymentAmount(e.target.value)}
                      inputProps={{ min: 0, step: 0.01 }}
                      helperText={`Total due: ${formatCurrency(breakdown?.total_amount_due || 0)}`}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Payment Date"
                      type="date"
                      value={paymentDate}
                      onChange={(e) => setPaymentDate(e.target.value)}
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Button
                      variant="outlined"
                      onClick={simulatePayment}
                      disabled={loading || !paymentAmount}
                      startIcon={<Calculate />}
                    >
                      Simulate Payment
                    </Button>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Payment Simulation */}
          {simulation && (
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Payment Simulation
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Current Status
                      </Typography>
                      <Chip
                        label={simulation.current_status}
                        color={getStatusColor(simulation.current_status)}
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        New Status
                      </Typography>
                      <Chip
                        label={simulation.simulated_status}
                        color={getStatusColor(simulation.simulated_status)}
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        New Paid Amount
                      </Typography>
                      <Typography variant="body1">
                        {formatCurrency(simulation.simulated_paid)}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Remaining Outstanding
                      </Typography>
                      <Typography variant="body1">
                        {formatCurrency(simulation.simulated_outstanding)}
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          )}
        </Grid>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={processing}>
          Cancel
        </Button>
        <Button
          variant="contained"
          onClick={processPayment}
          disabled={processing || !paymentAmount || parseFloat(paymentAmount) <= 0}
          startIcon={processing ? <CircularProgress size={20} /> : <Receipt />}
        >
          {processing ? 'Processing...' : 'Process Payment'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PaymentProcessor;
