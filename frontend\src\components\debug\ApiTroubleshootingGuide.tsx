import React from 'react';
import {
  Alert,
  AlertTitle,
  Box,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  ExpandMore,
  Error,
  Warning,
  Info,
  CheckCircle,
  PlayArrow,
  Storage,
  NetworkCheck,
} from '@mui/icons-material';

interface ApiTroubleshootingGuideProps {
  error?: string;
}

// Reusable code block component
const CodeBlock: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <Box
    component="code"
    sx={{
      bgcolor: 'grey.100',
      p: 0.5,
      borderRadius: 1,
      fontFamily: 'monospace',
      fontSize: '0.875rem',
      display: 'inline-block',
      mt: 0.5
    }}
  >
    {children}
  </Box>
);

const ApiTroubleshootingGuide: React.FC<ApiTroubleshootingGuideProps> = ({ error }) => {
  const isHtmlError = error?.includes('<!doctype') || error?.includes('Unexpected token');
  const isNetworkError = error?.includes('Network') || error?.includes('fetch');

  return (
    <Box sx={{ mt: 2 }}>
      <Alert severity="error" sx={{ mb: 2 }}>
        <AlertTitle>API Connection Error</AlertTitle>
        The frontend cannot connect to the Django backend API. This usually means the Django server is not running.
      </Alert>

      <Accordion defaultExpanded={isHtmlError}>
        <AccordionSummary expandIcon={<ExpandMore />}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Error color="error" />
            <Typography variant="h6">Django Server Not Running</Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Typography variant="body2" sx={{ mb: 2 }}>
            The most common cause is that the Django development server is not running.
          </Typography>
          
          <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
            ✅ Solution:
          </Typography>
          
          <List dense>
            <ListItem>
              <ListItemIcon><PlayArrow color="primary" /></ListItemIcon>
              <ListItemText 
                primary="Start the Django server"
                secondary={
                  <Box component="span">
                    Open terminal in the backend directory and run:<br />
                    <CodeBlock>python manage.py runserver</CodeBlock>
                  </Box>
                }
              />
            </ListItem>
            <ListItem>
              <ListItemIcon><CheckCircle color="success" /></ListItemIcon>
              <ListItemText 
                primary="Verify server is running"
                secondary="You should see: 'Starting development server at http://127.0.0.1:8000/'"
              />
            </ListItem>
            <ListItem>
              <ListItemIcon><NetworkCheck color="info" /></ListItemIcon>
              <ListItemText 
                primary="Test API endpoint"
                secondary="Visit http://127.0.0.1:8000/api/locations/countries/ in your browser"
              />
            </ListItem>
          </List>
        </AccordionDetails>
      </Accordion>

      <Accordion>
        <AccordionSummary expandIcon={<ExpandMore />}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Storage color="warning" />
            <Typography variant="h6">Database Issues</Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Typography variant="body2" sx={{ mb: 2 }}>
            The Django server might be running but having database connection issues.
          </Typography>
          
          <List dense>
            <ListItem>
              <ListItemIcon><Info color="info" /></ListItemIcon>
              <ListItemText 
                primary="Check database connection"
                secondary="Make sure PostgreSQL is running and the database exists"
              />
            </ListItem>
            <ListItem>
              <ListItemIcon><PlayArrow color="primary" /></ListItemIcon>
              <ListItemText 
                primary="Run migrations"
                secondary={
                  <Box component="span">
                    <CodeBlock>python manage.py migrate</CodeBlock>
                  </Box>
                }
              />
            </ListItem>
          </List>
        </AccordionDetails>
      </Accordion>

      <Accordion>
        <AccordionSummary expandIcon={<ExpandMore />}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Warning color="warning" />
            <Typography variant="h6">Port Conflicts</Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Typography variant="body2" sx={{ mb: 2 }}>
            The Django server might be running on a different port.
          </Typography>
          
          <List dense>
            <ListItem>
              <ListItemIcon><Info color="info" /></ListItemIcon>
              <ListItemText 
                primary="Check server output"
                secondary="Look for the actual port in the Django server startup message"
              />
            </ListItem>
            <ListItem>
              <ListItemIcon><NetworkCheck color="info" /></ListItemIcon>
              <ListItemText 
                primary="Update frontend proxy"
                secondary="Make sure the frontend is configured to proxy to the correct Django port"
              />
            </ListItem>
          </List>
        </AccordionDetails>
      </Accordion>

      {error && (
        <Alert severity="info" sx={{ mt: 2 }}>
          <AlertTitle>Error Details</AlertTitle>
          <Typography variant="body2" component="pre" sx={{ whiteSpace: 'pre-wrap', fontSize: '0.875rem' }}>
            {error}
          </Typography>
        </Alert>
      )}

      <Alert severity="info" sx={{ mt: 2 }}>
        <AlertTitle>Quick Test</AlertTitle>
        <Typography variant="body2">
          Click the "🔍 Check API Health (Debug)" button above to run a comprehensive API connectivity test.
          This will check all location hierarchy endpoints and provide detailed error information.
        </Typography>
      </Alert>
    </Box>
  );
};

export default ApiTroubleshootingGuide;
