import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  TextField,
  FormControlLabel,
  Switch,
  InputAdornment,
  Avatar,
  Divider,
  Stack,
  Fade,
  Alert,
  CircularProgress,
  IconButton,
  Breadcrumbs,
  Link,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TablePagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Place,
  LocationOn,
  ArrowBack,
  Add,
  Edit,
  Delete,
  Visibility,
  CheckCircle,
  Cancel,
  Home,
  Business,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useNotification } from '../../contexts/NotificationContext';
import ConfirmationDialog from '../../components/common/ConfirmationDialog';
import ApiTroubleshootingGuide from '../../components/debug/ApiTroubleshootingGuide';
import locationHierarchyService from '../../services/locationHierarchyService';
import type {
  SpecialLocation,
  SpecialLocationCreate,
  KebeleSelect
} from '../../types/locationHierarchy';

// Interfaces imported from locationHierarchyService

const SpecialLocationsPage: React.FC = () => {
  const navigate = useNavigate();
  const { showNotification } = useNotification();

  // State management
  const [specialLocations, setSpecialLocations] = useState<SpecialLocation[]>([]);
  const [kebeles, setKebeles] = useState<KebeleSelect[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);
  const [editingLocation, setEditingLocation] = useState<SpecialLocation | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [locationToDelete, setLocationToDelete] = useState<SpecialLocation | null>(null);
  const [deleting, setDeleting] = useState(false);

  // Form state
  const [formData, setFormData] = useState<SpecialLocationCreate>({
    kebele: 0,
    name: '',
    is_active: true,
  });
  const [formErrors, setFormErrors] = useState<any>({});

  // Pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Search and filter
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedKebele, setSelectedKebele] = useState('');

  useEffect(() => {
    loadSpecialLocations();
    loadKebeles();
  }, [searchTerm, selectedKebele]);

  const loadSpecialLocations = async () => {
    try {
      setLoading(true);
      const params: any = {};
      if (searchTerm) params.search = searchTerm;
      if (selectedKebele) params.kebele = selectedKebele;

      const response = await locationHierarchyService.getSpecialLocations(params);
      setSpecialLocations(response.results || response);
    } catch (error: any) {
      console.error('Error loading special locations:', error);
      setSpecialLocations([]);
      setApiError(error.message || error.toString());

      // Check if it's a network error or server error
      if (error.message?.includes('<!doctype') || error.message?.includes('Unexpected token')) {
        showNotification('Server error: Please check if the Django server is running', 'error');
      } else if (error.code === 'NETWORK_ERROR' || !navigator.onLine) {
        showNotification('Network error: Please check your connection', 'error');
      } else {
        showNotification('Failed to load special locations', 'error');
      }
    } finally {
      setLoading(false);
    }
  };

  const loadKebeles = async () => {
    try {
      const data = await locationHierarchyService.getKebelesSelect();
      setKebeles(data);
    } catch (error: any) {
      console.error('Error loading kebeles:', error);
      setKebeles([]);

      // Check if it's a server error
      if (error.message?.includes('<!doctype') || error.message?.includes('Unexpected token')) {
        showNotification('Server error: Cannot load kebeles. Please check if the Django server is running', 'error');
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setSubmitting(true);
      setFormErrors({});

      const submitData: SpecialLocationCreate = {
        ...formData,
        kebele: Number(formData.kebele),
      };

      if (editingLocation) {
        await locationHierarchyService.updateSpecialLocation(editingLocation.id, submitData);
        showNotification('Special location updated successfully', 'success');
      } else {
        await locationHierarchyService.createSpecialLocation(submitData);
        showNotification('Special location created successfully', 'success');
      }

      resetForm();
      loadSpecialLocations();
    } catch (error: any) {
      console.error('Error submitting form:', error);
      if (error.response?.data) {
        setFormErrors(error.response.data);
      }
      showNotification('Failed to save special location', 'error');
    } finally {
      setSubmitting(false);
    }
  };

  const handleEdit = (location: SpecialLocation) => {
    setEditingLocation(location);
    setFormData({
      kebele: location.kebele,
      name: location.name,
      is_active: location.is_active,
    });
    setShowForm(true);
  };

  const handleDelete = (location: SpecialLocation) => {
    setLocationToDelete(location);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!locationToDelete) return;

    try {
      setDeleting(true);
      await locationHierarchyService.deleteSpecialLocation(locationToDelete.id);
      showNotification('Special location deleted successfully', 'success');
      loadSpecialLocations();
      handleCloseDeleteDialog();
    } catch (error) {
      console.error('Error deleting special location:', error);
      showNotification('Failed to delete special location', 'error');
    } finally {
      setDeleting(false);
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setLocationToDelete(null);
    setDeleting(false);
  };

  const resetForm = () => {
    setFormData({
      kebele: 0,
      name: '',
      is_active: true,
    });
    setFormErrors({});
    setEditingLocation(null);
    setShowForm(false);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Fade in timeout={800}>
        <Box sx={{ mb: 4 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs sx={{ mb: 3 }}>
            <Link
              component="button"
              variant="body2"
              onClick={() => navigate('/dashboard')}
              sx={{ display: 'flex', alignItems: 'center', gap: 0.5, textDecoration: 'none' }}
            >
              <Home fontSize="small" />
              Dashboard
            </Link>
            <Link
              component="button"
              variant="body2"
              onClick={() => navigate('/locations')}
              sx={{ display: 'flex', alignItems: 'center', gap: 0.5, textDecoration: 'none' }}
            >
              <LocationOn fontSize="small" />
              Locations
            </Link>
            <Link
              component="button"
              variant="body2"
              onClick={() => navigate('/locations/hierarchy')}
              sx={{ display: 'flex', alignItems: 'center', gap: 0.5, textDecoration: 'none' }}
            >
              <LocationOn fontSize="small" />
              Location Hierarchy
            </Link>
            <Typography variant="body2" color="text.primary">
              Special Locations
            </Typography>
          </Breadcrumbs>

          {/* Page Header */}
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Avatar sx={{ bgcolor: 'primary.main', width: 56, height: 56 }}>
                <Place sx={{ fontSize: 28 }} />
              </Avatar>
              <Box>
                <Typography variant="h4" component="h1" sx={{ fontWeight: 600, mb: 0.5 }}>
                  Special Locations
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Manage special places and locations within kebeles
                </Typography>
              </Box>
            </Box>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setShowForm(!showForm)}
              size="large"
              sx={{ minWidth: 160 }}
            >
              {showForm ? 'Cancel' : 'Add Location'}
            </Button>
          </Box>
        </Box>
      </Fade>

      {/* Inline Form */}
      {showForm && (
        <Fade in timeout={600}>
          <Card sx={{ mb: 4 }}>
            <CardContent sx={{ p: 4 }}>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                {editingLocation ? 'Edit Special Location' : 'Add New Special Location'}
              </Typography>
              <Divider sx={{ mb: 3 }} />

              <form onSubmit={handleSubmit}>
                <Stack spacing={3}>
                  <FormControl fullWidth required error={!!formErrors.kebele}>
                    <InputLabel>Kebele</InputLabel>
                    <Select
                      value={formData.kebele || ''}
                      onChange={(e) => setFormData({ ...formData, kebele: Number(e.target.value) })}
                      label="Kebele"
                      startAdornment={
                        <InputAdornment position="start">
                          <Business color="action" />
                        </InputAdornment>
                      }
                    >
                      <MenuItem value="">
                        <em>Select a kebele</em>
                      </MenuItem>
                      {kebeles.map((kebele) => (
                        <MenuItem key={kebele.id} value={kebele.id}>
                          {kebele.display_name}
                        </MenuItem>
                      ))}
                    </Select>
                    {formErrors.kebele && (
                      <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 2 }}>
                        {formErrors.kebele}
                      </Typography>
                    )}
                  </FormControl>

                  <TextField
                    label="Location Name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    error={!!formErrors.name}
                    helperText={formErrors.name || 'Enter the name of the special location'}
                    fullWidth
                    required
                    placeholder="e.g., Central Market, Main Hospital, Primary School"
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Place color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />

                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.is_active}
                        onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                        color="primary"
                      />
                    }
                    label="Active Status"
                  />

                  <Box sx={{ display: 'flex', gap: 2, pt: 2 }}>
                    <Button
                      type="submit"
                      variant="contained"
                      disabled={submitting}
                      startIcon={submitting ? <CircularProgress size={20} /> : <Add />}
                      size="large"
                    >
                      {submitting ? 'Saving...' : editingLocation ? 'Update Location' : 'Create Location'}
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={resetForm}
                      disabled={submitting}
                      startIcon={<Cancel />}
                      size="large"
                    >
                      Cancel
                    </Button>
                  </Box>
                </Stack>
              </form>
            </CardContent>
          </Card>
        </Fade>
      )}

      {/* Search and Filter */}
      <Fade in timeout={1000}>
        <Card sx={{ mb: 4 }}>
          <CardContent sx={{ p: 3 }}>
            <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 2 }}>
              <TextField
                label="Search Special Locations"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search by name or kebele..."
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <LocationOn color="action" />
                    </InputAdornment>
                  ),
                }}
              />
              <FormControl>
                <InputLabel>Filter by Kebele</InputLabel>
                <Select
                  value={selectedKebele}
                  onChange={(e) => setSelectedKebele(e.target.value)}
                  label="Filter by Kebele"
                >
                  <MenuItem value="">All Kebeles</MenuItem>
                  {kebeles.map((kebele) => (
                    <MenuItem key={kebele.id} value={kebele.id.toString()}>
                      {kebele.display_name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          </CardContent>
        </Card>
      </Fade>

      {/* Data Table */}
      <Fade in timeout={1200}>
        <Card>
          <CardContent sx={{ p: 0 }}>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                <CircularProgress />
              </Box>
            ) : specialLocations.length === 0 ? (
              <Box sx={{ textAlign: 'center', p: 4 }}>
                <Place sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  No Special Locations Found
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  {searchTerm || selectedKebele
                    ? 'Try adjusting your search criteria or filters.'
                    : 'Get started by adding your first special location.'
                  }
                </Typography>
                {!searchTerm && !selectedKebele && (
                  <Button
                    variant="contained"
                    startIcon={<Add />}
                    onClick={() => setShowForm(true)}
                  >
                    Add First Location
                  </Button>
                )}
              </Box>
            ) : (
              <>
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell sx={{ fontWeight: 600 }}>Location Name</TableCell>
                        <TableCell sx={{ fontWeight: 600 }}>Kebele</TableCell>
                        <TableCell sx={{ fontWeight: 600 }}>Status</TableCell>
                        <TableCell sx={{ fontWeight: 600 }}>Created</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 600 }}>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {specialLocations
                        .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                        .map((location) => (
                          <TableRow key={location.id} hover>
                            <TableCell>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                <Avatar sx={{ bgcolor: 'primary.light', width: 40, height: 40 }}>
                                  <Place />
                                </Avatar>
                                <Box>
                                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                    {location.name}
                                  </Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    ID: {location.id}
                                  </Typography>
                                </Box>
                              </Box>
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2">
                                {location.kebele_name}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {location.kebele_full_name}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Chip
                                label={location.is_active ? 'Active' : 'Inactive'}
                                size="small"
                                color={location.is_active ? 'success' : 'default'}
                                icon={location.is_active ? <CheckCircle /> : <Cancel />}
                              />
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2">
                                {new Date(location.created_at).toLocaleDateString()}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {new Date(location.created_at).toLocaleTimeString()}
                              </Typography>
                            </TableCell>
                            <TableCell align="right">
                              <IconButton
                                size="small"
                                onClick={() => handleEdit(location)}
                                color="primary"
                                sx={{ mr: 1 }}
                              >
                                <Edit fontSize="small" />
                              </IconButton>
                              <IconButton
                                size="small"
                                onClick={() => handleDelete(location)}
                                color="error"
                              >
                                <Delete fontSize="small" />
                              </IconButton>
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </TableContainer>

                <TablePagination
                  rowsPerPageOptions={[5, 10, 25, 50]}
                  component="div"
                  count={specialLocations.length}
                  rowsPerPage={rowsPerPage}
                  page={page}
                  onPageChange={handleChangePage}
                  onRowsPerPageChange={handleChangeRowsPerPage}
                />
              </>
            )}
          </CardContent>
        </Card>
      </Fade>

      {/* API Error Troubleshooting Guide */}
      {apiError && (
        <Fade in timeout={1400}>
          <Box sx={{ mt: 4 }}>
            <ApiTroubleshootingGuide error={apiError} />
          </Box>
        </Fade>
      )}

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={deleteDialogOpen}
        title="Delete Special Location"
        message={
          locationToDelete
            ? `Are you sure you want to delete "${locationToDelete.name}"? This action cannot be undone.`
            : ''
        }
        onConfirm={handleConfirmDelete}
        onCancel={handleCloseDeleteDialog}
        confirmText="Delete"
        cancelText="Cancel"
        loading={deleting}
        severity="error"
      />
    </Container>
  );
};

export default SpecialLocationsPage;
