import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Avatar,
  IconButton,
  Alert,
  Skeleton,
  Divider,
  InputAdornment,
} from '@mui/material';
import {
  ArrowBack,
  Save,
  PhotoCamera,
  Person,
  Visibility,
  VisibilityOff,
} from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import userService from '../../services/userService';
import organizationService from '../../services/organizationService';
import type { UserCreate, UserUpdate, User, Organization } from '../../services/types';

interface UserFormPageProps {
  mode: 'create' | 'edit';
}

const UserFormPage: React.FC<UserFormPageProps> = ({ mode }) => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showSuccess, showError } = useNotification();

  const [formData, setFormData] = useState<UserCreate>({
    username: '',
    email: '',
    first_name: '',
    last_name: '',
    password: '',
    role: 'employee',
    organization: 0,
    phone_number: '',
    department: '',
    position: '',
    is_active: true,
    is_staff: false,
  });

  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [profilePicture, setProfilePicture] = useState<File | null>(null);
  const [profilePicturePreview, setProfilePicturePreview] = useState<string>('');
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const roles = [
    { value: 'admin', label: 'Administrator' },
    { value: 'manager', label: 'Manager' },
    { value: 'employee', label: 'Employee' },
    { value: 'clerk', label: 'Clerk' },
    { value: 'viewer', label: 'Viewer' },
  ];

  useEffect(() => {
    loadOrganizations();
    if (mode === 'edit' && id) {
      loadUser();
    }
  }, [mode, id]);

  const loadOrganizations = async () => {
    try {
      const response = await organizationService.getOrganizations();
      setOrganizations(response.results);
    } catch (err: any) {
      showError('Failed to load organizations');
    }
  };

  const loadUser = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      const data = await userService.getUser(parseInt(id));
      setFormData({
        username: data.username,
        email: data.email,
        first_name: data.first_name,
        last_name: data.last_name,
        password: '', // Don't populate password for edit
        role: data.role || 'employee',
        organization: data.organization || 0,
        phone_number: data.phone_number || '',
        department: data.department || '',
        position: data.position || '',
        is_active: Boolean(data.is_active),
        is_staff: Boolean(data.is_staff),
      });
      if (data.profile_picture) {
        setProfilePicturePreview(data.profile_picture);
      }
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to load user');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof UserCreate) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | { target: { value: unknown } }
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value
    }));
  };

  const handleSwitchChange = (field: keyof UserCreate) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.checked
    }));
  };

  const handleProfilePictureChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setProfilePicture(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setProfilePicturePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const validateForm = () => {
    if (!formData.username.trim()) {
      showError('Username is required');
      return false;
    }
    if (!formData.email.trim()) {
      showError('Email is required');
      return false;
    }
    if (!formData.first_name.trim()) {
      showError('First name is required');
      return false;
    }
    if (!formData.last_name.trim()) {
      showError('Last name is required');
      return false;
    }
    if (mode === 'create' && !formData.password) {
      showError('Password is required');
      return false;
    }
    if (mode === 'create' && formData.password !== confirmPassword) {
      showError('Passwords do not match');
      return false;
    }
    if (!formData.organization) {
      showError('Organization is required');
      return false;
    }
    return true;
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!validateForm()) return;

    try {
      setSaving(true);
      setError(null);

      if (mode === 'create') {
        const newUser = await userService.createUser(formData);
        
        // Upload profile picture if provided
        if (profilePicture) {
          await userService.uploadProfilePicture(newUser.id, profilePicture);
        }
        
        showSuccess('User created successfully');
        navigate(`/users/${newUser.id}`);
      } else {
        const updateData: UserUpdate = {
          email: formData.email,
          first_name: formData.first_name,
          last_name: formData.last_name,
          role: formData.role,
          phone_number: formData.phone_number,
          department: formData.department,
          position: formData.position,
          is_active: formData.is_active,
          is_staff: formData.is_staff,
        };
        
        await userService.updateUser(parseInt(id!), updateData);
        
        // Upload profile picture if provided
        if (profilePicture) {
          await userService.uploadProfilePicture(parseInt(id!), profilePicture);
        }
        
        showSuccess('User updated successfully');
        navigate(`/users/${id}`);
      }
    } catch (err: any) {
      setError(err.response?.data?.detail || `Failed to ${mode} user`);
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    if (mode === 'edit' && id) {
      navigate(`/users/${id}`);
    } else {
      navigate('/users');
    }
  };

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Skeleton variant="rectangular" width="100%" height={400} />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <IconButton onClick={handleCancel}>
            <ArrowBack />
          </IconButton>
          <Typography variant="h4" component="h1">
            {mode === 'create' ? 'Create User' : 'Edit User'}
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button variant="outlined" onClick={handleCancel}>
            Cancel
          </Button>
          <Button
            variant="contained"
            startIcon={<Save />}
            onClick={handleSubmit}
            disabled={saving}
          >
            {saving ? 'Saving...' : 'Save'}
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <Card>
          <CardContent>
            <Grid container spacing={3}>
              {/* Profile Picture */}
              <Grid size={{ xs: 12 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar
                    src={profilePicturePreview || undefined}
                    sx={{ width: 80, height: 80 }}
                  >
                    <Person />
                  </Avatar>
                  <Box>
                    <Typography variant="subtitle2" gutterBottom>
                      Profile Picture
                    </Typography>
                    <input
                      accept="image/*"
                      style={{ display: 'none' }}
                      id="profile-picture-upload"
                      type="file"
                      onChange={handleProfilePictureChange}
                    />
                    <label htmlFor="profile-picture-upload">
                      <Button
                        variant="outlined"
                        component="span"
                        startIcon={<PhotoCamera />}
                        size="small"
                      >
                        Upload Picture
                      </Button>
                    </label>
                  </Box>
                </Box>
              </Grid>

              <Grid size={{ xs: 12 }}>
                <Divider />
              </Grid>

              {/* Basic Information */}
              <Grid size={{ xs: 12, sm: 6 }}>
                <TextField
                  fullWidth
                  label="Username"
                  value={formData.username}
                  onChange={handleInputChange('username')}
                  required
                  disabled={mode === 'edit'} // Username cannot be changed
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <TextField
                  fullWidth
                  label="Email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange('email')}
                  required
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <TextField
                  fullWidth
                  label="First Name"
                  value={formData.first_name}
                  onChange={handleInputChange('first_name')}
                  required
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <TextField
                  fullWidth
                  label="Last Name"
                  value={formData.last_name}
                  onChange={handleInputChange('last_name')}
                  required
                />
              </Grid>

              {/* Password fields for create mode */}
              {mode === 'create' && (
                <>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <TextField
                      fullWidth
                      label="Password"
                      type={showPassword ? 'text' : 'password'}
                      value={formData.password}
                      onChange={handleInputChange('password')}
                      required
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              onClick={() => setShowPassword(!showPassword)}
                              edge="end"
                            >
                              {showPassword ? <VisibilityOff /> : <Visibility />}
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <TextField
                      fullWidth
                      label="Confirm Password"
                      type={showConfirmPassword ? 'text' : 'password'}
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      required
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                              edge="end"
                            >
                              {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Grid>
                </>
              )}

              <Grid size={{ xs: 12 }}>
                <Divider />
              </Grid>

              {/* Role and Organization */}
              <Grid size={{ xs: 12, sm: 6 }}>
                <FormControl fullWidth required>
                  <InputLabel>Role</InputLabel>
                  <Select
                    value={formData.role || 'employee'}
                    label="Role"
                    onChange={handleInputChange('role')}
                  >
                    {roles.map((role) => (
                      <MenuItem key={role.value} value={role.value}>
                        {role.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <FormControl fullWidth required>
                  <InputLabel>Organization</InputLabel>
                  <Select
                    value={formData.organization || ''}
                    label="Organization"
                    onChange={handleInputChange('organization')}
                  >
                    {organizations.map((org) => (
                      <MenuItem key={org.id} value={org.id}>
                        {org.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Additional Information */}
              <Grid size={{ xs: 12, sm: 6 }}>
                <TextField
                  fullWidth
                  label="Phone Number"
                  value={formData.phone_number}
                  onChange={handleInputChange('phone_number')}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <TextField
                  fullWidth
                  label="Department"
                  value={formData.department}
                  onChange={handleInputChange('department')}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <TextField
                  fullWidth
                  label="Position"
                  value={formData.position}
                  onChange={handleInputChange('position')}
                />
              </Grid>

              {/* Status Settings */}
              <Grid size={{ xs: 12 }}>
                <Divider />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={Boolean(formData.is_active)}
                      onChange={handleSwitchChange('is_active')}
                    />
                  }
                  label="Active User"
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={Boolean(formData.is_staff)}
                      onChange={handleSwitchChange('is_staff')}
                    />
                  }
                  label="Staff Access"
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </form>
    </Box>
  );
};

export default UserFormPage;
