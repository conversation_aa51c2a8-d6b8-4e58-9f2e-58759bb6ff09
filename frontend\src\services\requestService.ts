import apiClient from './api';

export interface DocumentRequest {
  id: string;
  request_number: string;
  requested_by: number;
  requested_by_name: string;
  documents: string[];
  documents_detail: DocumentSummary[];
  purpose: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  requested_date: string;
  required_date: string;
  due_date: string;
  status: 'pending' | 'approved' | 'rejected' | 'checked_out' | 'returned' | 'overdue' | 'cancelled';
  approved_by?: number;
  approved_by_name?: string;
  approved_date?: string;
  rejection_reason?: string;
  checked_out_date?: string;
  checked_out_by?: number;
  checked_out_by_name?: string;
  returned_date?: string;
  returned_to?: number;
  returned_to_name?: string;
  notes?: string;
  is_overdue: boolean;
  days_until_due?: number;
  can_be_approved: boolean;
  can_be_checked_out: boolean;
  can_be_returned: boolean;
  created_at: string;
  updated_at: string;
}

export interface DocumentSummary {
  id: string;
  title: string;
  document_type_name: string;
  mode: string;
  status: string;
  location_display: string;
}

export interface RequestCreate {
  documents: string[];
  purpose: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  required_date: string; // ISO datetime string
  due_date: string; // ISO datetime string
}

export interface RequestUpdate {
  documents?: string[];
  purpose?: string;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  required_date?: string;
  due_date?: string;
  notes?: string;
}

export interface RequestAction {
  action: 'approve' | 'reject' | 'checkout' | 'return';
  comments?: string;
  condition_notes?: string;
}

export interface RequestApproval {
  id: number;
  request: string;
  approver: number;
  approver_name: string;
  action: 'approved' | 'rejected' | 'returned_for_revision';
  comments?: string;
  created_at: string;
}

export interface RequestListResponse {
  count: number;
  next?: string;
  previous?: string;
  results: DocumentRequest[];
}

export interface RequestStats {
  total_requests: number;
  pending_requests: number;
  approved_requests: number;
  checked_out_requests: number;
  overdue_requests: number;
  returned_requests: number;
  rejected_requests: number;
  requests_by_priority: { [key: string]: number };
  requests_by_status: { [key: string]: number };
  recent_requests: number;
  my_requests: number;
  my_pending_approvals: number;
}

class RequestService {
  private baseUrl = '/requests';

  async getRequests(params?: {
    page?: number;
    page_size?: number;
    search?: string;
    status?: string;
    priority?: string;
    requested_by?: number;
    approved_by?: number;
    is_overdue?: boolean;
    ordering?: string;
  }): Promise<RequestListResponse> {
    const response = await apiClient.get(`${this.baseUrl}/`, { params });
    return response.data;
  }

  async getRequest(id: string): Promise<DocumentRequest> {
    const response = await apiClient.get(`${this.baseUrl}/${id}/`);
    return response.data;
  }

  async createRequest(data: RequestCreate): Promise<DocumentRequest> {
    const response = await apiClient.post(`${this.baseUrl}/`, data);
    return response.data;
  }

  async updateRequest(id: string, data: RequestUpdate): Promise<DocumentRequest> {
    const response = await apiClient.patch(`${this.baseUrl}/${id}/`, data);
    return response.data;
  }

  async deleteRequest(id: string): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/${id}/`);
  }

  async approveRequest(id: string, comments?: string): Promise<DocumentRequest> {
    const response = await apiClient.post(`${this.baseUrl}/${id}/approve/`, { comments });
    return response.data;
  }

  async rejectRequest(id: string, comments: string): Promise<DocumentRequest> {
    const response = await apiClient.post(`${this.baseUrl}/${id}/reject/`, { comments });
    return response.data;
  }

  async checkoutRequest(id: string, comments?: string): Promise<DocumentRequest> {
    const response = await apiClient.post(`${this.baseUrl}/${id}/checkout/`, { comments });
    return response.data;
  }

  async returnRequest(id: string, conditionNotes?: string): Promise<DocumentRequest> {
    const response = await apiClient.post(`${this.baseUrl}/${id}/return/`, { condition_notes: conditionNotes });
    return response.data;
  }

  async cancelRequest(id: string, reason?: string): Promise<DocumentRequest> {
    const response = await apiClient.post(`${this.baseUrl}/${id}/cancel/`, { reason });
    return response.data;
  }

  async getRequestApprovals(requestId: string): Promise<RequestApproval[]> {
    const response = await apiClient.get(`${this.baseUrl}/${requestId}/approvals/`);
    return response.data.results || response.data;
  }

  async getRequestStats(userId?: number): Promise<RequestStats> {
    const params = userId ? { user: userId } : {};
    const response = await apiClient.get(`${this.baseUrl}/stats/`, { params });
    return response.data;
  }

  async searchRequests(query: string, filters?: {
    status?: string;
    priority?: string;
    requested_by?: number;
  }): Promise<RequestListResponse> {
    const params = {
      search: query,
      ...filters,
    };
    
    const response = await apiClient.get(`${this.baseUrl}/search/`, { params });
    return response.data;
  }

  // Utility methods
  getStatusColor(status: string): 'success' | 'warning' | 'error' | 'info' | 'default' {
    switch (status) {
      case 'approved': return 'success';
      case 'pending': return 'warning';
      case 'checked_out': return 'info';
      case 'returned': return 'success';
      case 'rejected': return 'error';
      case 'overdue': return 'error';
      case 'cancelled': return 'default';
      default: return 'default';
    }
  }

  getPriorityColor(priority: string): 'success' | 'warning' | 'error' | 'info' | 'default' {
    switch (priority) {
      case 'low': return 'success';
      case 'normal': return 'info';
      case 'high': return 'warning';
      case 'urgent': return 'error';
      default: return 'default';
    }
  }

  getStatusIcon(status: string): string {
    switch (status) {
      case 'pending': return 'schedule';
      case 'approved': return 'check_circle';
      case 'rejected': return 'cancel';
      case 'checked_out': return 'assignment_turned_in';
      case 'returned': return 'assignment_return';
      case 'overdue': return 'warning';
      case 'cancelled': return 'block';
      default: return 'help';
    }
  }

  getPriorityIcon(priority: string): string {
    switch (priority) {
      case 'low': return 'keyboard_arrow_down';
      case 'normal': return 'remove';
      case 'high': return 'keyboard_arrow_up';
      case 'urgent': return 'priority_high';
      default: return 'help';
    }
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  formatDateTime(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getDaysUntilDue(dueDateString: string): number {
    const dueDate = new Date(dueDateString);
    const now = new Date();
    const diffTime = dueDate.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  isOverdue(dueDateString: string): boolean {
    return this.getDaysUntilDue(dueDateString) < 0;
  }

  isDueSoon(dueDateString: string, daysThreshold: number = 3): boolean {
    const daysUntilDue = this.getDaysUntilDue(dueDateString);
    return daysUntilDue <= daysThreshold && daysUntilDue > 0;
  }

  getStatusDisplay(status: string): string {
    switch (status) {
      case 'pending': return 'Pending Approval';
      case 'approved': return 'Approved';
      case 'rejected': return 'Rejected';
      case 'checked_out': return 'Checked Out';
      case 'returned': return 'Returned';
      case 'overdue': return 'Overdue';
      case 'cancelled': return 'Cancelled';
      default: return status.charAt(0).toUpperCase() + status.slice(1);
    }
  }

  getPriorityDisplay(priority: string): string {
    return priority.charAt(0).toUpperCase() + priority.slice(1);
  }

  canUserApprove(request: DocumentRequest, userId: number): boolean {
    return request.status === 'pending' && request.requested_by !== userId;
  }

  canUserCheckout(request: DocumentRequest, _userId: number): boolean {
    return request.status === 'approved' && request.can_be_checked_out;
  }

  canUserReturn(request: DocumentRequest, _userId: number): boolean {
    return request.status === 'checked_out' && request.can_be_returned;
  }

  canUserEdit(request: DocumentRequest, userId: number): boolean {
    return request.requested_by === userId && ['pending', 'rejected'].includes(request.status);
  }

  canUserCancel(request: DocumentRequest, userId: number): boolean {
    return request.requested_by === userId && ['pending', 'approved'].includes(request.status);
  }

  getNextActions(request: DocumentRequest, userId: number): string[] {
    const actions: string[] = [];
    
    if (this.canUserApprove(request, userId)) {
      actions.push('approve', 'reject');
    }
    
    if (this.canUserCheckout(request, userId)) {
      actions.push('checkout');
    }
    
    if (this.canUserReturn(request, userId)) {
      actions.push('return');
    }
    
    if (this.canUserEdit(request, userId)) {
      actions.push('edit');
    }
    
    if (this.canUserCancel(request, userId)) {
      actions.push('cancel');
    }
    
    return actions;
  }

  validateRequestDates(requiredDate: string, dueDate: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const required = new Date(requiredDate);
    const due = new Date(dueDate);

    // Due date must be at least 15 minutes after required date
    const minimumDueTime = new Date(required.getTime() + 15 * 60 * 1000);

    if (due < minimumDueTime) {
      errors.push('Due date must be at least 15 minutes after required date');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

export default new RequestService();
