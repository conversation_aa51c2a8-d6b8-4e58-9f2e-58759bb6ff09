#!/usr/bin/env python
"""
<PERSON>ript to populate the location hierarchy with Ethiopian administrative data
"""

import os
import sys
import django

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'arada_dms.settings')
django.setup()

from locations.location_hierarchy_models import Country, Region, Zone, City, SubCity, Kebele

def populate_location_hierarchy():
    """Populate the location hierarchy with sample Ethiopian data"""
    
    print("Creating location hierarchy data...")
    
    # Create Ethiopia
    ethiopia, created = Country.objects.get_or_create(
        code='ET',
        defaults={
            'name': 'Ethiopia',
            'phone_code': '+251',
            'currency': 'ETB',
            'is_active': True
        }
    )
    if created:
        print(f"✓ Created country: {ethiopia.name}")
    else:
        print(f"✓ Country already exists: {ethiopia.name}")
    
    # Create Addis Ababa Region
    addis_region, created = Region.objects.get_or_create(
        country=ethiopia,
        code='AA',
        defaults={
            'name': 'Addis Ababa',
            'capital_city': 'Addis Ababa',
            'population': 3500000,
            'area_km2': 527.0,
            'is_active': True
        }
    )
    if created:
        print(f"✓ Created region: {addis_region.name}")
    else:
        print(f"✓ Region already exists: {addis_region.name}")
    
    # Create Oromia Region
    oromia_region, created = Region.objects.get_or_create(
        country=ethiopia,
        code='OR',
        defaults={
            'name': 'Oromia',
            'capital_city': 'Adama',
            'population': 35000000,
            'area_km2': 353006.0,
            'is_active': True
        }
    )
    if created:
        print(f"✓ Created region: {oromia_region.name}")
    else:
        print(f"✓ Region already exists: {oromia_region.name}")
    
    # Create Addis Ababa Zone (Special Administrative Zone)
    addis_zone, created = Zone.objects.get_or_create(
        region=addis_region,
        code='AAZ',
        defaults={
            'name': 'Addis Ababa Special Zone',
            'population': 3500000,
            'area_km2': 527.0,
            'is_active': True
        }
    )
    if created:
        print(f"✓ Created zone: {addis_zone.name}")
    else:
        print(f"✓ Zone already exists: {addis_zone.name}")
    
    # Create West Shewa Zone in Oromia
    west_shewa_zone, created = Zone.objects.get_or_create(
        region=oromia_region,
        code='WSZ',
        defaults={
            'name': 'West Shewa Zone',
            'population': 2500000,
            'area_km2': 15000.0,
            'is_active': True
        }
    )
    if created:
        print(f"✓ Created zone: {west_shewa_zone.name}")
    else:
        print(f"✓ Zone already exists: {west_shewa_zone.name}")
    
    # Create Addis Ababa City
    addis_city, created = City.objects.get_or_create(
        zone=addis_zone,
        code='AAC',
        defaults={
            'name': 'Addis Ababa',
            'population': 3500000,
            'area_km2': 527.0,
            'is_capital': True,
            'is_active': True
        }
    )
    if created:
        print(f"✓ Created city: {addis_city.name}")
    else:
        print(f"✓ City already exists: {addis_city.name}")
    
    # Create Holeta City in West Shewa
    holeta_city, created = City.objects.get_or_create(
        zone=west_shewa_zone,
        code='HOL',
        defaults={
            'name': 'Holeta',
            'population': 50000,
            'area_km2': 25.0,
            'is_capital': False,
            'is_active': True
        }
    )
    if created:
        print(f"✓ Created city: {holeta_city.name}")
    else:
        print(f"✓ City already exists: {holeta_city.name}")
    
    # Create Arada SubCity in Addis Ababa
    arada_subcity, created = SubCity.objects.get_or_create(
        city=addis_city,
        code='ARA',
        defaults={
            'name': 'Arada',
            'type': 'subcity',
            'population': 225000,
            'area_km2': 9.0,
            'is_active': True
        }
    )
    if created:
        print(f"✓ Created subcity: {arada_subcity.name}")
    else:
        print(f"✓ SubCity already exists: {arada_subcity.name}")
    
    # Create Kirkos SubCity in Addis Ababa
    kirkos_subcity, created = SubCity.objects.get_or_create(
        city=addis_city,
        code='KIR',
        defaults={
            'name': 'Kirkos',
            'type': 'subcity',
            'population': 230000,
            'area_km2': 14.0,
            'is_active': True
        }
    )
    if created:
        print(f"✓ Created subcity: {kirkos_subcity.name}")
    else:
        print(f"✓ SubCity already exists: {kirkos_subcity.name}")
    
    # Create Holeta Town Woreda
    holeta_woreda, created = SubCity.objects.get_or_create(
        city=holeta_city,
        code='HTW',
        defaults={
            'name': 'Holeta Town',
            'type': 'woreda',
            'population': 50000,
            'area_km2': 25.0,
            'is_active': True
        }
    )
    if created:
        print(f"✓ Created woreda: {holeta_woreda.name}")
    else:
        print(f"✓ Woreda already exists: {holeta_woreda.name}")
    
    # Create Kebeles in Arada SubCity
    arada_kebeles = [
        {'name': 'Piassa', 'number': 1, 'code': 'ARA01'},
        {'name': 'Merkato', 'number': 2, 'code': 'ARA02'},
        {'name': 'Addis Ketema', 'number': 3, 'code': 'ARA03'},
        {'name': 'Shiromeda', 'number': 4, 'code': 'ARA04'},
        {'name': 'Gojjam Berenda', 'number': 5, 'code': 'ARA05'},
    ]
    
    for kebele_data in arada_kebeles:
        kebele, created = Kebele.objects.get_or_create(
            subcity=arada_subcity,
            code=kebele_data['code'],
            defaults={
                'name': kebele_data['name'],
                'number': kebele_data['number'],
                'population': 15000,
                'area_km2': 1.8,
                'is_active': True
            }
        )
        if created:
            print(f"✓ Created kebele: {kebele.display_name}")
        else:
            print(f"✓ Kebele already exists: {kebele.display_name}")
    
    # Create Kebeles in Kirkos SubCity
    kirkos_kebeles = [
        {'name': 'Mexico', 'number': 1, 'code': 'KIR01'},
        {'name': 'Kazanchis', 'number': 2, 'code': 'KIR02'},
        {'name': 'Arat Kilo', 'number': 3, 'code': 'KIR03'},
        {'name': 'Sidist Kilo', 'number': 4, 'code': 'KIR04'},
    ]
    
    for kebele_data in kirkos_kebeles:
        kebele, created = Kebele.objects.get_or_create(
            subcity=kirkos_subcity,
            code=kebele_data['code'],
            defaults={
                'name': kebele_data['name'],
                'number': kebele_data['number'],
                'population': 20000,
                'area_km2': 3.5,
                'is_active': True
            }
        )
        if created:
            print(f"✓ Created kebele: {kebele.display_name}")
        else:
            print(f"✓ Kebele already exists: {kebele.display_name}")
    
    # Create Kebeles in Holeta Woreda
    holeta_kebeles = [
        {'name': 'Holeta Center', 'number': 1, 'code': 'HTW01'},
        {'name': 'Holeta Genet', 'number': 2, 'code': 'HTW02'},
        {'name': 'Holeta Research', 'number': 3, 'code': 'HTW03'},
    ]
    
    for kebele_data in holeta_kebeles:
        kebele, created = Kebele.objects.get_or_create(
            subcity=holeta_woreda,
            code=kebele_data['code'],
            defaults={
                'name': kebele_data['name'],
                'number': kebele_data['number'],
                'population': 8000,
                'area_km2': 8.0,
                'is_active': True
            }
        )
        if created:
            print(f"✓ Created kebele: {kebele.display_name}")
        else:
            print(f"✓ Kebele already exists: {kebele.display_name}")
    
    print("\n🎉 Location hierarchy population completed!")
    print(f"📊 Summary:")
    print(f"   Countries: {Country.objects.count()}")
    print(f"   Regions: {Region.objects.count()}")
    print(f"   Zones: {Zone.objects.count()}")
    print(f"   Cities: {City.objects.count()}")
    print(f"   SubCities/Woredas: {SubCity.objects.count()}")
    print(f"   Kebeles: {Kebele.objects.count()}")

if __name__ == '__main__':
    populate_location_hierarchy()
