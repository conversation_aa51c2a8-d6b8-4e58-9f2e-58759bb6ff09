from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
import json
import uuid

from .models import (
    BusinessSector, BusinessSubSector, TaxPayerLevel,
    OrganizationBusinessType, IndividualTaxPayer, OrganizationTaxPayer
)

User = get_user_model()


class TaxPayerModelsTestCase(TestCase):
    """Test cases for taxpayer models"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create business sector
        self.sector = BusinessSector.objects.create(
            code='TEST',
            name='Test Sector',
            description='Test sector for testing',
            created_by=self.user
        )

        # Create business sub-sector
        self.subsector = BusinessSubSector.objects.create(
            business_sector=self.sector,
            code='001',
            name='Test Sub-Sector',
            description='Test sub-sector for testing',
            created_by=self.user
        )

        # Create tax payer level
        self.level = TaxPayerLevel.objects.create(
            name='Test Level',
            code='T',
            description='Test level for testing',
            minimum_annual_turnover=100000,
            maximum_annual_turnover=500000
        )

        # Create organization business type
        self.org_type = OrganizationBusinessType.objects.create(
            code='TEST',
            name='Test Organization Type',
            description='Test organization type for testing',
            requires_vat_registration=True
        )

    def test_business_sector_creation(self):
        """Test business sector creation"""
        self.assertEqual(self.sector.code, 'TEST')
        self.assertEqual(self.sector.name, 'Test Sector')
        self.assertEqual(str(self.sector), 'TEST - Test Sector')

    def test_business_subsector_creation(self):
        """Test business sub-sector creation"""
        self.assertEqual(self.subsector.code, '001')
        self.assertEqual(self.subsector.business_sector, self.sector)
        self.assertEqual(str(self.subsector), 'TEST-001')

    def test_individual_taxpayer_creation(self):
        """Test individual taxpayer creation"""
        individual = IndividualTaxPayer.objects.create(
            tin='1234567890',
            first_name='John',
            last_name='Doe',
            nationality='ET',
            gender='M',
            date_of_birth='1985-01-15',
            tax_payer_level=self.level,
            business_sector=self.sector,
            business_sub_sector=self.subsector,
            business_registration_date='2020-01-01',
            phone='+251911123456',
            created_by=self.user
        )

        self.assertEqual(individual.tin, '1234567890')
        self.assertEqual(individual.full_name, 'John Doe')
        self.assertEqual(individual.business_sector, self.sector)

    def test_organization_taxpayer_creation(self):
        """Test organization taxpayer creation"""
        organization = OrganizationTaxPayer.objects.create(
            tin='0987654321',
            business_name='Test Company',
            organization_business_type=self.org_type,
            tax_payer_level=self.level,
            business_sector=self.sector,
            business_sub_sector=self.subsector,
            business_registration_date='2020-01-01',
            manager_first_name='Jane',
            manager_last_name='Smith',
            phone='+251911987654',
            created_by=self.user
        )

        self.assertEqual(organization.tin, '0987654321')
        self.assertEqual(organization.business_name, 'Test Company')
        self.assertEqual(organization.organization_business_type, self.org_type)

    def test_tin_validation(self):
        """Test TIN validation"""
        # Test valid TIN
        individual = IndividualTaxPayer(
            tin='1234567890',
            first_name='John',
            last_name='Doe',
            nationality='ET',
            gender='M',
            date_of_birth='1985-01-15',
            tax_payer_level=self.level,
            business_sector=self.sector,
            business_sub_sector=self.subsector,
            business_registration_date='2020-01-01',
            phone='+251911123456',
            created_by=self.user
        )
        individual.full_clean()  # Should not raise validation error

    def test_vat_validation(self):
        """Test VAT number validation"""
        organization = OrganizationTaxPayer(
            tin='0987654321',
            business_name='Test Company',
            organization_business_type=self.org_type,
            tax_payer_level=self.level,
            business_sector=self.sector,
            business_sub_sector=self.subsector,
            business_registration_date='2020-01-01',
            manager_first_name='Jane',
            manager_last_name='Smith',
            phone='+251911987654',
            vat_number='ET0987654321',
            created_by=self.user
        )
        organization.full_clean()  # Should not raise validation error


class TaxPayerAPITestCase(APITestCase):
    """Test cases for taxpayer API endpoints"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)

        # Create test data
        self.sector = BusinessSector.objects.create(
            code='TEST',
            name='Test Sector',
            description='Test sector for testing',
            created_by=self.user
        )

        self.subsector = BusinessSubSector.objects.create(
            business_sector=self.sector,
            code='001',
            name='Test Sub-Sector',
            description='Test sub-sector for testing',
            created_by=self.user
        )

        self.level = TaxPayerLevel.objects.create(
            name='Test Level',
            code='T',
            description='Test level for testing',
            minimum_annual_turnover=100000,
            maximum_annual_turnover=500000
        )

    def test_business_sectors_api(self):
        """Test business sectors API"""
        url = reverse('taxpayers:business-sectors-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)

    def test_create_business_sector(self):
        """Test creating business sector via API"""
        url = reverse('taxpayers:business-sectors-list')
        data = {
            'code': 'NEW',
            'name': 'New Sector',
            'description': 'New sector for testing'
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(BusinessSector.objects.count(), 2)

    def test_individual_taxpayers_api(self):
        """Test individual taxpayers API"""
        # Create individual taxpayer
        individual = IndividualTaxPayer.objects.create(
            tin='1234567890',
            first_name='John',
            last_name='Doe',
            nationality='ET',
            gender='M',
            date_of_birth='1985-01-15',
            tax_payer_level=self.level,
            business_sector=self.sector,
            business_sub_sector=self.subsector,
            business_registration_date='2020-01-01',
            phone='+251911123456',
            created_by=self.user
        )

        url = reverse('taxpayers:individual-taxpayers-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)

    def test_statistics_api(self):
        """Test statistics API"""
        url = reverse('taxpayers:statistics')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('total_taxpayers', response.data)
        self.assertIn('individual_taxpayers', response.data)
        self.assertIn('organization_taxpayers', response.data)


class TaxPayerIntegrationTestCase(TestCase):
    """Integration tests for the complete taxpayer system"""

    def setUp(self):
        """Set up test data"""
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_complete_workflow(self):
        """Test complete taxpayer registration workflow"""
        # Login
        self.client.login(username='testuser', password='testpass123')

        # Create business sector
        sector = BusinessSector.objects.create(
            code='AGRI',
            name='Agriculture',
            description='Agricultural activities',
            created_by=self.user
        )

        # Create sub-sector
        subsector = BusinessSubSector.objects.create(
            business_sector=sector,
            code='001',
            name='Crop Production',
            description='Growing of crops',
            created_by=self.user
        )

        # Create tax payer level
        level = TaxPayerLevel.objects.create(
            name='Category C',
            code='C',
            description='Small taxpayers',
            minimum_annual_turnover=100000,
            maximum_annual_turnover=1000000
        )

        # Create individual taxpayer
        individual = IndividualTaxPayer.objects.create(
            tin='1234567890',
            first_name='John',
            last_name='Doe',
            nationality='ET',
            gender='M',
            date_of_birth='1985-01-15',
            tax_payer_level=level,
            business_sector=sector,
            business_sub_sector=subsector,
            business_registration_date='2020-01-01',
            phone='+251911123456',
            created_by=self.user
        )

        # Verify creation
        self.assertEqual(IndividualTaxPayer.objects.count(), 1)
        self.assertEqual(individual.business_sector, sector)
        self.assertEqual(individual.business_sub_sector, subsector)
        self.assertEqual(individual.tax_payer_level, level)

        # Test string representations
        self.assertEqual(str(sector), 'AGRI - Agriculture')
        self.assertEqual(str(subsector), 'AGRI-001')
        self.assertEqual(str(level), 'Category C (C)')
        self.assertEqual(individual.full_name, 'John Doe')
