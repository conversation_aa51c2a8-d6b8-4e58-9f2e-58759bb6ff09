import apiClient from './api';

export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  full_name: string;
  role: 'admin' | 'manager' | 'employee' | 'viewer';
  role_display: string;
  organization: number;
  organization_name: string;
  is_active: boolean;
  is_staff: boolean;
  is_superuser: boolean;
  last_login?: string;
  date_joined: string;
  phone_number?: string;
  department?: string;
  position?: string;
  profile_picture?: string;
  document_count: number;
  request_count: number;
}

export interface UserCreate {
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  password: string;
  role: 'admin' | 'manager' | 'employee' | 'viewer';
  organization: number;
  phone_number?: string;
  department?: string;
  position?: string;
  is_active?: boolean;
  is_staff?: boolean;
}

export interface UserUpdate {
  email?: string;
  first_name?: string;
  last_name?: string;
  role?: 'admin' | 'manager' | 'employee' | 'viewer';
  phone_number?: string;
  department?: string;
  position?: string;
  is_active?: boolean;
  is_staff?: boolean;
}

export interface UserPasswordUpdate {
  current_password: string;
  new_password: string;
  confirm_password: string;
}

export interface UserListResponse {
  count: number;
  next?: string;
  previous?: string;
  results: User[];
}

export interface UserProfile {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  full_name: string;
  role: string;
  role_display: string;
  organization: number;
  organization_name: string;
  phone_number?: string;
  department?: string;
  position?: string;
  profile_picture?: string;
  last_login?: string;
  date_joined: string;
  document_count: number;
  request_count: number;
  recent_documents: Array<{
    id: number;
    title: string;
    created_at: string;
  }>;
  recent_requests: Array<{
    id: number;
    document_title: string;
    status: string;
    created_at: string;
  }>;
}

class UserService {
  private baseUrl = '/accounts/users';

  async getUsers(params?: {
    page?: number;
    page_size?: number;
    search?: string;
    role?: string;
    organization?: number;
    is_active?: boolean;
    department?: string;
  }): Promise<UserListResponse> {
    const response = await apiClient.get(this.baseUrl, { params });
    return response.data;
  }

  async getUser(id: number): Promise<User> {
    const response = await apiClient.get(`${this.baseUrl}/${id}/`);
    return response.data;
  }

  async getUserProfile(id?: number): Promise<UserProfile> {
    const url = id ? `${this.baseUrl}/${id}/profile/` : '/accounts/profile/';
    const response = await apiClient.get(url);
    return response.data;
  }

  async createUser(data: UserCreate): Promise<User> {
    const response = await apiClient.post(this.baseUrl + '/', data);
    return response.data;
  }

  async updateUser(id: number, data: UserUpdate): Promise<User> {
    const response = await apiClient.patch(`${this.baseUrl}/${id}/`, data);
    return response.data;
  }

  async updateUserPassword(id: number, data: UserPasswordUpdate): Promise<void> {
    await apiClient.post(`${this.baseUrl}/${id}/change-password/`, data);
  }

  async uploadProfilePicture(id: number, file: File): Promise<User> {
    const formData = new FormData();
    formData.append('profile_picture', file);
    const response = await apiClient.patch(`${this.baseUrl}/${id}/upload-picture/`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  async deleteUser(id: number): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/${id}/`);
  }

  async toggleUserStatus(id: number): Promise<User> {
    const user = await this.getUser(id);
    return this.updateUser(id, { is_active: !user.is_active });
  }

  // Utility methods
  getRoleOptions() {
    return [
      { value: 'admin', label: 'Administrator', color: 'error', description: 'Full system access' },
      { value: 'manager', label: 'Manager', color: 'warning', description: 'Manage documents and users' },
      { value: 'employee', label: 'Employee', color: 'info', description: 'Create and manage documents' },
      { value: 'viewer', label: 'Viewer', color: 'default', description: 'View documents only' },
    ];
  }

  getRoleColor(role: string): 'error' | 'warning' | 'info' | 'default' {
    const roleConfig = this.getRoleOptions().find(opt => opt.value === role);
    return roleConfig?.color as any || 'default';
  }

  getRoleDescription(role: string): string {
    const roleConfig = this.getRoleOptions().find(opt => opt.value === role);
    return roleConfig?.description || '';
  }

  formatLastLogin(lastLogin?: string): string {
    if (!lastLogin) return 'Never';
    const date = new Date(lastLogin);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours} hours ago`;
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)} days ago`;
    return date.toLocaleDateString();
  }

  validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  validateUsername(username: string): boolean {
    const usernameRegex = /^[a-zA-Z0-9_]{3,30}$/;
    return usernameRegex.test(username);
  }

  validatePassword(password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  getInitials(firstName: string, lastName: string): string {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  }

  getStatusColor(isActive: boolean): 'success' | 'default' {
    return isActive ? 'success' : 'default';
  }

  getStatusText(isActive: boolean): string {
    return isActive ? 'Active' : 'Inactive';
  }
}

export default new UserService();
