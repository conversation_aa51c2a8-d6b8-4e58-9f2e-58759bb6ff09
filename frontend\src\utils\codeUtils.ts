/**
 * Utility functions for handling code fields in a case-insensitive manner
 */

/**
 * Normalize code input by converting to uppercase and trimming whitespace
 * @param code - The code string to normalize
 * @returns Normalized code string
 */
export const normalizeCode = (code: string): string => {
  if (!code) return '';
  return code.trim().toUpperCase();
};

/**
 * Format code for display (keeps original case but trims whitespace)
 * @param code - The code string to format
 * @returns Formatted code string
 */
export const formatCodeForDisplay = (code: string): string => {
  if (!code) return '';
  return code.trim();
};

/**
 * Compare two codes in a case-insensitive manner
 * @param code1 - First code to compare
 * @param code2 - Second code to compare
 * @returns True if codes are equal (case-insensitive)
 */
export const compareCodesIgnoreCase = (code1: string, code2: string): boolean => {
  return normalizeCode(code1) === normalizeCode(code2);
};

/**
 * Validate code format (basic validation)
 * @param code - The code to validate
 * @param maxLength - Maximum allowed length (default: 10)
 * @returns Validation result object
 */
export const validateCode = (code: string, maxLength: number = 10): { isValid: boolean; error?: string } => {
  if (!code || code.trim().length === 0) {
    return { isValid: false, error: 'Code is required' };
  }
  
  const normalizedCode = normalizeCode(code);
  
  if (normalizedCode.length > maxLength) {
    return { isValid: false, error: `Code must be ${maxLength} characters or less` };
  }
  
  // Check for valid characters (letters, numbers, hyphens, underscores)
  const validCodePattern = /^[A-Z0-9_-]+$/;
  if (!validCodePattern.test(normalizedCode)) {
    return { isValid: false, error: 'Code can only contain letters, numbers, hyphens, and underscores' };
  }
  
  return { isValid: true };
};

/**
 * Create a code input handler that automatically normalizes input
 * @param setValue - Function to set the value in the form
 * @param fieldName - Name of the field being updated
 * @returns Input change handler
 */
export const createCodeInputHandler = (
  setValue: (fieldName: string, value: string) => void,
  fieldName: string
) => {
  return (event: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = event.target.value;
    // Allow user to type in any case, but store normalized version
    setValue(fieldName, inputValue);
  };
};

/**
 * Create a code blur handler that normalizes the code on blur
 * @param setValue - Function to set the value in the form
 * @param fieldName - Name of the field being updated
 * @returns Blur event handler
 */
export const createCodeBlurHandler = (
  setValue: (fieldName: string, value: string) => void,
  fieldName: string
) => {
  return (event: React.FocusEvent<HTMLInputElement>) => {
    const inputValue = event.target.value;
    const normalizedValue = normalizeCode(inputValue);
    setValue(fieldName, normalizedValue);
  };
};

/**
 * Search codes in a case-insensitive manner
 * @param codes - Array of objects with code property
 * @param searchTerm - Search term
 * @param codeField - Field name containing the code (default: 'code')
 * @returns Filtered array
 */
export const searchCodesIgnoreCase = <T extends Record<string, any>>(
  codes: T[],
  searchTerm: string,
  codeField: string = 'code'
): T[] => {
  if (!searchTerm) return codes;
  
  const normalizedSearchTerm = normalizeCode(searchTerm);
  
  return codes.filter(item => {
    const itemCode = item[codeField];
    if (!itemCode) return false;
    return normalizeCode(itemCode).includes(normalizedSearchTerm);
  });
};
