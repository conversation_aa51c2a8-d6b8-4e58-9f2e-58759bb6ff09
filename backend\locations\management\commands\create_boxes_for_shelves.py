from django.core.management.base import BaseCommand
from locations.models import Shelf, Box


class Command(BaseCommand):
    help = 'Create boxes for existing shelves that don\'t have boxes'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force recreate boxes even if they already exist',
        )

    def handle(self, *args, **options):
        force = options['force']
        
        shelves = Shelf.objects.all()
        created_count = 0
        updated_count = 0
        
        for shelf in shelves:
            existing_boxes = shelf.boxes.count()
            expected_boxes = shelf.rows * shelf.columns
            
            if force or existing_boxes == 0:
                if force and existing_boxes > 0:
                    self.stdout.write(f'Deleting {existing_boxes} existing boxes for shelf {shelf}')
                    shelf.boxes.all().delete()
                    updated_count += 1
                
                self.stdout.write(f'Creating {expected_boxes} boxes for shelf {shelf} ({shelf.rows}x{shelf.columns})')
                shelf.create_boxes()
                created_count += 1
            elif existing_boxes != expected_boxes:
                self.stdout.write(
                    self.style.WARNING(
                        f'Shelf {shelf} has {existing_boxes} boxes but expects {expected_boxes}. Use --force to recreate.'
                    )
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully processed {created_count} shelves, updated {updated_count} shelves'
            )
        )
