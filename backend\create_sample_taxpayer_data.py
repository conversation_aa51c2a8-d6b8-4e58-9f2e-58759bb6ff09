#!/usr/bin/env python
"""
Script to create sample taxpayer data for testing
"""
import os
import sys
import django

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'document_management.settings')
django.setup()

from taxpayers.models import TaxPayerLevel, BusinessSector, BusinessSubSector, OrganizationBusinessType

def create_sample_data():
    print("Creating sample taxpayer data...")
    
    # Create Tax Payer Levels
    tax_levels = [
        {'code': 'A', 'name': 'Category A', 'description': 'Large taxpayers'},
        {'code': 'B', 'name': 'Category B', 'description': 'Medium taxpayers'},
        {'code': 'C', 'name': 'Category C', 'description': 'Small taxpayers'},
    ]
    
    for level_data in tax_levels:
        level, created = TaxPayerLevel.objects.get_or_create(
            code=level_data['code'],
            defaults={
                'name': level_data['name'],
                'description': level_data['description']
            }
        )
        if created:
            print(f"✅ Created TaxPayerLevel: {level.name}")
        else:
            print(f"⚠️  TaxPayerLevel already exists: {level.name}")
    
    # Create Business Sectors
    sectors = [
        {'code': 'AGR', 'name': 'Agriculture', 'description': 'Agricultural activities'},
        {'code': 'MAN', 'name': 'Manufacturing', 'description': 'Manufacturing and production'},
        {'code': 'TRD', 'name': 'Trade', 'description': 'Trading and commerce'},
        {'code': 'SRV', 'name': 'Services', 'description': 'Service industries'},
        {'code': 'CON', 'name': 'Construction', 'description': 'Construction and building'},
    ]
    
    for sector_data in sectors:
        sector, created = BusinessSector.objects.get_or_create(
            code=sector_data['code'],
            defaults={
                'name': sector_data['name'],
                'description': sector_data['description']
            }
        )
        if created:
            print(f"✅ Created BusinessSector: {sector.name}")
        else:
            print(f"⚠️  BusinessSector already exists: {sector.name}")
    
    # Create Business Sub-Sectors
    sub_sectors = [
        {'sector_code': 'AGR', 'code': '001', 'name': 'Crop Production', 'description': 'Growing of crops'},
        {'sector_code': 'AGR', 'code': '002', 'name': 'Livestock', 'description': 'Animal husbandry'},
        {'sector_code': 'MAN', 'code': '001', 'name': 'Food Processing', 'description': 'Food and beverage manufacturing'},
        {'sector_code': 'MAN', 'code': '002', 'name': 'Textile', 'description': 'Textile and clothing manufacturing'},
        {'sector_code': 'TRD', 'code': '001', 'name': 'Retail Trade', 'description': 'Retail trading activities'},
        {'sector_code': 'TRD', 'code': '002', 'name': 'Wholesale Trade', 'description': 'Wholesale trading activities'},
        {'sector_code': 'SRV', 'code': '001', 'name': 'Professional Services', 'description': 'Professional and technical services'},
        {'sector_code': 'SRV', 'code': '002', 'name': 'Financial Services', 'description': 'Banking and financial services'},
        {'sector_code': 'CON', 'code': '001', 'name': 'Building Construction', 'description': 'Construction of buildings'},
        {'sector_code': 'CON', 'code': '002', 'name': 'Infrastructure', 'description': 'Infrastructure development'},
    ]
    
    for sub_sector_data in sub_sectors:
        try:
            sector = BusinessSector.objects.get(code=sub_sector_data['sector_code'])
            sub_sector, created = BusinessSubSector.objects.get_or_create(
                business_sector=sector,
                code=sub_sector_data['code'],
                defaults={
                    'name': sub_sector_data['name'],
                    'description': sub_sector_data['description']
                }
            )
            if created:
                print(f"✅ Created BusinessSubSector: {sub_sector.name}")
            else:
                print(f"⚠️  BusinessSubSector already exists: {sub_sector.name}")
        except BusinessSector.DoesNotExist:
            print(f"❌ Sector {sub_sector_data['sector_code']} not found for sub-sector {sub_sector_data['name']}")
    
    # Create Organization Business Types
    org_types = [
        {'code': 'PLC', 'name': 'Private Limited Company', 'description': 'Private Limited Company'},
        {'code': 'SC', 'name': 'Share Company', 'description': 'Share Company'},
        {'code': 'COOP', 'name': 'Cooperative', 'description': 'Cooperative Organization'},
        {'code': 'NGO', 'name': 'Non-Governmental Organization', 'description': 'Non-Governmental Organization'},
        {'code': 'GOV', 'name': 'Government Entity', 'description': 'Government Organization'},
        {'code': 'SOLE', 'name': 'Sole Proprietorship', 'description': 'Sole Proprietorship Business'},
    ]
    
    for org_type_data in org_types:
        org_type, created = OrganizationBusinessType.objects.get_or_create(
            code=org_type_data['code'],
            defaults={
                'name': org_type_data['name'],
                'description': org_type_data['description']
            }
        )
        if created:
            print(f"✅ Created OrganizationBusinessType: {org_type.name}")
        else:
            print(f"⚠️  OrganizationBusinessType already exists: {org_type.name}")
    
    print("\n🎉 Sample taxpayer data creation completed!")
    print(f"📊 Summary:")
    print(f"   - TaxPayerLevels: {TaxPayerLevel.objects.count()}")
    print(f"   - BusinessSectors: {BusinessSector.objects.count()}")
    print(f"   - BusinessSubSectors: {BusinessSubSector.objects.count()}")
    print(f"   - OrganizationBusinessTypes: {OrganizationBusinessType.objects.count()}")

if __name__ == '__main__':
    create_sample_data()
