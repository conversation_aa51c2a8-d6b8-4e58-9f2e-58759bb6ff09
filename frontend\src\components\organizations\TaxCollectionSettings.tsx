/**
 * Tax Collection Settings Component
 * 
 * Manages penalty and interest rate configuration for tax collection
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Grid,
  TextField,
  Button,
  Box,
  Alert,
  Divider,
  InputAdornment,
  Chip,
  Paper,
} from '@mui/material';
import {
  AccountBalance,
  Person,
  Business,
  Percent,
  Save,
  Info,
  Warning,
} from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import organizationService from '../../services/organizationService';
import type { Organization } from '../../services/organizationService';

interface TaxCollectionSettingsProps {
  organization: Organization;
  onUpdate: (updatedOrg: Organization) => void;
  readOnly?: boolean;
}

const TaxCollectionSettings: React.FC<TaxCollectionSettingsProps> = ({
  organization,
  onUpdate,
  readOnly = false,
}) => {


  const [settings, setSettings] = useState({
    individual_penalty_rate: organization?.individual_penalty_rate || 5.0,
    individual_interest_rate: organization?.individual_interest_rate || 2.0,
    organization_penalty_rate: organization?.organization_penalty_rate || 10.0,
    organization_interest_rate: organization?.organization_interest_rate || 3.0,
  });
  const [loading, setLoading] = useState(false);
  const { showNotification } = useNotification();

  // Update settings when organization changes
  useEffect(() => {
    if (organization) {
      setSettings({
        individual_penalty_rate: Number(organization.individual_penalty_rate) || 5.0,
        individual_interest_rate: Number(organization.individual_interest_rate) || 2.0,
        organization_penalty_rate: Number(organization.organization_penalty_rate) || 10.0,
        organization_interest_rate: Number(organization.organization_interest_rate) || 3.0,
      });
    }
  }, [organization]);

  const handleChange = (field: keyof typeof settings) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = parseFloat(event.target.value) || 0;
    setSettings(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSave = async () => {
    try {
      setLoading(true);

      if (!organization || !organization.id) {
        throw new Error('Organization ID is missing');
      }

      const updatedOrg = await organizationService.updateOrganization(organization.id, {
        individual_penalty_rate: settings.individual_penalty_rate,
        individual_interest_rate: settings.individual_interest_rate,
        organization_penalty_rate: settings.organization_penalty_rate,
        organization_interest_rate: settings.organization_interest_rate,
      });

      // Merge the updated fields with the existing organization to preserve all fields
      const mergedOrganization = {
        ...organization,
        individual_penalty_rate: settings.individual_penalty_rate,
        individual_interest_rate: settings.individual_interest_rate,
        organization_penalty_rate: settings.organization_penalty_rate,
        organization_interest_rate: settings.organization_interest_rate,
        updated_at: updatedOrg.updated_at || new Date().toISOString(),
      };

      onUpdate(mergedOrganization);
      showNotification('Tax collection settings updated successfully', 'success');
    } catch (error) {
      console.error('Error updating tax settings:', error);
      showNotification('Failed to update tax collection settings', 'error');
    } finally {
      setLoading(false);
    }
  };

  const hasChanges = () => {
    return (
      settings.individual_penalty_rate !== (organization.individual_penalty_rate || 5.0) ||
      settings.individual_interest_rate !== (organization.individual_interest_rate || 2.0) ||
      settings.organization_penalty_rate !== (organization.organization_penalty_rate || 10.0) ||
      settings.organization_interest_rate !== (organization.organization_interest_rate || 3.0)
    );
  };

  return (
    <Card>
      <CardContent>
        <Box display="flex" alignItems="center" gap={1} mb={2}>
          <AccountBalance color="primary" />
          <Typography variant="h6">
            Tax Collection Settings
          </Typography>
          <Chip
            label="Configuration"
            size="small"
            color="primary"
            variant="outlined"
          />
        </Box>

        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="body2">
            Configure penalty and interest rates for tax collection. These rates will be automatically
            applied to overdue payments based on taxpayer type.
          </Typography>
        </Alert>

        <Grid container spacing={3}>
          {/* Individual Taxpayer Settings */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Paper sx={{ p: 2, bgcolor: 'primary.50', border: '1px solid', borderColor: 'primary.200' }}>
              <Box display="flex" alignItems="center" gap={1} mb={2}>
                <Person color="primary" />
                <Typography variant="h6" color="primary.main">
                  Individual Taxpayers
                </Typography>
              </Box>
              
              <Grid container spacing={2}>
                <Grid size={{ xs: 12 }}>
                  <TextField
                    fullWidth
                    label="Penalty Rate"
                    type="number"
                    value={settings.individual_penalty_rate}
                    onChange={handleChange('individual_penalty_rate')}
                    disabled={readOnly || loading}
                    InputProps={{
                      endAdornment: <InputAdornment position="end">%</InputAdornment>,
                    }}
                    inputProps={{
                      min: 0,
                      max: 100,
                      step: 0.1,
                    }}
                    helperText="One-time penalty rate applied when payment becomes overdue"
                  />
                </Grid>
                <Grid size={{ xs: 12 }}>
                  <TextField
                    fullWidth
                    label="Interest Rate (Monthly)"
                    type="number"
                    value={settings.individual_interest_rate}
                    onChange={handleChange('individual_interest_rate')}
                    disabled={readOnly || loading}
                    InputProps={{
                      endAdornment: <InputAdornment position="end">% / month</InputAdornment>,
                    }}
                    inputProps={{
                      min: 0,
                      max: 100,
                      step: 0.1,
                    }}
                    helperText="Monthly interest rate calculated based on days overdue"
                  />
                </Grid>
              </Grid>
            </Paper>
          </Grid>

          {/* Organization Taxpayer Settings */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Paper sx={{ p: 2, bgcolor: 'secondary.50', border: '1px solid', borderColor: 'secondary.200' }}>
              <Box display="flex" alignItems="center" gap={1} mb={2}>
                <Business color="secondary" />
                <Typography variant="h6" color="secondary.main">
                  Organization Taxpayers
                </Typography>
              </Box>
              
              <Grid container spacing={2}>
                <Grid size={{ xs: 12 }}>
                  <TextField
                    fullWidth
                    label="Penalty Rate"
                    type="number"
                    value={settings.organization_penalty_rate}
                    onChange={handleChange('organization_penalty_rate')}
                    disabled={readOnly || loading}
                    InputProps={{
                      endAdornment: <InputAdornment position="end">%</InputAdornment>,
                    }}
                    inputProps={{
                      min: 0,
                      max: 100,
                      step: 0.1,
                    }}
                    helperText="One-time penalty rate applied when payment becomes overdue"
                  />
                </Grid>
                <Grid size={{ xs: 12 }}>
                  <TextField
                    fullWidth
                    label="Interest Rate (Monthly)"
                    type="number"
                    value={settings.organization_interest_rate}
                    onChange={handleChange('organization_interest_rate')}
                    disabled={readOnly || loading}
                    InputProps={{
                      endAdornment: <InputAdornment position="end">% / month</InputAdornment>,
                    }}
                    inputProps={{
                      min: 0,
                      max: 100,
                      step: 0.1,
                    }}
                    helperText="Monthly interest rate calculated based on days overdue"
                  />
                </Grid>
              </Grid>
            </Paper>
          </Grid>
        </Grid>

        {/* Rate Comparison */}
        <Box mt={3}>
          <Typography variant="subtitle2" gutterBottom>
            Rate Comparison
          </Typography>
          <Grid container spacing={2}>
            <Grid size={{ xs: 6, sm: 3 }}>
              <Box textAlign="center" p={1} bgcolor="primary.50" borderRadius={1}>
                <Typography variant="caption" color="primary.main">Individual Penalty</Typography>
                <Typography variant="h6" color="primary.main">
                  {settings.individual_penalty_rate}%
                </Typography>
              </Box>
            </Grid>
            <Grid size={{ xs: 6, sm: 3 }}>
              <Box textAlign="center" p={1} bgcolor="primary.50" borderRadius={1}>
                <Typography variant="caption" color="primary.main">Individual Interest</Typography>
                <Typography variant="h6" color="primary.main">
                  {settings.individual_interest_rate}%/mo
                </Typography>
              </Box>
            </Grid>
            <Grid size={{ xs: 6, sm: 3 }}>
              <Box textAlign="center" p={1} bgcolor="secondary.50" borderRadius={1}>
                <Typography variant="caption" color="secondary.main">Organization Penalty</Typography>
                <Typography variant="h6" color="secondary.main">
                  {settings.organization_penalty_rate}%
                </Typography>
              </Box>
            </Grid>
            <Grid size={{ xs: 6, sm: 3 }}>
              <Box textAlign="center" p={1} bgcolor="secondary.50" borderRadius={1}>
                <Typography variant="caption" color="secondary.main">Organization Interest</Typography>
                <Typography variant="h6" color="secondary.main">
                  {settings.organization_interest_rate}%/mo
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Box>

        {/* Save Button */}
        {!readOnly && (
          <Box mt={3} display="flex" justifyContent="flex-end">
            <Button
              variant="contained"
              onClick={handleSave}
              disabled={loading || !hasChanges()}
              startIcon={<Save />}
            >
              {loading ? 'Saving...' : 'Save Tax Settings'}
            </Button>
          </Box>
        )}

        {/* Information */}
        <Alert severity="warning" sx={{ mt: 2 }}>
          <Typography variant="body2">
            <strong>Important:</strong> These rates will be automatically applied to all new and existing
            overdue tax collections. Changes will affect penalty and interest calculations immediately.
          </Typography>
        </Alert>
      </CardContent>
    </Card>
  );
};

export default TaxCollectionSettings;
