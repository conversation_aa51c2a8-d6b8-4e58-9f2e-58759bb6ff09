import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Avatar,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Menu,
  Alert,
} from '@mui/material';
import {
  Inventory,
  Add,
  Edit,
  Delete,
  DragIndicator,
  MoreVert,
  Refresh,
} from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import { normalizeCode, createCodeBlurHandler } from '../../utils/codeUtils';
import locationService from '../../services/locationService';
import type { Shelf, Kent, KentCreate } from '../../services/locationService';

interface GridPosition {
  row: number;
  column: number;
  position_code: string;
  box: {
    id: number;
    name: string;
    position_code: string;
  };
  kents: Kent[];
  kent_count: number;
  is_occupied: boolean;
}

interface ShelfGridData {
  shelf: Shelf;
  grid: GridPosition[][];
  available_positions: Array<[number, number]>;
}

interface ShelfGridViewProps {
  shelfId: number;
  onKentClick?: (kent: Kent) => void;
  onKentUpdate?: () => void;
  editable?: boolean;
}

const ShelfGridView: React.FC<ShelfGridViewProps> = ({
  shelfId,
  onKentClick,
  onKentUpdate,
  editable = false,
}) => {
  const { showSuccess, showError } = useNotification();

  const [gridData, setGridData] = useState<ShelfGridData | null>(null);
  const [loading, setLoading] = useState(true);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [selectedPosition, setSelectedPosition] = useState<{ row: number; column: number } | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedKent, setSelectedKent] = useState<Kent | null>(null);
  const [draggedKent, setDraggedKent] = useState<Kent | null>(null);

  const [newKentData, setNewKentData] = useState<KentCreate & { row: number; column: number }>({
    box: 0, // Will be set when position is selected
    name: '',
    code: '',
    description: '',
    capacity: 100,
    color: '',
    material: 'Cardboard',
    row: 1,
    column: 1,
  });

  useEffect(() => {
    loadGridData();
  }, [shelfId]);

  const loadGridData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/locations/shelves/${shelfId}/grid/`);
      const data = await response.json();
      setGridData(data);
    } catch (err) {
      showError('Failed to load shelf grid');
    } finally {
      setLoading(false);
    }
  };

  const handlePositionClick = (position: GridPosition) => {
    if (position.kents.length > 0) {
      // If there are kents, show the first one or handle multiple kents
      if (onKentClick) {
        onKentClick(position.kents[0]);
      }
    } else if (editable) {
      // Create new kent in this box
      setSelectedPosition({ row: position.row, column: position.column });
      setNewKentData({
        ...newKentData,
        box: position.box.id,
        row: position.row,
        column: position.column,
        name: `Kent ${position.position_code}`,
        code: `K${position.position_code}`,
      });
      setCreateDialogOpen(true);
    }
  };

  const handleKentMenuOpen = (event: React.MouseEvent<HTMLElement>, kent: Kent) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setSelectedKent(kent);
  };

  const handleKentMenuClose = () => {
    setAnchorEl(null);
    setSelectedKent(null);
  };

  const handleCreateKent = async () => {
    try {
      await locationService.createKent(newKentData);
      showSuccess('Kent created successfully');
      setCreateDialogOpen(false);
      loadGridData();
      if (onKentUpdate) onKentUpdate();
    } catch (err: any) {
      showError(err.response?.data?.message || 'Failed to create kent');
    }
  };

  const handleDeleteKent = async () => {
    if (!selectedKent) return;

    try {
      await locationService.deleteKent(selectedKent.id);
      showSuccess('Kent deleted successfully');
      loadGridData();
      if (onKentUpdate) onKentUpdate();
    } catch (err) {
      showError('Failed to delete kent');
    }
    handleKentMenuClose();
  };

  const handleDragStart = (event: React.DragEvent, kent: Kent) => {
    if (!editable) return;
    setDraggedKent(kent);
    event.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (event: React.DragEvent) => {
    if (!editable || !draggedKent) return;
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = async (event: React.DragEvent, targetPosition: GridPosition) => {
    if (!editable || !draggedKent || targetPosition.is_occupied) return;

    event.preventDefault();

    try {
      // Move kent to the target box
      await locationService.updateKent(draggedKent.id, {
        box: targetPosition.box.id,
      });
      showSuccess('Kent moved successfully');
      loadGridData();
      if (onKentUpdate) onKentUpdate();
    } catch (err) {
      showError('Failed to move kent');
    }

    setDraggedKent(null);
  };

  const getPositionColor = (position: GridPosition) => {
    if (position.is_occupied && position.kents.length > 0) {
      const kent = position.kents[0]; // Use first kent for color
      if (kent.is_full) return 'error.light';
      if ((kent.utilization || 0) > 80) return 'warning.light';
      return kent.color ? kent.color.toLowerCase() : 'primary.light';
    }
    return 'grey.100';
  };

  const getPositionTextColor = (position: GridPosition) => {
    if (position.is_occupied) {
      return 'white';
    }
    return 'text.primary';
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <Typography>Loading grid...</Typography>
      </Box>
    );
  }

  if (!gridData) {
    return (
      <Alert severity="error">Failed to load shelf grid data</Alert>
    );
  }

  return (
    <Box>
      {/* Grid Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          Shelf Grid ({gridData.shelf.name})
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <IconButton onClick={loadGridData} size="small">
            <Refresh />
          </IconButton>
          {editable && (
            <Typography variant="caption" color="text.secondary">
              Click empty positions to add kents • Drag to move
            </Typography>
          )}
        </Box>
      </Box>

      {/* Grid */}
      <Box sx={{ overflowX: 'auto', border: 1, borderColor: 'divider', borderRadius: 1, p: 1 }}>
        <Box sx={{ minWidth: gridData.grid[0]?.length * 90 || 400 }}>
          {gridData.grid.map((row, rowIndex) => (
            <Box key={rowIndex} sx={{ display: 'flex', mb: 1 }}>
              {row.map((position) => (
                <Paper
                  key={`${position.row}-${position.column}`}
                  sx={{
                    width: 90,
                    height: 90,
                    m: 0.5,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    cursor: editable || position.is_occupied ? 'pointer' : 'default',
                    bgcolor: getPositionColor(position),
                    color: getPositionTextColor(position),
                    border: draggedKent && !position.is_occupied ? 2 : 1,
                    borderColor: draggedKent && !position.is_occupied ? 'primary.main' : 'divider',
                    '&:hover': {
                      bgcolor: position.is_occupied 
                        ? getPositionColor(position) 
                        : editable ? 'grey.200' : 'grey.100',
                      transform: 'scale(1.02)',
                    },
                    transition: 'all 0.2s ease-in-out',
                  }}
                  onClick={() => handlePositionClick(position)}
                  onDragOver={handleDragOver}
                  onDrop={(e) => handleDrop(e, position)}
                >
                  <Typography variant="caption" fontWeight="bold" sx={{ fontSize: '0.7rem' }}>
                    {position.position_code}
                  </Typography>
                  
                  {position.kents.length > 0 ? (
                    <Box
                      sx={{ textAlign: 'center', position: 'relative', width: '100%' }}
                      draggable={editable}
                      onDragStart={(e) => handleDragStart(e, position.kents[0])}
                    >
                      <Inventory sx={{ fontSize: 20, mb: 0.5 }} />
                      {position.kents.length === 1 ? (
                        <>
                          <Typography variant="caption" sx={{ fontSize: '0.6rem', display: 'block' }}>
                            {position.kents[0].code}
                          </Typography>
                          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 0.5, mt: 0.5 }}>
                            <Chip
                              label={`${position.kents[0].file_count}F`}
                              size="small"
                              sx={{
                                height: 16,
                                fontSize: '0.5rem',
                                bgcolor: 'rgba(255,255,255,0.2)',
                                color: 'inherit'
                              }}
                            />
                            <Chip
                              label={`${(position.kents[0].utilization || 0).toFixed(0)}%`}
                              size="small"
                              sx={{
                                height: 16,
                                fontSize: '0.5rem',
                                bgcolor: 'rgba(255,255,255,0.2)',
                                color: 'inherit'
                              }}
                            />
                          </Box>
                        </>
                      ) : (
                        <>
                          <Typography variant="caption" sx={{ fontSize: '0.6rem', display: 'block' }}>
                            {position.kents.length} Kents
                          </Typography>
                          <Chip
                            label={`${position.kents.reduce((sum, kent) => sum + kent.file_count, 0)}F`}
                            size="small"
                            sx={{
                              height: 16,
                              fontSize: '0.5rem',
                              bgcolor: 'rgba(255,255,255,0.2)',
                              color: 'inherit'
                            }}
                          />
                        </>
                      )}

                      {editable && (
                        <IconButton
                          size="small"
                          sx={{ 
                            position: 'absolute', 
                            top: -8, 
                            right: -8,
                            bgcolor: 'rgba(0,0,0,0.5)',
                            color: 'white',
                            width: 20,
                            height: 20,
                            '&:hover': { bgcolor: 'rgba(0,0,0,0.7)' }
                          }}
                          onClick={(e) => handleKentMenuOpen(e, position.kents[0])}
                        >
                          <MoreVert sx={{ fontSize: 12 }} />
                        </IconButton>
                      )}
                      
                      {editable && (
                        <DragIndicator 
                          sx={{ 
                            position: 'absolute', 
                            bottom: 2, 
                            right: 2,
                            fontSize: 12,
                            opacity: 0.7
                          }} 
                        />
                      )}
                    </Box>
                  ) : (
                    <Add sx={{ fontSize: 20, opacity: editable ? 0.7 : 0.3 }} />
                  )}
                </Paper>
              ))}
            </Box>
          ))}
        </Box>
      </Box>

      {/* Legend */}
      <Box sx={{ mt: 2, display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'center' }}>
        <Typography variant="caption" color="text.secondary">Legend:</Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
          <Box sx={{ width: 12, height: 12, bgcolor: 'grey.100', border: 1, borderColor: 'divider' }} />
          <Typography variant="caption">Empty</Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
          <Box sx={{ width: 12, height: 12, bgcolor: 'primary.light' }} />
          <Typography variant="caption">Occupied</Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
          <Box sx={{ width: 12, height: 12, bgcolor: 'warning.light' }} />
          <Typography variant="caption">High Usage</Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
          <Box sx={{ width: 12, height: 12, bgcolor: 'error.light' }} />
          <Typography variant="caption">Full</Typography>
        </Box>
      </Box>

      {/* Create Kent Dialog */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Create Kent at {selectedPosition?.row && selectedPosition?.column ? `R${selectedPosition.row.toString().padStart(2, '0')}C${selectedPosition.column.toString().padStart(2, '0')}` : ''}</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            <TextField
              fullWidth
              label="Kent Name"
              value={newKentData.name}
              onChange={(e) => setNewKentData({ ...newKentData, name: e.target.value })}
            />
            <TextField
              fullWidth
              label="Kent Code"
              value={newKentData.code}
              onChange={(e) => setNewKentData({ ...newKentData, code: e.target.value })}
              onBlur={createCodeBlurHandler(
                (field, value) => setNewKentData(prev => ({ ...prev, [field]: value })),
                'code'
              )}
              helperText="Code will be automatically converted to uppercase"
            />
            <TextField
              fullWidth
              label="Capacity"
              type="number"
              value={newKentData.capacity}
              onChange={(e) => setNewKentData({ ...newKentData, capacity: parseInt(e.target.value) || 100 })}
            />
            <FormControl fullWidth>
              <InputLabel>Material</InputLabel>
              <Select
                value={newKentData.material}
                onChange={(e) => setNewKentData({ ...newKentData, material: e.target.value })}
                label="Material"
              >
                {locationService.getMaterialOptions().map((material) => (
                  <MenuItem key={material.value} value={material.value}>
                    {material.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleCreateKent} variant="contained">Create Kent</Button>
        </DialogActions>
      </Dialog>

      {/* Kent Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleKentMenuClose}
      >
        <MenuItem onClick={() => {
          if (selectedKent && onKentClick) onKentClick(selectedKent);
          handleKentMenuClose();
        }}>
          <Edit fontSize="small" sx={{ mr: 1 }} />
          Edit Kent
        </MenuItem>
        <MenuItem onClick={handleDeleteKent} sx={{ color: 'error.main' }}>
          <Delete fontSize="small" sx={{ mr: 1 }} />
          Delete Kent
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default ShelfGridView;
