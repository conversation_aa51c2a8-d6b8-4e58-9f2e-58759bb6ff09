import apiClient from './api';

// Geographical Location Hierarchy Interfaces
export interface Country {
  id: number;
  name: string;
  code: string;
}

export interface Region {
  id: number;
  name: string;
  code: string;
}

export interface Zone {
  id: number;
  name: string;
  code: string;
}

export interface City {
  id: number;
  name: string;
  code: string;
}

export interface SubCity {
  id: number;
  name: string;
  code: string;
  type: 'subcity' | 'woreda' | 'district';
}

export interface Kebele {
  id: number;
  name: string;
  code: string;
  number: number;
}

class GeographicalLocationService {
  private baseUrl = '/locations';

  // Get countries for dropdown
  async getCountries(): Promise<Country[]> {
    const url = `${this.baseUrl}/countries/select/`;
    console.log('🌍 Fetching countries from:', url);
    const response = await apiClient.get(url);
    console.log('🌍 Countries response:', response.data);
    return response.data;
  }

  // Get regions for dropdown (filtered by country)
  async getRegions(countryId?: number): Promise<Region[]> {
    const params = countryId ? { country: countryId } : {};
    const response = await apiClient.get(`${this.baseUrl}/regions/select/`, { params });
    return response.data;
  }

  // Get zones for dropdown (filtered by region)
  async getZones(regionId?: number): Promise<Zone[]> {
    const params = regionId ? { region: regionId } : {};
    const response = await apiClient.get(`${this.baseUrl}/zones/select/`, { params });
    return response.data;
  }

  // Get cities for dropdown (filtered by region - skipping zone level)
  async getCities(regionId?: number): Promise<City[]> {
    const params = regionId ? { region: regionId } : {};
    const response = await apiClient.get(`${this.baseUrl}/cities/select/`, { params });
    return response.data;
  }

  // Get subcities for dropdown (filtered by city)
  async getSubCities(cityId?: number): Promise<SubCity[]> {
    const params = cityId ? { city: cityId } : {};
    const response = await apiClient.get(`${this.baseUrl}/subcities/select/`, { params });
    return response.data;
  }

  // Get kebeles for dropdown (filtered by subcity)
  async getKebeles(subCityId?: number): Promise<Kebele[]> {
    const params = subCityId ? { subcity: subCityId } : {};
    const response = await apiClient.get(`${this.baseUrl}/kebeles/select/`, { params });
    return response.data;
  }

  // Utility method to get full location path
  getLocationPath(country?: string, region?: string, zone?: string, city?: string, subCity?: string, kebele?: string): string {
    const parts = [country, region, zone, city, subCity, kebele].filter(Boolean);
    return parts.join(' → ');
  }

  // Utility method to format location for display
  formatLocationSummary(country?: string, region?: string, city?: string): string {
    const parts = [city, region, country].filter(Boolean);
    return parts.join(', ');
  }

  // Validate location hierarchy (ensure parent-child relationships are correct)
  validateLocationHierarchy(data: {
    country?: string;
    region?: string;
    zone?: string;
    city?: string;
    subCity?: string;
    kebele?: string;
  }): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check if child locations are selected without parents
    if (data.region && !data.country) {
      errors.push('Country must be selected when region is specified');
    }
    if (data.zone && !data.region) {
      errors.push('Region must be selected when zone is specified');
    }
    if (data.city && !data.zone) {
      errors.push('Zone must be selected when city is specified');
    }
    if (data.subCity && !data.city) {
      errors.push('City must be selected when subcity is specified');
    }
    if (data.kebele && !data.subCity) {
      errors.push('Subcity must be selected when kebele is specified');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Get location breadcrumb for navigation
  getLocationBreadcrumb(data: {
    country?: string;
    region?: string;
    zone?: string;
    city?: string;
    subCity?: string;
    kebele?: string;
  }): Array<{ label: string; value: string; level: string }> {
    const breadcrumb = [];

    if (data.country) {
      breadcrumb.push({ label: data.country, value: data.country, level: 'country' });
    }
    if (data.region) {
      breadcrumb.push({ label: data.region, value: data.region, level: 'region' });
    }
    if (data.zone) {
      breadcrumb.push({ label: data.zone, value: data.zone, level: 'zone' });
    }
    if (data.city) {
      breadcrumb.push({ label: data.city, value: data.city, level: 'city' });
    }
    if (data.subCity) {
      breadcrumb.push({ label: data.subCity, value: data.subCity, level: 'subcity' });
    }
    if (data.kebele) {
      breadcrumb.push({ label: data.kebele, value: data.kebele, level: 'kebele' });
    }

    return breadcrumb;
  }

  // Search locations across all levels
  async searchLocations(query: string, level?: 'country' | 'region' | 'zone' | 'city' | 'subcity' | 'kebele'): Promise<any[]> {
    const params: any = { search: query };
    if (level) {
      params.level = level;
    }

    try {
      let endpoint = '';
      switch (level) {
        case 'country':
          endpoint = '/countries/';
          break;
        case 'region':
          endpoint = '/regions/';
          break;
        case 'zone':
          endpoint = '/zones/';
          break;
        case 'city':
          endpoint = '/cities/';
          break;
        case 'subcity':
          endpoint = '/subcities/';
          break;
        case 'kebele':
          endpoint = '/kebeles/';
          break;
        default:
          // Search across all levels
          const results = await Promise.all([
            this.getCountries(),
            this.getRegions(),
            this.getZones(),
            this.getCities(),
            this.getSubCities(),
            this.getKebeles()
          ]);
          
          return results.flat().filter(item => 
            item.name.toLowerCase().includes(query.toLowerCase()) ||
            item.code.toLowerCase().includes(query.toLowerCase())
          );
      }

      const response = await apiClient.get(`${this.baseUrl}${endpoint}`, { params });
      return response.data.results || response.data;
    } catch (error) {
      console.error('Error searching locations:', error);
      return [];
    }
  }
}

export default new GeographicalLocationService();
