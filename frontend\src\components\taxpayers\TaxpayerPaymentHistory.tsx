/**
 * Taxpayer Payment History Component
 * 
 * Comprehensive payment history with overdue tracking and period-based analytics
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Button,
  Tabs,
  Tab,
  Alert,
  CircularProgress,
  LinearProgress,
  Divider,
} from '@mui/material';
import {
  Payment,
  Warning,
  CheckCircle,
  Schedule,
  TrendingUp,
  Receipt,
  AccountBalance,
  Business,
} from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import revenueCollectionService from '../../services/revenueCollectionService';
import StatisticsCard from '../revenue-collection/StatisticsCard';
import type {
  TaxpayerPaymentSummary,
  RegionalRevenueCollection,
  CityServiceRevenueCollection,
} from '../../services/revenueCollectionService';

interface TaxpayerPaymentHistoryProps {
  taxpayerId: string;
  taxpayerType: 'individual' | 'organization';
  taxpayerName: string;
  taxpayerTin: string;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`payment-tabpanel-${index}`}
      aria-labelledby={`payment-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const TaxpayerPaymentHistory: React.FC<TaxpayerPaymentHistoryProps> = ({
  taxpayerId,
  taxpayerType,
  taxpayerName,
  taxpayerTin,
}) => {
  const { showError } = useNotification();
  
  const [loading, setLoading] = useState(true);
  const [tabValue, setTabValue] = useState(0);
  const [summaries, setSummaries] = useState<TaxpayerPaymentSummary[]>([]);
  const [collections, setCollections] = useState<(RegionalRevenueCollection | CityServiceRevenueCollection & { type: string })[]>([]);
  const [totals, setTotals] = useState({
    total_assessed: 0,
    total_paid: 0,
    total_outstanding: 0,
    total_overdue: 0,
  });

  useEffect(() => {
    loadPaymentHistory();
  }, [taxpayerId, taxpayerType]);

  const loadPaymentHistory = async () => {
    try {
      setLoading(true);
      const data = await revenueCollectionService.getTaxpayerPaymentHistory(taxpayerId, taxpayerType);
      setSummaries(data.summaries);
      setCollections(data.collections);
      setTotals(data.totals);
    } catch (error) {
      console.error('Error loading payment history:', error);
      showError('Failed to load payment history');
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ET', {
      style: 'currency',
      currency: 'ETB',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-ET', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'PAID': return 'success';
      case 'OVERDUE': return 'error';
      case 'PARTIAL': return 'warning';
      case 'PENDING': return 'info';
      case 'CANCELLED': return 'default';
      default: return 'default';
    }
  };

  const getPaymentStatusLabel = (status: string) => {
    switch (status) {
      case 'PAID': return 'Paid';
      case 'OVERDUE': return 'Overdue';
      case 'PARTIAL': return 'Partial';
      case 'PENDING': return 'Pending';
      case 'CANCELLED': return 'Cancelled';
      default: return status;
    }
  };

  const overallCompletionRate = totals.total_assessed > 0 
    ? (totals.total_paid / totals.total_assessed) * 100 
    : 100;

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="300px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ mt: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" gutterBottom>
          Payment History
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          {taxpayerName} (TIN: {taxpayerTin})
        </Typography>
      </Box>

      {/* Overall Statistics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatisticsCard
            title="Total Assessed"
            value={formatCurrency(totals.total_assessed)}
            subtitle="All periods"
            icon={<Receipt />}
            color="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatisticsCard
            title="Total Paid"
            value={formatCurrency(totals.total_paid)}
            subtitle={`${overallCompletionRate.toFixed(1)}% complete`}
            icon={<CheckCircle />}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatisticsCard
            title="Outstanding"
            value={formatCurrency(totals.total_outstanding)}
            subtitle="Remaining balance"
            icon={<Schedule />}
            color="warning"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatisticsCard
            title="Overdue"
            value={formatCurrency(totals.total_overdue)}
            subtitle="Requires attention"
            icon={<Warning />}
            color="error"
          />
        </Grid>
      </Grid>

      {/* Payment Completion Progress */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Overall Payment Progress
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Box sx={{ width: '100%', mr: 1 }}>
              <LinearProgress
                variant="determinate"
                value={overallCompletionRate}
                sx={{ height: 10, borderRadius: 5 }}
                color={overallCompletionRate >= 80 ? 'success' : overallCompletionRate >= 50 ? 'warning' : 'error'}
              />
            </Box>
            <Box sx={{ minWidth: 35 }}>
              <Typography variant="body2" color="text.secondary">
                {overallCompletionRate.toFixed(1)}%
              </Typography>
            </Box>
          </Box>
          <Typography variant="body2" color="text.secondary">
            {formatCurrency(totals.total_paid)} of {formatCurrency(totals.total_assessed)} paid
          </Typography>
        </CardContent>
      </Card>

      {/* Overdue Alert */}
      {totals.total_overdue > 0 && (
        <Alert severity="error" sx={{ mb: 3 }}>
          <Typography variant="body2">
            <strong>Overdue Payment Alert:</strong> {formatCurrency(totals.total_overdue)} in overdue payments requires immediate attention.
          </Typography>
        </Alert>
      )}

      {/* Tabs */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="Period Summary" />
            <Tab label="All Collections" />
          </Tabs>
        </Box>

        {/* Period Summary Tab */}
        <TabPanel value={tabValue} index={0}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Period</TableCell>
                  <TableCell align="right">Assessed</TableCell>
                  <TableCell align="right">Paid</TableCell>
                  <TableCell align="right">Outstanding</TableCell>
                  <TableCell align="right">Overdue</TableCell>
                  <TableCell>Progress</TableCell>
                  <TableCell>Last Payment</TableCell>
                  <TableCell>Next Due</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {summaries.map((summary) => {
                  const completionRate = summary.total_assessed > 0 
                    ? (summary.total_paid / summary.total_assessed) * 100 
                    : 100;
                  
                  return (
                    <TableRow key={summary.id}>
                      <TableCell>
                        <Typography variant="body2" fontWeight="medium">
                          {summary.period_name}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body2">
                          {formatCurrency(summary.total_assessed)}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body2" color="success.main">
                          {formatCurrency(summary.total_paid)}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body2" color="warning.main">
                          {formatCurrency(summary.total_outstanding)}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body2" color="error.main">
                          {formatCurrency(summary.total_overdue)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', minWidth: 100 }}>
                          <LinearProgress
                            variant="determinate"
                            value={completionRate}
                            sx={{ width: '100%', mr: 1, height: 6 }}
                            color={completionRate >= 80 ? 'success' : completionRate >= 50 ? 'warning' : 'error'}
                          />
                          <Typography variant="caption">
                            {completionRate.toFixed(0)}%
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {summary.last_payment_date ? formatDate(summary.last_payment_date) : 'No payments'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {summary.next_due_date ? formatDate(summary.next_due_date) : 'No pending'}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  );
                })}
                {summaries.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={8} align="center">
                      <Typography variant="body2" color="text.secondary">
                        No payment history found
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        {/* All Collections Tab */}
        <TabPanel value={tabValue} index={1}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Date</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Revenue Source</TableCell>
                  <TableCell>Period</TableCell>
                  <TableCell align="right">Amount</TableCell>
                  <TableCell align="right">Paid</TableCell>
                  <TableCell align="right">Outstanding</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Due Date</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {collections.map((collection) => (
                  <TableRow key={collection.id}>
                    <TableCell>{formatDate(collection.collection_date)}</TableCell>
                    <TableCell>
                      <Chip
                        label={(collection as any).type === 'regional' ? 'Regional' : 'City Service'}
                        color={(collection as any).type === 'regional' ? 'primary' : 'secondary'}
                        size="small"
                        icon={(collection as any).type === 'regional' ? <AccountBalance /> : <Business />}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {collection.revenue_source_name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {collection.category_name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {collection.period_name}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      <Typography variant="body2" fontWeight="medium">
                        {formatCurrency(collection.amount)}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      <Typography variant="body2" color="success.main">
                        {formatCurrency(collection.paid_amount)}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      <Typography variant="body2" color="warning.main">
                        {formatCurrency(collection.outstanding_amount)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getPaymentStatusLabel(collection.payment_status)}
                        color={getPaymentStatusColor(collection.payment_status)}
                        size="small"
                      />
                      {collection.is_overdue && (
                        <Typography variant="caption" color="error" display="block">
                          {collection.days_overdue} days overdue
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {collection.due_date ? formatDate(collection.due_date) : 'N/A'}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}
                {collections.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={9} align="center">
                      <Typography variant="body2" color="text.secondary">
                        No collections found
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>
      </Card>
    </Box>
  );
};

export default TaxpayerPaymentHistory;
