import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Chip,
} from '@mui/material';
import {
  Public,
  Edit,
  Delete,
  Home,
  LocationOn,
  Info,
  Language,
  Flag,
} from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import locationService from '../../services/locationService';
import type { Country } from '../../services/locationService';
import DetailPageTemplate from '../../components/common/DetailPageTemplate';
import ConfirmationDialog from '../../components/common/ConfirmationDialog';

const CountryDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showNotification } = useNotification();
  
  const [country, setCountry] = useState<Country | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    if (id) {
      loadCountry();
    }
  }, [id]);

  const loadCountry = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await locationService.getCountry(Number(id));
      setCountry(data);
    } catch (error) {
      console.error('Error loading country:', error);
      setError('Failed to load country');
      showNotification('Failed to load country', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    navigate(`/locations/countries/${id}/edit`);
  };

  const handleDelete = () => {
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!country) return;
    
    try {
      setDeleting(true);
      await locationService.deleteCountry(country.id);
      showNotification('Country deleted successfully', 'success');
      navigate('/locations/countries');
    } catch (error) {
      console.error('Error deleting country:', error);
      showNotification('Failed to delete country', 'error');
    } finally {
      setDeleting(false);
      setDeleteDialogOpen(false);
    }
  };

  const handleBack = () => {
    navigate('/locations/countries');
  };

  if (!country && !loading && !error) {
    setError('Country not found');
  }

  const breadcrumbs = [
    { label: 'Dashboard', path: '/dashboard', icon: <Home fontSize="small" /> },
    { label: 'Locations', path: '/locations', icon: <LocationOn fontSize="small" /> },
    { label: 'Countries', path: '/locations/countries', icon: <Public fontSize="small" /> },
    { label: country?.name || 'Country', path: undefined, icon: <Flag fontSize="small" /> },
  ];

  const actions = [
    {
      label: 'Edit',
      icon: <Edit />,
      onClick: handleEdit,
      color: 'primary' as const,
    },
    {
      label: 'Delete',
      icon: <Delete />,
      onClick: handleDelete,
      color: 'error' as const,
    },
  ];

  const chips = country ? [
    {
      label: country.is_active ? 'Active' : 'Inactive',
      color: country.is_active ? 'success' as const : 'error' as const,
    },
    ...(country.code ? [{
      label: `Code: ${country.code}`,
      color: 'info' as const,
    }] : []),
  ] : [];

  const sections = country ? [
    {
      title: 'Basic Information',
      icon: <Info />,
      content: (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'primary.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'primary.200'
          }}>
            <Flag sx={{ color: 'primary.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="primary.main" sx={{ fontWeight: 600 }}>
                Country Name
              </Typography>
              <Typography variant="body2" color="primary.dark">
                {country.name}
              </Typography>
            </Box>
          </Box>
          
          {country.code && (
            <Box sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              p: 2, 
              bgcolor: 'info.50',
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'info.200'
            }}>
              <Language sx={{ color: 'info.main', mr: 2 }} />
              <Box>
                <Typography variant="subtitle2" color="info.main" sx={{ fontWeight: 600 }}>
                  Country Code
                </Typography>
                <Typography variant="body2" color="info.dark">
                  {country.code}
                </Typography>
              </Box>
            </Box>
          )}
          
          {country.description && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" color="text.secondary" sx={{ fontWeight: 600, mb: 1 }}>
                Description
              </Typography>
              <Typography variant="body2" sx={{ 
                p: 2, 
                bgcolor: 'grey.50', 
                borderRadius: 1,
                border: '1px solid',
                borderColor: 'grey.200'
              }}>
                {country.description}
              </Typography>
            </Box>
          )}
        </Box>
      ),
    },
    {
      title: 'Statistics',
      icon: <LocationOn />,
      content: (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'success.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'success.200'
          }}>
            <LocationOn sx={{ color: 'success.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="success.main" sx={{ fontWeight: 600 }}>
                Regions
              </Typography>
              <Typography variant="body2" color="success.dark">
                {country.region_count || 0} regions
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'warning.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'warning.200'
          }}>
            <LocationOn sx={{ color: 'warning.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="warning.main" sx={{ fontWeight: 600 }}>
                Total Cities
              </Typography>
              <Typography variant="body2" color="warning.dark">
                {country.city_count || 0} cities
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 2, 
            bgcolor: 'info.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'info.200'
          }}>
            <Public sx={{ color: 'info.main', mr: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="info.main" sx={{ fontWeight: 600 }}>
                Status
              </Typography>
              <Typography variant="body2" color="info.dark">
                {country.is_active ? 'Active and available for use' : 'Inactive - not available for selection'}
              </Typography>
            </Box>
          </Box>
        </Box>
      ),
    },
  ] : [];

  return (
    <>
      <DetailPageTemplate
        loading={loading}
        error={error}
        breadcrumbs={breadcrumbs}
        title={country?.name || 'Country'}
        subtitle={country?.code ? `Country Code: ${country.code}` : undefined}
        avatar={{
          fallbackIcon: <Public sx={{ fontSize: 40 }} />,
          alt: country?.name || 'Country',
        }}
        chips={chips}
        actions={actions}
        sections={sections}
        onBack={handleBack}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleConfirmDelete}
        title="Delete Country"
        itemName={country?.name}
        itemType="Country"
        message={`Are you sure you want to delete "${country?.name}"? This will also affect all regions, cities, and other locations within this country.`}
        confirmText="Delete Country"
        severity="error"
        loading={deleting}
      />
    </>
  );
};

export default CountryDetailPage;
