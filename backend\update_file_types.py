#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to update existing files to use the new FileType model
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'arada_dms.settings')
django.setup()

from locations.models import File, FileType

def update_file_types():
    print("Updating existing files to use FileType model")
    print("=" * 60)
    
    # Get the Business FileType (default)
    try:
        business_file_type = FileType.objects.get(code='BUS')
        print(f"Using default FileType: {business_file_type}")
    except FileType.DoesNotExist:
        print("❌ Business FileType not found. Run migrations first.")
        return
    
    # Get all files
    files = File.objects.all()
    print(f"Found {files.count()} files to update")
    
    if files.count() == 0:
        print("✅ No files to update")
        return
    
    # Update files using raw SQL to avoid model validation issues
    from django.db import connection
    
    with connection.cursor() as cursor:
        # First, let's see what the current file_type values are
        cursor.execute("SELECT DISTINCT file_type FROM locations_file LIMIT 10")
        current_types = cursor.fetchall()
        print(f"Current file_type values: {current_types}")
        
        # Update all files to use the Business FileType ID
        cursor.execute(
            "UPDATE locations_file SET file_type = %s WHERE file_type IS NOT NULL",
            [business_file_type.id]
        )
        
        updated_count = cursor.rowcount
        print(f"✅ Updated {updated_count} files to use FileType: {business_file_type}")
    
    print("\n✅ File type update completed!")

if __name__ == '__main__':
    update_file_types()
