import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Alert,
  Button,
  Paper,
  Card,
  CardContent,
  Grid,
  Chip,
  Fade,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip,
  CircularProgress,
  CardActions
} from '@mui/material';
import {
  Assessment,
  Add,
  Edit,
  Delete,
  Refresh,
  Save,
  Cancel
} from '@mui/icons-material';
import { useNotification } from '../../../contexts/NotificationContext';
import taxpayerService from '../../../services/taxpayerService';
import { normalizeCode, createCodeBlurHandler } from '../../../utils/codeUtils';

interface TaxPayerLevel {
  id: string;
  name: string;
  code: string;
  description: string;
  minimum_annual_turnover?: number;
  maximum_annual_turnover?: number;
  turnover_range: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface TaxPayerLevelCreate {
  name: string;
  code: string;
  description?: string;
  minimum_annual_turnover?: number;
  maximum_annual_turnover?: number;
}

interface TaxPayerLevelsTabProps {
  onDataChange?: () => void;
}

const TaxPayerLevelsTab: React.FC<TaxPayerLevelsTabProps> = ({ onDataChange }) => {
  const { showNotification } = useNotification();
  const [levels, setLevels] = useState<TaxPayerLevel[]>([]);
  const [loading, setLoading] = useState(true);
  const [formOpen, setFormOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [editingLevel, setEditingLevel] = useState<TaxPayerLevel | null>(null);
  const [levelToDelete, setLevelToDelete] = useState<TaxPayerLevel | null>(null);
  const [formData, setFormData] = useState<TaxPayerLevelCreate>({
    name: '',
    code: '',
    description: '',
    minimum_annual_turnover: undefined,
    maximum_annual_turnover: undefined,
  });
  const [formErrors, setFormErrors] = useState<any>({});
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    loadLevels();
  }, []);

  const loadLevels = async () => {
    try {
      setLoading(true);
      const data = await taxpayerService.getTaxPayerLevels();
      setLevels(data.results || data);
    } catch (error) {
      console.error('Failed to load tax payer levels:', error);
      showNotification('Failed to load tax payer levels', 'error');
    } finally {
      setLoading(false);
    }
  };
  const handleCreate = () => {
    setEditingLevel(null);
    setFormData({
      name: '',
      code: '',
      description: '',
      minimum_annual_turnover: undefined,
      maximum_annual_turnover: undefined,
    });
    setFormErrors({});
    setFormOpen(true);
  };

  const handleEdit = (level: TaxPayerLevel) => {
    setEditingLevel(level);
    setFormData({
      name: level.name,
      code: level.code,
      description: level.description || '',
      minimum_annual_turnover: level.minimum_annual_turnover,
      maximum_annual_turnover: level.maximum_annual_turnover,
    });
    setFormErrors({});
    setFormOpen(true);
  };

  const handleDelete = (level: TaxPayerLevel) => {
    setLevelToDelete(level);
    setDeleteDialogOpen(true);
  };

  const handleSubmit = async () => {
    try {
      setSubmitting(true);
      setFormErrors({});

      if (editingLevel) {
        await taxpayerService.updateTaxPayerLevel(editingLevel.id, formData);
        showNotification('Tax payer level updated successfully', 'success');
      } else {
        await taxpayerService.createTaxPayerLevel(formData);
        showNotification('Tax payer level created successfully', 'success');
      }

      setFormOpen(false);
      loadLevels();
      onDataChange?.();
    } catch (error: any) {
      console.error('Error submitting form:', error);
      if (error.response?.data) {
        setFormErrors(error.response.data);
      }
      showNotification('Failed to save tax payer level', 'error');
    } finally {
      setSubmitting(false);
    }
  };

  const confirmDelete = async () => {
    if (!levelToDelete) return;

    try {
      await taxpayerService.deleteTaxPayerLevel(levelToDelete.id);
      showNotification('Tax payer level deleted successfully', 'success');
      setDeleteDialogOpen(false);
      setLevelToDelete(null);
      loadLevels();
      onDataChange?.();
    } catch (error) {
      console.error('Failed to delete tax payer level:', error);
      showNotification('Failed to delete tax payer level', 'error');
    }
  };

  const formatCurrency = (amount?: number) => {
    if (!amount) return 'N/A';
    return new Intl.NumberFormat('en-ET', {
      style: 'currency',
      currency: 'ETB',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Fade in timeout={600}>
        <Box>
          {/* Header */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
            <Typography variant="h5" component="h2" sx={{ fontWeight: 600 }}>
              Tax Payer Levels ({levels.length})
            </Typography>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="outlined"
                startIcon={<Refresh />}
                onClick={loadLevels}
                disabled={loading}
                sx={{ borderRadius: 2 }}
              >
                Refresh
              </Button>
              <Button
                variant="contained"
                startIcon={<Add />}
                onClick={handleCreate}
                sx={{
                  borderRadius: 2,
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                  }
                }}
              >
                Add Level
              </Button>
            </Box>
          </Box>

          {/* Tax Payer Levels Grid */}
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : levels.length === 0 ? (
            <Paper sx={{ p: 4, textAlign: 'center' }}>
              <Assessment sx={{ fontSize: 64, color: 'text.disabled', mb: 2 }} />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                No Tax Payer Levels Found
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Create your first tax payer level to get started
              </Typography>
              <Button
                variant="contained"
                startIcon={<Add />}
                onClick={handleCreate}
                sx={{
                  borderRadius: 2,
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                }}
              >
                Add First Level
              </Button>
            </Paper>
          ) : (
            <Grid container spacing={3}>
              {levels.map((level) => (
                <Grid key={level.id} size={{ xs: 12, sm: 6, md: 4 }}>
                  <Card
                    sx={{
                      height: '100%',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: 4
                      }
                    }}
                  >
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <Assessment sx={{ color: 'primary.main', mr: 1 }} />
                        <Typography variant="h6" sx={{ fontWeight: 600 }}>
                          {level.name}
                        </Typography>
                      </Box>

                      <Chip
                        label={level.code}
                        color="primary"
                        sx={{ mb: 2, fontWeight: 'bold' }}
                      />

                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        {level.description}
                      </Typography>

                      <Box sx={{ mb: 2 }}>
                        <Typography variant="caption" color="text.secondary">
                          Turnover Range:
                        </Typography>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {level.minimum_annual_turnover && level.maximum_annual_turnover
                            ? `${formatCurrency(level.minimum_annual_turnover)} - ${formatCurrency(level.maximum_annual_turnover)}`
                            : level.minimum_annual_turnover
                            ? `> ${formatCurrency(level.minimum_annual_turnover)}`
                            : level.maximum_annual_turnover
                            ? `< ${formatCurrency(level.maximum_annual_turnover)}`
                            : 'Not specified'
                          }
                        </Typography>
                      </Box>
                    </CardContent>

                    <CardActions sx={{ justifyContent: 'flex-end', pt: 0 }}>
                      <Tooltip title="Edit Level">
                        <IconButton
                          size="small"
                          onClick={() => handleEdit(level)}
                          sx={{ color: 'primary.main' }}
                        >
                          <Edit />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete Level">
                        <IconButton
                          size="small"
                          onClick={() => handleDelete(level)}
                          sx={{ color: 'error.main' }}
                        >
                          <Delete />
                        </IconButton>
                      </Tooltip>
                    </CardActions>
                  </Card>
                </Grid>
              ))}
            </Grid>
          )}
          {/* Form Dialog */}
          <Dialog
            open={formOpen}
            onClose={() => setFormOpen(false)}
            maxWidth="sm"
            fullWidth
          >
            <DialogTitle>
              {editingLevel ? 'Edit Tax Payer Level' : 'Create Tax Payer Level'}
            </DialogTitle>
            <DialogContent>
              <Box sx={{ pt: 2 }}>
                <Grid container spacing={2}>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <TextField
                      fullWidth
                      label="Level Code"
                      value={formData.code}
                      onChange={(e) => setFormData({ ...formData, code: e.target.value })}
                      onBlur={createCodeBlurHandler(
                        (field, value) => setFormData(prev => ({ ...prev, [field]: value })),
                        'code'
                      )}
                      error={!!formErrors.code}
                      helperText={formErrors.code?.[0] || 'Code will be automatically converted to uppercase'}
                      placeholder="e.g., A, B, C, D"
                    />
                  </Grid>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <TextField
                      fullWidth
                      label="Level Name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      error={!!formErrors.name}
                      helperText={formErrors.name?.[0]}
                      placeholder="e.g., Category A - Large Taxpayers"
                    />
                  </Grid>
                  <Grid size={{ xs: 12 }}>
                    <TextField
                      fullWidth
                      label="Description"
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      error={!!formErrors.description}
                      helperText={formErrors.description?.[0]}
                      multiline
                      rows={2}
                      placeholder="Describe this tax payer level..."
                    />
                  </Grid>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <TextField
                      fullWidth
                      label="Minimum Annual Turnover (ETB)"
                      type="number"
                      value={formData.minimum_annual_turnover || ''}
                      onChange={(e) => setFormData({
                        ...formData,
                        minimum_annual_turnover: e.target.value ? Number(e.target.value) : undefined
                      })}
                      error={!!formErrors.minimum_annual_turnover}
                      helperText={formErrors.minimum_annual_turnover?.[0]}
                      placeholder="e.g., 1000000"
                    />
                  </Grid>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <TextField
                      fullWidth
                      label="Maximum Annual Turnover (ETB)"
                      type="number"
                      value={formData.maximum_annual_turnover || ''}
                      onChange={(e) => setFormData({
                        ...formData,
                        maximum_annual_turnover: e.target.value ? Number(e.target.value) : undefined
                      })}
                      error={!!formErrors.maximum_annual_turnover}
                      helperText={formErrors.maximum_annual_turnover?.[0]}
                      placeholder="e.g., 5000000"
                    />
                  </Grid>
                </Grid>
              </Box>
            </DialogContent>
            <DialogActions>
              <Button
                onClick={() => setFormOpen(false)}
                startIcon={<Cancel />}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmit}
                variant="contained"
                disabled={submitting}
                startIcon={submitting ? <CircularProgress size={20} /> : <Save />}
                sx={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                  }
                }}
              >
                {submitting ? 'Saving...' : 'Save'}
              </Button>
            </DialogActions>
          </Dialog>

          {/* Delete Confirmation Dialog */}
          <Dialog
            open={deleteDialogOpen}
            onClose={() => setDeleteDialogOpen(false)}
          >
            <DialogTitle>Confirm Delete</DialogTitle>
            <DialogContent>
              <Typography>
                Are you sure you want to delete the tax payer level "{levelToDelete?.name}"?
                This action cannot be undone.
              </Typography>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setDeleteDialogOpen(false)}>
                Cancel
              </Button>
              <Button
                onClick={confirmDelete}
                color="error"
                variant="contained"
              >
                Delete
              </Button>
            </DialogActions>
          </Dialog>
        </Box>
      </Fade>
    </Box>
  );
};

export default TaxPayerLevelsTab;
