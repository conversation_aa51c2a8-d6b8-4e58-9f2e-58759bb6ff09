import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  CircularProgress,
  Alert,
  Breadcrumbs,
  Link,
  Button,
  Collapse,
  IconButton,
} from '@mui/material';
import {
  Public,
  LocationOn,
  Business,
  LocationCity,
  Home,
  Map,
  NavigateNext,
  ExpandMore,
  ExpandLess,
  Clear,
} from '@mui/icons-material';
import geographicalLocationService from '../../services/geographicalLocationService';
import type { Country, Region, City, SubCity, Kebele } from '../../services/geographicalLocationService';

interface CompactLocationSelectorProps {
  value?: {
    country?: number | null;
    region?: number | null;
    city?: number | null;
    subcity?: number | null;
    kebele?: number | null;
  };
  onChange?: (location: {
    country?: number | null;
    region?: number | null;
    city?: number | null;
    subcity?: number | null;
    kebele?: number | null;
  }) => void;
  errors?: {
    country?: string;
    region?: string;
    city?: string;
    subcity?: string;
    kebele?: string;
  };
  required?: boolean;
  disabled?: boolean;
  showBreadcrumbs?: boolean;
  collapsible?: boolean;
  title?: string;
}

const CompactLocationSelector: React.FC<CompactLocationSelectorProps> = ({
  value = {},
  onChange,
  errors = {},
  required = false,
  disabled = false,
  showBreadcrumbs = true,
  collapsible = false,
  title = "Location Selection",
}) => {
  // State management
  const [countries, setCountries] = useState<Country[]>([]);
  const [regions, setRegions] = useState<Region[]>([]);
  const [cities, setCities] = useState<City[]>([]);
  const [subcities, setSubcities] = useState<SubCity[]>([]);
  const [kebeles, setKebeles] = useState<Kebele[]>([]);

  const [selectedObjects, setSelectedObjects] = useState<{
    country?: Country;
    region?: Region;
    city?: City;
    subcity?: SubCity;
    kebele?: Kebele;
  }>({});

  const [loading, setLoading] = useState({
    countries: false,
    regions: false,
    cities: false,
    subcities: false,
    kebeles: false,
  });

  const [expanded, setExpanded] = useState(!collapsible);

  // Load data functions
  const loadCountries = async () => {
    setLoading(prev => ({ ...prev, countries: true }));
    try {
      const data = await geographicalLocationService.getCountries();
      setCountries(data);
    } catch (error) {
      console.error('Error loading countries:', error);
    } finally {
      setLoading(prev => ({ ...prev, countries: false }));
    }
  };

  const loadRegions = async (countryId?: number) => {
    setLoading(prev => ({ ...prev, regions: true }));
    try {
      const data = await geographicalLocationService.getRegions(countryId);
      setRegions(data);
    } catch (error) {
      console.error('Error loading regions:', error);
    } finally {
      setLoading(prev => ({ ...prev, regions: false }));
    }
  };

  const loadCities = async (regionId?: number) => {
    setLoading(prev => ({ ...prev, cities: true }));
    try {
      const data = await geographicalLocationService.getCities(regionId);
      setCities(data);
    } catch (error) {
      console.error('Error loading cities:', error);
    } finally {
      setLoading(prev => ({ ...prev, cities: false }));
    }
  };

  const loadSubcities = async (cityId?: number) => {
    setLoading(prev => ({ ...prev, subcities: true }));
    try {
      const data = await geographicalLocationService.getSubCities(cityId);
      setSubcities(data);
    } catch (error) {
      console.error('Error loading subcities:', error);
    } finally {
      setLoading(prev => ({ ...prev, subcities: false }));
    }
  };

  const loadKebeles = async (subcityId?: number) => {
    setLoading(prev => ({ ...prev, kebeles: true }));
    try {
      const data = await geographicalLocationService.getKebeles(subcityId);
      setKebeles(data);
    } catch (error) {
      console.error('Error loading kebeles:', error);
    } finally {
      setLoading(prev => ({ ...prev, kebeles: false }));
    }
  };

  // Initial load
  useEffect(() => {
    loadCountries();
    if (value.country) {
      loadRegions(value.country);
    }
    if (value.region) {
      loadCities(value.region);
    }
    if (value.city) {
      loadSubcities(value.city);
    }
    if (value.subcity) {
      loadKebeles(value.subcity);
    }
  }, []);

  // Update selected objects when value changes
  useEffect(() => {
    const updateSelectedObjects = async () => {
      const newSelectedObjects: typeof selectedObjects = {};

      if (value.country && countries.length > 0) {
        newSelectedObjects.country = countries.find(c => c.id === value.country);
      }
      if (value.region && regions.length > 0) {
        newSelectedObjects.region = regions.find(r => r.id === value.region);
      }
      if (value.city && cities.length > 0) {
        newSelectedObjects.city = cities.find(c => c.id === value.city);
      }
      if (value.subcity && subcities.length > 0) {
        newSelectedObjects.subcity = subcities.find(s => s.id === value.subcity);
      }
      if (value.kebele && kebeles.length > 0) {
        newSelectedObjects.kebele = kebeles.find(k => k.id === value.kebele);
      }

      setSelectedObjects(newSelectedObjects);
    };

    updateSelectedObjects();
  }, [value, countries, regions, cities, subcities, kebeles]);

  // Selection handlers
  const handleCountryChange = (countryId: number | '') => {
    const country = countryId ? countries.find(c => c.id === countryId) : undefined;
    
    onChange?.({
      country: countryId || null,
      region: null,
      city: null,
      subcity: null,
      kebele: null,
    });

    // Reset dependent data
    setRegions([]);
    setCities([]);
    setSubcities([]);
    setKebeles([]);

    if (countryId) {
      loadRegions(countryId as number);
    }
  };

  const handleRegionChange = (regionId: number | '') => {
    onChange?.({
      ...value,
      region: regionId || null,
      city: null,
      subcity: null,
      kebele: null,
    });

    // Reset dependent data
    setCities([]);
    setSubcities([]);
    setKebeles([]);

    if (regionId) {
      loadCities(regionId as number);
    }
  };

  const handleCityChange = (cityId: number | '') => {
    onChange?.({
      ...value,
      city: cityId || null,
      subcity: null,
      kebele: null,
    });

    // Reset dependent data
    setSubcities([]);
    setKebeles([]);

    if (cityId) {
      loadSubcities(cityId as number);
    }
  };

  const handleSubcityChange = (subcityId: number | '') => {
    onChange?.({
      ...value,
      subcity: subcityId || null,
      kebele: null,
    });

    // Reset dependent data
    setKebeles([]);

    if (subcityId) {
      loadKebeles(subcityId as number);
    }
  };

  const handleKebeleChange = (kebeleId: number | '') => {
    onChange?.({
      ...value,
      kebele: kebeleId || null,
    });
  };

  const handleClear = () => {
    onChange?.({
      country: null,
      region: null,
      city: null,
      subcity: null,
      kebele: null,
    });
    setRegions([]);
    setCities([]);
    setSubcities([]);
    setKebeles([]);
  };

  // Breadcrumb component
  const LocationBreadcrumbs = () => {
    if (!showBreadcrumbs) return null;

    const breadcrumbItems = [
      selectedObjects.country && { label: selectedObjects.country.name, icon: <Public /> },
      selectedObjects.region && { label: selectedObjects.region.name, icon: <LocationOn /> },
      selectedObjects.city && { label: selectedObjects.city.name, icon: <LocationCity /> },
      selectedObjects.subcity && { label: selectedObjects.subcity.name, icon: <Business /> },
      selectedObjects.kebele && { label: selectedObjects.kebele.name, icon: <Home /> },
    ].filter(Boolean);

    if (breadcrumbItems.length === 0) return null;

    return (
      <Box sx={{ mb: 2, p: 2, bgcolor: 'primary.50', borderRadius: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
          <Map color="primary" fontSize="small" />
          <Typography variant="caption" color="primary" sx={{ fontWeight: 600 }}>
            Selected Location Path
          </Typography>
          <Button size="small" onClick={handleClear} sx={{ ml: 'auto', minWidth: 'auto', p: 0.5 }}>
            <Clear fontSize="small" />
          </Button>
        </Box>
        <Breadcrumbs separator={<NavigateNext fontSize="small" />}>
          {breadcrumbItems.map((item, index) => (
            <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              {React.cloneElement(item.icon, { fontSize: 'small' })}
              <Typography variant="caption">{item.label}</Typography>
            </Box>
          ))}
        </Breadcrumbs>
      </Box>
    );
  };

  const content = (
    <Box>
      <LocationBreadcrumbs />
      
      <Grid container spacing={2}>
        {/* Country */}
        <Grid size={{ xs: 12, sm: 6, md: 4 }}>
          <FormControl fullWidth error={!!errors.country} disabled={disabled}>
            <InputLabel>Country {required && '*'}</InputLabel>
            <Select
              value={value.country || ''}
              onChange={(e) => handleCountryChange(e.target.value as number)}
              label={`Country ${required ? '*' : ''}`}
              endAdornment={loading.countries && <CircularProgress size={20} />}
            >
              <MenuItem value="">
                <em>Select Country</em>
              </MenuItem>
              {countries.map((country) => (
                <MenuItem key={country.id} value={country.id}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Public fontSize="small" />
                    {country.name}
                  </Box>
                </MenuItem>
              ))}
            </Select>
            {errors.country && (
              <Typography variant="caption" color="error" sx={{ mt: 0.5 }}>
                {errors.country}
              </Typography>
            )}
          </FormControl>
        </Grid>

        {/* Region */}
        <Grid size={{ xs: 12, sm: 6, md: 4 }}>
          <FormControl fullWidth error={!!errors.region} disabled={disabled || !value.country}>
            <InputLabel>Region</InputLabel>
            <Select
              value={value.region || ''}
              onChange={(e) => handleRegionChange(e.target.value as number)}
              label="Region"
              endAdornment={loading.regions && <CircularProgress size={20} />}
            >
              <MenuItem value="">
                <em>Select Region</em>
              </MenuItem>
              {regions.map((region) => (
                <MenuItem key={region.id} value={region.id}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <LocationOn fontSize="small" />
                    {region.name}
                  </Box>
                </MenuItem>
              ))}
            </Select>
            {errors.region && (
              <Typography variant="caption" color="error" sx={{ mt: 0.5 }}>
                {errors.region}
              </Typography>
            )}
          </FormControl>
        </Grid>

        {/* City */}
        <Grid size={{ xs: 12, sm: 6, md: 4 }}>
          <FormControl fullWidth error={!!errors.city} disabled={disabled || !value.region}>
            <InputLabel>City</InputLabel>
            <Select
              value={value.city || ''}
              onChange={(e) => handleCityChange(e.target.value as number)}
              label="City"
              endAdornment={loading.cities && <CircularProgress size={20} />}
            >
              <MenuItem value="">
                <em>Select City</em>
              </MenuItem>
              {cities.map((city) => (
                <MenuItem key={city.id} value={city.id}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <LocationCity fontSize="small" />
                    {city.name}
                  </Box>
                </MenuItem>
              ))}
            </Select>
            {errors.city && (
              <Typography variant="caption" color="error" sx={{ mt: 0.5 }}>
                {errors.city}
              </Typography>
            )}
          </FormControl>
        </Grid>

        {/* SubCity */}
        <Grid size={{ xs: 12, sm: 6, md: 4 }}>
          <FormControl fullWidth error={!!errors.subcity} disabled={disabled || !value.city}>
            <InputLabel>SubCity / Woreda</InputLabel>
            <Select
              value={value.subcity || ''}
              onChange={(e) => handleSubcityChange(e.target.value as number)}
              label="SubCity / Woreda"
              endAdornment={loading.subcities && <CircularProgress size={20} />}
            >
              <MenuItem value="">
                <em>Select SubCity</em>
              </MenuItem>
              {subcities.map((subcity) => (
                <MenuItem key={subcity.id} value={subcity.id}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Business fontSize="small" />
                    {subcity.name}
                  </Box>
                </MenuItem>
              ))}
            </Select>
            {errors.subcity && (
              <Typography variant="caption" color="error" sx={{ mt: 0.5 }}>
                {errors.subcity}
              </Typography>
            )}
          </FormControl>
        </Grid>

        {/* Kebele */}
        <Grid size={{ xs: 12, sm: 6, md: 4 }}>
          <FormControl fullWidth error={!!errors.kebele} disabled={disabled || !value.subcity}>
            <InputLabel>Kebele</InputLabel>
            <Select
              value={value.kebele || ''}
              onChange={(e) => handleKebeleChange(e.target.value as number)}
              label="Kebele"
              endAdornment={loading.kebeles && <CircularProgress size={20} />}
            >
              <MenuItem value="">
                <em>Select Kebele</em>
              </MenuItem>
              {kebeles.map((kebele) => (
                <MenuItem key={kebele.id} value={kebele.id}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Home fontSize="small" />
                    {kebele.name}
                  </Box>
                </MenuItem>
              ))}
            </Select>
            {errors.kebele && (
              <Typography variant="caption" color="error" sx={{ mt: 0.5 }}>
                {errors.kebele}
              </Typography>
            )}
          </FormControl>
        </Grid>
      </Grid>
    </Box>
  );

  if (collapsible) {
    return (
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: expanded ? 2 : 0 }}>
            <Typography variant="h6" component="h3">
              {title}
            </Typography>
            <IconButton onClick={() => setExpanded(!expanded)}>
              {expanded ? <ExpandLess /> : <ExpandMore />}
            </IconButton>
          </Box>
          <Collapse in={expanded}>
            {content}
          </Collapse>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" component="h3" sx={{ mb: 2 }}>
          {title}
        </Typography>
        {content}
      </CardContent>
    </Card>
  );
};

export default CompactLocationSelector;
