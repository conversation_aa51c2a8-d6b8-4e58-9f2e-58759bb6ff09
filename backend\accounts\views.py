from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenObtainPairView
from django.contrib.auth import authenticate, login, logout
from django.utils import timezone
from .models import User, UserSession
from .serializers import (
    UserSerializer, UserCreateSerializer, UserUpdateSerializer,
    PasswordChangeSerializer, LoginSerializer, UserSessionSerializer,
    UserProfileSerializer
)
from requests.models import AuditLog


class CustomTokenObtainPairView(TokenObtainPairView):
    """Custom JWT token view with audit logging"""

    def post(self, request, *args, **kwargs):
        response = super().post(request, *args, **kwargs)

        if response.status_code == 200:
            # Log successful login
            username = request.data.get('username')
            user = User.objects.filter(username=username).first()
            if user:
                # Create user session
                UserSession.objects.create(
                    user=user,
                    session_key=request.session.session_key or 'jwt-session',
                    ip_address=self.get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', '')
                )

                # Log audit entry
                AuditLog.log_action(
                    user=user,
                    action=AuditLog.Action.LOGIN,
                    description="User logged in via JWT",
                    ip_address=self.get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', '')
                )

        return response

    def get_client_ip(self, request):
        """Get client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def login_view(request):
    """Login view with session management"""
    serializer = LoginSerializer(data=request.data)
    if serializer.is_valid():
        user = serializer.validated_data['user']

        # Generate JWT tokens
        refresh = RefreshToken.for_user(user)
        access_token = refresh.access_token

        # Update last login
        user.last_login = timezone.now()
        user.last_login_ip = get_client_ip(request)
        user.save()

        # Create user session
        UserSession.objects.create(
            user=user,
            session_key=request.session.session_key or str(refresh),
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )

        # Log audit entry
        AuditLog.log_action(
            user=user,
            action=AuditLog.Action.LOGIN,
            description="User logged in",
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )

        return Response({
            'access': str(access_token),
            'refresh': str(refresh),
            'user': UserSerializer(user).data
        })

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def logout_view(request):
    """Logout view with session cleanup"""
    try:
        # Mark user sessions as inactive
        UserSession.objects.filter(
            user=request.user,
            is_active=True
        ).update(
            is_active=False,
            logout_time=timezone.now()
        )

        # Log audit entry
        AuditLog.log_action(
            user=request.user,
            action=AuditLog.Action.LOGOUT,
            description="User logged out",
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )

        return Response({'message': 'Successfully logged out'})
    except Exception as e:
        return Response(
            {'error': 'Logout failed'},
            status=status.HTTP_400_BAD_REQUEST
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def profile_view(request):
    """Get current user profile"""
    serializer = UserProfileSerializer(request.user)
    return Response(serializer.data)


@api_view(['PUT', 'PATCH'])
@permission_classes([permissions.IsAuthenticated])
def update_profile_view(request):
    """Update current user profile"""
    serializer = UserUpdateSerializer(
        request.user,
        data=request.data,
        partial=request.method == 'PATCH'
    )
    if serializer.is_valid():
        serializer.save()

        # Log audit entry
        AuditLog.log_action(
            user=request.user,
            action=AuditLog.Action.UPDATE,
            obj=request.user,
            description="User updated profile",
            ip_address=get_client_ip(request)
        )

        return Response(UserProfileSerializer(request.user).data)

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def change_password_view(request):
    """Change user password"""
    serializer = PasswordChangeSerializer(
        data=request.data,
        context={'request': request}
    )
    if serializer.is_valid():
        user = request.user
        user.set_password(serializer.validated_data['new_password'])
        user.save()

        # Invalidate all user sessions
        UserSession.objects.filter(user=user, is_active=True).update(
            is_active=False,
            logout_time=timezone.now()
        )

        # Log audit entry
        AuditLog.log_action(
            user=user,
            action=AuditLog.Action.UPDATE,
            obj=user,
            description="User changed password",
            ip_address=get_client_ip(request)
        )

        return Response({'message': 'Password changed successfully'})

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UserListCreateView(generics.ListCreateAPIView):
    """List and create users"""

    queryset = User.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return UserCreateSerializer
        return UserSerializer

    def get_queryset(self):
        queryset = User.objects.select_related('organization')

        # Filter by organization if user is not admin
        if not self.request.user.has_role(User.Role.ADMIN):
            queryset = queryset.filter(organization=self.request.user.organization)

        return queryset

    def perform_create(self, serializer):
        try:
            # Log audit entry
            user = serializer.save()
            AuditLog.log_action(
                user=self.request.user,
                action=AuditLog.Action.CREATE,
                obj=user,
                description=f"Created user: {user.username}",
                ip_address=get_client_ip(self.request)
            )
        except Exception as e:
            # Log the error for debugging
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error creating user: {str(e)}")
            raise


class UserDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, and delete user"""

    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = User.objects.select_related('organization')

        # Filter by organization if user is not admin
        if not self.request.user.has_role(User.Role.ADMIN):
            queryset = queryset.filter(organization=self.request.user.organization)

        return queryset

    def perform_update(self, serializer):
        user = serializer.save()
        AuditLog.log_action(
            user=self.request.user,
            action=AuditLog.Action.UPDATE,
            obj=user,
            description=f"Updated user: {user.username}",
            ip_address=get_client_ip(self.request)
        )

    def perform_destroy(self, instance):
        AuditLog.log_action(
            user=self.request.user,
            action=AuditLog.Action.DELETE,
            obj=instance,
            description=f"Deleted user: {instance.username}",
            ip_address=get_client_ip(self.request)
        )
        instance.delete()


class UserSessionListView(generics.ListAPIView):
    """List user sessions"""

    serializer_class = UserSessionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return UserSession.objects.filter(
            user=self.request.user
        ).order_by('-login_time')


def get_client_ip(request):
    """Get client IP address"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip
