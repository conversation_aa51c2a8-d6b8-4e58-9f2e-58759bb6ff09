import requests
import json

# Test the organization update API
url = "http://127.0.0.1:8000/api/organizations/2/"
data = {
    "individual_penalty_rate": 8.0,
    "individual_interest_rate": 2.8,
    "organization_penalty_rate": 15.0,
    "organization_interest_rate": 4.0
}

print("Testing organization update API...")
print(f"URL: {url}")
print(f"Data: {json.dumps(data, indent=2)}")

try:
    response = requests.patch(url, json=data)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    
    if response.status_code == 200:
        print("✅ API update successful!")
        
        # Verify the update
        get_response = requests.get(url)
        if get_response.status_code == 200:
            org_data = get_response.json()
            print("\n✅ Verification - Current values:")
            print(f"Individual penalty rate: {org_data.get('individual_penalty_rate')}")
            print(f"Individual interest rate: {org_data.get('individual_interest_rate')}")
            print(f"Organization penalty rate: {org_data.get('organization_penalty_rate')}")
            print(f"Organization interest rate: {org_data.get('organization_interest_rate')}")
        else:
            print(f"❌ Failed to verify update: {get_response.status_code}")
    else:
        print(f"❌ API update failed: {response.status_code}")
        print(f"Error: {response.text}")
        
except Exception as e:
    print(f"❌ Error: {e}")
