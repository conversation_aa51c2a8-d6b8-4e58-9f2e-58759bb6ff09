# Generated by Django 5.2.3 on 2025-07-16 03:39

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("revenue_collection", "0002_cityservicerevenuecollection_due_date_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="cityservicerevenuecollection",
            name="interest_amount",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text="Interest amount for late payment",
                max_digits=15,
                validators=[django.core.validators.MinValueValidator(0)],
            ),
        ),
        migrations.AddField(
            model_name="cityservicerevenuecollection",
            name="interest_calculated_date",
            field=models.DateField(
                blank=True,
                help_text="Date when interest was last calculated",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="cityservicerevenuecollection",
            name="interest_rate",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text="Interest rate percentage per month",
                max_digits=5,
                validators=[
                    django.core.validators.MinValueValidator(0),
                    django.core.validators.MaxValueValidator(100),
                ],
            ),
        ),
        migrations.AddField(
            model_name="cityservicerevenuecollection",
            name="penalty_amount",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text="Penalty amount for late payment",
                max_digits=15,
                validators=[django.core.validators.MinValueValidator(0)],
            ),
        ),
        migrations.AddField(
            model_name="cityservicerevenuecollection",
            name="penalty_calculated_date",
            field=models.DateField(
                blank=True, help_text="Date when penalty was last calculated", null=True
            ),
        ),
        migrations.AddField(
            model_name="cityservicerevenuecollection",
            name="penalty_rate",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text="Penalty rate percentage",
                max_digits=5,
                validators=[
                    django.core.validators.MinValueValidator(0),
                    django.core.validators.MaxValueValidator(100),
                ],
            ),
        ),
        migrations.AddField(
            model_name="regionalrevenuecollection",
            name="interest_amount",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text="Interest amount for late payment",
                max_digits=15,
                validators=[django.core.validators.MinValueValidator(0)],
            ),
        ),
        migrations.AddField(
            model_name="regionalrevenuecollection",
            name="interest_calculated_date",
            field=models.DateField(
                blank=True,
                help_text="Date when interest was last calculated",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="regionalrevenuecollection",
            name="interest_rate",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text="Interest rate percentage per month",
                max_digits=5,
                validators=[
                    django.core.validators.MinValueValidator(0),
                    django.core.validators.MaxValueValidator(100),
                ],
            ),
        ),
        migrations.AddField(
            model_name="regionalrevenuecollection",
            name="penalty_amount",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text="Penalty amount for late payment",
                max_digits=15,
                validators=[django.core.validators.MinValueValidator(0)],
            ),
        ),
        migrations.AddField(
            model_name="regionalrevenuecollection",
            name="penalty_calculated_date",
            field=models.DateField(
                blank=True, help_text="Date when penalty was last calculated", null=True
            ),
        ),
        migrations.AddField(
            model_name="regionalrevenuecollection",
            name="penalty_rate",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text="Penalty rate percentage",
                max_digits=5,
                validators=[
                    django.core.validators.MinValueValidator(0),
                    django.core.validators.MaxValueValidator(100),
                ],
            ),
        ),
    ]
