# Generated by Django 5.2.3 on 2025-07-23 17:22

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("taxpayers", "0004_individualtaxpayer_business_closure_date_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="taxpayerlevel",
            options={
                "ordering": ["priority", "code"],
                "verbose_name": "Tax Payer Level",
                "verbose_name_plural": "Tax Payer Levels",
            },
        ),
        migrations.AddField(
            model_name="taxpayerlevel",
            name="max_daily_income",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                help_text="Maximum average daily income for this level (null for unlimited)",
                max_digits=15,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="taxpayerlevel",
            name="min_daily_income",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text="Minimum average daily income for this level (in local currency)",
                max_digits=15,
            ),
        ),
        migrations.AddField(
            model_name="taxpayerlevel",
            name="priority",
            field=models.IntegerField(
                default=1,
                help_text="Level priority (1=highest level like A, 3=lowest level like C)",
            ),
        ),
        migrations.AddField(
            model_name="taxpayerlevel",
            name="requires_annual_assessment",
            field=models.BooleanField(
                default=True,
                help_text="Whether taxpayers at this level require annual income assessment",
            ),
        ),
        migrations.AddField(
            model_name="taxpayerlevel",
            name="tax_rate_percentage",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text="Tax rate percentage for this level",
                max_digits=5,
            ),
        ),
        migrations.CreateModel(
            name="DailyIncomeAnalysis",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "analysis_year",
                    models.IntegerField(help_text="Year of analysis (e.g., 2024)"),
                ),
                (
                    "analysis_date",
                    models.DateField(
                        auto_now_add=True, help_text="Date when analysis was conducted"
                    ),
                ),
                (
                    "taxpayer_type",
                    models.CharField(
                        choices=[
                            ("individual", "Individual Taxpayer"),
                            ("organization", "Organization Taxpayer"),
                        ],
                        help_text="Type of taxpayer",
                        max_length=20,
                    ),
                ),
                (
                    "taxpayer_id",
                    models.UUIDField(
                        help_text="ID of the taxpayer (individual or organization)"
                    ),
                ),
                (
                    "total_annual_income",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Total annual income reported/calculated",
                        max_digits=15,
                    ),
                ),
                (
                    "average_daily_income",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Average daily income (total_annual_income / 365)",
                        max_digits=15,
                    ),
                ),
                (
                    "working_days",
                    models.IntegerField(
                        default=365,
                        help_text="Number of working days considered for calculation",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending Review"),
                            ("approved", "Approved"),
                            ("rejected", "Rejected"),
                            ("implemented", "Level Change Implemented"),
                        ],
                        default="pending",
                        help_text="Status of the analysis",
                        max_length=20,
                    ),
                ),
                ("reviewed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "review_notes",
                    models.TextField(blank=True, help_text="Notes from the reviewer"),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who created this analysis",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_analyses",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "current_level",
                    models.ForeignKey(
                        help_text="Current taxpayer level",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="current_analyses",
                        to="taxpayers.taxpayerlevel",
                    ),
                ),
                (
                    "recommended_level",
                    models.ForeignKey(
                        blank=True,
                        help_text="Recommended level based on income analysis",
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="recommended_analyses",
                        to="taxpayers.taxpayerlevel",
                    ),
                ),
                (
                    "reviewed_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="Revenue expert who reviewed this analysis",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="reviewed_analyses",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Daily Income Analysis",
                "verbose_name_plural": "Daily Income Analyses",
                "ordering": ["-analysis_year", "-created_at"],
                "unique_together": {("taxpayer_type", "taxpayer_id", "analysis_year")},
            },
        ),
        migrations.CreateModel(
            name="LevelUpgradeNotification",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "title",
                    models.CharField(help_text="Notification title", max_length=200),
                ),
                ("message", models.TextField(help_text="Notification message")),
                (
                    "is_read",
                    models.BooleanField(
                        default=False, help_text="Whether notification has been read"
                    ),
                ),
                (
                    "is_dismissed",
                    models.BooleanField(
                        default=False,
                        help_text="Whether notification has been dismissed",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("read_at", models.DateTimeField(blank=True, null=True)),
                ("dismissed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "analysis",
                    models.OneToOneField(
                        help_text="Related income analysis",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notification",
                        to="taxpayers.dailyincomeanalysis",
                    ),
                ),
            ],
            options={
                "verbose_name": "Level Upgrade Notification",
                "verbose_name_plural": "Level Upgrade Notifications",
                "ordering": ["-created_at"],
            },
        ),
    ]
