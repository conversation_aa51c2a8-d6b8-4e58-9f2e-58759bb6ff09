#!/usr/bin/env python
"""
Test script to verify the new location hierarchy works correctly
Building → Shelf → Box → Kent → File → Documents
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'arada_dms.settings')
django.setup()

from accounts.models import User
from organizations.models import Organization
from locations.models import Building, Shelf, Box, Kent, File
from documents.models import DocumentType, Document

def test_hierarchy():
    print("Testing new location hierarchy: Building → Shelf → Box → Kent → File → Documents")
    print("=" * 80)
    
    # Get or create test organization
    org, created = Organization.objects.get_or_create(
        name="Test Organization",
        defaults={
            'short_name': 'TEST',
            'description': 'Test organization for hierarchy testing',
            'is_default': True
        }
    )
    print(f"Organization: {org.name} ({'created' if created else 'exists'})")
    
    # Create building
    building, created = Building.objects.get_or_create(
        organization=org,
        code="B1",
        defaults={
            'name': 'Main Building',
            'description': 'Main building for testing'
        }
    )
    print(f"Building: {building.name} ({building.code}) ({'created' if created else 'exists'})")
    
    # Create shelf
    shelf, created = Shelf.objects.get_or_create(
        building=building,
        code="S1",
        defaults={
            'name': 'Shelf 1',
            'description': 'First shelf for testing',
            'rows': 3,
            'columns': 4
        }
    )
    print(f"Shelf: {shelf.name} ({shelf.code}) - {shelf.rows}x{shelf.columns} grid ({'created' if created else 'exists'})")
    
    # Create boxes at different positions
    boxes_created = 0
    for row in range(1, 4):  # 3 rows
        for col in range(1, 3):  # 2 columns per row
            box, created = Box.objects.get_or_create(
                shelf=shelf,
                row=row,
                column=col,
                defaults={
                    'name': f'Box R{row:02d}C{col:02d}',
                    'description': f'Box at row {row}, column {col}',
                    'color': 'Blue' if (row + col) % 2 == 0 else 'Red',
                    'material': 'Cardboard'
                }
            )
            if created:
                boxes_created += 1
            print(f"  Box: {box.position_code} - {box.name} ({'created' if created else 'exists'})")
    
    print(f"Created {boxes_created} new boxes")
    
    # Create kents in some boxes
    kents_created = 0
    for box in Box.objects.filter(shelf=shelf)[:3]:  # First 3 boxes
        for i in range(1, 3):  # 2 kents per box
            kent, created = Kent.objects.get_or_create(
                box=box,
                code=f"K{i}",
                defaults={
                    'name': f'Kent {i}',
                    'description': f'Kent {i} in {box.position_code}',
                    'capacity': 50,
                    'color': 'Green',
                    'material': 'Metal'
                }
            )
            if created:
                kents_created += 1
            print(f"    Kent: {kent.code} - {kent.name} in {kent.box.position_code} ({'created' if created else 'exists'})")
    
    print(f"Created {kents_created} new kents")
    
    # Create files in some kents
    files_created = 0
    for kent in Kent.objects.filter(box__shelf=shelf)[:2]:  # First 2 kents
        for i in range(1, 3):  # 2 files per kent
            file_obj, created = File.objects.get_or_create(
                kent=kent,
                file_number=f"F{kent.id}-{i:03d}",
                defaults={
                    'name': f'Business File {i}',
                    'description': f'Test business file {i}',
                    'file_type': 'business',
                    'business_name': f'Test Business {i}',
                    'tin_number': f'TIN{i:06d}',
                    'status': 'active'
                }
            )
            if created:
                files_created += 1
            print(f"      File: {file_obj.file_number} - {file_obj.name} ({'created' if created else 'exists'})")
    
    print(f"Created {files_created} new files")
    
    # Create document type
    doc_type, created = DocumentType.objects.get_or_create(
        organization=org,
        name="Tax Certificate",
        defaults={
            'description': 'Tax certificate documents',
            'code': 'TAX'
        }
    )
    print(f"Document Type: {doc_type.name} ({'created' if created else 'exists'})")
    
    # Create documents in files
    documents_created = 0
    for file_obj in File.objects.filter(kent__box__shelf=shelf)[:2]:  # First 2 files
        for i in range(1, 4):  # 3 documents per file
            document, created = Document.objects.get_or_create(
                document_file=file_obj,
                title=f"{doc_type.name} {i}",
                defaults={
                    'document_type': doc_type,
                    'description': f'Test {doc_type.name.lower()} document {i}',
                    'mode': 'physical',
                    'status': 'active',
                    'kent': file_obj.kent  # Legacy field auto-populated
                }
            )
            if created:
                documents_created += 1
            print(f"        Document: {document.title} in {document.document_file.name} ({'created' if created else 'exists'})")
    
    print(f"Created {documents_created} new documents")
    
    print("\n" + "=" * 80)
    print("HIERARCHY VERIFICATION")
    print("=" * 80)
    
    # Test the complete hierarchy
    for building in Building.objects.filter(organization=org):
        print(f"\n🏢 {building.name} ({building.code})")
        
        for shelf in building.shelves.all():
            print(f"  📚 {shelf.name} ({shelf.code}) - {shelf.rows}x{shelf.columns}")
            
            for box in shelf.boxes.all():
                print(f"    📦 {box.position_code} - {box.name or 'Unnamed'}")
                
                for kent in box.kents.all():
                    print(f"      🗃️  {kent.code} - {kent.name}")
                    
                    for file_obj in kent.files.all():
                        print(f"        📁 {file_obj.file_number} - {file_obj.name}")
                        
                        for document in file_obj.documents.all():
                            print(f"          📄 {document.title}")
    
    print("\n" + "=" * 80)
    print("STATISTICS")
    print("=" * 80)
    
    total_buildings = Building.objects.filter(organization=org).count()
    total_shelves = Shelf.objects.filter(building__organization=org).count()
    total_boxes = Box.objects.filter(shelf__building__organization=org).count()
    total_kents = Kent.objects.filter(box__shelf__building__organization=org).count()
    total_files = File.objects.filter(kent__box__shelf__building__organization=org).count()
    total_documents = Document.objects.filter(document_file__kent__box__shelf__building__organization=org).count()
    
    print(f"Buildings: {total_buildings}")
    print(f"Shelves: {total_shelves}")
    print(f"Boxes: {total_boxes}")
    print(f"Kents: {total_kents}")
    print(f"Files: {total_files}")
    print(f"Documents: {total_documents}")
    
    print("\n" + "=" * 80)
    print("FULL CODE EXAMPLES")
    print("=" * 80)
    
    # Show full codes for each level
    for box in Box.objects.filter(shelf__building__organization=org)[:3]:
        print(f"Box Full Code: {box.full_code}")
        
        for kent in box.kents.all()[:1]:
            print(f"  Kent Full Code: {kent.full_code}")
            
            for file_obj in kent.files.all()[:1]:
                print(f"    File Full Code: {file_obj.full_code}")
    
    print("\n✅ Hierarchy test completed successfully!")
    print("The new structure Building → Shelf → Box → Kent → File → Documents is working correctly.")

if __name__ == '__main__':
    test_hierarchy()
