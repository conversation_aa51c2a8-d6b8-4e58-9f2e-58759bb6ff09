"""
Management command to populate revenue collection system with sample data

This command creates sample revenue categories, sources, and periods
with Ethiopian context and realistic data.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import date, timedelta
from revenue_collection.models import (
    RegionalCategory, CityServiceCategory,
    RegionalRevenueSource, CityServiceRevenueSource,
    RevenuePeriod
)

User = get_user_model()


class Command(BaseCommand):
    help = 'Populate revenue collection system with sample Ethiopian data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing data before populating',
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write('Clearing existing revenue data...')
            RegionalRevenueSource.objects.all().delete()
            CityServiceRevenueSource.objects.all().delete()
            RegionalCategory.objects.all().delete()
            CityServiceCategory.objects.all().delete()
            RevenuePeriod.objects.all().delete()
            self.stdout.write(self.style.SUCCESS('Existing data cleared.'))

        # Get or create admin user
        admin_user = User.objects.filter(is_superuser=True).first()
        if not admin_user:
            admin_user = User.objects.create_user(
                username='admin',
                email='<EMAIL>',
                password='admin123',
                is_superuser=True,
                is_staff=True
            )

        self.stdout.write('Creating regional categories...')
        self.create_regional_categories(admin_user)

        self.stdout.write('Creating city service categories...')
        self.create_city_service_categories(admin_user)

        self.stdout.write('Creating regional revenue sources...')
        self.create_regional_revenue_sources(admin_user)

        self.stdout.write('Creating city service revenue sources...')
        self.create_city_service_revenue_sources(admin_user)

        self.stdout.write('Creating revenue periods...')
        self.create_revenue_periods(admin_user)

        self.stdout.write(self.style.SUCCESS('Successfully populated revenue collection data!'))

    def create_regional_categories(self, user):
        """Create regional revenue categories"""
        categories = [
            {
                'code': 'INCOME',
                'name': 'የገቢ ግብር (Income Tax)',
                'description': 'Tax on individual and business income'
            },
            {
                'code': 'LAND',
                'name': 'የመሬት ግብር (Land Tax)',
                'description': 'Tax on land ownership and usage'
            },
            {
                'code': 'AGRI',
                'name': 'የግብርና ግብር (Agricultural Tax)',
                'description': 'Tax on agricultural activities and products'
            },
            {
                'code': 'MINING',
                'name': 'የማዕድን ግብር (Mining Tax)',
                'description': 'Tax on mining activities and mineral extraction'
            },
            {
                'code': 'FOREST',
                'name': 'የደን ግብር (Forest Tax)',
                'description': 'Tax on forest products and timber'
            },
            {
                'code': 'STAMP',
                'name': 'የማህተም ግብር (Stamp Duty)',
                'description': 'Tax on legal documents and transactions'
            }
        ]

        for cat_data in categories:
            category, created = RegionalCategory.objects.get_or_create(
                code=cat_data['code'],
                defaults={
                    'name': cat_data['name'],
                    'description': cat_data['description'],
                    'created_by': user
                }
            )
            if created:
                self.stdout.write(f'  Created: {category}')

    def create_city_service_categories(self, user):
        """Create city service revenue categories"""
        categories = [
            {
                'code': 'BUSINESS',
                'name': 'የንግድ ፈቃድ (Business License)',
                'description': 'Business licensing and permits'
            },
            {
                'code': 'BUILDING',
                'name': 'የግንባታ ፈቃድ (Building Permit)',
                'description': 'Construction and building permits'
            },
            {
                'code': 'MARKET',
                'name': 'የገበያ አገልግሎት (Market Services)',
                'description': 'Market stall rentals and services'
            },
            {
                'code': 'TRANSPORT',
                'name': 'የትራንስፖርት አገልግሎት (Transport Services)',
                'description': 'Transportation and vehicle services'
            },
            {
                'code': 'WASTE',
                'name': 'የቆሻሻ አስወጣ (Waste Management)',
                'description': 'Waste collection and management services'
            },
            {
                'code': 'WATER',
                'name': 'የውሃ አገልግሎት (Water Services)',
                'description': 'Water supply and sanitation services'
            },
            {
                'code': 'PROPERTY',
                'name': 'የንብረት ግብር (Property Tax)',
                'description': 'Municipal property tax'
            }
        ]

        for cat_data in categories:
            category, created = CityServiceCategory.objects.get_or_create(
                code=cat_data['code'],
                defaults={
                    'name': cat_data['name'],
                    'description': cat_data['description'],
                    'created_by': user
                }
            )
            if created:
                self.stdout.write(f'  Created: {category}')

    def create_regional_revenue_sources(self, user):
        """Create regional revenue sources"""
        # Income tax sources
        income_category = RegionalCategory.objects.get(code='INCOME')
        income_sources = [
            {'code': 'INC001', 'name': 'የግል ገቢ ግብር (Personal Income Tax)'},
            {'code': 'INC002', 'name': 'የንግድ ገቢ ግብር (Business Income Tax)'},
            {'code': 'INC003', 'name': 'የኮርፖሬት ገቢ ግብር (Corporate Income Tax)'},
            {'code': 'INC004', 'name': 'የካፒታል ገይን ግብር (Capital Gains Tax)'},
        ]

        # Land tax sources
        land_category = RegionalCategory.objects.get(code='LAND')
        land_sources = [
            {'code': 'LND001', 'name': 'የከተማ መሬት ግብር (Urban Land Tax)'},
            {'code': 'LND002', 'name': 'የገጠር መሬት ግብር (Rural Land Tax)'},
            {'code': 'LND003', 'name': 'የመሬት ኪራይ ግብር (Land Lease Tax)'},
        ]

        # Agricultural tax sources
        agri_category = RegionalCategory.objects.get(code='AGRI')
        agri_sources = [
            {'code': 'AGR001', 'name': 'የሰብል ግብር (Crop Tax)'},
            {'code': 'AGR002', 'name': 'የእንስሳት ግብር (Livestock Tax)'},
            {'code': 'AGR003', 'name': 'የግብርና ምርት ግብር (Agricultural Product Tax)'},
        ]

        all_sources = [
            (income_category, income_sources),
            (land_category, land_sources),
            (agri_category, agri_sources)
        ]

        for category, sources in all_sources:
            for source_data in sources:
                source, created = RegionalRevenueSource.objects.get_or_create(
                    code=source_data['code'],
                    defaults={
                        'category': category,
                        'name': source_data['name'],
                        'description': f"Revenue source under {category.name}",
                        'created_by': user
                    }
                )
                if created:
                    self.stdout.write(f'  Created: {source}')

    def create_city_service_revenue_sources(self, user):
        """Create city service revenue sources"""
        # Business license sources
        business_category = CityServiceCategory.objects.get(code='BUSINESS')
        business_sources = [
            {'code': 'BUS001', 'name': 'የንግድ ፈቃድ ክፍያ (Business License Fee)'},
            {'code': 'BUS002', 'name': 'የንግድ ፈቃድ እድሳት (License Renewal)'},
            {'code': 'BUS003', 'name': 'የንግድ ምዝገባ (Business Registration)'},
        ]

        # Building permit sources
        building_category = CityServiceCategory.objects.get(code='BUILDING')
        building_sources = [
            {'code': 'BLD001', 'name': 'የግንባታ ፈቃድ ክፍያ (Building Permit Fee)'},
            {'code': 'BLD002', 'name': 'የግንባታ ቁጥጥር ክፍያ (Construction Inspection Fee)'},
            {'code': 'BLD003', 'name': 'የግንባታ ማጠናቀቂያ ሰርተፊኬት (Completion Certificate)'},
        ]

        # Market services sources
        market_category = CityServiceCategory.objects.get(code='MARKET')
        market_sources = [
            {'code': 'MKT001', 'name': 'የገበያ ቦታ ኪራይ (Market Stall Rent)'},
            {'code': 'MKT002', 'name': 'የገበያ አገልግሎት ክፍያ (Market Service Fee)'},
            {'code': 'MKT003', 'name': 'የገበያ ጽዳት ክፍያ (Market Cleaning Fee)'},
        ]

        all_sources = [
            (business_category, business_sources),
            (building_category, building_sources),
            (market_category, market_sources)
        ]

        for category, sources in all_sources:
            for source_data in sources:
                source, created = CityServiceRevenueSource.objects.get_or_create(
                    code=source_data['code'],
                    defaults={
                        'category': category,
                        'name': source_data['name'],
                        'description': f"Revenue source under {category.name}",
                        'created_by': user
                    }
                )
                if created:
                    self.stdout.write(f'  Created: {source}')

    def create_revenue_periods(self, user):
        """Create revenue periods for current and upcoming years"""
        current_year = timezone.now().year
        
        # Create quarterly periods for current and next year
        for year in [current_year, current_year + 1]:
            quarters = [
                ('Q1', 1, 3),
                ('Q2', 4, 6),
                ('Q3', 7, 9),
                ('Q4', 10, 12)
            ]
            
            for quarter, start_month, end_month in quarters:
                start_date = date(year, start_month, 1)
                
                # Calculate end date (last day of end month)
                if end_month == 12:
                    end_date = date(year, 12, 31)
                else:
                    next_month = date(year, end_month + 1, 1)
                    end_date = next_month - timedelta(days=1)
                
                period, created = RevenuePeriod.objects.get_or_create(
                    code=f'{year}{quarter}',
                    defaults={
                        'name': f'{quarter} {year}',
                        'start_date': start_date,
                        'end_date': end_date,
                        'is_closed': year < current_year,  # Close past periods
                        'created_by': user
                    }
                )
                if created:
                    self.stdout.write(f'  Created: {period}')

        # Create monthly periods for current year
        for month in range(1, 13):
            start_date = date(current_year, month, 1)
            
            # Calculate end date (last day of month)
            if month == 12:
                end_date = date(current_year, 12, 31)
            else:
                next_month = date(current_year, month + 1, 1)
                end_date = next_month - timedelta(days=1)
            
            month_names = [
                'January', 'February', 'March', 'April', 'May', 'June',
                'July', 'August', 'September', 'October', 'November', 'December'
            ]
            
            period, created = RevenuePeriod.objects.get_or_create(
                code=f'{current_year}-{month:02d}',
                defaults={
                    'name': f'{month_names[month-1]} {current_year}',
                    'start_date': start_date,
                    'end_date': end_date,
                    'is_closed': month < timezone.now().month,  # Close past months
                    'created_by': user
                }
            )
            if created:
                self.stdout.write(f'  Created: {period}')
